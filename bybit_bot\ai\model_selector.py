"""
Model Selector for Autonomous Trading Bot
Implements adaptive model selection and ensemble management
"""
import asyncio
import time
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum

from ..core.config import BotConfig
from ..core.logger import TradingBotLogger
from ..database.connection import DatabaseManager

# Scikit-learn imports
from sklearn.ensemble import (
    RandomForestRegressor, RandomForestClassifier,
    GradientBoostingRegressor, GradientBoostingClassifier,
    ExtraTreesRegressor, ExtraTreesClassifier,
    AdaBoostRegressor, AdaBoostClassifier
)
from sklearn.linear_model import (
    LinearRegression, LogisticRegression,
    Ridge, Lasso, ElasticNet
)
from sklearn.svm import SVR, SVC
from sklearn.neural_network import MLPRegressor, MLPClassifier

# Optional imports for XGBoost and LightGBM
try:
    import xgboost as xgb
    XGB_AVAILABLE = True
except ImportError:
    xgb = None  # type: ignore
    XGB_AVAILABLE = False

try:
    import lightgbm as lgb
    LGB_AVAILABLE = True
except ImportError:
    lgb = None
    LGB_AVAILABLE = False

# Check TensorFlow availability
try:
    import tensorflow as tf
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False


class ModelType(Enum):
    """Model types"""
    REGRESSION = "regression"
    CLASSIFICATION = "classification"
    TIME_SERIES = "time_series"
    ENSEMBLE = "ensemble"
    NEURAL_NETWORK = "neural_network"
    DEEP_LEARNING = "deep_learning"
    REINFORCEMENT_LEARNING = "reinforcement_learning"


class ModelFamily(Enum):
    """Model families"""
    LINEAR = "linear"
    TREE_BASED = "tree_based"
    NEURAL_NETWORK = "neural_network"
    ENSEMBLE = "ensemble"
    PROBABILISTIC = "probabilistic"
    KERNEL_METHODS = "kernel_methods"
    DEEP_LEARNING = "deep_learning"


class SelectionCriteria(Enum):
    """Model selection criteria"""
    PERFORMANCE = "performance"
    SPEED = "speed"
    INTERPRETABILITY = "interpretability"
    ROBUSTNESS = "robustness"
    MEMORY_EFFICIENCY = "memory_efficiency"
    SCALABILITY = "scalability"
    GENERALIZATION = "generalization"
    STABILITY = "stability"


class EnsembleMethod(Enum):
    """Ensemble methods"""
    VOTING = "voting"
    STACKING = "stacking"
    BLENDING = "blending"
    BAGGING = "bagging"
    BOOSTING = "boosting"
    DYNAMIC_SELECTION = "dynamic_selection"


@dataclass
class ModelPerformance:
    """Model performance metrics"""
    model_id: str
    model_name: str
    training_score: float
    validation_score: float
    test_score: float
    cross_validation_score: float
    std_dev: float
    training_time: float
    prediction_time: float
    memory_usage: float
    stability_score: float
    interpretability_score: float
    robustness_score: float
    last_updated: datetime


@dataclass
class ModelMetadata:
    """Model metadata"""
    model_id: str
    model_name: str
    model_type: ModelType
    model_family: ModelFamily
    hyperparameters: Dict[str, Any]
    feature_importance: Dict[str, float]
    training_data_size: int
    feature_count: int
    target_variable: str
    preprocessing_steps: List[str]
    validation_method: str
    created_at: datetime
    updated_at: datetime
    version: int


@dataclass
class ModelCandidate:
    """Model candidate for selection"""
    model_id: str
    model_name: str
    model_type: ModelType
    model_family: ModelFamily
    trained_model: Any
    performance: ModelPerformance
    metadata: ModelMetadata
    selection_score: float
    confidence: float
    suitability_score: float
    risk_score: float


@dataclass
class EnsembleConfig:
    """Ensemble configuration"""
    ensemble_id: str
    ensemble_method: EnsembleMethod
    base_models: List[str]
    model_weights: Dict[str, float]
    combination_strategy: str
    meta_learner: Optional[str]
    performance_threshold: float
    diversity_threshold: float
    created_at: datetime


@dataclass
class SelectionResult:
    """Model selection result"""
    selection_id: str
    selected_model: str
    selection_criteria: SelectionCriteria
    selection_score: float
    confidence: float
    alternative_models: List[str]
    performance_comparison: Dict[str, ModelPerformance]
    selection_reasoning: List[str]
    created_at: datetime


class ModelSelector:
    """
    Adaptive model selector for trading applications
    
    Features:
    - Dynamic model selection based on performance
    - Ensemble model creation and management
    - Real-time model switching
    - Performance monitoring and comparison
    - Automated hyperparameter tuning
    - Model interpretability analysis
    - Robustness testing
    - Memory and speed optimization
    - Online learning adaptation
    - Multi-objective optimization
    """
    
    def __init__(self, config: BotConfig, database_manager: DatabaseManager):
        self.config = config
        self.db_manager = database_manager
        self.logger = TradingBotLogger("ModelSelector")
        
        # Model registry
        self.model_registry: Dict[str, ModelCandidate] = {}
        self.model_factory: Dict[str, Callable] = {}
        self.ensemble_registry: Dict[str, EnsembleConfig] = {}
        
        # Performance tracking
        self.performance_history: Dict[str, List[ModelPerformance]] = {}
        self.model_comparisons: Dict[str, Dict[str, float]] = {}
        self.selection_history: List[SelectionResult] = []
        
        # Model pools
        self.active_models: Dict[str, ModelCandidate] = {}
        self.retired_models: Dict[str, ModelCandidate] = {}
        self.candidate_models: Dict[str, ModelCandidate] = {}
        
        # Selection criteria weights
        self.criteria_weights: Dict[SelectionCriteria, float] = {
            SelectionCriteria.PERFORMANCE: 0.4,
            SelectionCriteria.SPEED: 0.15,
            SelectionCriteria.INTERPRETABILITY: 0.1,
            SelectionCriteria.ROBUSTNESS: 0.15,
            SelectionCriteria.MEMORY_EFFICIENCY: 0.1,
            SelectionCriteria.SCALABILITY: 0.05,
            SelectionCriteria.GENERALIZATION: 0.05
        }
        
        # Configuration
        self.max_models_per_type = 5
        self.performance_threshold = 0.7
        self.evaluation_window = 1000  # samples
        self.retraining_interval = 3600  # seconds
        
        # Metrics
        self.selector_metrics = {
            'total_models': 0,
            'active_models': 0,
            'ensembles_created': 0,
            'model_switches': 0,
            'performance_improvements': 0,
            'selection_accuracy': 0.0,
            'average_model_performance': 0.0
        }
        
        # Control flags
        self.is_running = False
        self.auto_selection = True
        self.ensemble_enabled = True
        
        # Initialize components
        self._initialize_model_factory()
        self._initialize_hyperparameter_spaces()
    
    async def initialize(self):
        """Initialize the model selector"""
        try:
            self.logger.info("Initializing Model Selector")
            
            # Load existing models
            await self._load_existing_models()
            
            # Initialize base models
            await self._initialize_base_models()
            
            # Start selection loops
            self.is_running = True
            asyncio.create_task(self._model_evaluation_loop())
            asyncio.create_task(self._model_selection_loop())
            asyncio.create_task(self._ensemble_management_loop())
            asyncio.create_task(self._performance_monitoring_loop())
            
            self.logger.info("Model Selector initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Model Selector: {e}")
            raise
    
    async def shutdown(self):
        """Shutdown the model selector"""
        try:
            self.logger.info("Shutting down Model Selector")
            
            self.is_running = False
            
            # Save models
            await self._save_models()
            
            self.logger.info("Model Selector shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error shutting down Model Selector: {e}")
    
    async def select_best_model(self, 
                              task_type: str,
                              data: pd.DataFrame,
                              target: str,
                              criteria: SelectionCriteria = SelectionCriteria.PERFORMANCE) -> SelectionResult:
        """Select the best model for a given task"""
        try:
            self.logger.info(f"Selecting best model for task: {task_type}")
            candidates = await self._generate_candidate_models(task_type, data, target)
            evaluated_candidates = await self._evaluate_candidates(candidates, data, target)
            best_model = await self._select_best_candidate(evaluated_candidates, criteria)
            if best_model is None:
                self.logger.error("No valid model candidate found.")
                raise ValueError("No valid model candidate found.")
            result = SelectionResult(
                selection_id=f"selection_{int(time.time())}",
                selected_model=best_model.model_id,
                selection_criteria=criteria,
                selection_score=best_model.selection_score,
                confidence=best_model.confidence,
                alternative_models=[c.model_id for c in evaluated_candidates[:3] if c.model_id != best_model.model_id],
                performance_comparison={c.model_id: c.performance for c in evaluated_candidates},
                selection_reasoning=await self._generate_selection_reasoning(best_model, evaluated_candidates),
                created_at=datetime.now()
            )
            self.active_models[task_type] = best_model
            self.selection_history.append(result)
            self.selector_metrics['model_switches'] += 1
            return result
        except Exception as e:
            self.logger.error(f"Error selecting best model: {e}")
            raise
    
    async def create_ensemble(self, 
                            task_type: str,
                            base_models: List[str],
                            ensemble_method: EnsembleMethod = EnsembleMethod.VOTING,
                            data: Optional[pd.DataFrame] = None,
                            target: Optional[str] = None) -> EnsembleConfig:
        """Create ensemble model"""
        try:
            self.logger.info(f"Creating ensemble for task: {task_type}")
            validated_models = await self._validate_base_models(base_models)
            if data is None or target is None:
                self.logger.error("Data and target must be provided for ensemble creation.")
                raise ValueError("Data and target must be provided for ensemble creation.")
            model_weights = await self._calculate_model_weights(validated_models, data, target)
            ensemble_config = EnsembleConfig(
                ensemble_id=f"ensemble_{task_type}_{int(time.time())}",
                ensemble_method=ensemble_method,
                base_models=validated_models,
                model_weights=model_weights,
                combination_strategy=await self._determine_combination_strategy(
                    ensemble_method, validated_models
                ),
                meta_learner=await self._select_meta_learner(ensemble_method) if ensemble_method == EnsembleMethod.STACKING else None,
                performance_threshold=self.performance_threshold,
                diversity_threshold=0.3,
                created_at=datetime.now()
            )
            ensemble_model = await self._train_ensemble(ensemble_config, data, target)
            ensemble_performance = await self._evaluate_ensemble(ensemble_model, data, target)
            self.ensemble_registry[ensemble_config.ensemble_id] = ensemble_config
            ensemble_candidate = ModelCandidate(
                model_id=ensemble_config.ensemble_id,
                model_name=f"Ensemble_{ensemble_method.value}",
                model_type=ModelType.ENSEMBLE,
                model_family=ModelFamily.ENSEMBLE,
                trained_model=ensemble_model,
                performance=ensemble_performance,
                metadata=await self._create_ensemble_metadata(ensemble_config),
                selection_score=ensemble_performance.validation_score,
                confidence=0.8,
                suitability_score=0.85,
                risk_score=0.2
            )
            self.model_registry[ensemble_config.ensemble_id] = ensemble_candidate
            self.selector_metrics['ensembles_created'] += 1
            return ensemble_config
        except Exception as e:
            self.logger.error(f"Error creating ensemble: {e}")
            raise
    
    async def update_model_performance(self, 
                                     model_id: str,
                                     performance_data: Dict[str, float]):
        """Update model performance"""
        try:
            if model_id not in self.model_registry:
                self.logger.warning(f"Model {model_id} not found in registry")
                return
            
            model = self.model_registry[model_id]
            
            # Update performance metrics
            model.performance.validation_score = performance_data.get('validation_score', model.performance.validation_score)
            model.performance.test_score = performance_data.get('test_score', model.performance.test_score)
            model.performance.prediction_time = performance_data.get('prediction_time', model.performance.prediction_time)
            model.performance.last_updated = datetime.now()
            
            # Update performance history
            if model_id not in self.performance_history:
                self.performance_history[model_id] = []
            
            self.performance_history[model_id].append(model.performance)
            
            # Keep only recent history
            if len(self.performance_history[model_id]) > 100:
                self.performance_history[model_id] = self.performance_history[model_id][-100:]
            
            # Update selection score
            model.selection_score = await self._calculate_selection_score(model)
            
            # Check if model needs to be retired
            if await self._should_retire_model(model):
                await self._retire_model(model_id)
            
            # Check if better model is available
            if self.auto_selection:
                await self._check_for_better_model(model_id)
            
            self.logger.info(f"Updated performance for model {model_id}: {performance_data}")
            
        except Exception as e:
            self.logger.error(f"Error updating model performance: {e}")
    
    async def switch_model(self, 
                          task_type: str,
                          new_model_id: str,
                          reason: str = "Manual switch") -> bool:
        """Switch to a different model"""
        try:
            if new_model_id not in self.model_registry:
                self.logger.error(f"Model {new_model_id} not found in registry")
                return False
            current_model = self.active_models.get(task_type)
            new_model = self.model_registry[new_model_id]
            if new_model is None:
                self.logger.error(f"New model {new_model_id} is None.")
                return False
            self.active_models[task_type] = new_model
            self.logger.info(f"Switched model for {task_type}: {current_model.model_id if current_model else 'None'} -> {new_model_id}")
            self.selector_metrics['model_switches'] += 1
            await self._store_switch_event(task_type, current_model, new_model_id, reason)
            return True
        except Exception as e:
            self.logger.error(f"Error switching model: {e}")
            return False
    
    async def get_model_recommendations(self, 
                                      task_type: str,
                                      data_characteristics: Dict[str, Any]) -> List[str]:
        """Get model recommendations based on data characteristics"""
        try:
            # Analyze data characteristics
            data_analysis = await self._analyze_data_characteristics(data_characteristics)
            
            # Get suitable models
            suitable_models = await self._get_suitable_models(task_type, data_analysis)
            
            # Rank models
            ranked_models = await self._rank_models(suitable_models, data_analysis)
            
            # Return top recommendations
            return [model.model_id for model in ranked_models[:5]]
            
        except Exception as e:
            self.logger.error(f"Error getting model recommendations: {e}")
            return []
    
    async def get_model_insights(self) -> Dict[str, Any]:
        """Get model insights and analytics"""
        try:
            # Performance analysis
            performance_analysis = await self._analyze_model_performance()
            
            # Model comparison
            model_comparison = await self._compare_models()
            
            # Ensemble analysis
            ensemble_analysis = await self._analyze_ensembles()
            
            # Selection history analysis
            selection_analysis = await self._analyze_selection_history()
            
            # Feature importance analysis
            feature_analysis = await self._analyze_feature_importance()
            
            return {
                'performance_analysis': performance_analysis,
                'model_comparison': model_comparison,
                'ensemble_analysis': ensemble_analysis,
                'selection_analysis': selection_analysis,
                'feature_analysis': feature_analysis,
                'active_models': {k: v.model_name for k, v in self.active_models.items()},
                'total_models': len(self.model_registry),
                'selector_metrics': self.selector_metrics
            }
            
        except Exception as e:
            self.logger.error(f"Error getting model insights: {e}")
            return {'error': str(e)}
    
    async def optimize_hyperparameters(self, 
                                     model_id: str,
                                     data: pd.DataFrame,
                                     target: str) -> Dict[str, Any]:
        """Optimize hyperparameters for a model"""
        try:
            if model_id not in self.model_registry:
                raise ValueError(f"Model {model_id} not found")
            
            model = self.model_registry[model_id]
            
            # Get hyperparameter space
            param_space = await self._get_hyperparameter_space(model.model_name)
            
            # Perform grid search
            best_params = await self._perform_grid_search(
                model.model_name, param_space, data, target
            )
            
            # Update model with best parameters
            updated_model = await self._update_model_parameters(model_id, best_params)
            
            # Evaluate updated model
            performance = await self._evaluate_model(updated_model, data, target)
            
            return {
                'model_id': model_id,
                'best_parameters': best_params,
                'performance_improvement': performance.validation_score - model.performance.validation_score,
                'updated_performance': asdict(performance)
            }
            
        except Exception as e:
            self.logger.error(f"Error optimizing hyperparameters: {e}")
            return {'error': str(e)}
    
    async def _model_evaluation_loop(self):
        """Model evaluation loop"""
        while self.is_running:
            try:
                # Evaluate active models
                for task_type, model in self.active_models.items():
                    if await self._needs_evaluation(model):
                        await self._evaluate_model_performance(model)
                
                # Evaluate candidate models
                for model_id, model in self.candidate_models.items():
                    if await self._needs_evaluation(model):
                        await self._evaluate_model_performance(model)
                
                await asyncio.sleep(self.retraining_interval // 4)
                
            except Exception as e:
                self.logger.error(f"Error in model evaluation loop: {e}")
                await asyncio.sleep(self.retraining_interval)
    
    async def _model_selection_loop(self):
        """Model selection loop"""
        while self.is_running:
            try:
                if self.auto_selection:
                    # Check for better models
                    for task_type, current_model in self.active_models.items():
                        better_model = await self._find_better_model(current_model)
                        if better_model:
                            await self.switch_model(task_type, better_model.model_id, "Auto-selection")
                
                await asyncio.sleep(self.retraining_interval // 2)
                
            except Exception as e:
                self.logger.error(f"Error in model selection loop: {e}")
                await asyncio.sleep(self.retraining_interval)
    
    async def _ensemble_management_loop(self):
        """Ensemble management loop"""
        while self.is_running:
            try:
                if self.ensemble_enabled:
                    # Update ensemble weights
                    for ensemble_id, config in self.ensemble_registry.items():
                        if await self._should_update_ensemble(config):
                            await self._update_ensemble_weights(ensemble_id)
                    
                    # Create new ensembles
                    await self._create_beneficial_ensembles()
                
                await asyncio.sleep(self.retraining_interval)
                
            except Exception as e:
                self.logger.error(f"Error in ensemble management loop: {e}")
                await asyncio.sleep(self.retraining_interval)
    
    async def _performance_monitoring_loop(self):
        """Performance monitoring loop"""
        while self.is_running:
            try:
                # Monitor model performance
                await self._monitor_model_performance()
                
                # Update metrics
                await self._update_selector_metrics()
                
                # Check for performance degradation
                await self._check_performance_degradation()
                
                await asyncio.sleep(self.retraining_interval // 6)
                
            except Exception as e:
                self.logger.error(f"Error in performance monitoring loop: {e}")
                await asyncio.sleep(self.retraining_interval)
    
    def _initialize_model_factory(self):
        """Initialize model factory"""
        self.model_factory = {
            'random_forest_regressor': lambda: RandomForestRegressor(n_estimators=100, random_state=42),
            'random_forest_classifier': lambda: RandomForestClassifier(n_estimators=100, random_state=42),
            'gradient_boosting_regressor': lambda: GradientBoostingRegressor(n_estimators=100, random_state=42),
            'gradient_boosting_classifier': lambda: GradientBoostingClassifier(n_estimators=100, random_state=42),
            'linear_regression': lambda: LinearRegression(),
            'logistic_regression': lambda: LogisticRegression(random_state=42),
            'ridge_regression': lambda: Ridge(random_state=42),
            'lasso_regression': lambda: Lasso(random_state=42),
            'elastic_net': lambda: ElasticNet(random_state=42),
            'svr': lambda: SVR(),
            'svc': lambda: SVC(random_state=42),
            'mlp_regressor': lambda: MLPRegressor(random_state=42),
            'mlp_classifier': lambda: MLPClassifier(random_state=42),
            'extra_trees_regressor': lambda: ExtraTreesRegressor(n_estimators=100, random_state=42),
            'extra_trees_classifier': lambda: ExtraTreesClassifier(n_estimators=100, random_state=42),
            'adaboost_regressor': lambda: AdaBoostRegressor(n_estimators=100, random_state=42),
            'adaboost_classifier': lambda: AdaBoostClassifier(n_estimators=100, random_state=42)
        }

        # Add XGBoost models if available
        if XGB_AVAILABLE and xgb is not None:
            # Type assertion to help IDE understand xgb is not None here
            import xgboost as xgb_module  # Re-import for type clarity
            self.model_factory.update({
                'xgboost_regressor': lambda: xgb_module.XGBRegressor(n_estimators=100, random_state=42),
                'xgboost_classifier': lambda: xgb_module.XGBClassifier(n_estimators=100, random_state=42)
            })

        # Add LightGBM models if available
        if LGB_AVAILABLE and lgb is not None:
            # Type assertion to help IDE understand lgb is not None here
            import lightgbm as lgb_module  # Re-import for type clarity
            self.model_factory.update({
                'lightgbm_regressor': lambda: lgb_module.LGBMRegressor(n_estimators=100, random_state=42),
                'lightgbm_classifier': lambda: lgb_module.LGBMClassifier(n_estimators=100, random_state=42)
            })

        # Add neural network models if TensorFlow is available
        if TENSORFLOW_AVAILABLE:
            self.model_factory.update({
                'lstm_regressor': self._create_lstm_regressor,
                'lstm_classifier': self._create_lstm_classifier,
                'gru_regressor': self._create_gru_regressor,
                'gru_classifier': self._create_gru_classifier,
                'cnn_regressor': self._create_cnn_regressor,
                'cnn_classifier': self._create_cnn_classifier
            })
    
    def _initialize_hyperparameter_spaces(self):
        """Initialize hyperparameter spaces"""
        self.hyperparameter_spaces = {
            'random_forest_regressor': {
                'n_estimators': [50, 100, 200],
                'max_depth': [5, 10, 20, None],
                'min_samples_split': [2, 5, 10],
                'min_samples_leaf': [1, 2, 4]
            },
            'gradient_boosting_regressor': {
                'n_estimators': [50, 100, 200],
                'learning_rate': [0.01, 0.1, 0.2],
                'max_depth': [3, 5, 7],
                'subsample': [0.8, 0.9, 1.0]
            },
            'ridge_regression': {
                'alpha': [0.1, 1.0, 10.0, 100.0]
            },
            'lasso_regression': {
                'alpha': [0.1, 1.0, 10.0, 100.0]
            },
            'elastic_net': {
                'alpha': [0.1, 1.0, 10.0],
                'l1_ratio': [0.1, 0.5, 0.9]
            },
            'svr': {
                'C': [0.1, 1.0, 10.0],
                'gamma': ['scale', 'auto'],
                'kernel': ['rbf', 'linear', 'poly']
            },
            'mlp_regressor': {
                'hidden_layer_sizes': [(50,), (100,), (50, 50), (100, 50)],
                'activation': ['relu', 'tanh'],
                'alpha': [0.0001, 0.001, 0.01],
                'learning_rate': ['constant', 'adaptive']
            }
        }

        # Add XGBoost hyperparameter spaces if available
        if xgb is not None:
            self.hyperparameter_spaces.update({
                'xgboost_regressor': {
                    'n_estimators': [50, 100, 200],
                    'learning_rate': [0.01, 0.1, 0.2],
                    'max_depth': [3, 5, 7],
                    'subsample': [0.8, 0.9, 1.0],
                    'colsample_bytree': [0.8, 0.9, 1.0]
                },
                'xgboost_classifier': {
                    'n_estimators': [50, 100, 200],
                    'learning_rate': [0.01, 0.1, 0.2],
                    'max_depth': [3, 5, 7],
                    'subsample': [0.8, 0.9, 1.0],
                    'colsample_bytree': [0.8, 0.9, 1.0]
                }
            })

        # Add LightGBM hyperparameter spaces if available
        if lgb is not None:
            self.hyperparameter_spaces.update({
                'lightgbm_regressor': {
                    'n_estimators': [50, 100, 200],
                    'learning_rate': [0.01, 0.1, 0.2],
                    'max_depth': [3, 5, 7],
                    'subsample': [0.8, 0.9, 1.0],
                    'colsample_bytree': [0.8, 0.9, 1.0]
                },
                'lightgbm_classifier': {
                    'n_estimators': [50, 100, 200],
                    'learning_rate': [0.01, 0.1, 0.2],
                    'max_depth': [3, 5, 7],
                    'subsample': [0.8, 0.9, 1.0],
                    'colsample_bytree': [0.8, 0.9, 1.0]
                }
            })
    
    # TensorFlow model creation methods (imports moved inside functions)
    def _create_lstm_regressor(self):
        """Create LSTM regressor"""
        try:
            import tensorflow as tf
            try:
                from tensorflow.keras.models import Sequential
                from tensorflow.keras.layers import Dense, LSTM
            except ImportError:
                from keras.models import Sequential
                from keras.layers import Dense, LSTM
        except ImportError:
            raise ImportError("TensorFlow not available")
        model = Sequential([
            LSTM(50, return_sequences=True, input_shape=(None, 1)),
            LSTM(50),
            Dense(25),
            Dense(1)
        ])
        model.compile(optimizer='adam', loss='mse')
        return model

    def _create_lstm_classifier(self):
        """Create LSTM classifier"""
        try:
            import tensorflow as tf
            try:
                from tensorflow.keras.models import Sequential
                from tensorflow.keras.layers import Dense, LSTM
            except ImportError:
                from keras.models import Sequential
                from keras.layers import Dense, LSTM
        except ImportError:
            raise ImportError("TensorFlow not available")
        model = Sequential([
            LSTM(50, return_sequences=True, input_shape=(None, 1)),
            LSTM(50),
            Dense(25),
            Dense(1, activation='sigmoid')
        ])
        model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])
        return model

    def _create_gru_regressor(self):
        """Create GRU regressor"""
        try:
            import tensorflow as tf
            from keras.models import Sequential
            from keras.layers import Dense, GRU
        except ImportError:
            raise ImportError("TensorFlow not available")
        model = Sequential([
            GRU(50, return_sequences=True, input_shape=(None, 1)),
            GRU(50),
            Dense(25),
            Dense(1)
        ])
        model.compile(optimizer='adam', loss='mse')
        return model

    def _create_gru_classifier(self):
        """Create GRU classifier"""
        try:
            import tensorflow as tf
            from keras.models import Sequential
            from keras.layers import Dense, GRU
        except ImportError:
            raise ImportError("TensorFlow not available")
        model = Sequential([
            GRU(50, return_sequences=True, input_shape=(None, 1)),
            GRU(50),
            Dense(25),
            Dense(1, activation='sigmoid')
        ])
        model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])
        return model

    def _create_cnn_regressor(self):
        """Create CNN regressor"""
        try:
            import tensorflow as tf
            from keras.models import Sequential
            from keras.layers import Dense, Conv1D, MaxPooling1D, Flatten
        except ImportError:
            raise ImportError("TensorFlow not available")
        model = Sequential([
            Conv1D(filters=64, kernel_size=3, activation='relu', input_shape=(None, 1)),
            Conv1D(filters=64, kernel_size=3, activation='relu'),
            MaxPooling1D(pool_size=2),
            Flatten(),
            Dense(50, activation='relu'),
            Dense(1)
        ])
        model.compile(optimizer='adam', loss='mse')
        return model

    def _create_cnn_classifier(self):
        """Create CNN classifier"""
        try:
            import tensorflow as tf
            try:
                from tensorflow.keras.models import Sequential
                from tensorflow.keras.layers import Dense, Conv1D, MaxPooling1D, Flatten
            except ImportError:
                from keras.models import Sequential
                from keras.layers import Dense, Conv1D, MaxPooling1D, Flatten
        except ImportError:
            raise ImportError("TensorFlow not available")
        model = Sequential([
            Conv1D(filters=64, kernel_size=3, activation='relu', input_shape=(None, 1)),
            Conv1D(filters=64, kernel_size=3, activation='relu'),
            MaxPooling1D(pool_size=2),
            Flatten(),
            Dense(50, activation='relu'),
            Dense(1, activation='sigmoid')
        ])
        model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])
        return model

    # Placeholder methods for complex operations
    async def _load_existing_models(self): pass
    async def _initialize_base_models(self): pass
    async def _save_models(self): pass
    async def _generate_candidate_models(self, task_type, data, target): return []
    async def _evaluate_candidates(self, candidates, data, target): return []
    async def _select_best_candidate(self, candidates, criteria): return candidates[0] if candidates else None
    async def _generate_selection_reasoning(self, best_model, candidates): return []
    async def _validate_base_models(self, models): return models
    async def _calculate_model_weights(self, models, data, target): return {}
    async def _determine_combination_strategy(self, method, models): return "average"
    async def _select_meta_learner(self, method): return "linear_regression"
    async def _train_ensemble(self, config, data, target): return None
    async def _evaluate_ensemble(self, model, data, target):
        """Evaluate ensemble model performance"""
        try:
            if model is None:
                return ModelPerformance(
                    model_id="ensemble_unknown",
                    model_name="Unknown Ensemble",
                    training_score=0.0,
                    validation_score=0.0,
                    test_score=0.0,
                    cross_validation_score=0.0,
                    std_dev=1.0,
                    training_time=0.0,
                    prediction_time=0.1,
                    memory_usage=100.0,
                    stability_score=0.0,
                    interpretability_score=0.0,
                    robustness_score=0.0,
                    last_updated=datetime.now()
                )

            # Extract model information
            model_id = getattr(model, 'ensemble_id', 'ensemble_unknown')
            model_name = f"Ensemble_{getattr(model, 'ensemble_method', 'unknown')}"

            # Simulate evaluation metrics based on data characteristics
            data_size = len(data) if hasattr(data, '__len__') else 1000
            complexity_factor = min(1.0, data_size / 10000)  # Normalize by expected size

            # Base performance with some variation based on data
            base_score = 0.75 + (complexity_factor * 0.15)  # 0.75 to 0.90 range
            validation_score = base_score + (hash(str(model_id)) % 100) / 1000  # Add deterministic variation

            return ModelPerformance(
                model_id=model_id,
                model_name=model_name,
                training_score=min(0.95, validation_score + 0.05),
                validation_score=validation_score,
                test_score=max(0.5, validation_score - 0.02),
                cross_validation_score=validation_score,
                std_dev=0.02 + (1 - complexity_factor) * 0.03,  # Lower std_dev for more data
                training_time=data_size * 0.001,  # Proportional to data size
                prediction_time=0.05 + (data_size / 100000),  # Scales with data
                memory_usage=50.0 + (data_size * 0.01),  # Memory usage scales with data
                stability_score=min(0.95, base_score + 0.1),
                interpretability_score=0.6,  # Ensembles are typically less interpretable
                robustness_score=min(0.9, base_score + 0.05),
                last_updated=datetime.now()
            )

        except Exception as e:
            self.logger.error(f"Error evaluating ensemble: {e}")
            return ModelPerformance(
                model_id="ensemble_error",
                model_name="Error Ensemble",
                training_score=0.0,
                validation_score=0.0,
                test_score=0.0,
                cross_validation_score=0.0,
                std_dev=1.0,
                training_time=0.0,
                prediction_time=0.1,
                memory_usage=100.0,
                stability_score=0.0,
                interpretability_score=0.0,
                robustness_score=0.0,
                last_updated=datetime.now()
            )
    async def _create_ensemble_metadata(self, config):
        """Create metadata for ensemble model"""
        try:
            ensemble_id = getattr(config, 'ensemble_id', 'ensemble_unknown')
            ensemble_method = getattr(config, 'ensemble_method', EnsembleMethod.VOTING)
            base_models = getattr(config, 'base_models', [])
            model_weights = getattr(config, 'model_weights', {})
            combination_strategy = getattr(config, 'combination_strategy', 'average')

            # Create hyperparameters from config
            hyperparameters = {
                'ensemble_method': ensemble_method.value if hasattr(ensemble_method, 'value') else str(ensemble_method),
                'combination_strategy': combination_strategy,
                'base_models_count': len(base_models),
                'performance_threshold': getattr(config, 'performance_threshold', 0.8),
                'diversity_threshold': getattr(config, 'diversity_threshold', 0.3)
            }

            # Add model weights to hyperparameters
            if model_weights:
                hyperparameters['model_weights'] = model_weights

            # Create feature importance (simplified for ensemble)
            feature_importance = {}
            if base_models:
                # Distribute importance equally among base models
                importance_per_model = 1.0 / len(base_models)
                for i, model_id in enumerate(base_models):
                    feature_importance[f'base_model_{i}_{model_id}'] = importance_per_model

            return ModelMetadata(
                model_id=ensemble_id,
                model_name=f"Ensemble_{ensemble_method.value if hasattr(ensemble_method, 'value') else str(ensemble_method)}",
                model_type=ModelType.ENSEMBLE,
                model_family=ModelFamily.ENSEMBLE,
                hyperparameters=hyperparameters,
                feature_importance=feature_importance,
                training_data_size=1000,  # Default size
                feature_count=len(base_models),  # Number of base models as features
                target_variable="price_direction",  # Default target
                preprocessing_steps=[
                    "ensemble_combination",
                    "weight_normalization",
                    "prediction_aggregation"
                ],
                validation_method="cross_validation",
                created_at=getattr(config, 'created_at', datetime.now()),
                updated_at=datetime.now(),
                version=1
            )

        except Exception as e:
            self.logger.error(f"Error creating ensemble metadata: {e}")
            return ModelMetadata(
                model_id="ensemble_error",
                model_name="Error_Ensemble",
                model_type=ModelType.ENSEMBLE,
                model_family=ModelFamily.ENSEMBLE,
                hyperparameters={},
                feature_importance={},
                training_data_size=0,
                feature_count=0,
                target_variable="unknown",
                preprocessing_steps=[],
                validation_method="none",
                created_at=datetime.now(),
                updated_at=datetime.now(),
                version=1
            )
    async def _calculate_selection_score(self, model):
        """Calculate selection score for a model"""
        try:
            if not model or not hasattr(model, 'performance'):
                return 0.0

            # Weight different criteria
            performance_score = model.performance.validation_score * self.criteria_weights[SelectionCriteria.PERFORMANCE]
            speed_score = (1.0 / max(0.001, model.performance.prediction_time)) * 0.1 * self.criteria_weights[SelectionCriteria.SPEED]
            stability_score = model.performance.stability_score * self.criteria_weights[SelectionCriteria.ROBUSTNESS]
            memory_score = (1.0 / max(1.0, model.performance.memory_usage / 100)) * self.criteria_weights[SelectionCriteria.MEMORY_EFFICIENCY]

            total_score = performance_score + speed_score + stability_score + memory_score
            return min(1.0, max(0.0, total_score))
        except Exception as e:
            self.logger.error(f"Error calculating selection score: {e}")
            return 0.0

    async def _should_retire_model(self, model):
        """Check if model should be retired"""
        try:
            if not model or not hasattr(model, 'performance'):
                return True

            # Retire if performance is too low
            if model.performance.validation_score < self.performance_threshold * 0.5:
                return True

            # Retire if model is too unstable
            if model.performance.std_dev > 0.3:
                return True

            # Check performance history
            if model.model_id in self.performance_history:
                recent_scores = [p.validation_score for p in self.performance_history[model.model_id][-10:]]
                if len(recent_scores) >= 5 and all(score < self.performance_threshold * 0.7 for score in recent_scores[-5:]):
                    return True

            return False
        except Exception as e:
            self.logger.error(f"Error checking if model should retire: {e}")
            return False

    async def _retire_model(self, model_id):
        """Retire a model"""
        try:
            if model_id in self.model_registry:
                model = self.model_registry[model_id]
                self.retired_models[model_id] = model
                del self.model_registry[model_id]

                # Remove from active models if present
                for task_type, active_model in list(self.active_models.items()):
                    if active_model.model_id == model_id:
                        del self.active_models[task_type]

                self.logger.info(f"Retired model: {model_id}")
        except Exception as e:
            self.logger.error(f"Error retiring model {model_id}: {e}")

    async def _check_for_better_model(self, model_id):
        """Check if there's a better model available"""
        try:
            if model_id not in self.model_registry:
                return

            current_model = self.model_registry[model_id]
            current_score = current_model.selection_score

            # Check all other models
            for other_id, other_model in self.model_registry.items():
                if other_id != model_id and other_model.selection_score > current_score * 1.1:
                    # Found significantly better model
                    for task_type, active_model in self.active_models.items():
                        if active_model.model_id == model_id:
                            await self.switch_model(task_type, other_id, "Better model found")
                            break
        except Exception as e:
            self.logger.error(f"Error checking for better model: {e}")

    async def _store_switch_event(self, task_type, old_model, new_model, reason):
        """Store model switch event"""
        try:
            event = {
                'timestamp': datetime.now(),
                'task_type': task_type,
                'old_model': old_model.model_id if old_model else None,
                'new_model': new_model,
                'reason': reason
            }

            # Store in database or log
            self.logger.info(f"Model switch event: {event}")

            # Could store in database here
            # await self.db_manager.store_switch_event(event)

        except Exception as e:
            self.logger.error(f"Error storing switch event: {e}")

    async def _analyze_data_characteristics(self, characteristics):
        """Analyze data characteristics"""
        try:
            analysis = {
                'data_size': characteristics.get('size', 1000),
                'feature_count': characteristics.get('features', 10),
                'data_type': characteristics.get('type', 'numerical'),
                'complexity': 'medium',
                'noise_level': characteristics.get('noise', 0.1),
                'seasonality': characteristics.get('seasonal', False),
                'trend': characteristics.get('trend', 'stable')
            }

            # Determine complexity
            if analysis['data_size'] > 10000 and analysis['feature_count'] > 50:
                analysis['complexity'] = 'high'
            elif analysis['data_size'] < 1000 or analysis['feature_count'] < 5:
                analysis['complexity'] = 'low'

            return analysis
        except Exception as e:
            self.logger.error(f"Error analyzing data characteristics: {e}")
            return {}

    async def _get_suitable_models(self, task_type, analysis):
        """Get suitable models for task and data"""
        try:
            suitable_models = []

            # Based on task type and data characteristics
            if task_type == 'regression':
                if analysis.get('complexity') == 'high':
                    suitable_models.extend(['random_forest_regressor', 'gradient_boosting_regressor'])
                    if XGB_AVAILABLE:
                        suitable_models.append('xgboost_regressor')
                else:
                    suitable_models.extend(['linear_regression', 'ridge_regression'])

            elif task_type == 'classification':
                if analysis.get('complexity') == 'high':
                    suitable_models.extend(['random_forest_classifier', 'gradient_boosting_classifier'])
                    if XGB_AVAILABLE:
                        suitable_models.append('xgboost_classifier')
                else:
                    suitable_models.extend(['logistic_regression', 'svc'])

            # Return model candidates from registry
            return [model for model_id, model in self.model_registry.items()
                   if model.model_name in suitable_models]
        except Exception as e:
            self.logger.error(f"Error getting suitable models: {e}")
            return []

    async def _rank_models(self, models, analysis):
        """Rank models based on analysis"""
        try:
            if not models:
                return []

            # Sort by selection score
            ranked = sorted(models, key=lambda m: m.selection_score, reverse=True)

            # Apply analysis-based adjustments
            complexity = analysis.get('complexity', 'medium')

            if complexity == 'high':
                # Prefer ensemble and tree-based models
                ensemble_models = [m for m in ranked if m.model_family == ModelFamily.ENSEMBLE]
                tree_models = [m for m in ranked if m.model_family == ModelFamily.TREE_BASED]
                other_models = [m for m in ranked if m.model_family not in [ModelFamily.ENSEMBLE, ModelFamily.TREE_BASED]]
                ranked = ensemble_models + tree_models + other_models

            return ranked
        except Exception as e:
            self.logger.error(f"Error ranking models: {e}")
            return models
    async def _analyze_model_performance(self):
        """Analyze model performance across all models"""
        try:
            analysis = {
                'total_models': len(self.model_registry),
                'active_models': len(self.active_models),
                'retired_models': len(self.retired_models),
                'average_performance': 0.0,
                'best_performer': None,
                'worst_performer': None,
                'performance_distribution': {}
            }

            if self.model_registry:
                scores = [model.performance.validation_score for model in self.model_registry.values()]
                analysis['average_performance'] = sum(scores) / len(scores)

                best_model = max(self.model_registry.values(), key=lambda m: m.performance.validation_score)
                worst_model = min(self.model_registry.values(), key=lambda m: m.performance.validation_score)

                analysis['best_performer'] = {
                    'model_id': best_model.model_id,
                    'score': best_model.performance.validation_score
                }
                analysis['worst_performer'] = {
                    'model_id': worst_model.model_id,
                    'score': worst_model.performance.validation_score
                }

                # Performance distribution
                ranges = [(0.0, 0.5), (0.5, 0.7), (0.7, 0.8), (0.8, 0.9), (0.9, 1.0)]
                for low, high in ranges:
                    count = sum(1 for score in scores if low <= score < high)
                    analysis['performance_distribution'][f'{low}-{high}'] = count

            return analysis
        except Exception as e:
            self.logger.error(f"Error analyzing model performance: {e}")
            return {}

    async def _compare_models(self):
        """Compare models across different metrics"""
        try:
            comparison = {
                'by_performance': [],
                'by_speed': [],
                'by_memory': [],
                'by_stability': []
            }

            if self.model_registry:
                models = list(self.model_registry.values())

                # Sort by different criteria
                by_performance = sorted(models, key=lambda m: m.performance.validation_score, reverse=True)
                by_speed = sorted(models, key=lambda m: m.performance.prediction_time)
                by_memory = sorted(models, key=lambda m: m.performance.memory_usage)
                by_stability = sorted(models, key=lambda m: m.performance.stability_score, reverse=True)

                comparison['by_performance'] = [(m.model_id, m.performance.validation_score) for m in by_performance[:5]]
                comparison['by_speed'] = [(m.model_id, m.performance.prediction_time) for m in by_speed[:5]]
                comparison['by_memory'] = [(m.model_id, m.performance.memory_usage) for m in by_memory[:5]]
                comparison['by_stability'] = [(m.model_id, m.performance.stability_score) for m in by_stability[:5]]

            return comparison
        except Exception as e:
            self.logger.error(f"Error comparing models: {e}")
            return {}

    async def _analyze_ensembles(self):
        """Analyze ensemble performance and composition"""
        try:
            analysis = {
                'total_ensembles': len(self.ensemble_registry),
                'ensemble_methods': {},
                'average_performance': 0.0,
                'best_ensemble': None
            }

            if self.ensemble_registry:
                # Count ensemble methods
                for config in self.ensemble_registry.values():
                    method = config.ensemble_method.value
                    analysis['ensemble_methods'][method] = analysis['ensemble_methods'].get(method, 0) + 1

                # Find best ensemble
                ensemble_models = [model for model in self.model_registry.values()
                                 if model.model_type == ModelType.ENSEMBLE]

                if ensemble_models:
                    scores = [model.performance.validation_score for model in ensemble_models]
                    analysis['average_performance'] = sum(scores) / len(scores)

                    best_ensemble = max(ensemble_models, key=lambda m: m.performance.validation_score)
                    analysis['best_ensemble'] = {
                        'ensemble_id': best_ensemble.model_id,
                        'score': best_ensemble.performance.validation_score
                    }

            return analysis
        except Exception as e:
            self.logger.error(f"Error analyzing ensembles: {e}")
            return {}

    async def _analyze_selection_history(self):
        """Analyze model selection history"""
        try:
            analysis = {
                'total_selections': len(self.selection_history),
                'selection_criteria_usage': {},
                'average_confidence': 0.0,
                'selection_trends': {}
            }

            if self.selection_history:
                # Count criteria usage
                for selection in self.selection_history:
                    criteria = selection.selection_criteria.value
                    analysis['selection_criteria_usage'][criteria] = analysis['selection_criteria_usage'].get(criteria, 0) + 1

                # Average confidence
                confidences = [s.confidence for s in self.selection_history]
                analysis['average_confidence'] = sum(confidences) / len(confidences)

                # Recent trends (last 10 selections)
                recent = self.selection_history[-10:]
                selected_models = [s.selected_model for s in recent]
                for model_id in set(selected_models):
                    analysis['selection_trends'][model_id] = selected_models.count(model_id)

            return analysis
        except Exception as e:
            self.logger.error(f"Error analyzing selection history: {e}")
            return {}

    async def _analyze_feature_importance(self):
        """Analyze feature importance across models"""
        try:
            analysis = {
                'global_importance': {},
                'model_specific': {},
                'top_features': []
            }

            all_features = {}

            for model in self.model_registry.values():
                if hasattr(model, 'metadata') and model.metadata.feature_importance:
                    model_id = model.model_id
                    analysis['model_specific'][model_id] = model.metadata.feature_importance

                    # Aggregate global importance
                    for feature, importance in model.metadata.feature_importance.items():
                        if feature not in all_features:
                            all_features[feature] = []
                        all_features[feature].append(importance)

            # Calculate global importance (average across models)
            for feature, importances in all_features.items():
                analysis['global_importance'][feature] = sum(importances) / len(importances)

            # Top features
            if analysis['global_importance']:
                sorted_features = sorted(analysis['global_importance'].items(),
                                       key=lambda x: x[1], reverse=True)
                analysis['top_features'] = sorted_features[:10]

            return analysis
        except Exception as e:
            self.logger.error(f"Error analyzing feature importance: {e}")
            return {}

    async def _get_hyperparameter_space(self, model_name):
        """Get hyperparameter space for model"""
        try:
            return self.hyperparameter_spaces.get(model_name, {})
        except Exception as e:
            self.logger.error(f"Error getting hyperparameter space for {model_name}: {e}")
            return {}

    async def _perform_grid_search(self, model_name, space, data, target):
        """Perform grid search for hyperparameter optimization"""
        try:
            if not space:
                return {}

            # Simplified grid search - in practice would use sklearn.model_selection.GridSearchCV
            best_params = {}
            best_score = 0.0

            # For demonstration, just return the first parameter combination
            for param, values in space.items():
                if values:
                    best_params[param] = values[0]  # Take first value as "best"

            self.logger.info(f"Grid search completed for {model_name}: {best_params}")
            return best_params
        except Exception as e:
            self.logger.error(f"Error performing grid search: {e}")
            return {}

    async def _update_model_parameters(self, model_id, params):
        """Update model parameters"""
        try:
            if model_id not in self.model_registry:
                return None

            model = self.model_registry[model_id]

            # Update hyperparameters in metadata
            if hasattr(model, 'metadata'):
                model.metadata.hyperparameters.update(params)
                model.metadata.updated_at = datetime.now()
                model.metadata.version += 1

            # In practice, would retrain model with new parameters
            self.logger.info(f"Updated parameters for model {model_id}: {params}")
            return model
        except Exception as e:
            self.logger.error(f"Error updating model parameters: {e}")
            return None

    async def _evaluate_model(self, model, data, target):
        """Evaluate model performance"""
        try:
            if model is None:
                return ModelPerformance("", "", 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.1, 100.0, 0.0, 0.0, 0.0, datetime.now())

            # Simulate evaluation based on model and data characteristics
            model_id = getattr(model, 'model_id', 'unknown')
            model_name = getattr(model, 'model_name', 'unknown')

            # Base performance with variation based on model type and data characteristics
            base_score = 0.7
            if hasattr(model, 'model_family'):
                if model.model_family == ModelFamily.ENSEMBLE:
                    base_score = 0.8
                elif model.model_family == ModelFamily.TREE_BASED:
                    base_score = 0.75
                elif model.model_family == ModelFamily.NEURAL_NETWORK:
                    base_score = 0.78

            # Adjust based on data size and target complexity
            data_size = len(data) if hasattr(data, '__len__') else 1000
            target_complexity = len(str(target)) if target else 5  # Simple complexity measure

            # Add deterministic variation based on model_id and data characteristics
            variation = (hash(f"{model_id}_{data_size}_{target_complexity}") % 100) / 1000
            validation_score = max(0.0, min(1.0, base_score + variation))

            return ModelPerformance(
                model_id=model_id,
                model_name=model_name,
                training_score=min(0.95, validation_score + 0.05),
                validation_score=validation_score,
                test_score=max(0.0, validation_score - 0.02),
                cross_validation_score=validation_score,
                std_dev=0.02 + abs(variation),
                training_time=data_size * 0.001,
                prediction_time=0.1,
                memory_usage=100.0 + (data_size * 0.01),
                stability_score=min(0.9, validation_score + 0.1),
                interpretability_score=0.7,
                robustness_score=min(0.85, validation_score + 0.05),
                last_updated=datetime.now()
            )
        except Exception as e:
            self.logger.error(f"Error evaluating model: {e}")
            return ModelPerformance("error", "error", 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.1, 100.0, 0.0, 0.0, 0.0, datetime.now())

    async def _needs_evaluation(self, model):
        """Check if model needs evaluation"""
        try:
            if not model or not hasattr(model, 'performance'):
                return True

            # Check if evaluation is recent
            time_since_update = datetime.now() - model.performance.last_updated
            if time_since_update.total_seconds() > self.retraining_interval:
                return True

            # Check if performance has degraded
            if model.model_id in self.performance_history:
                recent_scores = [p.validation_score for p in self.performance_history[model.model_id][-5:]]
                if len(recent_scores) >= 3:
                    trend = sum(recent_scores[-3:]) / 3 - sum(recent_scores[-5:-2]) / 2
                    if trend < -0.05:  # Significant downward trend
                        return True

            return False
        except Exception as e:
            self.logger.error(f"Error checking if model needs evaluation: {e}")
            return False

    async def _evaluate_model_performance(self, model):
        """Evaluate and update model performance"""
        try:
            if not model:
                return

            # Simulate performance evaluation
            current_time = datetime.now()

            # Update performance with some variation
            base_score = model.performance.validation_score
            variation = (hash(str(current_time)) % 100 - 50) / 1000  # Small random variation
            new_score = max(0.0, min(1.0, base_score + variation))

            model.performance.validation_score = new_score
            model.performance.last_updated = current_time

            # Update performance history
            if model.model_id not in self.performance_history:
                self.performance_history[model.model_id] = []

            self.performance_history[model.model_id].append(model.performance)

            # Keep only recent history
            if len(self.performance_history[model.model_id]) > 100:
                self.performance_history[model.model_id] = self.performance_history[model.model_id][-100:]

            # Update selection score
            model.selection_score = await self._calculate_selection_score(model)

            self.logger.debug(f"Evaluated model {model.model_id}: score={new_score:.3f}")

        except Exception as e:
            self.logger.error(f"Error evaluating model performance: {e}")

    async def _find_better_model(self, current_model):
        """Find a better model than the current one"""
        try:
            if not current_model:
                return None

            current_score = current_model.selection_score
            best_alternative = None
            best_score = current_score

            # Check all models in registry
            for model_id, model in self.model_registry.items():
                if model_id != current_model.model_id and model.selection_score > best_score * 1.05:  # 5% improvement threshold
                    best_alternative = model
                    best_score = model.selection_score

            return best_alternative
        except Exception as e:
            self.logger.error(f"Error finding better model: {e}")
            return None

    async def _should_update_ensemble(self, config):
        """Check if ensemble should be updated"""
        try:
            if not config:
                return False

            # Check if ensemble is old
            time_since_creation = datetime.now() - config.created_at
            if time_since_creation.total_seconds() > self.retraining_interval * 2:
                return True

            # Check if base models have changed significantly
            for model_id in config.base_models:
                if model_id in self.model_registry:
                    model = self.model_registry[model_id]
                    if model.performance.last_updated > config.created_at:
                        return True

            return False
        except Exception as e:
            self.logger.error(f"Error checking if ensemble should update: {e}")
            return False

    async def _update_ensemble_weights(self, ensemble_id):
        """Update ensemble weights"""
        try:
            if ensemble_id not in self.ensemble_registry:
                return

            config = self.ensemble_registry[ensemble_id]

            # Recalculate weights based on current model performance
            total_score = 0.0
            model_scores = {}

            for model_id in config.base_models:
                if model_id in self.model_registry:
                    score = self.model_registry[model_id].performance.validation_score
                    model_scores[model_id] = score
                    total_score += score

            # Normalize weights
            if total_score > 0:
                for model_id in model_scores:
                    config.model_weights[model_id] = model_scores[model_id] / total_score

            self.logger.info(f"Updated ensemble weights for {ensemble_id}: {config.model_weights}")

        except Exception as e:
            self.logger.error(f"Error updating ensemble weights: {e}")

    async def _create_beneficial_ensembles(self):
        """Create beneficial ensembles automatically"""
        try:
            # Find high-performing models that could form good ensembles
            high_performers = [model for model in self.model_registry.values()
                             if model.performance.validation_score > self.performance_threshold]

            if len(high_performers) >= 3:
                # Group by model family for diversity
                families = {}
                for model in high_performers:
                    family = model.model_family
                    if family not in families:
                        families[family] = []
                    families[family].append(model)

                # Create ensemble if we have models from different families
                if len(families) >= 2:
                    ensemble_models = []
                    for family, models in families.items():
                        # Take best model from each family
                        best_in_family = max(models, key=lambda m: m.performance.validation_score)
                        ensemble_models.append(best_in_family.model_id)
                        if len(ensemble_models) >= 5:  # Limit ensemble size
                            break

                    if len(ensemble_models) >= 2:
                        # Create ensemble (simplified - would need actual data)
                        self.logger.info(f"Creating beneficial ensemble with models: {ensemble_models}")
                        # await self.create_ensemble("auto_ensemble", ensemble_models)

        except Exception as e:
            self.logger.error(f"Error creating beneficial ensembles: {e}")

    async def _monitor_model_performance(self):
        """Monitor model performance"""
        try:
            # Check for performance degradation
            degraded_models = []

            for model_id, model in self.model_registry.items():
                if model_id in self.performance_history and len(self.performance_history[model_id]) >= 5:
                    recent_scores = [p.validation_score for p in self.performance_history[model_id][-5:]]
                    avg_recent = sum(recent_scores) / len(recent_scores)

                    # Also check current model performance
                    current_score = model.performance.validation_score

                    if avg_recent < self.performance_threshold * 0.8 or current_score < self.performance_threshold * 0.7:
                        degraded_models.append(model_id)

            if degraded_models:
                self.logger.warning(f"Models with degraded performance: {degraded_models}")

        except Exception as e:
            self.logger.error(f"Error monitoring model performance: {e}")

    async def _update_selector_metrics(self):
        """Update selector metrics"""
        try:
            self.selector_metrics['total_models'] = len(self.model_registry)
            self.selector_metrics['active_models'] = len(self.active_models)

            if self.model_registry:
                scores = [model.performance.validation_score for model in self.model_registry.values()]
                self.selector_metrics['average_model_performance'] = sum(scores) / len(scores)

            # Calculate selection accuracy (simplified)
            if self.selection_history:
                recent_selections = self.selection_history[-10:]
                high_confidence = sum(1 for s in recent_selections if s.confidence > 0.8)
                self.selector_metrics['selection_accuracy'] = high_confidence / len(recent_selections)

        except Exception as e:
            self.logger.error(f"Error updating selector metrics: {e}")

    async def _check_performance_degradation(self):
        """Check for performance degradation"""
        try:
            # Check if overall system performance is degrading
            if len(self.selection_history) >= 10:
                recent_scores = [s.selection_score for s in self.selection_history[-10:]]
                older_scores = [s.selection_score for s in self.selection_history[-20:-10]] if len(self.selection_history) >= 20 else []

                if older_scores:
                    recent_avg = sum(recent_scores) / len(recent_scores)
                    older_avg = sum(older_scores) / len(older_scores)

                    if recent_avg < older_avg * 0.9:  # 10% degradation
                        self.logger.warning("System performance degradation detected")
                        # Could trigger retraining or model refresh here

        except Exception as e:
            self.logger.error(f"Error checking performance degradation: {e}")

