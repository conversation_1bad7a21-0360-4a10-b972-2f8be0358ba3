import React, { useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { Menu, Button, Text, Card } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { theme } from '../styles/theme';

interface LogLevelSelectorProps {
    title: string;
    value: string;
    onValueChange: (value: string) => void;
    levels: string[];
    disabled?: boolean;
    icon?: string;
}

const LogLevelSelector: React.FC<LogLevelSelectorProps> = ({
    title,
    value,
    onValueChange,
    levels,
    disabled = false,
    icon,
}) => {
    const [menuVisible, setMenuVisible] = useState(false);

    const getLevelColor = (level: string) => {
        switch (level) {
            case 'DEBUG':
                return '#9E9E9E';
            case 'INFO':
                return '#2196F3';
            case 'WARNING':
                return '#FF9800';
            case 'ERROR':
                return '#F44336';
            case 'CRITICAL':
                return '#D32F2F';
            default:
                return theme.colors.onSurface;
        }
    };

    const getLevelIcon = (level: string) => {
        switch (level) {
            case 'DEBUG':
                return 'bug-report';
            case 'INFO':
                return 'info';
            case 'WARNING':
                return 'warning';
            case 'ERROR':
                return 'error';
            case 'CRITICAL':
                return 'dangerous';
            default:
                return 'circle';
        }
    };

    return (
        <Card style={[styles.container, disabled && styles.disabled]}>
            <Card.Content style={styles.content}>
                <View style={styles.leftSection}>
                    {icon && (
                        <View style={styles.iconContainer}>
                            <Icon
                                name={icon}
                                size={24}
                                color={theme.colors.onSurfaceVariant}
                            />
                        </View>
                    )}
                    <View style={styles.textContainer}>
                        <Text variant="titleMedium" style={styles.title}>
                            {title}
                        </Text>
                        <Text variant="bodySmall" style={styles.subtitle}>
                            Current level: {value}
                        </Text>
                    </View>
                </View>

                <View style={styles.rightSection}>
                    <Menu
                        visible={menuVisible}
                        onDismiss={() => setMenuVisible(false)}
                        anchor={
                            <Button
                                mode="outlined"
                                onPress={() => setMenuVisible(true)}
                                disabled={disabled}
                                style={styles.button}
                                contentStyle={styles.buttonContent}
                                labelStyle={[
                                    styles.buttonLabel,
                                    { color: getLevelColor(value) }
                                ]}
                                icon={() => (
                                    <Icon
                                        name={getLevelIcon(value)}
                                        size={16}
                                        color={getLevelColor(value)}
                                    />
                                )}
                            >
                                {value}
                            </Button>
                        }
                        contentStyle={styles.menuContent}
                    >
                        {levels.map((level) => (
                            <Menu.Item
                                key={level}
                                onPress={() => {
                                    onValueChange(level);
                                    setMenuVisible(false);
                                }}
                                title={level}
                                titleStyle={[
                                    styles.menuItemTitle,
                                    { color: getLevelColor(level) }
                                ]}
                                leadingIcon={() => (
                                    <Icon
                                        name={getLevelIcon(level)}
                                        size={20}
                                        color={getLevelColor(level)}
                                    />
                                )}
                                style={[
                                    styles.menuItem,
                                    value === level && styles.selectedMenuItem
                                ]}
                            />
                        ))}
                    </Menu>
                </View>
            </Card.Content>
        </Card>
    );
};

const styles = StyleSheet.create({
    container: {
        marginVertical: 4,
        backgroundColor: theme.colors.surface,
        elevation: 1,
        borderRadius: 12,
    },
    disabled: {
        opacity: 0.6,
    },
    content: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 12,
        paddingHorizontal: 16,
        minHeight: 64,
    },
    leftSection: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
    },
    iconContainer: {
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: theme.colors.surfaceVariant,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 16,
    },
    textContainer: {
        flex: 1,
    },
    title: {
        color: theme.colors.onSurface,
        fontWeight: '600',
        marginBottom: 2,
    },
    subtitle: {
        color: theme.colors.onSurfaceVariant,
    },
    rightSection: {
        marginLeft: 16,
    },
    button: {
        minWidth: 100,
        borderColor: theme.colors.outline,
    },
    buttonContent: {
        height: 40,
        paddingHorizontal: 12,
    },
    buttonLabel: {
        fontSize: 14,
        fontWeight: '600',
    },
    menuContent: {
        backgroundColor: theme.colors.surface,
        borderRadius: 8,
        elevation: 8,
        marginTop: 8,
    },
    menuItem: {
        paddingVertical: 8,
        paddingHorizontal: 16,
        minHeight: 48,
    },
    selectedMenuItem: {
        backgroundColor: theme.colors.primaryContainer,
    },
    menuItemTitle: {
        fontSize: 14,
        fontWeight: '500',
    },
});

export default LogLevelSelector;
