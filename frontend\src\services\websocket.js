import { io } from 'socket.io-client'

class WebSocketService {
    constructor() {
        this.socket = null
        this.subscribers = new Map()
        this.reconnectAttempts = 0
        this.maxReconnectAttempts = 5
        this.reconnectInterval = 5000
        this.isConnected = false
    }

    connect() {
        try {
            this.socket = io('ws://91.179.83.180:8000', {
                transports: ['websocket'],
                upgrade: true,
                rememberUpgrade: true,
                autoConnect: true,
                reconnection: true,
                reconnectionAttempts: this.maxReconnectAttempts,
                reconnectionDelay: this.reconnectInterval,
                timeout: 20000,
            })

            this.socket.on('connect', () => {
                console.log('✅ WebSocket connected to trading system')
                this.isConnected = true
                this.reconnectAttempts = 0
                this.notifySubscribers('connection', { status: 'connected' })
            })

            this.socket.on('disconnect', () => {
                console.log('❌ WebSocket disconnected')
                this.isConnected = false
                this.notifySubscribers('connection', { status: 'disconnected' })
            })

            this.socket.on('connect_error', (error) => {
                console.error('WebSocket connection error:', error)
                this.reconnectAttempts++
                if (this.reconnectAttempts >= this.maxReconnectAttempts) {
                    console.error('Max reconnection attempts reached')
                }
            })

            // Real-time trading data
            this.socket.on('market_data', (data) => {
                this.notifySubscribers('market_data', data)
            })

            this.socket.on('trade_update', (data) => {
                this.notifySubscribers('trade_update', data)
            })

            this.socket.on('position_update', (data) => {
                this.notifySubscribers('position_update', data)
            })

            this.socket.on('balance_update', (data) => {
                this.notifySubscribers('balance_update', data)
            })

            this.socket.on('order_update', (data) => {
                this.notifySubscribers('order_update', data)
            })

            // AI and system updates
            this.socket.on('ai_prediction', (data) => {
                this.notifySubscribers('ai_prediction', data)
            })

            this.socket.on('strategy_update', (data) => {
                this.notifySubscribers('strategy_update', data)
            })

            this.socket.on('system_health', (data) => {
                this.notifySubscribers('system_health', data)
            })

            this.socket.on('profit_update', (data) => {
                this.notifySubscribers('profit_update', data)
            })

            this.socket.on('risk_alert', (data) => {
                this.notifySubscribers('risk_alert', data)
            })

            // Performance metrics
            this.socket.on('performance_metrics', (data) => {
                this.notifySubscribers('performance_metrics', data)
            })

            this.socket.on('portfolio_update', (data) => {
                this.notifySubscribers('portfolio_update', data)
            })

        } catch (error) {
            console.error('Failed to initialize WebSocket:', error)
        }
    }

    disconnect() {
        if (this.socket) {
            this.socket.disconnect()
            this.socket = null
            this.isConnected = false
        }
    }

    subscribe(event, callback) {
        if (!this.subscribers.has(event)) {
            this.subscribers.set(event, new Set())
        }
        this.subscribers.get(event).add(callback)

        // Return unsubscribe function
        return () => {
            const callbacks = this.subscribers.get(event)
            if (callbacks) {
                callbacks.delete(callback)
                if (callbacks.size === 0) {
                    this.subscribers.delete(event)
                }
            }
        }
    }

    notifySubscribers(event, data) {
        const callbacks = this.subscribers.get(event)
        if (callbacks) {
            callbacks.forEach(callback => {
                try {
                    callback(data)
                } catch (error) {
                    console.error(`Error in WebSocket callback for ${event}:`, error)
                }
            })
        }
    }

    // Send commands to the trading system
    sendCommand(command, data = {}) {
        if (this.socket && this.isConnected) {
            this.socket.emit(command, data)
        } else {
            console.warn('WebSocket not connected, cannot send command:', command)
        }
    }

    // Trading commands
    placeOrder(orderData) {
        this.sendCommand('place_order', orderData)
    }

    cancelOrder(orderId) {
        this.sendCommand('cancel_order', { orderId })
    }

    updateStrategy(strategyId, settings) {
        this.sendCommand('update_strategy', { strategyId, settings })
    }

    emergencyStop() {
        this.sendCommand('emergency_stop')
    }

    // Subscribe to specific market data
    subscribeToSymbol(symbol) {
        this.sendCommand('subscribe_symbol', { symbol })
    }

    unsubscribeFromSymbol(symbol) {
        this.sendCommand('unsubscribe_symbol', { symbol })
    }

    // AI commands
    requestPrediction(symbol) {
        this.sendCommand('request_prediction', { symbol })
    }

    updateAISettings(settings) {
        this.sendCommand('update_ai_settings', settings)
    }
}

// Create singleton instance
const websocketService = new WebSocketService()

// Auto-connect when service is imported
websocketService.connect()

export default websocketService
