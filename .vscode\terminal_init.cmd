@echo off
REM Terminal Initialization Script for Bybit Trading Bot
REM Optimized for AI tool integration and command execution

REM Set console title
title Bybit Trading Bot - AI Terminal

REM Set environment variables for optimal AI tool integration
set PYTHONUNBUFFERED=1
set PYTHONIOENCODING=utf-8
set CONDA_DEFAULT_ENV=bybit-trader
set CONDA_PREFIX=E:\conda\miniconda3\envs\bybit-trader

REM Activate conda environment
call E:\conda\miniconda3\Scripts\activate.bat bybit-trader

REM Change to project directory
cd /d E:\The_real_deal_copy\Bybit_Bot\BOT

REM Load terminal aliases
call .vscode\terminal_aliases.cmd

REM Display environment status
echo.
echo ========================================
echo Bybit Trading Bot Terminal Ready
echo ========================================
echo Environment: %CONDA_DEFAULT_ENV%
echo Python: %CONDA_PREFIX%\python.exe
echo Working Directory: %CD%
echo ========================================
echo.
echo Terminal optimized for AI tool integration
echo Commands execute with minimal latency
echo Type 'help' for available aliases
echo.

REM Ensure proper command prompt
prompt $P$G
