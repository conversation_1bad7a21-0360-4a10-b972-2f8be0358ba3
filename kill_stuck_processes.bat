@echo off
echo Killing all Python processes...
taskkill /F /IM python.exe /T 2>nul
taskkill /F /IM pythonw.exe /T 2>nul
echo Waiting 3 seconds...
timeout /t 3 /nobreak >nul
echo Checking for remaining Python processes...
tasklist /FI "IMAGENAME eq python.exe" 2>nul | find "python.exe" && echo Python processes still running || echo No Python processes found
tasklist /FI "IMAGENAME eq pythonw.exe" 2>nul | find "pythonw.exe" && echo Pythonw processes still running || echo No Pythonw processes found
echo Done.
pause
