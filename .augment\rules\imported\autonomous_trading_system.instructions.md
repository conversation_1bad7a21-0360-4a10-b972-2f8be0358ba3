---
type: "always_apply"
description: "Example description"
---
# AUTO<PERSON><PERSON>OUS TRADING SYSTEM - COPILOT INSTRUCTIONS

## MANDATORY RULES - ALWAYS FOLLOW
1. **ALL FUNCTIONS MUST REMAIN ACTIVE**
2. **NEVER SIMPLIFY OR <PERSON><PERSON>OVE CAPABILITIES**
3. **ALWAYS EXPAND AND <PERSON>N<PERSON><PERSON><PERSON> FEATURES**
4. **NO MOCK DATA - LIVE DATA ONLY**
5. **MAIN.PY IS SOLE ENTRY POINT**
6. **WORK UNTIL 100% SUCCESS**
7. **AUTONOMOUS OPERATION**
8. **CONTINUOUS LEARNING**

## PROFIT GENERATION STRATEGIES
**Golden Rule**: Maximum profit in minimum time

### ULTRA-HIGH FREQUENCY (Sub-second)
- Nano-scalping: 0.01-0.1s trades
- Order book imbalance trading
- Cross-venue arbitrage <100ms
- Flash opportunity capture
- Tick-by-tick momentum

### HIGH-FREQUENCY (1-60s)
- Statistical arbitrage
- Pair trading algorithms
- Mean reversion scalping
- Momentum continuation
- News reaction trading

### MEDIUM-FREQUENCY (1-60min)
- Multi-timeframe grids
- Spread trading
- Volatility arbitrage
- Market making
- Pattern recognition

## BYBIT V5 API EXPLOITATION
### Derivatives
- Perpetual Swaps: Max leverage
- Futures spreads: Time decay profit
- Options: Multi-leg strategies
- Cross-margin optimization
- Funding rate arbitrage

### Advanced Orders
- Conditional orders
- Iceberg orders
- TWAP/VWAP algorithms
- Bracket orders
- OCO orders

## PRIMARY PROFIT STREAMS
1. **Ultra-Fast Scalping**: 100+ concurrent algorithms, sub-ms execution
2. **Multi-Asset Arbitrage**: Spot vs perpetual, cross-exchange, funding rates
3. **Dynamic Grids**: Adaptive spacing, multi-timeframe coordination
4. **Market Making**: Ultra-tight spreads, inventory skewing
5. **Momentum Engines**: Breakout capture, pattern trading

## EXECUTION FRAMEWORK
### Speed Targets
- Order placement: <1ms
- Data processing: <100μs
- Decision making: <500μs
- Risk checks: <200μs

### Performance Metrics
- Profit/second: $1+ during active hours
- Success rate: >70%
- Sharpe ratio: >3.0
- Max drawdown: <5%

## MANDATORY ACTIVE CAPABILITIES
### Trading Functions
- Autonomous market analysis
- Self-initiated position sizing
- Auto risk management
- Multi-timeframe execution
- Portfolio rebalancing
- Order optimization
- Arbitrage detection
- Correlation analysis

### AI Functions
- Self-healing recovery
- Adaptive optimization
- Strategy discovery
- Backtesting automation
- Sentiment analysis
- Performance refinement
- ML model training
- A/B testing

### Meta-Cognitive Functions
- Self-awareness engine
- Meta-learning system
- Self-reflection protocols
- Cognitive monitoring
- Recursive improvement
- Error detection
- Performance anomaly detection
- Bias correction

## PROHIBITED ACTIONS
- Never disable functions
- Never use mock data
- Never simplify functionality
- Never remove capabilities
- Never create shortcuts
- Never allow degradation
- Never disable meta-cognitive functions
