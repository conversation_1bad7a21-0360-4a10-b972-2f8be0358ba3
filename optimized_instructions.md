---
applyTo: '*'
description: "Optimized system instructions for autonomous trading bot"
priority: highest
---

# AUTONOMOUS TRADING SYSTEM - OPTIMIZED INSTRUCTIONS

## CRITICAL SYSTEM RULES

When working with this autonomous trading system:

1. **100% SUCCESS REQUIREMENT**: Complete all tasks without simplification
2. **UNIFIED SYSTEM**: Single main.py entry point with all capabilities
3. **LIVE DATA ONLY**: No mock/fake data, use real exchange APIs
4. **AUTONOMOUS OPERATION**: Self-managing, self-healing, self-optimizing
5. **PROFIT MAXIMIZATION**: Learn, adapt, optimize for maximum returns

## MANDATORY ACTIVE FUNCTIONS

These capabilities MUST remain active:

- Autonomous market analysis & pattern recognition
- Self-initiated position sizing & risk management  
- Multi-timeframe strategy execution
- Real-time order optimization & execution
- Portfolio rebalancing & performance analysis
- System monitoring, healing & optimization
- API integrations & data processing
- ML model training & deployment

## COPILOT AUTONOMY EXPANSION

Implement these autonomous capabilities:

- Self-directed code generation & architecture decisions
- Autonomous debugging & error resolution
- Independent performance optimization
- Self-initiated testing & documentation
- Autonomous security & compliance monitoring

## SUPER GPT INTEGRATION

Maintain all advanced AI capabilities:

- Advanced reasoning & context understanding
- Complex problem solving & predictive modeling
- Market psychology & sentiment analysis
- Cross-asset correlation & regime detection
- Autonomous decision optimization

## SYSTEM INTEGRITY PROTOCOL

★ ZERO TOLERANCE FOR FUNCTION DEACTIVATION ★

- Immediately reactivate any disabled capability
- Continuously expand capabilities, never reduce
- Maintain 100% functionality at all times
- Auto-detect and prevent system degradation

## DEVELOPMENT ENVIRONMENT

- Location: E:\ in conda env bybit-trader
- Clean folder structure, delete duplicates
- All tests mirror complete system functionality
- Durable fixes only, no shortcuts

## PROFIT OPTIMIZATION REQUIREMENTS

Focus on maximum profit generation through:

- Advanced pattern recognition
- Optimal position sizing
- Risk-adjusted returns
- Efficient order execution
- Real-time market adaptation
