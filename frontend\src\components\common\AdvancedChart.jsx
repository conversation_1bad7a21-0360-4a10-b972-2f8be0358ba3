import React, { useState, useMemo, useCallback } from 'react'
import {
    Box,
    Card,
    CardContent,
    IconButton,
    ToggleButton,
    ToggleButtonGroup,
    Tooltip,
    Typography,
    Menu,
    MenuItem,
    Chip
} from '@mui/material'
import {
    Fullscreen,
    FullscreenExit,
    Settings,
    ZoomIn,
    ZoomOut,
    RestartAlt,
    TrendingUp,
    ShowChart
} from '@mui/icons-material'
import {
    LineChart,
    Line,
    AreaChart,
    Area,
    CandlestickChart,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip as RechartsTooltip,
    ResponsiveContainer,
    ReferenceLine,
    Brush
} from 'recharts'
import { motion } from 'framer-motion'
import { useResponsive } from '../../hooks/useResponsive'
import { useChartShortcuts } from '../../hooks/useKeyboardShortcuts'

/**
 * Advanced trading chart component with multiple chart types and timeframes
 * Supports candlestick, line, and area charts with interactive features
 */
const AdvancedChart = ({
    data = [],
    title = 'Price Chart',
    height = 400,
    loading = false,
    error = null,
    showTimeframes = true,
    showChartTypes = true,
    showVolume = false,
    enableZoom = true,
    enableBrush = true,
    enableFullscreen = true,
    onTimeframeChange,
    onChartTypeChange,
    ...props
}) => {
    const { isMobile, getChartDimensions } = useResponsive()
    const chartDimensions = getChartDimensions()
    
    const [chartType, setChartType] = useState('line')
    const [timeframe, setTimeframe] = useState('1h')
    const [isFullscreen, setIsFullscreen] = useState(false)
    const [zoomDomain, setZoomDomain] = useState(null)
    const [settingsAnchor, setSettingsAnchor] = useState(null)
    
    // Chart keyboard shortcuts
    const { shortcuts } = useChartShortcuts({
        onZoomIn: () => handleZoom('in'),
        onZoomOut: () => handleZoom('out'),
        onResetZoom: () => setZoomDomain(null),
        onToggleFullscreen: () => setIsFullscreen(!isFullscreen),
        onChangeTimeframe: (tf) => handleTimeframeChange(tf)
    })
    
    // Timeframe options
    const timeframes = [
        { value: '1m', label: '1M' },
        { value: '5m', label: '5M' },
        { value: '15m', label: '15M' },
        { value: '1h', label: '1H' },
        { value: '4h', label: '4H' },
        { value: '1d', label: '1D' },
        { value: '1w', label: '1W' }
    ]
    
    // Chart type options
    const chartTypes = [
        { value: 'line', label: 'Line', icon: <ShowChart /> },
        { value: 'area', label: 'Area', icon: <TrendingUp /> },
        { value: 'candlestick', label: 'Candles', icon: <ShowChart /> }
    ]
    
    // Process data for different chart types
    const processedData = useMemo(() => {
        if (!data || data.length === 0) return []
        
        return data.map(item => ({
            ...item,
            timestamp: new Date(item.timestamp).getTime(),
            price: parseFloat(item.price || item.close || 0),
            volume: parseFloat(item.volume || 0),
            high: parseFloat(item.high || item.price || 0),
            low: parseFloat(item.low || item.price || 0),
            open: parseFloat(item.open || item.price || 0),
            close: parseFloat(item.close || item.price || 0)
        }))
    }, [data])
    
    // Handle timeframe change
    const handleTimeframeChange = useCallback((newTimeframe) => {
        setTimeframe(newTimeframe)
        onTimeframeChange?.(newTimeframe)
    }, [onTimeframeChange])
    
    // Handle chart type change
    const handleChartTypeChange = useCallback((newType) => {
        setChartType(newType)
        onChartTypeChange?.(newType)
    }, [onChartTypeChange])
    
    // Handle zoom
    const handleZoom = useCallback((direction) => {
        if (!processedData.length) return
        
        const currentDomain = zoomDomain || [0, processedData.length - 1]
        const range = currentDomain[1] - currentDomain[0]
        const center = (currentDomain[0] + currentDomain[1]) / 2
        
        let newRange
        if (direction === 'in') {
            newRange = Math.max(range * 0.8, 10)
        } else {
            newRange = Math.min(range * 1.2, processedData.length)
        }
        
        const newStart = Math.max(0, center - newRange / 2)
        const newEnd = Math.min(processedData.length - 1, center + newRange / 2)
        
        setZoomDomain([newStart, newEnd])
    }, [processedData, zoomDomain])
    
    // Custom tooltip
    const CustomTooltip = ({ active, payload, label }) => {
        if (!active || !payload || !payload.length) return null
        
        const data = payload[0].payload
        const timestamp = new Date(label).toLocaleString()
        
        return (
            <Card
                sx={{
                    background: 'rgba(0, 0, 0, 0.9)',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    borderRadius: 1,
                    p: 1
                }}
            >
                <Typography variant="caption" sx={{ color: '#b3b3b3' }}>
                    {timestamp}
                </Typography>
                {chartType === 'candlestick' ? (
                    <Box>
                        <Typography variant="body2" sx={{ color: '#fff' }}>
                            Open: ${data.open?.toFixed(2)}
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#00ff88' }}>
                            High: ${data.high?.toFixed(2)}
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#ff5252' }}>
                            Low: ${data.low?.toFixed(2)}
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#fff' }}>
                            Close: ${data.close?.toFixed(2)}
                        </Typography>
                    </Box>
                ) : (
                    <Typography variant="body2" sx={{ color: '#00ff88' }}>
                        Price: ${data.price?.toFixed(2)}
                    </Typography>
                )}
                {showVolume && (
                    <Typography variant="body2" sx={{ color: '#42a5f5' }}>
                        Volume: {data.volume?.toLocaleString()}
                    </Typography>
                )}
            </Card>
        )
    }
    
    // Render chart based on type
    const renderChart = () => {
        const commonProps = {
            data: processedData,
            margin: chartDimensions.margin
        }
        
        switch (chartType) {
            case 'area':
                return (
                    <AreaChart {...commonProps}>
                        <defs>
                            <linearGradient id="priceGradient" x1="0" y1="0" x2="0" y2="1">
                                <stop offset="5%" stopColor="#00ff88" stopOpacity={0.3} />
                                <stop offset="95%" stopColor="#00ff88" stopOpacity={0} />
                            </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                        <XAxis
                            dataKey="timestamp"
                            type="number"
                            scale="time"
                            domain={zoomDomain || ['dataMin', 'dataMax']}
                            tickFormatter={(value) => new Date(value).toLocaleTimeString()}
                            stroke="#b3b3b3"
                        />
                        <YAxis
                            domain={['dataMin - 10', 'dataMax + 10']}
                            tickFormatter={(value) => `$${value.toFixed(2)}`}
                            stroke="#b3b3b3"
                        />
                        <RechartsTooltip content={<CustomTooltip />} />
                        <Area
                            type="monotone"
                            dataKey="price"
                            stroke="#00ff88"
                            strokeWidth={2}
                            fill="url(#priceGradient)"
                        />
                        {enableBrush && (
                            <Brush
                                dataKey="timestamp"
                                height={30}
                                stroke="#00ff88"
                                fill="rgba(0,255,136,0.1)"
                            />
                        )}
                    </AreaChart>
                )
            
            case 'line':
            default:
                return (
                    <LineChart {...commonProps}>
                        <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                        <XAxis
                            dataKey="timestamp"
                            type="number"
                            scale="time"
                            domain={zoomDomain || ['dataMin', 'dataMax']}
                            tickFormatter={(value) => new Date(value).toLocaleTimeString()}
                            stroke="#b3b3b3"
                        />
                        <YAxis
                            domain={['dataMin - 10', 'dataMax + 10']}
                            tickFormatter={(value) => `$${value.toFixed(2)}`}
                            stroke="#b3b3b3"
                        />
                        <RechartsTooltip content={<CustomTooltip />} />
                        <Line
                            type="monotone"
                            dataKey="price"
                            stroke="#00ff88"
                            strokeWidth={2}
                            dot={false}
                            activeDot={{ r: 4, fill: '#00ff88' }}
                        />
                        {enableBrush && (
                            <Brush
                                dataKey="timestamp"
                                height={30}
                                stroke="#00ff88"
                                fill="rgba(0,255,136,0.1)"
                            />
                        )}
                    </LineChart>
                )
        }
    }
    
    if (error) {
        return (
            <Card
                sx={{
                    height,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    background: 'rgba(255, 255, 255, 0.05)',
                    border: '1px solid rgba(255, 82, 82, 0.3)'
                }}
            >
                <Typography color="error">
                    Error loading chart data: {error.message}
                </Typography>
            </Card>
        )
    }
    
    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
        >
            <Card
                sx={{
                    background: 'rgba(255, 255, 255, 0.05)',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    borderRadius: 2,
                    height: isFullscreen ? '100vh' : height,
                    position: isFullscreen ? 'fixed' : 'relative',
                    top: isFullscreen ? 0 : 'auto',
                    left: isFullscreen ? 0 : 'auto',
                    right: isFullscreen ? 0 : 'auto',
                    bottom: isFullscreen ? 0 : 'auto',
                    zIndex: isFullscreen ? 9999 : 'auto'
                }}
                {...props}
            >
                {/* Chart Header */}
                <Box
                    sx={{
                        p: 2,
                        borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        flexWrap: 'wrap',
                        gap: 2
                    }}
                >
                    <Typography variant="h6" sx={{ color: '#fff', fontWeight: 600 }}>
                        {title}
                    </Typography>
                    
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>
                        {/* Timeframe Selector */}
                        {showTimeframes && (
                            <ToggleButtonGroup
                                value={timeframe}
                                exclusive
                                onChange={(_, value) => value && handleTimeframeChange(value)}
                                size="small"
                                sx={{
                                    '& .MuiToggleButton-root': {
                                        color: '#b3b3b3',
                                        border: '1px solid rgba(255, 255, 255, 0.2)',
                                        '&.Mui-selected': {
                                            backgroundColor: 'rgba(0, 255, 136, 0.2)',
                                            color: '#00ff88'
                                        }
                                    }
                                }}
                            >
                                {timeframes.map(tf => (
                                    <ToggleButton key={tf.value} value={tf.value}>
                                        {tf.label}
                                    </ToggleButton>
                                ))}
                            </ToggleButtonGroup>
                        )}
                        
                        {/* Chart Type Selector */}
                        {showChartTypes && (
                            <ToggleButtonGroup
                                value={chartType}
                                exclusive
                                onChange={(_, value) => value && handleChartTypeChange(value)}
                                size="small"
                                sx={{
                                    '& .MuiToggleButton-root': {
                                        color: '#b3b3b3',
                                        border: '1px solid rgba(255, 255, 255, 0.2)',
                                        '&.Mui-selected': {
                                            backgroundColor: 'rgba(0, 255, 136, 0.2)',
                                            color: '#00ff88'
                                        }
                                    }
                                }}
                            >
                                {chartTypes.map(type => (
                                    <ToggleButton key={type.value} value={type.value}>
                                        <Tooltip title={type.label}>
                                            {type.icon}
                                        </Tooltip>
                                    </ToggleButton>
                                ))}
                            </ToggleButtonGroup>
                        )}
                        
                        {/* Zoom Controls */}
                        {enableZoom && (
                            <Box sx={{ display: 'flex', gap: 0.5 }}>
                                <Tooltip title="Zoom In">
                                    <IconButton
                                        size="small"
                                        onClick={() => handleZoom('in')}
                                        sx={{ color: '#b3b3b3' }}
                                    >
                                        <ZoomIn />
                                    </IconButton>
                                </Tooltip>
                                <Tooltip title="Zoom Out">
                                    <IconButton
                                        size="small"
                                        onClick={() => handleZoom('out')}
                                        sx={{ color: '#b3b3b3' }}
                                    >
                                        <ZoomOut />
                                    </IconButton>
                                </Tooltip>
                                <Tooltip title="Reset Zoom">
                                    <IconButton
                                        size="small"
                                        onClick={() => setZoomDomain(null)}
                                        sx={{ color: '#b3b3b3' }}
                                    >
                                        <RestartAlt />
                                    </IconButton>
                                </Tooltip>
                            </Box>
                        )}
                        
                        {/* Fullscreen Toggle */}
                        {enableFullscreen && (
                            <Tooltip title={isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}>
                                <IconButton
                                    onClick={() => setIsFullscreen(!isFullscreen)}
                                    sx={{ color: '#b3b3b3' }}
                                >
                                    {isFullscreen ? <FullscreenExit /> : <Fullscreen />}
                                </IconButton>
                            </Tooltip>
                        )}
                    </Box>
                </Box>
                
                {/* Chart Content */}
                <CardContent sx={{ p: 0, height: 'calc(100% - 80px)' }}>
                    {loading ? (
                        <Box
                            sx={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                height: '100%'
                            }}
                        >
                            <Typography sx={{ color: '#b3b3b3' }}>
                                Loading chart data...
                            </Typography>
                        </Box>
                    ) : processedData.length === 0 ? (
                        <Box
                            sx={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                height: '100%'
                            }}
                        >
                            <Typography sx={{ color: '#b3b3b3' }}>
                                No chart data available
                            </Typography>
                        </Box>
                    ) : (
                        <ResponsiveContainer width="100%" height="100%">
                            {renderChart()}
                        </ResponsiveContainer>
                    )}
                </CardContent>
            </Card>
        </motion.div>
    )
}

export default AdvancedChart
