import React, { useState, useEffect } from 'react';
import { StyleSheet, View, ScrollView, RefreshControl } from 'react-native';
import { Text, Card, Button, Chip, DataTable, FAB } from 'react-native-paper';
import { useQuery } from '@tanstack/react-query';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';
import apiService from '../services/api';
import { theme } from '../styles/theme';

const TradingScreen: React.FC = () => {
    const [refreshing, setRefreshing] = useState(false);

    const {
        data: tradingData,
        isLoading,
        error,
        refetch,
    } = useQuery({
        queryKey: ['trading'],
        queryFn: apiService.getTradingData,
        refetchInterval: 5000, // Refresh every 5 seconds
    });

    const handleRefresh = async () => {
        setRefreshing(true);
        await refetch();
        setRefreshing(false);
    };

    const handleEmergencyStop = () => {
        // TODO: Implement emergency stop
        console.log('Emergency stop triggered');
    };

    if (isLoading && !tradingData) {
        return <LoadingSpinner message="Loading trading data..." />;
    }

    if (error) {
        return <ErrorMessage error={error} onRetry={refetch} />;
    }

    const positions = tradingData?.positions || [];
    const orders = tradingData?.orders || [];
    const strategies = tradingData?.strategies || [];

    return (
        <View style={styles.container}>
            <ScrollView
                style={styles.scrollView}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={handleRefresh}
                        colors={[theme.colors.primary]}
                    />
                }
                showsVerticalScrollIndicator={false}
            >
                {/* Trading Status */}
                <Card style={styles.statusCard}>
                    <Card.Content>
                        <View style={styles.statusHeader}>
                            <Text variant="titleLarge" style={styles.cardTitle}>
                                Trading Status
                            </Text>
                            <Chip
                                icon="trending-up"
                                style={[
                                    styles.statusChip,
                                    { backgroundColor: tradingData?.isActive ? theme.colors.primaryContainer : theme.colors.errorContainer }
                                ]}
                            >
                                {tradingData?.isActive ? 'Active' : 'Inactive'}
                            </Chip>
                        </View>
                        
                        <View style={styles.statusGrid}>
                            <View style={styles.statusItem}>
                                <Text variant="headlineSmall" style={styles.statusValue}>
                                    {tradingData?.totalProfit?.toFixed(2) || '0.00'}
                                </Text>
                                <Text variant="bodySmall" style={styles.statusLabel}>
                                    Total P&L (USDT)
                                </Text>
                            </View>
                            <View style={styles.statusItem}>
                                <Text variant="headlineSmall" style={styles.statusValue}>
                                    {tradingData?.dailyProfit?.toFixed(2) || '0.00'}
                                </Text>
                                <Text variant="bodySmall" style={styles.statusLabel}>
                                    Daily P&L (USDT)
                                </Text>
                            </View>
                            <View style={styles.statusItem}>
                                <Text variant="headlineSmall" style={styles.statusValue}>
                                    {positions.length}
                                </Text>
                                <Text variant="bodySmall" style={styles.statusLabel}>
                                    Open Positions
                                </Text>
                            </View>
                            <View style={styles.statusItem}>
                                <Text variant="headlineSmall" style={styles.statusValue}>
                                    {orders.length}
                                </Text>
                                <Text variant="bodySmall" style={styles.statusLabel}>
                                    Pending Orders
                                </Text>
                            </View>
                        </View>
                    </Card.Content>
                </Card>

                {/* Active Strategies */}
                <Card style={styles.card}>
                    <Card.Content>
                        <Text variant="titleMedium" style={styles.cardTitle}>
                            Active Strategies
                        </Text>
                        {strategies.length > 0 ? (
                            strategies.map((strategy: any, index: number) => (
                                <View key={index} style={styles.strategyItem}>
                                    <View style={styles.strategyHeader}>
                                        <Text variant="titleSmall" style={styles.strategyName}>
                                            {strategy.name}
                                        </Text>
                                        <Chip
                                            mode="outlined"
                                            style={styles.strategyStatus}
                                        >
                                            {strategy.status}
                                        </Chip>
                                    </View>
                                    <Text variant="bodySmall" style={styles.strategyDescription}>
                                        {strategy.description}
                                    </Text>
                                    <View style={styles.strategyStats}>
                                        <Text variant="bodySmall">
                                            Profit: {strategy.profit?.toFixed(2) || '0.00'} USDT
                                        </Text>
                                        <Text variant="bodySmall">
                                            Win Rate: {strategy.winRate?.toFixed(1) || '0.0'}%
                                        </Text>
                                    </View>
                                </View>
                            ))
                        ) : (
                            <View style={styles.emptyState}>
                                <Icon name="psychology" size={48} color={theme.colors.outline} />
                                <Text variant="bodyMedium" style={styles.emptyText}>
                                    No active strategies
                                </Text>
                            </View>
                        )}
                    </Card.Content>
                </Card>

                {/* Open Positions */}
                <Card style={styles.card}>
                    <Card.Content>
                        <Text variant="titleMedium" style={styles.cardTitle}>
                            Open Positions
                        </Text>
                        {positions.length > 0 ? (
                            <DataTable>
                                <DataTable.Header>
                                    <DataTable.Title>Symbol</DataTable.Title>
                                    <DataTable.Title>Side</DataTable.Title>
                                    <DataTable.Title numeric>Size</DataTable.Title>
                                    <DataTable.Title numeric>P&L</DataTable.Title>
                                </DataTable.Header>
                                {positions.map((position: any, index: number) => (
                                    <DataTable.Row key={index}>
                                        <DataTable.Cell>{position.symbol}</DataTable.Cell>
                                        <DataTable.Cell>
                                            <Chip
                                                mode="outlined"
                                                style={[
                                                    styles.sideChip,
                                                    position.side === 'Buy' 
                                                        ? { borderColor: theme.colors.primary }
                                                        : { borderColor: theme.colors.error }
                                                ]}
                                            >
                                                {position.side}
                                            </Chip>
                                        </DataTable.Cell>
                                        <DataTable.Cell numeric>{position.size}</DataTable.Cell>
                                        <DataTable.Cell numeric>
                                            <Text
                                                style={[
                                                    styles.pnlText,
                                                    { color: position.pnl >= 0 ? theme.colors.primary : theme.colors.error }
                                                ]}
                                            >
                                                {position.pnl?.toFixed(2) || '0.00'}
                                            </Text>
                                        </DataTable.Cell>
                                    </DataTable.Row>
                                ))}
                            </DataTable>
                        ) : (
                            <View style={styles.emptyState}>
                                <Icon name="account-balance" size={48} color={theme.colors.outline} />
                                <Text variant="bodyMedium" style={styles.emptyText}>
                                    No open positions
                                </Text>
                            </View>
                        )}
                    </Card.Content>
                </Card>

                {/* Pending Orders */}
                <Card style={styles.card}>
                    <Card.Content>
                        <Text variant="titleMedium" style={styles.cardTitle}>
                            Pending Orders
                        </Text>
                        {orders.length > 0 ? (
                            <DataTable>
                                <DataTable.Header>
                                    <DataTable.Title>Symbol</DataTable.Title>
                                    <DataTable.Title>Type</DataTable.Title>
                                    <DataTable.Title numeric>Price</DataTable.Title>
                                    <DataTable.Title numeric>Qty</DataTable.Title>
                                </DataTable.Header>
                                {orders.map((order: any, index: number) => (
                                    <DataTable.Row key={index}>
                                        <DataTable.Cell>{order.symbol}</DataTable.Cell>
                                        <DataTable.Cell>{order.type}</DataTable.Cell>
                                        <DataTable.Cell numeric>{order.price}</DataTable.Cell>
                                        <DataTable.Cell numeric>{order.quantity}</DataTable.Cell>
                                    </DataTable.Row>
                                ))}
                            </DataTable>
                        ) : (
                            <View style={styles.emptyState}>
                                <Icon name="pending-actions" size={48} color={theme.colors.outline} />
                                <Text variant="bodyMedium" style={styles.emptyText}>
                                    No pending orders
                                </Text>
                            </View>
                        )}
                    </Card.Content>
                </Card>
            </ScrollView>

            {/* Emergency Stop FAB */}
            <FAB
                icon="stop"
                style={styles.emergencyFab}
                onPress={handleEmergencyStop}
                label="Emergency Stop"
                color={theme.colors.onError}
                customSize={56}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: theme.colors.background,
    },
    scrollView: {
        flex: 1,
    },
    statusCard: {
        margin: 16,
        backgroundColor: theme.colors.surface,
    },
    card: {
        marginHorizontal: 16,
        marginBottom: 16,
        backgroundColor: theme.colors.surface,
    },
    cardTitle: {
        color: theme.colors.onSurface,
        fontWeight: '700',
        marginBottom: 16,
    },
    statusHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 16,
    },
    statusChip: {
        borderRadius: 16,
    },
    statusGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
    },
    statusItem: {
        width: '48%',
        alignItems: 'center',
        marginBottom: 16,
        padding: 12,
        backgroundColor: theme.colors.surfaceVariant,
        borderRadius: 8,
    },
    statusValue: {
        color: theme.colors.primary,
        fontWeight: '700',
        marginBottom: 4,
    },
    statusLabel: {
        color: theme.colors.onSurfaceVariant,
        textAlign: 'center',
    },
    strategyItem: {
        marginBottom: 16,
        padding: 12,
        backgroundColor: theme.colors.surfaceVariant,
        borderRadius: 8,
    },
    strategyHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
    },
    strategyName: {
        color: theme.colors.onSurface,
        fontWeight: '600',
    },
    strategyStatus: {
        height: 24,
    },
    strategyDescription: {
        color: theme.colors.onSurfaceVariant,
        marginBottom: 8,
    },
    strategyStats: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    sideChip: {
        height: 24,
    },
    pnlText: {
        fontWeight: '600',
    },
    emptyState: {
        alignItems: 'center',
        padding: 32,
    },
    emptyText: {
        color: theme.colors.onSurfaceVariant,
        marginTop: 16,
    },
    emergencyFab: {
        position: 'absolute',
        margin: 16,
        right: 0,
        bottom: 0,
        backgroundColor: theme.colors.error,
    },
});

export default TradingScreen;
