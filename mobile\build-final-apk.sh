#!/bin/bash

# Enhanced Bybit Trading Bot Mobile App Build Script
# Optimized for Motorola Moto G32 (XT2235-2) - Android 13
# Features: Logging Controls, Multiple Pages, Easy UI

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# App information
APP_NAME="Bybit Trading Bot"
VERSION="1.0.0"
BUILD_DATE=$(date +"%Y-%m-%d %H:%M:%S")
DEVICE="Motorola Moto G32 (XT2235-2)"
ANDROID_VERSION="13"
ARCHITECTURE="ARM64"

echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${CYAN}║                 BYBIT TRADING BOT MOBILE APP                 ║${NC}"
echo -e "${CYAN}║                    Enhanced Build Script                     ║${NC}"
echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
echo ""
echo -e "${GREEN}📱 Target Device: ${DEVICE}${NC}"
echo -e "${GREEN}🤖 Android Version: ${ANDROID_VERSION}${NC}"
echo -e "${GREEN}🏗️  Architecture: ${ARCHITECTURE}${NC}"
echo -e "${GREEN}📅 Build Date: ${BUILD_DATE}${NC}"
echo -e "${GREEN}🔢 Version: ${VERSION}${NC}"
echo ""

# Enhanced Features
echo -e "${CYAN}✨ Enhanced Features:${NC}"
echo -e "   📊 6 Main Pages: Dashboard, Trading, Portfolio, AI Status, Logs, Settings"
echo -e "   🎛️  Logging Controls: 7 categories with toggle switches"
echo -e "   📱 Mobile Optimized: Touch-friendly with haptic feedback"
echo -e "   🌙 Dark Theme: Battery optimized design"
echo -e "   🔄 Real-time Updates: Live data streaming"
echo -e "   📤 Export Functionality: Share and export logs"
echo -e "   🔒 Security Features: Biometric auth and auto-lock"
echo ""

# Check prerequisites
check_prerequisites() {
    echo -e "${YELLOW}🔍 Checking prerequisites...${NC}"
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js is not installed${NC}"
        exit 1
    fi
    
    NODE_VERSION=$(node --version)
    echo -e "${GREEN}✅ Node.js: ${NODE_VERSION}${NC}"
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}❌ npm is not installed${NC}"
        exit 1
    fi
    
    NPM_VERSION=$(npm --version)
    echo -e "${GREEN}✅ npm: ${NPM_VERSION}${NC}"
    
    # Check if we're in the mobile directory
    if [ ! -f "package.json" ]; then
        echo -e "${RED}❌ Please run this script from the mobile directory${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ All prerequisites met${NC}"
    echo ""
}

# Install dependencies
install_dependencies() {
    echo -e "${YELLOW}📦 Installing dependencies...${NC}"
    
    if [ ! -d "node_modules" ]; then
        npm install
    else
        echo -e "${GREEN}✅ Dependencies already installed${NC}"
    fi
    
    echo ""
}

# Configure for Moto G32
configure_device() {
    echo -e "${YELLOW}🔧 Configuring for Motorola Moto G32...${NC}"
    
    # Create device-specific configuration
    mkdir -p android/app/src/main/res/values
    
    cat > android/app/src/main/res/values/device-config.xml << EOF
<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Motorola Moto G32 Optimization -->
    <string name="device_model">XT2235-2</string>
    <string name="device_name">Motorola Moto G32</string>
    <string name="android_version">13</string>
    <string name="architecture">ARM64</string>
    
    <!-- Display Configuration -->
    <dimen name="screen_width">720dp</dimen>
    <dimen name="screen_height">1612dp</dimen>
    <integer name="density_dpi">420</integer>
    
    <!-- Network Configuration -->
    <string name="api_base_url">http://91.179.83.180:8000</string>
    <string name="wifi_network">192.168.129.10</string>
    
    <!-- App Configuration -->
    <string name="app_version">${VERSION}</string>
    <string name="build_date">${BUILD_DATE}</string>
    
    <!-- Enhanced Features -->
    <bool name="logging_controls_enabled">true</bool>
    <bool name="haptic_feedback_enabled">true</bool>
    <bool name="dark_theme_default">true</bool>
    <bool name="auto_refresh_enabled">true</bool>
</resources>
EOF
    
    echo -e "${GREEN}✅ Device configuration created${NC}"
    echo ""
}

# Build debug APK
build_debug() {
    echo -e "${YELLOW}🔨 Building debug APK...${NC}"
    
    npx react-native build-android --mode=debug
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Debug APK built successfully${NC}"
        DEBUG_APK="android/app/build/outputs/apk/debug/app-debug.apk"
        if [ -f "$DEBUG_APK" ]; then
            echo -e "${GREEN}📱 Debug APK location: ${DEBUG_APK}${NC}"
        fi
    else
        echo -e "${RED}❌ Debug build failed${NC}"
        return 1
    fi
    echo ""
}

# Build release APK
build_release() {
    echo -e "${YELLOW}🚀 Building release APK...${NC}"
    
    # Clean previous builds
    cd android
    ./gradlew clean
    cd ..
    
    # Build release APK
    npx react-native build-android --mode=release
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Release APK built successfully${NC}"
        RELEASE_APK="android/app/build/outputs/apk/release/app-release.apk"
        if [ -f "$RELEASE_APK" ]; then
            echo -e "${GREEN}📱 Release APK location: ${RELEASE_APK}${NC}"
            
            # Get APK size
            APK_SIZE=$(du -h "$RELEASE_APK" | cut -f1)
            echo -e "${GREEN}📏 APK Size: ${APK_SIZE}${NC}"
            
            # Rename APK with version and device info
            NEW_APK_NAME="BybitTradingBot-v${VERSION}-MotoG32-Enhanced.apk"
            cp "$RELEASE_APK" "$NEW_APK_NAME"
            echo -e "${GREEN}📱 Enhanced APK: ${NEW_APK_NAME}${NC}"
        fi
    else
        echo -e "${RED}❌ Release build failed${NC}"
        return 1
    fi
    echo ""
}

# Install on connected device
install_on_device() {
    echo -e "${YELLOW}📲 Installing on connected device...${NC}"
    
    # Check if device is connected
    if ! command -v adb &> /dev/null; then
        echo -e "${RED}❌ ADB is not installed or not in PATH${NC}"
        return 1
    fi
    
    DEVICES=$(adb devices | grep -v "List of devices" | grep "device$" | wc -l)
    if [ "$DEVICES" -eq 0 ]; then
        echo -e "${RED}❌ No Android device connected${NC}"
        echo -e "${YELLOW}💡 Please connect your Motorola G32 via USB and enable USB debugging${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ Found ${DEVICES} connected device(s)${NC}"
    
    # Install the APK
    RELEASE_APK="android/app/build/outputs/apk/release/app-release.apk"
    if [ -f "$RELEASE_APK" ]; then
        adb install -r "$RELEASE_APK"
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ App installed successfully on device${NC}"
        else
            echo -e "${RED}❌ Installation failed${NC}"
            return 1
        fi
    else
        echo -e "${RED}❌ Release APK not found. Build it first.${NC}"
        return 1
    fi
    echo ""
}

# Generate installation package
create_installation_package() {
    echo -e "${YELLOW}📦 Creating installation package...${NC}"
    
    PACKAGE_DIR="BybitTradingBot-MotoG32-Package"
    mkdir -p "$PACKAGE_DIR"
    
    # Copy APK
    if [ -f "BybitTradingBot-v${VERSION}-MotoG32-Enhanced.apk" ]; then
        cp "BybitTradingBot-v${VERSION}-MotoG32-Enhanced.apk" "$PACKAGE_DIR/"
    fi
    
    # Copy installation guide
    if [ -f "INSTALLATION_GUIDE.md" ]; then
        cp "INSTALLATION_GUIDE.md" "$PACKAGE_DIR/"
    fi
    
    # Create quick setup guide
    cat > "$PACKAGE_DIR/QUICK_SETUP.txt" << EOF
BYBIT TRADING BOT - QUICK SETUP GUIDE
====================================

Device: Motorola Moto G32 (XT2235-2)
Android: 13
Version: ${VERSION}
Build: ${BUILD_DATE}

INSTALLATION STEPS:
1. Enable "Install from unknown sources" in Settings > Security
2. Transfer the APK file to your phone
3. Open the APK file and tap "Install"
4. Open the app and configure logging in Settings > Logging

FEATURES:
✅ 6 Main Pages (Dashboard, Trading, Portfolio, AI Status, Logs, Settings)
✅ 7 Logging Categories with Toggle Controls
✅ Real-time Log Viewing and Export
✅ Touch-optimized Interface with Haptic Feedback
✅ Dark Theme for Battery Optimization
✅ Biometric Authentication Support

NETWORK:
- Primary: WiFi (192.168.129.10)
- Trading System: 91.179.83.180:8000
- Fallback: Mobile Data (4G LTE)

SUPPORT:
- Check logs in the Logs page for troubleshooting
- Use Settings > About > Send Feedback for issues
- Export logs for technical support

Enjoy your enhanced trading bot mobile experience!
EOF
    
    echo -e "${GREEN}✅ Installation package created: ${PACKAGE_DIR}/${NC}"
    echo ""
}

# Main execution
main() {
    check_prerequisites
    install_dependencies
    configure_device
    
    echo -e "${CYAN}What would you like to build?${NC}"
    echo "1) Debug APK (for testing)"
    echo "2) Release APK (for production use)"
    echo "3) Build and install on connected device"
    echo "4) Build everything and create installation package"
    echo ""
    
    read -p "Enter your choice (1-4): " choice
    
    case $choice in
        1)
            build_debug
            ;;
        2)
            build_release
            create_installation_package
            ;;
        3)
            build_release
            install_on_device
            ;;
        4)
            build_debug
            build_release
            install_on_device
            create_installation_package
            ;;
        *)
            echo -e "${RED}❌ Invalid choice${NC}"
            exit 1
            ;;
    esac
    
    echo -e "${GREEN}🎉 Build process completed!${NC}"
    echo -e "${CYAN}📱 Your enhanced Bybit Trading Bot mobile app is ready for your Motorola G32${NC}"
    echo ""
    echo -e "${YELLOW}Next Steps:${NC}"
    echo "1. Transfer the APK to your Motorola G32"
    echo "2. Install the app (enable unknown sources if needed)"
    echo "3. Configure logging controls in Settings > Logging"
    echo "4. Enjoy the enhanced mobile trading experience!"
}

# Run main function
main
