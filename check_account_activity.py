#!/usr/bin/env python3
"""
Check actual Bybit account activity
"""

from dotenv import load_dotenv
import os
from pybit.unified_trading import HTTP
from datetime import datetime, timedelta

# Load environment variables
load_dotenv()

def check_account_activity():
    """Check actual trading activity on Bybit account"""
    try:
        print("CHECKING BYBIT ACCOUNT ACTIVITY")
        print("=" * 50)
        
        # Initialize Bybit session
        session = HTTP(
            testnet=False,
            api_key=os.getenv('BYBIT_API_KEY'),
            api_secret=os.getenv('BYBIT_API_SECRET')
        )
        
        # Check current balance
        print("1. ACCOUNT BALANCE:")
        balance = session.get_wallet_balance(accountType='UNIFIED')
        if balance['retCode'] == 0:
            account_info = balance['result']['list'][0]
            total_equity = float(account_info['totalEquity'])
            available_balance = float(account_info['totalAvailableBalance'])
            print(f"   Total Equity: ${total_equity:.2f}")
            print(f"   Available Balance: ${available_balance:.2f}")
        else:
            print(f"   ERROR: {balance['retMsg']}")
            return False
            
        # Check open orders
        print("\n2. OPEN ORDERS:")
        try:
            orders = session.get_open_orders(category='linear', settleCoin='USDT')
            if orders['retCode'] == 0:
                open_orders = orders['result']['list']
                print(f"   Open Orders: {len(open_orders)}")
                if open_orders:
                    for order in open_orders[:5]:  # Show first 5
                        print(f"   - {order['symbol']}: {order['side']} {order['qty']} @ {order['price']}")
                else:
                    print("   No open orders")
            else:
                print(f"   ERROR: {orders['retMsg']}")
        except Exception as e:
            print(f"   ERROR getting orders: {str(e)}")

        # Check recent trades/executions
        print("\n3. RECENT TRADES (Last 24 hours):")
        trades = session.get_executions(category='linear', limit=50)
        if trades['retCode'] == 0:
            recent_trades = trades['result']['list']
            print(f"   Total Recent Executions: {len(recent_trades)}")
            
            # Filter trades from last 24 hours
            now = datetime.now()
            last_24h = now - timedelta(hours=24)
            
            recent_24h = []
            for trade in recent_trades:
                trade_time = datetime.fromtimestamp(int(trade['execTime']) / 1000)
                if trade_time > last_24h:
                    recent_24h.append(trade)
                    
            print(f"   Trades in last 24h: {len(recent_24h)}")
            
            if recent_24h:
                print("   Recent trades:")
                for trade in recent_24h[:10]:  # Show first 10
                    trade_time = datetime.fromtimestamp(int(trade['execTime']) / 1000)
                    print(f"   - {trade_time.strftime('%H:%M:%S')}: {trade['symbol']} {trade['side']} {trade['execQty']} @ {trade['execPrice']}")
            else:
                print("   NO TRADES IN LAST 24 HOURS")
                
        else:
            print(f"   ERROR: {trades['retMsg']}")
            
        # Check positions
        print("\n4. CURRENT POSITIONS:")
        positions = session.get_positions(category='linear', settleCoin='USDT')
        if positions['retCode'] == 0:
            active_positions = [pos for pos in positions['result']['list'] if float(pos['size']) > 0]
            print(f"   Active Positions: {len(active_positions)}")
            
            if active_positions:
                for pos in active_positions:
                    print(f"   - {pos['symbol']}: {pos['side']} {pos['size']} (PnL: {pos['unrealisedPnl']})")
            else:
                print("   No active positions")
        else:
            print(f"   ERROR: {positions['retMsg']}")
            
        # Summary
        print("\n" + "=" * 50)
        print("SUMMARY:")
        if len(recent_24h) > 0:
            print("   STATUS: TRADING ACTIVITY DETECTED")
            print(f"   TRADES: {len(recent_24h)} in last 24 hours")
        else:
            print("   STATUS: NO TRADING ACTIVITY")
            print("   ISSUE: Bot may not be placing actual trades")
            
        return len(recent_24h) > 0
        
    except Exception as e:
        print(f"ERROR checking account: {str(e)}")
        return False

if __name__ == "__main__":
    has_activity = check_account_activity()
    if not has_activity:
        print("\nWARNING: No trading activity detected!")
        print("The bot may be running but not placing actual trades.")
    else:
        print("\nSUCCESS: Trading activity confirmed!")
