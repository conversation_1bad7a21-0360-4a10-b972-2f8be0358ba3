import React, { useState } from 'react';
import { StyleSheet, View, ScrollView } from 'react-native';
import { Text, Card, List, Divider, Switch, Button } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LoggingControls from '../components/LoggingControls';
import { theme } from '../styles/theme';

const SettingsScreen: React.FC = () => {
    const [activeSection, setActiveSection] = useState<string>('general');
    const [settings, setSettings] = useState({
        notifications: {
            pushEnabled: true,
            tradeAlerts: true,
            profitAlerts: true,
            systemAlerts: true,
        },
        trading: {
            autoTrading: true,
            riskLevel: 'medium',
            maxPositionSize: 25,
        },
        security: {
            biometricEnabled: true,
            autoLockEnabled: true,
            autoLockTimeout: 300,
        },
        display: {
            darkMode: true,
            compactView: false,
            showAdvanced: false,
        },
    });

    const sections = [
        { id: 'general', title: 'General', icon: 'settings' },
        { id: 'logging', title: 'Logging', icon: 'description' },
        { id: 'trading', title: 'Trading', icon: 'trending-up' },
        { id: 'notifications', title: 'Notifications', icon: 'notifications' },
        { id: 'security', title: 'Security', icon: 'security' },
        { id: 'about', title: 'About', icon: 'info' },
    ];

    const updateSetting = (section: string, key: string, value: any) => {
        setSettings(prev => ({
            ...prev,
            [section]: {
                ...prev[section as keyof typeof prev],
                [key]: value,
            },
        }));
    };

    const renderGeneralSettings = () => (
        <View>
            <Card style={styles.settingsCard}>
                <Card.Content>
                    <Text variant="titleMedium" style={styles.cardTitle}>
                        Display Settings
                    </Text>
                    <List.Item
                        title="Dark Mode"
                        description="Use dark theme for better battery life"
                        left={() => <Icon name="dark-mode" size={24} color={theme.colors.onSurface} />}
                        right={() => (
                            <Switch
                                value={settings.display.darkMode}
                                onValueChange={(value) => updateSetting('display', 'darkMode', value)}
                            />
                        )}
                    />
                    <List.Item
                        title="Compact View"
                        description="Show more information in less space"
                        left={() => <Icon name="view-compact" size={24} color={theme.colors.onSurface} />}
                        right={() => (
                            <Switch
                                value={settings.display.compactView}
                                onValueChange={(value) => updateSetting('display', 'compactView', value)}
                            />
                        )}
                    />
                    <List.Item
                        title="Show Advanced Options"
                        description="Display advanced trading features"
                        left={() => <Icon name="tune" size={24} color={theme.colors.onSurface} />}
                        right={() => (
                            <Switch
                                value={settings.display.showAdvanced}
                                onValueChange={(value) => updateSetting('display', 'showAdvanced', value)}
                            />
                        )}
                    />
                </Card.Content>
            </Card>
        </View>
    );

    const renderTradingSettings = () => (
        <View>
            <Card style={styles.settingsCard}>
                <Card.Content>
                    <Text variant="titleMedium" style={styles.cardTitle}>
                        Trading Configuration
                    </Text>
                    <List.Item
                        title="Auto Trading"
                        description="Enable autonomous trading"
                        left={() => <Icon name="auto-mode" size={24} color={theme.colors.onSurface} />}
                        right={() => (
                            <Switch
                                value={settings.trading.autoTrading}
                                onValueChange={(value) => updateSetting('trading', 'autoTrading', value)}
                            />
                        )}
                    />
                    <List.Item
                        title="Risk Level"
                        description={`Current: ${settings.trading.riskLevel}`}
                        left={() => <Icon name="shield" size={24} color={theme.colors.onSurface} />}
                        onPress={() => {
                            // TODO: Open risk level selector
                        }}
                    />
                    <List.Item
                        title="Max Position Size"
                        description={`${settings.trading.maxPositionSize}% of portfolio`}
                        left={() => <Icon name="account-balance" size={24} color={theme.colors.onSurface} />}
                        onPress={() => {
                            // TODO: Open position size selector
                        }}
                    />
                </Card.Content>
            </Card>
        </View>
    );

    const renderNotificationSettings = () => (
        <View>
            <Card style={styles.settingsCard}>
                <Card.Content>
                    <Text variant="titleMedium" style={styles.cardTitle}>
                        Notification Preferences
                    </Text>
                    <List.Item
                        title="Push Notifications"
                        description="Enable push notifications"
                        left={() => <Icon name="notifications" size={24} color={theme.colors.onSurface} />}
                        right={() => (
                            <Switch
                                value={settings.notifications.pushEnabled}
                                onValueChange={(value) => updateSetting('notifications', 'pushEnabled', value)}
                            />
                        )}
                    />
                    <List.Item
                        title="Trade Alerts"
                        description="Notify when trades are executed"
                        left={() => <Icon name="trending-up" size={24} color={theme.colors.onSurface} />}
                        right={() => (
                            <Switch
                                value={settings.notifications.tradeAlerts}
                                onValueChange={(value) => updateSetting('notifications', 'tradeAlerts', value)}
                                disabled={!settings.notifications.pushEnabled}
                            />
                        )}
                    />
                    <List.Item
                        title="Profit Alerts"
                        description="Notify on profit/loss milestones"
                        left={() => <Icon name="attach-money" size={24} color={theme.colors.onSurface} />}
                        right={() => (
                            <Switch
                                value={settings.notifications.profitAlerts}
                                onValueChange={(value) => updateSetting('notifications', 'profitAlerts', value)}
                                disabled={!settings.notifications.pushEnabled}
                            />
                        )}
                    />
                    <List.Item
                        title="System Alerts"
                        description="Notify on system status changes"
                        left={() => <Icon name="warning" size={24} color={theme.colors.onSurface} />}
                        right={() => (
                            <Switch
                                value={settings.notifications.systemAlerts}
                                onValueChange={(value) => updateSetting('notifications', 'systemAlerts', value)}
                                disabled={!settings.notifications.pushEnabled}
                            />
                        )}
                    />
                </Card.Content>
            </Card>
        </View>
    );

    const renderSecuritySettings = () => (
        <View>
            <Card style={styles.settingsCard}>
                <Card.Content>
                    <Text variant="titleMedium" style={styles.cardTitle}>
                        Security & Privacy
                    </Text>
                    <List.Item
                        title="Biometric Authentication"
                        description="Use fingerprint or face unlock"
                        left={() => <Icon name="fingerprint" size={24} color={theme.colors.onSurface} />}
                        right={() => (
                            <Switch
                                value={settings.security.biometricEnabled}
                                onValueChange={(value) => updateSetting('security', 'biometricEnabled', value)}
                            />
                        )}
                    />
                    <List.Item
                        title="Auto Lock"
                        description="Lock app when inactive"
                        left={() => <Icon name="lock" size={24} color={theme.colors.onSurface} />}
                        right={() => (
                            <Switch
                                value={settings.security.autoLockEnabled}
                                onValueChange={(value) => updateSetting('security', 'autoLockEnabled', value)}
                            />
                        )}
                    />
                    <List.Item
                        title="Auto Lock Timeout"
                        description={`${settings.security.autoLockTimeout / 60} minutes`}
                        left={() => <Icon name="timer" size={24} color={theme.colors.onSurface} />}
                        onPress={() => {
                            // TODO: Open timeout selector
                        }}
                        disabled={!settings.security.autoLockEnabled}
                    />
                </Card.Content>
            </Card>
        </View>
    );

    const renderAboutSettings = () => (
        <View>
            <Card style={styles.settingsCard}>
                <Card.Content>
                    <Text variant="titleMedium" style={styles.cardTitle}>
                        App Information
                    </Text>
                    <List.Item
                        title="Version"
                        description="1.0.0 (Build 1)"
                        left={() => <Icon name="info" size={24} color={theme.colors.onSurface} />}
                    />
                    <List.Item
                        title="Device"
                        description="Motorola Moto G32 (XT2235-2)"
                        left={() => <Icon name="phone-android" size={24} color={theme.colors.onSurface} />}
                    />
                    <List.Item
                        title="Trading System"
                        description="*************:8000"
                        left={() => <Icon name="cloud" size={24} color={theme.colors.onSurface} />}
                    />
                    <List.Item
                        title="Last Updated"
                        description={new Date().toLocaleDateString()}
                        left={() => <Icon name="update" size={24} color={theme.colors.onSurface} />}
                    />
                </Card.Content>
            </Card>

            <Card style={styles.settingsCard}>
                <Card.Content>
                    <Text variant="titleMedium" style={styles.cardTitle}>
                        Support
                    </Text>
                    <Button
                        mode="outlined"
                        onPress={() => {
                            // TODO: Open support
                        }}
                        style={styles.supportButton}
                        icon="help"
                    >
                        Help & Support
                    </Button>
                    <Button
                        mode="outlined"
                        onPress={() => {
                            // TODO: Send feedback
                        }}
                        style={styles.supportButton}
                        icon="feedback"
                    >
                        Send Feedback
                    </Button>
                </Card.Content>
            </Card>
        </View>
    );

    const renderContent = () => {
        switch (activeSection) {
            case 'general':
                return renderGeneralSettings();
            case 'logging':
                return <LoggingControls />;
            case 'trading':
                return renderTradingSettings();
            case 'notifications':
                return renderNotificationSettings();
            case 'security':
                return renderSecuritySettings();
            case 'about':
                return renderAboutSettings();
            default:
                return renderGeneralSettings();
        }
    };

    return (
        <View style={styles.container}>
            {/* Section Navigation */}
            <View style={styles.navigationContainer}>
                <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={styles.navigationContent}
                >
                    {sections.map((section) => (
                        <Button
                            key={section.id}
                            mode={activeSection === section.id ? 'contained' : 'outlined'}
                            onPress={() => setActiveSection(section.id)}
                            style={[
                                styles.navigationButton,
                                activeSection === section.id && styles.activeNavigationButton
                            ]}
                            icon={section.icon}
                            compact
                        >
                            {section.title}
                        </Button>
                    ))}
                </ScrollView>
            </View>

            <Divider />

            {/* Content */}
            <ScrollView style={styles.contentContainer} showsVerticalScrollIndicator={false}>
                {renderContent()}
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: theme.colors.background,
    },
    navigationContainer: {
        backgroundColor: theme.colors.surface,
        paddingVertical: 8,
        elevation: 2,
    },
    navigationContent: {
        paddingHorizontal: 16,
        gap: 8,
    },
    navigationButton: {
        marginHorizontal: 2,
    },
    activeNavigationButton: {
        backgroundColor: theme.colors.primary,
    },
    contentContainer: {
        flex: 1,
        padding: 16,
    },
    settingsCard: {
        marginBottom: 16,
        backgroundColor: theme.colors.surface,
    },
    cardTitle: {
        color: theme.colors.onSurface,
        fontWeight: '700',
        marginBottom: 8,
    },
    supportButton: {
        marginVertical: 4,
    },
});

export default SettingsScreen;
