@echo off
REM Augment Extension Management Script
REM Run this script to safely enable/disable Augment extension

echo ================================================
echo AUGMENT EXTENSION MANAGEMENT
echo ================================================
echo.
echo Current Augment Extension Status:
code --list-extensions | findstr augment

echo.
echo Choose an option:
echo 1. Enable Augment Extension
echo 2. Disable Augment Extension  
echo 3. Check SSH Key Permissions
echo 4. Fix SSH Key Permissions
echo 5. View Extension Host Logs
echo 6. Reset VS Code Extension Host
echo 7. Exit
echo.

set /p choice="Enter your choice (1-7): "

if "%choice%"=="1" goto :enable
if "%choice%"=="2" goto :disable
if "%choice%"=="3" goto :check_ssh
if "%choice%"=="4" goto :fix_ssh
if "%choice%"=="5" goto :view_logs
if "%choice%"=="6" goto :reset_host
if "%choice%"=="7" goto :exit

:enable
echo Enabling Augment Extension...
code --enable-extension augment.vscode-augment
echo [OK] Augment extension enabled. Restart VS Code to take effect.
echo [WARNING] Monitor for extension host crashes!
pause
goto :menu

:disable
echo Disabling Augment Extension...
code --disable-extension augment.vscode-augment
echo [OK] Augment extension disabled. Restart VS Code to take effect.
pause
goto :menu

:check_ssh
echo Checking SSH Key Permissions...
icacls "c:\Users\<USER>\.augment\ssh-keys\augment_remote_agent_key"
echo.
echo [INFO] Correct permissions should show only:
echo   MSI\herma:(R) 
echo   [Read access for your user only]
pause
goto :menu

:fix_ssh
echo Fixing SSH Key Permissions...
icacls "c:\Users\<USER>\.augment\ssh-keys\augment_remote_agent_key" /inheritance:r /grant:r "MSI\herma:(R)"
echo [OK] SSH key permissions fixed
pause
goto :menu

:view_logs
echo Opening Extension Host Logs...
echo [INFO] Look for errors related to Augment extension
start "" "%APPDATA%\Code - Insiders\logs"
pause
goto :menu

:reset_host
echo Resetting VS Code Extension Host...
echo [INFO] This will restart VS Code and clear extension host cache
taskkill /f /im "Code - Insiders.exe" 2>nul
timeout /t 2 >nul
rmdir /s /q "%APPDATA%\Code - Insiders\CachedExtensions" 2>nul
rmdir /s /q "%APPDATA%\Code - Insiders\logs" 2>nul
echo [OK] Extension host reset. Restart VS Code manually.
pause
goto :menu

:menu
cls
goto :start

:exit
echo Goodbye!
exit /b 0

:start
goto :menu
