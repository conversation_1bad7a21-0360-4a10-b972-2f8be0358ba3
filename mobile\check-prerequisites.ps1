# Quick Prerequisites Checker for MSI Cyborg 15 A12VF
# Run this first to check what you need to install

Write-Host "🔍 Checking prerequisites for your MSI Cyborg 15..." -ForegroundColor Green
Write-Host ""

# Check Node.js
Write-Host "Checking Node.js..." -NoNewline
try {
    $nodeVersion = node --version 2>$null
    if ($nodeVersion) {
        Write-Host " ✅ Found: $nodeVersion" -ForegroundColor Green
    } else {
        throw "Not found"
    }
} catch {
    Write-Host " ❌ Not installed" -ForegroundColor Red
    Write-Host "   📥 Download from: https://nodejs.org/ (Choose LTS version)" -ForegroundColor Yellow
}

# Check npm
Write-Host "Checking npm..." -NoNewline
try {
    $npmVersion = npm --version 2>$null
    if ($npmVersion) {
        Write-Host " ✅ Found: v$npmVersion" -ForegroundColor Green
    } else {
        throw "Not found"
    }
} catch {
    Write-Host " ❌ Not installed (comes with Node.js)" -ForegroundColor Red
}

# Check Git
Write-Host "Checking Git..." -NoNewline
try {
    $gitVersion = git --version 2>$null
    if ($gitVersion) {
        Write-Host " ✅ Found: $gitVersion" -ForegroundColor Green
    } else {
        throw "Not found"
    }
} catch {
    Write-Host " ❌ Not installed" -ForegroundColor Red
    Write-Host "   📥 Download from: https://git-scm.com/download/win" -ForegroundColor Yellow
}

# Check Java
Write-Host "Checking Java..." -NoNewline
try {
    $javaVersion = java -version 2>&1 | Select-String "version" | Select-Object -First 1
    if ($javaVersion) {
        Write-Host " ✅ Found: $($javaVersion.Line.Split('"')[1])" -ForegroundColor Green
    } else {
        throw "Not found"
    }
} catch {
    Write-Host " ❌ Not installed" -ForegroundColor Red
    Write-Host "   📥 Download from: https://adoptium.net/ (Choose JDK 11+)" -ForegroundColor Yellow
}

# Check Android SDK
Write-Host "Checking Android SDK..." -NoNewline
$androidHome = "C:\Users\<USER>\AppData\Local\Android\Sdk"
if (Test-Path $androidHome) {
    Write-Host " ✅ Found at: $androidHome" -ForegroundColor Green
} else {
    Write-Host " ❌ Not found" -ForegroundColor Red
    Write-Host "   📥 Install Android Studio from: https://developer.android.com/studio" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎯 NEXT STEPS:" -ForegroundColor Cyan
Write-Host "1. Install any missing prerequisites above" -ForegroundColor White
Write-Host "2. Restart PowerShell after installations" -ForegroundColor White
Write-Host "3. Run this script again to verify" -ForegroundColor White
Write-Host "4. Then run the build script for your Moto G32" -ForegroundColor White

Write-Host ""
Write-Host "📋 For detailed instructions, see: COMPLETE_SETUP_GUIDE.md" -ForegroundColor Yellow
