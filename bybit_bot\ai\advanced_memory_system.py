"""
Advanced Memory System - Time-Aware Pattern Recognition and Learning
Implements sophisticated memory capabilities with temporal context for trading optimization
"""

import asyncio
import json
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field, asdict
from enum import Enum
from collections import defaultdict, deque
import numpy as np
import hashlib

from ..core.config import BotConfig
from ..core.logger import TradingBotLogger
from ..database.connection import DatabaseManager
from .memory_manager import PersistentMemoryManager, TradingMemory, PatternType, MemoryImportance


class TemporalPattern(Enum):
    """Types of temporal patterns"""
    INTRADAY = "intraday"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    SEASONAL = "seasonal"
    MARKET_CYCLE = "market_cycle"
    VOLATILITY_REGIME = "volatility_regime"


@dataclass
class TimeAwareMemory:
    """Enhanced memory with temporal context"""
    memory_id: str
    base_memory: TradingMemory
    temporal_context: Dict[str, Any]
    time_patterns: List[TemporalPattern]
    correlation_data: Dict[str, float]
    performance_decay: float
    temporal_weight: float
    created_at: datetime
    last_updated: datetime
    access_frequency: int
    temporal_relevance_score: float


@dataclass
class MarketCorrelation:
    """Market correlation data"""
    symbol_pair: Tuple[str, str]
    correlation_coefficient: float
    time_window: str
    confidence: float
    last_updated: datetime
    historical_data: List[Tuple[datetime, float]]


@dataclass
class StrategyPerformanceMemory:
    """Strategy performance with temporal context"""
    strategy_name: str
    time_period: str
    market_conditions: Dict[str, Any]
    performance_metrics: Dict[str, float]
    parameter_evolution: List[Dict[str, Any]]
    success_patterns: List[Dict[str, Any]]
    failure_patterns: List[Dict[str, Any]]
    temporal_effectiveness: Dict[str, float]
    last_updated: datetime


class AdvancedMemorySystem:
    """
    Advanced memory system with time-aware pattern recognition
    Provides sophisticated learning and adaptation capabilities
    """
    
    def __init__(self, config: BotConfig, database_manager: DatabaseManager, 
                 base_memory_manager: PersistentMemoryManager):
        self.config = config
        self.db = database_manager
        self.base_memory = base_memory_manager
        self.logger = TradingBotLogger(config)
        
        # Advanced memory storage
        self.time_aware_memories: Dict[str, TimeAwareMemory] = {}
        self.market_correlations: Dict[Tuple[str, str], MarketCorrelation] = {}
        self.strategy_memories: Dict[str, StrategyPerformanceMemory] = {}
        
        # Temporal pattern recognition
        self.temporal_patterns: Dict[TemporalPattern, Dict[str, Any]] = {}
        self.pattern_evolution: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
        
        # Performance tracking
        self.memory_performance: Dict[str, Dict[str, float]] = defaultdict(dict)
        self.correlation_matrix: Dict[str, Dict[str, float]] = defaultdict(dict)
        
        # Configuration
        self.max_memories = 50000
        self.correlation_threshold = 0.7
        self.temporal_decay_rate = 0.95
        self.pattern_confidence_threshold = 0.8
        
        # Time windows for analysis
        self.time_windows = {
            'short': timedelta(hours=1),
            'medium': timedelta(hours=6),
            'long': timedelta(days=1),
            'extended': timedelta(days=7)
        }
        
    async def initialize(self):
        """Initialize the advanced memory system"""
        try:
            self.logger.info("INITIALIZING Advanced Memory System...")
            
            # Initialize base memory manager
            await self.base_memory.initialize()
            
            # Load time-aware memories
            await self._load_time_aware_memories()
            
            # Load market correlations
            await self._load_market_correlations()
            
            # Load strategy performance memories
            await self._load_strategy_memories()
            
            # Initialize temporal pattern recognition
            await self._initialize_temporal_patterns()
            
            # Start background tasks
            asyncio.create_task(self._memory_maintenance_loop())
            asyncio.create_task(self._correlation_update_loop())
            asyncio.create_task(self._pattern_evolution_loop())
            
            self.logger.info(f"SUCCESS: Advanced Memory System initialized with {len(self.time_aware_memories)} memories")
            
        except Exception as e:
            self.logger.error(f"ERROR initializing Advanced Memory System: {e}")
            raise
    
    async def store_time_aware_experience(self, 
                                        market_conditions: Dict[str, Any],
                                        strategy_used: str,
                                        action_taken: str,
                                        outcome: Dict[str, Any],
                                        temporal_context: Dict[str, Any],
                                        correlation_data: Optional[Dict[str, float]] = None) -> str:
        """Store experience with temporal context"""
        try:
            # Store base memory first
            base_memory_id = await self.base_memory.store_trading_experience(
                market_conditions, strategy_used, action_taken, outcome
            )
            
            # Get base memory
            base_memory = self.base_memory.memories.get(base_memory_id)
            if not base_memory:
                raise ValueError(f"Failed to retrieve base memory {base_memory_id}")
            
            # Analyze temporal patterns
            time_patterns = self._analyze_temporal_patterns(temporal_context, market_conditions)
            
            # Calculate temporal relevance
            temporal_relevance = self._calculate_temporal_relevance(temporal_context, outcome)
            
            # Create time-aware memory
            memory_id = f"ta_{base_memory_id}"
            time_aware_memory = TimeAwareMemory(
                memory_id=memory_id,
                base_memory=base_memory,
                temporal_context=temporal_context,
                time_patterns=time_patterns,
                correlation_data=correlation_data or {},
                performance_decay=1.0,
                temporal_weight=temporal_relevance,
                created_at=datetime.now(timezone.utc),
                last_updated=datetime.now(timezone.utc),
                access_frequency=0,
                temporal_relevance_score=temporal_relevance
            )
            
            # Store in memory
            self.time_aware_memories[memory_id] = time_aware_memory
            
            # Update correlations if provided
            if correlation_data:
                await self._update_correlations(correlation_data, temporal_context)
            
            # Update strategy memory
            await self._update_strategy_memory(strategy_used, outcome, temporal_context)
            
            # Save to database
            await self._save_time_aware_memory(time_aware_memory)
            
            self.logger.info(f"Stored time-aware memory: {memory_id}")
            return memory_id
            
        except Exception as e:
            self.logger.error(f"Error storing time-aware experience: {e}")
            raise
    
    async def find_similar_patterns(self, 
                                  current_conditions: Dict[str, Any],
                                  temporal_context: Dict[str, Any],
                                  min_similarity: float = 0.7) -> List[TimeAwareMemory]:
        """Find similar patterns with temporal context"""
        try:
            similar_memories = []
            current_time = datetime.now(timezone.utc)
            
            for memory in self.time_aware_memories.values():
                # Calculate similarity
                similarity = self._calculate_temporal_similarity(
                    current_conditions, temporal_context,
                    memory.base_memory.market_conditions, memory.temporal_context
                )
                
                # Apply temporal decay
                time_diff = (current_time - memory.created_at).total_seconds() / 86400  # days
                decay_factor = self.temporal_decay_rate ** time_diff
                adjusted_similarity = similarity * decay_factor * memory.temporal_weight
                
                if adjusted_similarity >= min_similarity:
                    memory.access_frequency += 1
                    memory.last_updated = current_time
                    similar_memories.append(memory)
            
            # Sort by adjusted similarity
            similar_memories.sort(key=lambda m: m.temporal_relevance_score, reverse=True)
            
            return similar_memories[:10]  # Return top 10
            
        except Exception as e:
            self.logger.error(f"Error finding similar patterns: {e}")
            return []
    
    async def get_strategy_performance_memory(self, strategy_name: str, 
                                            time_period: str = "all") -> Optional[StrategyPerformanceMemory]:
        """Get strategy performance memory for specific time period"""
        try:
            key = f"{strategy_name}_{time_period}"
            return self.strategy_memories.get(key)
            
        except Exception as e:
            self.logger.error(f"Error getting strategy memory: {e}")
            return None
    
    async def get_market_correlations(self, symbol: str, 
                                    min_correlation: float = 0.5) -> List[MarketCorrelation]:
        """Get market correlations for a symbol"""
        try:
            correlations = []
            
            for (sym1, sym2), correlation in self.market_correlations.items():
                if (sym1 == symbol or sym2 == symbol) and abs(correlation.correlation_coefficient) >= min_correlation:
                    correlations.append(correlation)
            
            # Sort by correlation strength
            correlations.sort(key=lambda c: abs(c.correlation_coefficient), reverse=True)
            
            return correlations
            
        except Exception as e:
            self.logger.error(f"Error getting market correlations: {e}")
            return []
    
    def _analyze_temporal_patterns(self, temporal_context: Dict[str, Any], 
                                 market_conditions: Dict[str, Any]) -> List[TemporalPattern]:
        """Analyze temporal patterns in the data"""
        patterns = []
        
        # Check for intraday patterns
        if 'hour_of_day' in temporal_context:
            hour = temporal_context['hour_of_day']
            if 9 <= hour <= 11 or 14 <= hour <= 16:  # Market open/close times
                patterns.append(TemporalPattern.INTRADAY)
        
        # Check for weekly patterns
        if 'day_of_week' in temporal_context:
            day = temporal_context['day_of_week']
            if day in [1, 5]:  # Monday/Friday effects
                patterns.append(TemporalPattern.WEEKLY)
        
        # Check for volatility regime patterns
        if 'volatility' in market_conditions:
            volatility = market_conditions['volatility']
            if volatility > 0.02:  # High volatility
                patterns.append(TemporalPattern.VOLATILITY_REGIME)
        
        return patterns
    
    def _calculate_temporal_relevance(self, temporal_context: Dict[str, Any], 
                                    outcome: Dict[str, Any]) -> float:
        """Calculate temporal relevance score"""
        relevance = 0.5  # Base relevance
        
        # Boost relevance for successful outcomes
        if outcome.get('success', False):
            relevance += 0.3
        
        # Boost for high profit outcomes
        profit = outcome.get('profit', 0)
        if profit > 0:
            relevance += min(0.2, profit / 1000)  # Cap at 0.2
        
        # Boost for specific time patterns
        if 'market_session' in temporal_context:
            session = temporal_context['market_session']
            if session in ['US', 'EUROPEAN']:  # Active sessions
                relevance += 0.1
        
        return min(1.0, relevance)

    def _calculate_temporal_similarity(self, current_conditions: Dict[str, Any],
                                     current_temporal: Dict[str, Any],
                                     stored_conditions: Dict[str, Any],
                                     stored_temporal: Dict[str, Any]) -> float:
        """Calculate similarity including temporal context"""
        # Base market conditions similarity
        market_similarity = self._calculate_market_similarity(current_conditions, stored_conditions)

        # Temporal context similarity
        temporal_similarity = self._calculate_temporal_context_similarity(current_temporal, stored_temporal)

        # Weighted combination
        return 0.7 * market_similarity + 0.3 * temporal_similarity

    def _calculate_market_similarity(self, conditions1: Dict[str, Any],
                                   conditions2: Dict[str, Any]) -> float:
        """Calculate market conditions similarity"""
        similarity = 0.0
        total_weight = 0.0

        weights = {
            'volatility': 0.25,
            'volume': 0.20,
            'price_change': 0.20,
            'rsi': 0.15,
            'macd': 0.10,
            'trend_strength': 0.10
        }

        for key, weight in weights.items():
            if key in conditions1 and key in conditions2:
                val1 = float(conditions1[key])
                val2 = float(conditions2[key])

                # Normalize difference to 0-1 scale
                if key == 'rsi':
                    diff = abs(val1 - val2) / 100.0
                elif key in ['volatility', 'volume']:
                    diff = min(1.0, abs(val1 - val2) / max(val1, val2, 0.001))
                else:
                    diff = min(1.0, abs(val1 - val2) / (abs(val1) + abs(val2) + 0.001))

                similarity += weight * (1.0 - diff)
                total_weight += weight

        return similarity / max(total_weight, 0.001)

    def _calculate_temporal_context_similarity(self, temporal1: Dict[str, Any],
                                             temporal2: Dict[str, Any]) -> float:
        """Calculate temporal context similarity"""
        similarity = 0.0
        total_weight = 0.0

        weights = {
            'hour_of_day': 0.3,
            'day_of_week': 0.2,
            'market_session': 0.3,
            'is_holiday': 0.1,
            'volatility_regime': 0.1
        }

        for key, weight in weights.items():
            if key in temporal1 and key in temporal2:
                if key == 'hour_of_day':
                    # Circular similarity for hours
                    h1, h2 = temporal1[key], temporal2[key]
                    diff = min(abs(h1 - h2), 24 - abs(h1 - h2)) / 12.0
                    similarity += weight * (1.0 - diff)
                elif key == 'day_of_week':
                    # Circular similarity for days
                    d1, d2 = temporal1[key], temporal2[key]
                    diff = min(abs(d1 - d2), 7 - abs(d1 - d2)) / 3.5
                    similarity += weight * (1.0 - diff)
                elif key in ['market_session', 'is_holiday']:
                    # Exact match for categorical
                    similarity += weight * (1.0 if temporal1[key] == temporal2[key] else 0.0)
                else:
                    # Numeric similarity
                    val1, val2 = float(temporal1[key]), float(temporal2[key])
                    diff = abs(val1 - val2) / (abs(val1) + abs(val2) + 0.001)
                    similarity += weight * (1.0 - min(1.0, diff))

                total_weight += weight

        return similarity / max(total_weight, 0.001)

    async def _update_correlations(self, correlation_data: Dict[str, float],
                                 temporal_context: Dict[str, Any]):
        """Update market correlations"""
        try:
            current_time = datetime.now(timezone.utc)

            # Process correlation pairs
            symbols = list(correlation_data.keys())
            for i, sym1 in enumerate(symbols):
                for sym2 in symbols[i+1:]:
                    if sym1 in correlation_data and sym2 in correlation_data:
                        # Calculate correlation coefficient
                        corr_coeff = self._calculate_correlation_coefficient(
                            correlation_data[sym1], correlation_data[sym2]
                        )

                        pair = (sym1, sym2) if sym1 < sym2 else (sym2, sym1)

                        if pair in self.market_correlations:
                            # Update existing correlation
                            correlation = self.market_correlations[pair]
                            correlation.correlation_coefficient = corr_coeff
                            correlation.last_updated = current_time
                            correlation.historical_data.append((current_time, corr_coeff))

                            # Keep only recent data
                            if len(correlation.historical_data) > 1000:
                                correlation.historical_data = correlation.historical_data[-1000:]
                        else:
                            # Create new correlation
                            correlation = MarketCorrelation(
                                symbol_pair=pair,
                                correlation_coefficient=corr_coeff,
                                time_window="1h",
                                confidence=0.8,
                                last_updated=current_time,
                                historical_data=[(current_time, corr_coeff)]
                            )
                            self.market_correlations[pair] = correlation

        except Exception as e:
            self.logger.error(f"Error updating correlations: {e}")

    def _calculate_correlation_coefficient(self, val1: float, val2: float) -> float:
        """Calculate simple correlation coefficient"""
        # This is a simplified correlation calculation
        # In practice, you'd use historical price data
        if abs(val1) < 0.001 or abs(val2) < 0.001:
            return 0.0

        # Simple correlation based on value similarity
        ratio = min(val1, val2) / max(val1, val2)
        return ratio if val1 * val2 > 0 else -ratio

    async def _update_strategy_memory(self, strategy_name: str, outcome: Dict[str, Any],
                                    temporal_context: Dict[str, Any]):
        """Update strategy performance memory"""
        try:
            time_period = temporal_context.get('market_session', 'unknown')
            key = f"{strategy_name}_{time_period}"

            current_time = datetime.now(timezone.utc)

            if key in self.strategy_memories:
                strategy_mem = self.strategy_memories[key]

                # Update performance metrics
                if 'profit' in outcome:
                    profit = outcome['profit']
                    current_avg = strategy_mem.performance_metrics.get('avg_profit', 0.0)
                    current_count = strategy_mem.performance_metrics.get('trade_count', 0)
                    new_avg = (current_avg * current_count + profit) / (current_count + 1)

                    strategy_mem.performance_metrics['avg_profit'] = new_avg
                    strategy_mem.performance_metrics['trade_count'] = current_count + 1

                    if outcome.get('success', False):
                        strategy_mem.performance_metrics['success_count'] = \
                            strategy_mem.performance_metrics.get('success_count', 0) + 1

                strategy_mem.last_updated = current_time
            else:
                # Create new strategy memory
                strategy_mem = StrategyPerformanceMemory(
                    strategy_name=strategy_name,
                    time_period=time_period,
                    market_conditions={},
                    performance_metrics={
                        'avg_profit': outcome.get('profit', 0.0),
                        'trade_count': 1,
                        'success_count': 1 if outcome.get('success', False) else 0
                    },
                    parameter_evolution=[],
                    success_patterns=[],
                    failure_patterns=[],
                    temporal_effectiveness={},
                    last_updated=current_time
                )
                self.strategy_memories[key] = strategy_mem

        except Exception as e:
            self.logger.error(f"Error updating strategy memory: {e}")

    async def _load_time_aware_memories(self):
        """Load time-aware memories from database"""
        try:
            rows = await self.db.fetch_all(
                "SELECT * FROM time_aware_memories ORDER BY created_at DESC LIMIT 1000"
            )

            for row in rows:
                memory_data = json.loads(row['memory_data'])
                memory = TimeAwareMemory(**memory_data)
                self.time_aware_memories[memory.memory_id] = memory

        except Exception as e:
            self.logger.error(f"Error loading time-aware memories: {e}")

    async def _load_market_correlations(self):
        """Load market correlations from database"""
        try:
            rows = await self.db.fetch_all(
                "SELECT * FROM market_correlations ORDER BY last_updated DESC"
            )

            for row in rows:
                correlation_data = json.loads(row['correlation_data'])
                correlation = MarketCorrelation(**correlation_data)
                self.market_correlations[correlation.symbol_pair] = correlation

        except Exception as e:
            self.logger.error(f"Error loading market correlations: {e}")

    async def _load_strategy_memories(self):
        """Load strategy performance memories from database"""
        try:
            rows = await self.db.fetch_all(
                "SELECT * FROM strategy_performance_memories ORDER BY last_updated DESC"
            )

            for row in rows:
                memory_data = json.loads(row['memory_data'])
                memory = StrategyPerformanceMemory(**memory_data)
                key = f"{memory.strategy_name}_{memory.time_period}"
                self.strategy_memories[key] = memory

        except Exception as e:
            self.logger.error(f"Error loading strategy memories: {e}")

    async def _initialize_temporal_patterns(self):
        """Initialize temporal pattern recognition"""
        try:
            for pattern_type in TemporalPattern:
                self.temporal_patterns[pattern_type] = {
                    'occurrences': 0,
                    'success_rate': 0.0,
                    'avg_profit': 0.0,
                    'confidence': 0.0
                }

        except Exception as e:
            self.logger.error(f"Error initializing temporal patterns: {e}")

    async def _save_time_aware_memory(self, memory: TimeAwareMemory):
        """Save time-aware memory to database"""
        try:
            memory_data = asdict(memory)
            # Convert datetime objects to strings for JSON serialization
            memory_data['created_at'] = memory.created_at.isoformat()
            memory_data['last_updated'] = memory.last_updated.isoformat()
            memory_data['base_memory'] = asdict(memory.base_memory)
            memory_data['base_memory']['timestamp'] = memory.base_memory.timestamp.isoformat()
            memory_data['base_memory']['last_accessed'] = memory.base_memory.last_accessed.isoformat()

            await self.db.execute_sql(
                """
                INSERT INTO time_aware_memories (memory_id, memory_data, created_at)
                VALUES (:memory_id, :memory_data, :created_at)
                ON CONFLICT (memory_id)
                DO UPDATE SET memory_data = :memory_data, updated_at = CURRENT_TIMESTAMP
                """,
                {
                    'memory_id': memory.memory_id,
                    'memory_data': json.dumps(memory_data),
                    'created_at': memory.created_at
                }
            )

        except Exception as e:
            self.logger.error(f"Error saving time-aware memory: {e}")

    async def _memory_maintenance_loop(self):
        """Background task for memory maintenance"""
        while True:
            try:
                await asyncio.sleep(3600)  # Run every hour

                # Apply temporal decay
                current_time = datetime.now(timezone.utc)
                for memory in self.time_aware_memories.values():
                    time_diff = (current_time - memory.created_at).total_seconds() / 86400
                    memory.performance_decay *= (self.temporal_decay_rate ** (time_diff / 30))

                # Remove old memories
                if len(self.time_aware_memories) > self.max_memories:
                    # Sort by relevance and keep top memories
                    sorted_memories = sorted(
                        self.time_aware_memories.items(),
                        key=lambda x: x[1].temporal_relevance_score * x[1].performance_decay,
                        reverse=True
                    )

                    # Keep top 80% of memories
                    keep_count = int(self.max_memories * 0.8)
                    memories_to_keep = dict(sorted_memories[:keep_count])
                    self.time_aware_memories = memories_to_keep

                self.logger.info(f"Memory maintenance completed. Active memories: {len(self.time_aware_memories)}")

            except Exception as e:
                self.logger.error(f"Error in memory maintenance: {e}")

    async def _correlation_update_loop(self):
        """Background task for correlation updates"""
        while True:
            try:
                await asyncio.sleep(1800)  # Run every 30 minutes

                # Update correlation matrix
                await self._update_correlation_matrix()

                self.logger.info("Correlation matrix updated")

            except Exception as e:
                self.logger.error(f"Error in correlation update: {e}")

    async def _pattern_evolution_loop(self):
        """Background task for pattern evolution tracking"""
        while True:
            try:
                await asyncio.sleep(7200)  # Run every 2 hours

                # Analyze pattern evolution
                await self._analyze_pattern_evolution()

                self.logger.info("Pattern evolution analysis completed")

            except Exception as e:
                self.logger.error(f"Error in pattern evolution: {e}")

    async def _update_correlation_matrix(self):
        """Update the correlation matrix"""
        try:
            # Build correlation matrix from recent correlations
            symbols = set()
            for (sym1, sym2) in self.market_correlations.keys():
                symbols.add(sym1)
                symbols.add(sym2)

            for sym1 in symbols:
                if sym1 not in self.correlation_matrix:
                    self.correlation_matrix[sym1] = {}

                for sym2 in symbols:
                    if sym1 != sym2:
                        pair = (sym1, sym2) if sym1 < sym2 else (sym2, sym1)
                        if pair in self.market_correlations:
                            correlation = self.market_correlations[pair]
                            self.correlation_matrix[sym1][sym2] = correlation.correlation_coefficient
                        else:
                            self.correlation_matrix[sym1][sym2] = 0.0

        except Exception as e:
            self.logger.error(f"Error updating correlation matrix: {e}")

    async def _analyze_pattern_evolution(self):
        """Analyze how patterns evolve over time"""
        try:
            current_time = datetime.now(timezone.utc)

            # Analyze temporal patterns
            for pattern_type in TemporalPattern:
                pattern_memories = [
                    m for m in self.time_aware_memories.values()
                    if pattern_type in m.time_patterns
                ]

                if pattern_memories:
                    # Calculate pattern statistics
                    success_count = sum(1 for m in pattern_memories
                                      if m.base_memory.outcome.get('success', False))
                    total_profit = sum(m.base_memory.outcome.get('profit', 0)
                                     for m in pattern_memories)

                    self.temporal_patterns[pattern_type] = {
                        'occurrences': len(pattern_memories),
                        'success_rate': success_count / len(pattern_memories),
                        'avg_profit': total_profit / len(pattern_memories),
                        'confidence': min(1.0, len(pattern_memories) / 100)
                    }

        except Exception as e:
            self.logger.error(f"Error analyzing pattern evolution: {e}")

    async def get_memory_statistics(self) -> Dict[str, Any]:
        """Get comprehensive memory system statistics"""
        try:
            current_time = datetime.now(timezone.utc)

            stats = {
                'total_memories': len(self.time_aware_memories),
                'total_correlations': len(self.market_correlations),
                'total_strategy_memories': len(self.strategy_memories),
                'temporal_patterns': dict(self.temporal_patterns),
                'memory_age_distribution': {},
                'correlation_strength_distribution': {},
                'strategy_performance_summary': {}
            }

            # Memory age distribution
            age_buckets = {'<1h': 0, '1h-1d': 0, '1d-1w': 0, '1w-1m': 0, '>1m': 0}
            for memory in self.time_aware_memories.values():
                age = (current_time - memory.created_at).total_seconds()
                if age < 3600:
                    age_buckets['<1h'] += 1
                elif age < 86400:
                    age_buckets['1h-1d'] += 1
                elif age < 604800:
                    age_buckets['1d-1w'] += 1
                elif age < 2592000:
                    age_buckets['1w-1m'] += 1
                else:
                    age_buckets['>1m'] += 1

            stats['memory_age_distribution'] = age_buckets

            # Correlation strength distribution
            corr_buckets = {'weak': 0, 'moderate': 0, 'strong': 0}
            for correlation in self.market_correlations.values():
                strength = abs(correlation.correlation_coefficient)
                if strength < 0.3:
                    corr_buckets['weak'] += 1
                elif strength < 0.7:
                    corr_buckets['moderate'] += 1
                else:
                    corr_buckets['strong'] += 1

            stats['correlation_strength_distribution'] = corr_buckets

            # Strategy performance summary
            for strategy_name, memory in self.strategy_memories.items():
                stats['strategy_performance_summary'][strategy_name] = {
                    'avg_profit': memory.performance_metrics.get('avg_profit', 0.0),
                    'trade_count': memory.performance_metrics.get('trade_count', 0),
                    'success_rate': (memory.performance_metrics.get('success_count', 0) /
                                   max(memory.performance_metrics.get('trade_count', 1), 1))
                }

            return stats

        except Exception as e:
            self.logger.error(f"Error getting memory statistics: {e}")
            return {}
