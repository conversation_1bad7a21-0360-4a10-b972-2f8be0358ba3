import React, { useState, useEffect, useCallback } from 'react';
import { StyleSheet, View, FlatList, RefreshControl, Alert } from 'react-native';
import { Text, Card, Chip, Button, Searchbar, Menu, FAB } from 'react-native-paper';
import { showMessage } from 'react-native-flash-message';
import Icon from 'react-native-vector-icons/MaterialIcons';
import loggingService, { LogEntry, LoggingCategory } from '../services/loggingService';
import { theme } from '../styles/theme';

interface LogViewerProps {
    category?: string;
    autoRefresh?: boolean;
    refreshInterval?: number;
}

const LogViewer: React.FC<LogViewerProps> = ({
    category,
    autoRefresh = false,
    refreshInterval = 5000,
}) => {
    const [logs, setLogs] = useState<LogEntry[]>([]);
    const [categories, setCategories] = useState<LoggingCategory[]>([]);
    const [loading, setLoading] = useState(true);
    const [refreshing, setRefreshing] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedCategory, setSelectedCategory] = useState<string>(category || 'all');
    const [selectedLevel, setSelectedLevel] = useState<string>('all');
    const [categoryMenuVisible, setCategoryMenuVisible] = useState(false);
    const [levelMenuVisible, setLevelMenuVisible] = useState(false);
    const [page, setPage] = useState(0);
    const [hasMore, setHasMore] = useState(true);

    const logLevels = ['all', 'DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'];

    useEffect(() => {
        loadCategories();
        loadLogs(true);
    }, [selectedCategory, selectedLevel]);

    useEffect(() => {
        let interval: NodeJS.Timeout;
        if (autoRefresh && !loading) {
            interval = setInterval(() => {
                loadLogs(true, false);
            }, refreshInterval);
        }
        return () => {
            if (interval) clearInterval(interval);
        };
    }, [autoRefresh, refreshInterval, loading, selectedCategory, selectedLevel]);

    const loadCategories = async () => {
        try {
            const response = await loggingService.getLoggingCategories();
            setCategories(response.categories);
        } catch (error) {
            console.error('Failed to load categories:', error);
        }
    };

    const loadLogs = async (reset: boolean = false, showLoading: boolean = true) => {
        try {
            if (showLoading) setLoading(true);
            
            const currentPage = reset ? 0 : page;
            const limit = 50;
            const offset = currentPage * limit;

            let response;
            if (selectedCategory === 'all') {
                response = await loggingService.getRecentLogs(limit);
            } else {
                response = await loggingService.getLogsByCategory(selectedCategory, limit, offset);
            }

            let filteredLogs = response.logs;

            // Filter by level
            if (selectedLevel !== 'all') {
                filteredLogs = filteredLogs.filter(log => log.level === selectedLevel);
            }

            // Filter by search query
            if (searchQuery.trim()) {
                filteredLogs = filteredLogs.filter(log =>
                    log.message.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    log.level.toLowerCase().includes(searchQuery.toLowerCase())
                );
            }

            if (reset) {
                setLogs(filteredLogs);
                setPage(0);
            } else {
                setLogs(prev => [...prev, ...filteredLogs]);
            }

            setHasMore(filteredLogs.length === limit);
            
        } catch (error) {
            showMessage({
                message: 'Failed to load logs',
                description: error instanceof Error ? error.message : 'Unknown error',
                type: 'danger',
            });
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    };

    const handleRefresh = useCallback(() => {
        setRefreshing(true);
        loadLogs(true, false);
    }, [selectedCategory, selectedLevel, searchQuery]);

    const handleLoadMore = () => {
        if (!loading && hasMore) {
            setPage(prev => prev + 1);
        }
    };

    const handleSearch = (query: string) => {
        setSearchQuery(query);
        // Debounce search
        setTimeout(() => {
            loadLogs(true, false);
        }, 500);
    };

    const handleClearLogs = () => {
        if (selectedCategory === 'all') {
            Alert.alert(
                'Clear All Logs',
                'Are you sure you want to clear all logs?',
                [
                    { text: 'Cancel', style: 'cancel' },
                    {
                        text: 'Clear All',
                        style: 'destructive',
                        onPress: async () => {
                            try {
                                const promises = categories.map(cat =>
                                    loggingService.clearLogsByCategory(cat.id)
                                );
                                await Promise.all(promises);
                                loadLogs(true, false);
                                showMessage({
                                    message: 'All logs cleared',
                                    type: 'success',
                                });
                            } catch (error) {
                                showMessage({
                                    message: 'Failed to clear logs',
                                    type: 'danger',
                                });
                            }
                        },
                    },
                ]
            );
        } else {
            Alert.alert(
                'Clear Category Logs',
                `Clear all logs for ${selectedCategory}?`,
                [
                    { text: 'Cancel', style: 'cancel' },
                    {
                        text: 'Clear',
                        style: 'destructive',
                        onPress: async () => {
                            try {
                                await loggingService.clearLogsByCategory(selectedCategory);
                                loadLogs(true, false);
                                showMessage({
                                    message: 'Logs cleared',
                                    type: 'success',
                                });
                            } catch (error) {
                                showMessage({
                                    message: 'Failed to clear logs',
                                    type: 'danger',
                                });
                            }
                        },
                    },
                ]
            );
        }
    };

    const getLevelColor = (level: string) => {
        switch (level) {
            case 'DEBUG': return '#9E9E9E';
            case 'INFO': return '#2196F3';
            case 'WARNING': return '#FF9800';
            case 'ERROR': return '#F44336';
            case 'CRITICAL': return '#D32F2F';
            default: return theme.colors.onSurface;
        }
    };

    const renderLogItem = ({ item }: { item: LogEntry }) => (
        <Card style={styles.logCard}>
            <Card.Content style={styles.logContent}>
                <View style={styles.logHeader}>
                    <Chip
                        mode="outlined"
                        textStyle={[styles.levelChip, { color: getLevelColor(item.level) }]}
                        style={[styles.levelChipContainer, { borderColor: getLevelColor(item.level) }]}
                    >
                        {item.level}
                    </Chip>
                    <Text variant="bodySmall" style={styles.timestamp}>
                        {item.timestamp}
                    </Text>
                </View>
                <Text variant="bodyMedium" style={styles.logMessage}>
                    {item.message}
                </Text>
                {item.category && (
                    <Text variant="bodySmall" style={styles.category}>
                        Category: {item.category}
                    </Text>
                )}
            </Card.Content>
        </Card>
    );

    const renderEmptyState = () => (
        <View style={styles.emptyContainer}>
            <Icon name="description" size={64} color={theme.colors.outline} />
            <Text variant="headlineSmall" style={styles.emptyTitle}>
                No logs found
            </Text>
            <Text variant="bodyMedium" style={styles.emptyDescription}>
                {searchQuery ? 'Try adjusting your search criteria' : 'No logs available for the selected filters'}
            </Text>
        </View>
    );

    return (
        <View style={styles.container}>
            {/* Search and Filters */}
            <View style={styles.filtersContainer}>
                <Searchbar
                    placeholder="Search logs..."
                    onChangeText={handleSearch}
                    value={searchQuery}
                    style={styles.searchbar}
                />
                
                <View style={styles.filterButtons}>
                    <Menu
                        visible={categoryMenuVisible}
                        onDismiss={() => setCategoryMenuVisible(false)}
                        anchor={
                            <Button
                                mode="outlined"
                                onPress={() => setCategoryMenuVisible(true)}
                                style={styles.filterButton}
                                icon="filter-list"
                            >
                                {selectedCategory === 'all' ? 'All Categories' : 
                                 categories.find(c => c.id === selectedCategory)?.name || selectedCategory}
                            </Button>
                        }
                    >
                        <Menu.Item
                            onPress={() => {
                                setSelectedCategory('all');
                                setCategoryMenuVisible(false);
                            }}
                            title="All Categories"
                        />
                        {categories.map(cat => (
                            <Menu.Item
                                key={cat.id}
                                onPress={() => {
                                    setSelectedCategory(cat.id);
                                    setCategoryMenuVisible(false);
                                }}
                                title={cat.name}
                            />
                        ))}
                    </Menu>

                    <Menu
                        visible={levelMenuVisible}
                        onDismiss={() => setLevelMenuVisible(false)}
                        anchor={
                            <Button
                                mode="outlined"
                                onPress={() => setLevelMenuVisible(true)}
                                style={styles.filterButton}
                                icon="tune"
                            >
                                {selectedLevel === 'all' ? 'All Levels' : selectedLevel}
                            </Button>
                        }
                    >
                        {logLevels.map(level => (
                            <Menu.Item
                                key={level}
                                onPress={() => {
                                    setSelectedLevel(level);
                                    setLevelMenuVisible(false);
                                }}
                                title={level === 'all' ? 'All Levels' : level}
                            />
                        ))}
                    </Menu>
                </View>
            </View>

            {/* Logs List */}
            <FlatList
                data={logs}
                renderItem={renderLogItem}
                keyExtractor={(item, index) => `${item.timestamp}-${index}`}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={handleRefresh}
                        colors={[theme.colors.primary]}
                    />
                }
                onEndReached={handleLoadMore}
                onEndReachedThreshold={0.1}
                ListEmptyComponent={!loading ? renderEmptyState : null}
                contentContainerStyle={logs.length === 0 ? styles.emptyList : undefined}
                showsVerticalScrollIndicator={false}
            />

            {/* Floating Action Button */}
            <FAB
                icon="delete-sweep"
                style={styles.fab}
                onPress={handleClearLogs}
                label="Clear"
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: theme.colors.background,
    },
    filtersContainer: {
        padding: 16,
        backgroundColor: theme.colors.surface,
        elevation: 2,
    },
    searchbar: {
        marginBottom: 12,
        backgroundColor: theme.colors.surfaceVariant,
    },
    filterButtons: {
        flexDirection: 'row',
        gap: 8,
    },
    filterButton: {
        flex: 1,
    },
    logCard: {
        marginHorizontal: 16,
        marginVertical: 4,
        backgroundColor: theme.colors.surface,
    },
    logContent: {
        paddingVertical: 12,
    },
    logHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
    },
    levelChipContainer: {
        height: 24,
    },
    levelChip: {
        fontSize: 12,
        fontWeight: '600',
    },
    timestamp: {
        color: theme.colors.onSurfaceVariant,
        fontSize: 12,
    },
    logMessage: {
        color: theme.colors.onSurface,
        lineHeight: 20,
        marginBottom: 4,
    },
    category: {
        color: theme.colors.onSurfaceVariant,
        fontSize: 12,
        fontStyle: 'italic',
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 32,
    },
    emptyList: {
        flexGrow: 1,
    },
    emptyTitle: {
        color: theme.colors.onSurface,
        marginTop: 16,
        marginBottom: 8,
    },
    emptyDescription: {
        color: theme.colors.onSurfaceVariant,
        textAlign: 'center',
    },
    fab: {
        position: 'absolute',
        margin: 16,
        right: 0,
        bottom: 0,
        backgroundColor: theme.colors.primary,
    },
});

export default LogViewer;
