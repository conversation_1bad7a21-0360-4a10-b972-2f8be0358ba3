import { NavigationContainer } from '@react-navigation/native';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';
import { AppState, StatusBar } from 'react-native';
import FlashMessage from 'react-native-flash-message';
import { Provider as PaperProvider } from 'react-native-paper';
import {
    SafeAreaProvider,
    initialWindowMetrics,
} from 'react-native-safe-area-context';

import LoadingScreen from './src/components/LoadingScreen';
import { AuthProvider, useAuth } from './src/context/AuthContext';
import AppNavigator from './src/navigation/AppNavigator';
import AuthNavigator from './src/navigation/AuthNavigator';
import { initializeNotifications } from './src/services/notifications';
import { theme } from './src/styles/theme';

// Create React Query client
const queryClient = new QueryClient({
    defaultOptions: {
        queries: {
            retry: 2,
            staleTime: 30000,
            cacheTime: 300000,
        },
    },
});

const AppContent: React.FC = () => {
    const { isAuthenticated, isLoading } = useAuth();
    const [appState, setAppState] = useState(AppState.currentState);

    useEffect(() => {
        // Initialize push notifications
        initializeNotifications();

        // Handle app state changes
        const handleAppStateChange = (nextAppState: string) => {
            if (appState.match(/inactive|background/) && nextAppState === 'active') {
                // App has come to the foreground
                queryClient.invalidateQueries();
            }
            setAppState(nextAppState);
        };

        const subscription = AppState.addEventListener('change', handleAppStateChange);

        return () => {
            subscription?.remove();
        };
    }, [appState]);

    if (isLoading) {
        return <LoadingScreen />;
    }

    return (
        <NavigationContainer theme={theme.navigation}>
            {isAuthenticated ? <AppNavigator /> : <AuthNavigator />}
        </NavigationContainer>
    );
};

const App: React.FC = () => {
    return (
        <SafeAreaProvider initialMetrics={initialWindowMetrics}>
            <QueryClientProvider client={queryClient}>
                <PaperProvider theme={theme.paper}>
                    <AuthProvider>
                        <StatusBar
                            barStyle="light-content"
                            backgroundColor={theme.colors.background}
                            translucent={false}
                        />
                        <AppContent />
                        <FlashMessage position="top" />
                    </AuthProvider>
                </PaperProvider>
            </QueryClientProvider>
        </SafeAreaProvider>
    );
};

export default App;
