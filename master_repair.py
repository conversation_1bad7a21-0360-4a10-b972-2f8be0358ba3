#!/usr/bin/env python3
"""
MASTER SYSTEM REPAIR - Fix ALL critical errors at once
"""
import os
import sys
import sqlite3
import subprocess
from pathlib import Path

def run_master_repair():
    """Execute all critical system repairs"""
    
    print("MASTER SYSTEM REPAIR INITIATED")
    print("=" * 50)
    
    # Change to correct directory
    try:
        os.chdir(r"e:\The_real_deal_copy\Bybit_Bot\BOT")
        print(f"Working directory: {os.getcwd()}")
    except Exception as e:
        print(f"CRITICAL ERROR: Cannot change directory: {e}")
        return False
    
    repair_success = 0
    total_repairs = 0
    
    # Repair 1: Database Tables
    print("\n" + "="*50)
    print("REPAIR 1: CREATING MISSING DATABASE TABLES")
    print("="*50)
    total_repairs += 1
    
    try:
        if fix_database_tables():
            repair_success += 1
            print("✅ DATABASE REPAIR: SUCCESS")
        else:
            print("❌ DATABASE REPAIR: FAILED")
    except Exception as e:
        print(f"❌ DATABASE REPAIR: EXCEPTION - {e}")
    
    # Repair 2: Enhanced Client Methods
    print("\n" + "="*50)
    print("REPAIR 2: VERIFYING ENHANCED CLIENT METHODS")
    print("="*50)
    total_repairs += 1
    
    try:
        if verify_enhanced_client():
            repair_success += 1
            print("✅ ENHANCED CLIENT: SUCCESS")
        else:
            print("❌ ENHANCED CLIENT: FAILED")
    except Exception as e:
        print(f"❌ ENHANCED CLIENT: EXCEPTION - {e}")
    
    # Repair 3: AI System Components
    print("\n" + "="*50)
    print("REPAIR 3: FIXING AI SYSTEM COMPONENTS")
    print("="*50)
    total_repairs += 1
    
    try:
        if fix_ai_components():
            repair_success += 1
            print("✅ AI COMPONENTS: SUCCESS")
        else:
            print("❌ AI COMPONENTS: FAILED")
    except Exception as e:
        print(f"❌ AI COMPONENTS: EXCEPTION - {e}")
    
    # Repair 4: API Configuration
    print("\n" + "="*50)
    print("REPAIR 4: CHECKING API CONFIGURATION")
    print("="*50)
    total_repairs += 1
    
    try:
        if check_api_config():
            repair_success += 1
            print("✅ API CONFIG: SUCCESS")
        else:
            print("❌ API CONFIG: FAILED")
    except Exception as e:
        print(f"❌ API CONFIG: EXCEPTION - {e}")
    
    # Final Results
    print("\n" + "="*50)
    print("MASTER REPAIR RESULTS")
    print("="*50)
    print(f"SUCCESSFUL REPAIRS: {repair_success}/{total_repairs}")
    
    if repair_success == total_repairs:
        print("🎉 ALL REPAIRS SUCCESSFUL! SYSTEM READY!")
        return True
    else:
        failed = total_repairs - repair_success
        print(f"⚠️  {failed} REPAIRS FAILED - MANUAL INTERVENTION REQUIRED")
        return False

def fix_database_tables():
    """Create all missing database tables"""
    try:
        db_path = "bybit_trading_bot.db"
        
        print(f"Connecting to database: {db_path}")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create system_state table
        print("Creating system_state table...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS system_state (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                component VARCHAR(50) UNIQUE NOT NULL,
                last_run_timestamp DATETIME NOT NULL,
                state_data TEXT DEFAULT '{}',
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create trading_memories table
        print("Creating trading_memories table...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trading_memories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol VARCHAR(20) NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                pattern_data TEXT NOT NULL,
                pattern_hash VARCHAR(64) NOT NULL,
                outcome TEXT NOT NULL,
                success BOOLEAN NOT NULL,
                profit_loss REAL DEFAULT 0,
                strategy VARCHAR(50),
                confidence REAL DEFAULT 0,
                market_conditions TEXT,
                technical_indicators TEXT,
                metadata TEXT DEFAULT '{}',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create strategy_memories table
        print("Creating strategy_memories table...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS strategy_memories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                strategy_name VARCHAR(50) NOT NULL,
                memory_data TEXT NOT NULL,
                performance_score REAL DEFAULT 0,
                total_trades INTEGER DEFAULT 0,
                winning_trades INTEGER DEFAULT 0,
                total_pnl REAL DEFAULT 0,
                max_drawdown REAL DEFAULT 0,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create indexes
        print("Creating database indexes...")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trading_memories_symbol ON trading_memories(symbol)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_strategy_memories_name ON strategy_memories(strategy_name)")
        
        conn.commit()
        conn.close()
        
        print("Database tables created successfully!")
        return True
        
    except Exception as e:
        print(f"Database creation error: {e}")
        return False

def verify_enhanced_client():
    """Verify enhanced client has all required methods"""
    try:
        client_path = Path("bybit_bot/exchange/enhanced_bybit_client.py")
        
        if not client_path.exists():
            print(f"Enhanced client file not found: {client_path}")
            return False
        
        with open(client_path, 'r') as f:
            content = f.read()
        
        # Check for required methods
        required_methods = ["get_account_balance", "get_market_data"]
        found_methods = []
        missing_methods = []
        
        for method in required_methods:
            if f"def {method}" in content or f"async def {method}" in content:
                found_methods.append(method)
                print(f"✓ Found method: {method}")
            else:
                missing_methods.append(method)
                print(f"✗ Missing method: {method}")
        
        if missing_methods:
            print(f"Missing methods: {missing_methods}")
            return False
        
        print("All required methods found in enhanced client!")
        return True
        
    except Exception as e:
        print(f"Enhanced client verification error: {e}")
        return False

def fix_ai_components():
    """Fix AI component import issues"""
    try:
        ai_dir = Path("bybit_bot/ai")
        
        if not ai_dir.exists():
            print(f"AI directory not found: {ai_dir}")
            return False
        
        # Check intelligent_ml_system
        intelligent_ml_path = ai_dir / "intelligent_ml_system.py"
        if intelligent_ml_path.exists():
            with open(intelligent_ml_path, 'r') as f:
                content = f.read()
            
            if "class IntelligentMLSystem" in content:
                print("✓ IntelligentMLSystem class found")
            else:
                print("✗ IntelligentMLSystem class missing")
                return False
        else:
            print("✗ intelligent_ml_system.py not found")
            return False
        
        # Check openrouter_client
        openrouter_path = ai_dir / "openrouter_client.py"
        if openrouter_path.exists():
            with open(openrouter_path, 'r') as f:
                content = f.read()
            
            if "class OpenRouterClient" in content:
                print("✓ OpenRouterClient class found")
            else:
                print("✗ OpenRouterClient class missing")
                return False
        else:
            print("✗ openrouter_client.py not found")
            return False
        
        print("AI components verified successfully!")
        return True
        
    except Exception as e:
        print(f"AI component verification error: {e}")
        return False

def check_api_config():
    """Check API configuration"""
    try:
        config_path = Path("config.yaml")
        
        if not config_path.exists():
            print("Config file not found: config.yaml")
            return False
        
        with open(config_path, 'r') as f:
            content = f.read()
        
        # Basic check for bybit configuration
        if 'bybit:' not in content and 'bybit ' not in content:
            print("No bybit configuration found in config.yaml")
            return False
        
        # Look for api_key and api_secret
        lines = content.split('\n')
        api_key = None
        api_secret = None
        
        for line in lines:
            if 'api_key:' in line:
                api_key = line.split('api_key:')[-1].strip().strip('"\'')
            elif 'api_secret:' in line:
                api_secret = line.split('api_secret:')[-1].strip().strip('"\'')
        
        if not api_key or not api_secret:
            print("Missing API credentials in config")
            return False
        
        if len(api_key) < 10 or len(api_secret) < 20:
            print("API credentials appear invalid (too short)")
            return False
        
        print(f"✓ API configuration found - Key: {api_key[:8]}..., Secret: {api_secret[:8]}...")
        return True
        
    except Exception as e:
        print(f"API config check error: {e}")
        return False

if __name__ == "__main__":
    print("BYBIT BOT MASTER SYSTEM REPAIR")
    print("Fixing all critical system errors...")
    
    success = run_master_repair()
    
    if success:
        print("\n🎉 MASTER REPAIR COMPLETED SUCCESSFULLY!")
        print("System is now ready for operation!")
        sys.exit(0)
    else:
        print("\n⚠️  MASTER REPAIR PARTIALLY FAILED!")
        print("Some manual intervention may be required.")
        sys.exit(1)
