#!/usr/bin/env python3
"""
Install the six package
"""
import subprocess
import sys

def install_six():
    try:
        print("Installing six package...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", "six"], 
                              capture_output=True, text=True, check=True)
        print("SUCCESS: six package installed")
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"ERROR installing six: {e}")
        print(f"stdout: {e.stdout}")
        print(f"stderr: {e.stderr}")
        return False

if __name__ == "__main__":
    install_six()
