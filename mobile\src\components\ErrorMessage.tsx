import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Button, Card, Text } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { theme } from '../styles/theme';

interface ErrorMessageProps {
    error: Error | unknown;
    onRetry?: () => void;
    message?: string;
}

const ErrorMessage: React.FC<ErrorMessageProps> = ({
    error,
    onRetry,
    message
}) => {
    const errorMessage = message ||
        (error instanceof Error ? error.message : 'An unexpected error occurred');

    return (
        <View style={styles.container}>
            <Card style={styles.card}>
                <Card.Content style={styles.content}>
                    <Icon
                        name="error-outline"
                        size={48}
                        color={theme.colors.error}
                        style={styles.icon}
                    />
                    <Text variant="headlineSmall" style={styles.title}>
                        Something went wrong
                    </Text>
                    <Text variant="bodyMedium" style={styles.message}>
                        {errorMessage}
                    </Text>
                    {onRetry && (
                        <Button
                            mode="contained"
                            onPress={onRetry}
                            style={styles.retryButton}
                        >
                            Try Again
                        </Button>
                    )}
                </Card.Content>
            </Card>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: theme.colors.background,
        padding: theme.spacing.lg,
    },
    card: {
        width: '100%',
        maxWidth: 400,
        backgroundColor: theme.colors.surface,
    },
    content: {
        alignItems: 'center',
        padding: theme.spacing.xl,
    },
    icon: {
        marginBottom: theme.spacing.lg,
    },
    title: {
        color: theme.colors.text,
        textAlign: 'center',
        marginBottom: theme.spacing.md,
        fontWeight: theme.typography.weights.semibold,
    },
    message: {
        color: theme.colors.textSecondary,
        textAlign: 'center',
        marginBottom: theme.spacing.lg,
    },
    retryButton: {
        marginTop: theme.spacing.md,
    },
});

export default ErrorMessage;
