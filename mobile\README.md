# Bybit Trading Bot Mobile App

A professional React Native mobile application for monitoring and controlling your autonomous trading system.

## Features

- **Real-time Dashboard**: Live profit tracking, system health monitoring
- **Trading Control**: Manage strategies, view positions, emergency stop
- **Portfolio Overview**: Asset allocation, performance charts
- **AI Monitoring**: ML model status, predictions, confidence metrics
- **Settings Management**: Risk controls, API configuration
- **Push Notifications**: Trade alerts, profit/loss notifications
- **Secure Authentication**: Biometric login support

## Setup Instructions

### Prerequisites

- Node.js 16+ installed
- React Native CLI: `npm install -g @react-native-community/cli`
- Android Studio with Android SDK
- Java JDK 11+

### Installation

1. Navigate to mobile directory:

```bash
cd mobile
```

2. Install dependencies:

```bash
npm install
```

3. Install iOS dependencies (if developing for iOS):

```bash
cd ios && pod install && cd ..
```

4. Start Metro bundler:

```bash
npm start
```

5. Run on Android:

```bash
npm run android
```

### Configuration

1. Update API endpoint in `src/config/api.js`
2. Configure push notifications in `src/services/notifications.js`
3. Set up authentication in `src/services/auth.js`

## Project Structure

```
mobile/
├── src/
│   ├── components/     # Reusable UI components
│   ├── screens/        # Screen components
│   ├── navigation/     # Navigation configuration
│   ├── services/       # API and utility services
│   ├── hooks/          # Custom React hooks
│   ├── styles/         # Theme and styling
│   └── utils/          # Helper functions
├── android/            # Android-specific files
├── ios/                # iOS-specific files
└── package.json
```

## Build for Production

### Android APK

```bash
cd android
./gradlew assembleRelease
```

### Android Bundle (for Play Store)

```bash
cd android
./gradlew bundleRelease
```

## Security Notes

- All API communications use HTTPS
- Sensitive data encrypted with AsyncStorage encryption
- Biometric authentication for app access
- API keys securely stored in Keychain/Keystore
