import { useState, useEffect } from 'react'
import { useTheme } from '@mui/material/styles'
import useMediaQuery from '@mui/material/useMediaQuery'

/**
 * Custom hook for responsive design utilities
 * Provides breakpoint detection and responsive values
 */
export const useResponsive = () => {
    const theme = useTheme()
    
    // Material-UI breakpoint queries
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'))
    const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'))
    const isDesktop = useMediaQuery(theme.breakpoints.up('md'))
    const isLargeDesktop = useMediaQuery(theme.breakpoints.up('lg'))
    const isExtraLarge = useMediaQuery(theme.breakpoints.up('xl'))
    
    // Custom breakpoints for trading interface
    const isCompact = useMediaQuery('(max-width: 768px)')
    const isWide = useMediaQuery('(min-width: 1200px)')
    const isUltraWide = useMediaQuery('(min-width: 1600px)')
    
    // Screen dimensions
    const [screenSize, setScreenSize] = useState({
        width: typeof window !== 'undefined' ? window.innerWidth : 0,
        height: typeof window !== 'undefined' ? window.innerHeight : 0
    })
    
    useEffect(() => {
        const handleResize = () => {
            setScreenSize({
                width: window.innerWidth,
                height: window.innerHeight
            })
        }
        
        window.addEventListener('resize', handleResize)
        return () => window.removeEventListener('resize', handleResize)
    }, [])
    
    // Responsive value selector
    const getResponsiveValue = (values) => {
        if (typeof values === 'object' && values !== null) {
            if (isExtraLarge && values.xl !== undefined) return values.xl
            if (isLargeDesktop && values.lg !== undefined) return values.lg
            if (isDesktop && values.md !== undefined) return values.md
            if (isTablet && values.sm !== undefined) return values.sm
            if (isMobile && values.xs !== undefined) return values.xs
            return values.default || values
        }
        return values
    }
    
    // Grid columns calculator
    const getGridColumns = () => {
        if (isMobile) return 1
        if (isTablet) return 2
        if (isDesktop) return 3
        if (isLargeDesktop) return 4
        return 6
    }
    
    // Chart dimensions
    const getChartDimensions = () => {
        const baseHeight = isMobile ? 200 : isTablet ? 300 : 400
        return {
            width: '100%',
            height: baseHeight,
            margin: isMobile ? { top: 10, right: 10, bottom: 20, left: 20 } 
                             : { top: 20, right: 30, bottom: 40, left: 40 }
        }
    }
    
    // Sidebar configuration
    const getSidebarConfig = () => ({
        width: isMobile ? '100%' : isTablet ? 240 : 280,
        collapsedWidth: isMobile ? 0 : 60,
        shouldCollapse: isMobile,
        overlay: isMobile
    })
    
    // Table configuration
    const getTableConfig = () => ({
        pageSize: isMobile ? 5 : isTablet ? 10 : 25,
        density: isMobile ? 'compact' : 'standard',
        showToolbar: !isMobile,
        virtualizeRows: isDesktop
    })
    
    return {
        // Breakpoint flags
        isMobile,
        isTablet,
        isDesktop,
        isLargeDesktop,
        isExtraLarge,
        isCompact,
        isWide,
        isUltraWide,
        
        // Screen dimensions
        screenSize,
        
        // Utility functions
        getResponsiveValue,
        getGridColumns,
        getChartDimensions,
        getSidebarConfig,
        getTableConfig,
        
        // Device type detection
        deviceType: isMobile ? 'mobile' : isTablet ? 'tablet' : 'desktop',
        
        // Orientation (for mobile)
        isLandscape: screenSize.width > screenSize.height,
        isPortrait: screenSize.width <= screenSize.height
    }
}

export default useResponsive
