"""
Enhanced Performance Analysis Module for Bybit Trading Bot
Advanced performance tracking, analytics, and adaptive learning system
"""

import asyncio
import logging
import json
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, List, Optional, Tuple, Union
from dataclasses import dataclass, asdict
from enum import Enum
import statistics
import numpy as np
# Import pandas with fallback to avoid dependency issues
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    pd = None
from collections import defaultdict, deque

from bybit_bot.core.config import BotConfig
from bybit_bot.core.logger import TradingBotLogger
from bybit_bot.database.connection import DatabaseManager

logger = logging.getLogger("bybit_trading_bot.performance_analyzer")


class MarketCondition(Enum):
    """Market condition enumeration"""
    BULLISH = "bullish"
    BEARISH = "bearish"
    SIDEWAYS = "sideways"
    VOLATILE = "volatile"
    UNKNOWN = "unknown"


class TradeQuality(Enum):
    """Trade quality classification"""
    EXCELLENT = "excellent"
    GOOD = "good"
    AVERAGE = "average"
    POOR = "poor"
    TERRIBLE = "terrible"


@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics data structure"""
    total_pnl: float = 0.0
    win_rate: float = 0.0
    profit_factor: float = 0.0
    sharpe_ratio: float = 0.0
    sortino_ratio: float = 0.0
    max_drawdown: float = 0.0
    max_drawdown_duration: int = 0
    average_trade_duration: float = 0.0
    trades_today: int = 0
    trades_total: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    average_win: float = 0.0
    average_loss: float = 0.0
    largest_win: float = 0.0
    largest_loss: float = 0.0
    consecutive_wins: int = 0
    consecutive_losses: int = 0
    max_consecutive_wins: int = 0
    max_consecutive_losses: int = 0
    volatility: float = 0.0
    calmar_ratio: float = 0.0
    recovery_factor: float = 0.0
    best_strategy: Optional[str] = None
    worst_strategy: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)


@dataclass
class StrategyPerformance:
    """Strategy-specific performance metrics"""
    name: str
    trades: int = 0
    wins: int = 0
    losses: int = 0
    total_pnl: float = 0.0
    win_rate: float = 0.0
    profit_factor: float = 0.0
    avg_duration: float = 0.0
    max_drawdown: float = 0.0
    sharpe_ratio: float = 0.0
    last_used: Optional[datetime] = None
    confidence_score: float = 0.0

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        data = asdict(self)
        if self.last_used:
            data['last_used'] = self.last_used.isoformat()
        return data


class PerformanceAnalyzer:
    """
    Enhanced performance analysis and adaptive learning system
    Provides comprehensive trading performance analytics and insights
    """

    def __init__(self, config: BotConfig, database_manager: DatabaseManager):
        self.config = config
        self.db = database_manager
        self.logger = TradingBotLogger(config)

        # Core performance metrics
        self.metrics = PerformanceMetrics()

        # Strategy and symbol performance tracking
        self.strategy_performance: Dict[str, StrategyPerformance] = {}
        self.symbol_performance: Dict[str, Dict[str, Any]] = defaultdict(dict)

        # Performance history with efficient storage
        self.daily_performance: deque = deque(maxlen=365)  # Keep 1 year of daily data
        self.hourly_performance: deque = deque(maxlen=24*7)  # Keep 1 week of hourly data
        self.trade_history: deque = deque(maxlen=1000)  # Keep last 1000 trades

        # Real-time tracking
        self.current_streak = 0  # Current win/loss streak
        self.daily_trades_count = 0
        self.session_start_time = datetime.now(timezone.utc)

        # Adaptive learning parameters
        self.learning_window = 100
        self.adaptation_threshold = 0.05
        self.min_trades_for_analysis = 10

        # Market condition tracking
        self.current_market_condition = MarketCondition.UNKNOWN
        self.market_volatility = 0.0

        # Performance benchmarks
        self.benchmarks = {
            "excellent_win_rate": 0.65,
            "good_win_rate": 0.55,
            "excellent_profit_factor": 2.0,
            "good_profit_factor": 1.5,
            "max_acceptable_drawdown": 0.15
        }

    def _safe_float_extract(self, value: Any, default: float = 0.0) -> float:
        """Safely extract float value from various data types including SQLAlchemy objects"""
        if value is None:
            return default

        # Handle SQLAlchemy objects
        if hasattr(value, '__float__'):
            try:
                return float(value)
            except (ValueError, TypeError):
                return default

        # Handle regular Python types
        if isinstance(value, (int, float)):
            return float(value)

        # Handle string representations
        if isinstance(value, str):
            try:
                return float(value)
            except (ValueError, TypeError):
                return default

        return default

    def _safe_int_extract(self, value: Any, default: int = 0) -> int:
        """Safely extract integer value from various data types"""
        if value is None:
            return default

        if hasattr(value, '__int__'):
            try:
                return int(value)
            except (ValueError, TypeError):
                return default

        if isinstance(value, (int, float)):
            return int(value)

        if isinstance(value, str):
            try:
                return int(float(value))
            except (ValueError, TypeError):
                return default

        return default

    def _safe_datetime_extract(self, value: Any, default: Optional[datetime] = None) -> Optional[datetime]:
        """Safely extract datetime value"""
        if value is None:
            return default

        if isinstance(value, datetime):
            return value

        if isinstance(value, str):
            try:
                return datetime.fromisoformat(value.replace('Z', '+00:00'))
            except (ValueError, TypeError):
                return default

        return default

    def _extract_trade_data(self, trade: Any) -> Dict[str, Any]:
        """Extract trade data safely from various sources"""
        if isinstance(trade, dict):
            return {
                'profit_loss': self._safe_float_extract(trade.get('profit_loss')),
                'entry_time': self._safe_datetime_extract(trade.get('entry_time')),
                'exit_time': self._safe_datetime_extract(trade.get('exit_time')),
                'strategy': trade.get('strategy_used', trade.get('strategy', 'unknown')),
                'symbol': trade.get('symbol', 'unknown'),
                'entry_price': self._safe_float_extract(trade.get('entry_price')),
                'exit_price': self._safe_float_extract(trade.get('exit_price')),
                'quantity': self._safe_float_extract(trade.get('quantity')),
                'fees': self._safe_float_extract(trade.get('fees'))
            }
        else:
            # Handle SQLAlchemy model objects
            return {
                'profit_loss': self._safe_float_extract(getattr(trade, 'profit_loss', None)),
                'entry_time': self._safe_datetime_extract(getattr(trade, 'entry_time', None)) or
                             self._safe_datetime_extract(getattr(trade, 'executed_at', None)),
                'exit_time': self._safe_datetime_extract(getattr(trade, 'exit_time', None)),
                'strategy': getattr(trade, 'strategy_used', None) or getattr(trade, 'strategy', 'unknown'),
                'symbol': getattr(trade, 'symbol', 'unknown'),
                'entry_price': self._safe_float_extract(getattr(trade, 'entry_price', None)),
                'exit_price': self._safe_float_extract(getattr(trade, 'exit_price', None)),
                'quantity': self._safe_float_extract(getattr(trade, 'quantity', None)),
                'fees': self._safe_float_extract(getattr(trade, 'fees', None))
            }

    async def initialize(self) -> None:
        """Initialize the performance analyzer with enhanced capabilities"""
        try:
            self.logger.info("Initializing Enhanced Performance Analyzer...")

            # Load historical data
            await self._load_historical_data()

            # Calculate comprehensive metrics
            await self._calculate_comprehensive_metrics()

            # Initialize strategy tracking
            await self._initialize_strategy_tracking()

            # Analyze market conditions
            await self._analyze_market_conditions()

            # Set up real-time monitoring
            self._setup_realtime_monitoring()

            self.logger.info(f"Performance Analyzer initialized successfully")
            self.logger.info(f"Loaded {len(self.trade_history)} trades, "
                           f"tracking {len(self.strategy_performance)} strategies")

        except Exception as e:
            self.logger.error(f"Failed to initialize Performance Analyzer: {e}")
            raise

    async def shutdown(self) -> None:
        """Graceful shutdown with data persistence"""
        try:
            # Save final metrics
            await self._save_comprehensive_metrics()

            # Generate final performance report
            final_report = await self.generate_performance_report()
            self.logger.info(f"Final Performance Report: {json.dumps(final_report, indent=2)}")

            self.logger.info("Performance Analyzer shutdown complete")

        except Exception as e:
            self.logger.error(f"Error during Performance Analyzer shutdown: {e}")

    async def start(self):
        """Start the performance analyzer"""
        try:
            self.logger.info("Starting Performance Analyzer...")

            # Initialize if not already done
            if not hasattr(self, 'initialized') or not self.initialized:
                await self.initialize()
                self.initialized = True

            self.logger.info("SUCCESS: Performance Analyzer started")

        except Exception as e:
            self.logger.error(f"ERROR: Failed to start Performance Analyzer: {e}")
            raise

    async def analyze_performance(self):
        """Analyze current performance - main monitoring method"""
        try:
            # Update comprehensive metrics
            await self._calculate_comprehensive_metrics()

            # Analyze market conditions
            await self._analyze_market_conditions()

            # Update strategy performance
            await self._initialize_strategy_tracking()

            # Log current performance
            self.logger.info(f"Performance Analysis - Total P&L: ${self.metrics.total_pnl:.2f}, "
                           f"Win Rate: {self.metrics.win_rate:.2%}, "
                           f"Trades Today: {self.metrics.trades_today}")

            return {
                'total_pnl': self.metrics.total_pnl,
                'win_rate': self.metrics.win_rate,
                'profit_factor': self.metrics.profit_factor,
                'trades_today': self.metrics.trades_today,
                'market_condition': self.current_market_condition.value
            }

        except Exception as e:
            self.logger.error(f"ERROR: Performance analysis failed: {e}")
            return {'error': str(e)}

    def _setup_realtime_monitoring(self) -> None:
        """Setup real-time performance monitoring"""
        self.session_start_time = datetime.now(timezone.utc)
        self.daily_trades_count = 0
        self.current_streak = 0

        # Reset daily metrics if new day
        now = datetime.now(timezone.utc)
        if not self.daily_performance or \
           (self.daily_performance and
            self.daily_performance[-1].get('date', now).date() != now.date()):
            self.daily_trades_count = 0

    async def _load_historical_data(self) -> None:
        """Load and process historical trading data"""
        try:
            # Load recent trades (last 30 days)
            start_date = datetime.now(timezone.utc) - timedelta(days=30)
            trades = await self.db.get_trades(start_date=start_date, limit=1000)

            if not trades:
                self.logger.info("No historical trades found")
                return

            # Process trades into structured format
            processed_trades = []
            for trade in trades:
                trade_data = self._extract_trade_data(trade)
                if trade_data['profit_loss'] != 0:  # Only include completed trades
                    processed_trades.append(trade_data)

            # Store in trade history
            self.trade_history.extend(processed_trades[-1000:])  # Keep last 1000

            # Process daily performance
            await self._process_daily_performance(processed_trades)

            self.logger.info(f"Loaded {len(processed_trades)} historical trades")

        except Exception as e:
            self.logger.error(f"Error loading historical data: {e}")
            # Initialize with empty data
            self.trade_history.clear()
            self.daily_performance.clear()

    async def _process_daily_performance(self, trades: List[Dict[str, Any]]) -> None:
        """Process trades into daily performance metrics"""
        daily_data = defaultdict(lambda: {
            'date': None,
            'total_pnl': 0.0,
            'trades': 0,
            'wins': 0,
            'losses': 0,
            'volume': 0.0,
            'fees': 0.0
        })

        for trade in trades:
            if not trade['entry_time']:
                continue

            date_key = trade['entry_time'].date()
            day_data = daily_data[date_key]

            if day_data['date'] is None:
                day_data['date'] = date_key

            day_data['total_pnl'] += trade['profit_loss']
            day_data['trades'] += 1
            day_data['volume'] += trade['quantity'] * trade['entry_price']
            day_data['fees'] += trade['fees']

            if trade['profit_loss'] > 0:
                day_data['wins'] += 1
            else:
                day_data['losses'] += 1

        # Convert to list and sort by date
        daily_list = list(daily_data.values())
        daily_list.sort(key=lambda x: x['date'])

        # Store in deque (automatically maintains max length)
        self.daily_performance.clear()
        self.daily_performance.extend(daily_list)

    async def _calculate_comprehensive_metrics(self) -> None:
        """Calculate comprehensive performance metrics"""
        try:
            if not self.trade_history:
                self.logger.info("No trades available for metrics calculation")
                return

            trades = list(self.trade_history)

            # Basic metrics
            self.metrics.trades_total = len(trades)
            self.metrics.winning_trades = sum(1 for t in trades if t['profit_loss'] > 0)
            self.metrics.losing_trades = sum(1 for t in trades if t['profit_loss'] < 0)

            # P&L metrics
            self.metrics.total_pnl = sum(t['profit_loss'] for t in trades)

            if self.metrics.trades_total > 0:
                self.metrics.win_rate = self.metrics.winning_trades / self.metrics.trades_total

            # Win/Loss analysis
            winning_trades = [t['profit_loss'] for t in trades if t['profit_loss'] > 0]
            losing_trades = [t['profit_loss'] for t in trades if t['profit_loss'] < 0]

            if winning_trades:
                self.metrics.average_win = statistics.mean(winning_trades)
                self.metrics.largest_win = max(winning_trades)

            if losing_trades:
                self.metrics.average_loss = statistics.mean(losing_trades)
                self.metrics.largest_loss = min(losing_trades)  # Most negative

            # Profit factor
            total_wins = sum(winning_trades) if winning_trades else 0
            total_losses = abs(sum(losing_trades)) if losing_trades else 0

            if total_losses > 0:
                self.metrics.profit_factor = total_wins / total_losses
            else:
                self.metrics.profit_factor = float('inf') if total_wins > 0 else 0.0

            # Duration analysis
            durations = []
            for trade in trades:
                if trade['entry_time'] and trade['exit_time']:
                    duration = (trade['exit_time'] - trade['entry_time']).total_seconds() / 3600
                    if duration > 0:
                        durations.append(duration)

            if durations:
                self.metrics.average_trade_duration = statistics.mean(durations)

            # Advanced metrics
            await self._calculate_risk_metrics()
            await self._calculate_streak_metrics()

            # Today's trades
            today = datetime.now(timezone.utc).date()
            self.metrics.trades_today = sum(1 for t in trades
                                          if t['entry_time'] and t['entry_time'].date() == today)

            self.logger.info(f"Calculated comprehensive metrics: "
                           f"Total P&L: ${self.metrics.total_pnl:.2f}, "
                           f"Win Rate: {self.metrics.win_rate:.2%}, "
                           f"Profit Factor: {self.metrics.profit_factor:.2f}")

        except Exception as e:
            self.logger.error(f"Error calculating comprehensive metrics: {e}")
            self._reset_metrics()

    async def _calculate_risk_metrics(self) -> None:
        """Calculate advanced risk metrics"""
        try:
            if not self.daily_performance:
                return

            # Extract daily returns
            daily_returns = [day['total_pnl'] for day in self.daily_performance if day['total_pnl'] != 0]

            if len(daily_returns) < 2:
                return

            returns_array = np.array(daily_returns)

            # Sharpe Ratio (annualized)
            if len(returns_array) > 1:
                mean_return = np.mean(returns_array)
                std_return = np.std(returns_array)
                if std_return > 0:
                    self.metrics.sharpe_ratio = (mean_return / std_return) * np.sqrt(252)

            # Sortino Ratio (downside deviation)
            negative_returns = returns_array[returns_array < 0]
            if len(negative_returns) > 0:
                downside_deviation = np.std(negative_returns)
                if downside_deviation > 0:
                    self.metrics.sortino_ratio = (np.mean(returns_array) / downside_deviation) * np.sqrt(252)

            # Maximum Drawdown
            cumulative_returns = np.cumsum(returns_array)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = (running_max - cumulative_returns) / np.maximum(running_max, 1)

            if len(drawdowns) > 0:
                self.metrics.max_drawdown = np.max(drawdowns)

                # Drawdown duration
                in_drawdown = drawdowns > 0.01  # 1% threshold
                if np.any(in_drawdown):
                    drawdown_periods = []
                    current_period = 0
                    for is_dd in in_drawdown:
                        if is_dd:
                            current_period += 1
                        else:
                            if current_period > 0:
                                drawdown_periods.append(current_period)
                            current_period = 0
                    if current_period > 0:
                        drawdown_periods.append(current_period)

                    if drawdown_periods:
                        self.metrics.max_drawdown_duration = max(drawdown_periods)

            # Volatility
            self.metrics.volatility = np.std(returns_array) * np.sqrt(252) if len(returns_array) > 1 else 0.0

            # Calmar Ratio
            if self.metrics.max_drawdown > 0:
                annual_return = np.mean(returns_array) * 252
                self.metrics.calmar_ratio = annual_return / self.metrics.max_drawdown

            # Recovery Factor
            if self.metrics.max_drawdown > 0:
                self.metrics.recovery_factor = self.metrics.total_pnl / self.metrics.max_drawdown

        except Exception as e:
            self.logger.error(f"Error calculating risk metrics: {e}")

    async def _calculate_streak_metrics(self) -> None:
        """Calculate winning/losing streak metrics"""
        try:
            if not self.trade_history:
                return

            trades = list(self.trade_history)
            current_wins = 0
            current_losses = 0
            max_wins = 0
            max_losses = 0

            for trade in trades:
                pnl = trade['profit_loss']

                if pnl > 0:
                    current_wins += 1
                    current_losses = 0
                    max_wins = max(max_wins, current_wins)
                elif pnl < 0:
                    current_losses += 1
                    current_wins = 0
                    max_losses = max(max_losses, current_losses)

            self.metrics.max_consecutive_wins = max_wins
            self.metrics.max_consecutive_losses = max_losses

            # Current streak
            if trades:
                last_trade = trades[-1]
                if last_trade['profit_loss'] > 0:
                    self.current_streak = current_wins
                else:
                    self.current_streak = -current_losses

        except Exception as e:
            self.logger.error(f"Error calculating streak metrics: {e}")

    def _reset_metrics(self) -> None:
        """Reset metrics to default values"""
        self.metrics = PerformanceMetrics()

    async def _initialize_strategy_tracking(self) -> None:
        """Initialize comprehensive strategy performance tracking"""
        try:
            if not self.trade_history:
                self.logger.info("No trades available for strategy tracking")
                return

            strategy_data = defaultdict(lambda: {
                'trades': [],
                'total_pnl': 0.0,
                'wins': 0,
                'losses': 0
            })

            # Group trades by strategy
            for trade in self.trade_history:
                strategy = trade['strategy']
                strategy_data[strategy]['trades'].append(trade)
                strategy_data[strategy]['total_pnl'] += trade['profit_loss']

                if trade['profit_loss'] > 0:
                    strategy_data[strategy]['wins'] += 1
                else:
                    strategy_data[strategy]['losses'] += 1

            # Calculate strategy metrics
            for strategy_name, data in strategy_data.items():
                trades = data['trades']
                total_trades = len(trades)

                if total_trades < self.min_trades_for_analysis:
                    continue

                strategy_perf = StrategyPerformance(name=strategy_name)
                strategy_perf.trades = total_trades
                strategy_perf.wins = data['wins']
                strategy_perf.losses = data['losses']
                strategy_perf.total_pnl = data['total_pnl']
                strategy_perf.win_rate = data['wins'] / total_trades if total_trades > 0 else 0.0

                # Calculate profit factor
                winning_trades = [t['profit_loss'] for t in trades if t['profit_loss'] > 0]
                losing_trades = [t['profit_loss'] for t in trades if t['profit_loss'] < 0]

                total_wins = sum(winning_trades) if winning_trades else 0
                total_losses = abs(sum(losing_trades)) if losing_trades else 0

                if total_losses > 0:
                    strategy_perf.profit_factor = total_wins / total_losses
                else:
                    strategy_perf.profit_factor = float('inf') if total_wins > 0 else 0.0

                # Average duration
                durations = []
                for trade in trades:
                    if trade['entry_time'] and trade['exit_time']:
                        duration = (trade['exit_time'] - trade['entry_time']).total_seconds() / 3600
                        if duration > 0:
                            durations.append(duration)

                if durations:
                    strategy_perf.avg_duration = statistics.mean(durations)

                # Last used
                last_trade = max(trades, key=lambda x: x['entry_time'] or datetime.min.replace(tzinfo=timezone.utc))
                strategy_perf.last_used = last_trade['entry_time']

                # Confidence score based on multiple factors
                strategy_perf.confidence_score = self._calculate_strategy_confidence(strategy_perf)

                self.strategy_performance[strategy_name] = strategy_perf

            # Identify best and worst strategies
            if self.strategy_performance:
                strategies_by_pnl = sorted(self.strategy_performance.items(),
                                         key=lambda x: x[1].total_pnl, reverse=True)

                if strategies_by_pnl:
                    self.metrics.best_strategy = strategies_by_pnl[0][0]
                    self.metrics.worst_strategy = strategies_by_pnl[-1][0]

            self.logger.info(f"Initialized tracking for {len(self.strategy_performance)} strategies")

        except Exception as e:
            self.logger.error(f"Error initializing strategy tracking: {e}")
            self.strategy_performance.clear()

    def _calculate_strategy_confidence(self, strategy: StrategyPerformance) -> float:
        """Calculate confidence score for a strategy (0-1)"""
        try:
            factors = []

            # Win rate factor
            if strategy.win_rate >= self.benchmarks["excellent_win_rate"]:
                factors.append(1.0)
            elif strategy.win_rate >= self.benchmarks["good_win_rate"]:
                factors.append(0.8)
            else:
                factors.append(max(0.0, strategy.win_rate / self.benchmarks["good_win_rate"]))

            # Profit factor
            if strategy.profit_factor >= self.benchmarks["excellent_profit_factor"]:
                factors.append(1.0)
            elif strategy.profit_factor >= self.benchmarks["good_profit_factor"]:
                factors.append(0.8)
            else:
                factors.append(max(0.0, strategy.profit_factor / self.benchmarks["good_profit_factor"]))

            # Sample size factor
            sample_factor = min(1.0, strategy.trades / 50)  # Full confidence at 50+ trades
            factors.append(sample_factor)

            # Recency factor
            if strategy.last_used:
                days_since_used = (datetime.now(timezone.utc) - strategy.last_used).days
                recency_factor = max(0.1, 1.0 - (days_since_used / 30))  # Decay over 30 days
                factors.append(recency_factor)
            else:
                factors.append(0.1)

            return statistics.mean(factors)

        except Exception as e:
            self.logger.error(f"Error calculating strategy confidence: {e}")
            return 0.0

    async def _analyze_market_conditions(self) -> None:
        """Analyze current market conditions"""
        try:
            if len(self.daily_performance) < 5:
                self.current_market_condition = MarketCondition.UNKNOWN
                return

            recent_days = list(self.daily_performance)[-10:]  # Last 10 days
            daily_pnls = [day['total_pnl'] for day in recent_days]

            if not daily_pnls:
                return

            # Calculate volatility
            self.market_volatility = np.std(daily_pnls) if len(daily_pnls) > 1 else 0.0

            # Determine trend
            positive_days = sum(1 for pnl in daily_pnls if pnl > 0)
            negative_days = sum(1 for pnl in daily_pnls if pnl < 0)

            avg_pnl = statistics.mean(daily_pnls)

            # Classify market condition
            if self.market_volatility > np.mean(daily_pnls) * 2:  # High volatility
                self.current_market_condition = MarketCondition.VOLATILE
            elif positive_days > negative_days * 1.5 and avg_pnl > 0:
                self.current_market_condition = MarketCondition.BULLISH
            elif negative_days > positive_days * 1.5 and avg_pnl < 0:
                self.current_market_condition = MarketCondition.BEARISH
            else:
                self.current_market_condition = MarketCondition.SIDEWAYS

            self.logger.info(f"Market condition: {self.current_market_condition.value}, "
                           f"Volatility: {self.market_volatility:.2f}")

        except Exception as e:
            self.logger.error(f"Error analyzing market conditions: {e}")
            self.current_market_condition = MarketCondition.UNKNOWN

    async def analyze_trade_performance(self, trade_data: Dict[str, Any]) -> Dict[str, Any]:
        """Comprehensive trade performance analysis"""
        try:
            # Extract trade information
            pnl = self._safe_float_extract(trade_data.get('profit_loss'))
            entry_price = self._safe_float_extract(trade_data.get('entry_price'))
            exit_price = self._safe_float_extract(trade_data.get('exit_price'))
            quantity = self._safe_float_extract(trade_data.get('quantity'))
            strategy = trade_data.get('strategy_used', trade_data.get('strategy', 'unknown'))

            analysis = {
                'trade_quality': self._classify_trade_quality(pnl, entry_price),
                'profit_percentage': (pnl / (entry_price * quantity)) * 100 if entry_price and quantity else 0.0,
                'risk_reward_ratio': abs(exit_price - entry_price) / entry_price if entry_price and exit_price else 0.0,
                'duration_analysis': self._analyze_trade_duration(trade_data),
                'strategy_effectiveness': self._get_strategy_effectiveness(strategy),
                'market_timing': self._analyze_market_timing(trade_data),
                'recommendations': []
            }

            # Generate specific recommendations
            recommendations = self._generate_trade_recommendations(analysis, trade_data)
            analysis['recommendations'] = recommendations

            return analysis

        except Exception as e:
            self.logger.error(f"Error analyzing trade performance: {e}")
            return {'error': str(e)}

    def _classify_trade_quality(self, pnl: float, entry_price: float) -> str:
        """Classify trade quality based on P&L and percentage return"""
        if entry_price <= 0:
            return TradeQuality.AVERAGE.value

        pnl_percentage = abs(pnl / entry_price) * 100

        if pnl > 0:  # Profitable trade
            if pnl_percentage >= 5.0:
                return TradeQuality.EXCELLENT.value
            elif pnl_percentage >= 2.0:
                return TradeQuality.GOOD.value
            else:
                return TradeQuality.AVERAGE.value
        else:  # Loss trade
            if pnl_percentage >= 5.0:
                return TradeQuality.TERRIBLE.value
            elif pnl_percentage >= 2.0:
                return TradeQuality.POOR.value
            else:
                return TradeQuality.AVERAGE.value

    def _analyze_trade_duration(self, trade_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze trade duration and timing"""
        entry_time = self._safe_datetime_extract(trade_data.get('entry_time'))
        exit_time = self._safe_datetime_extract(trade_data.get('exit_time'))

        if not entry_time or not exit_time:
            return {'duration_hours': 0, 'classification': 'unknown'}

        duration_hours = (exit_time - entry_time).total_seconds() / 3600

        if duration_hours < 0.5:
            classification = 'scalp'
        elif duration_hours < 4:
            classification = 'short_term'
        elif duration_hours < 24:
            classification = 'intraday'
        elif duration_hours < 168:  # 1 week
            classification = 'swing'
        else:
            classification = 'position'

        return {
            'duration_hours': duration_hours,
            'classification': classification,
            'entry_hour': entry_time.hour,
            'exit_hour': exit_time.hour
        }

    def _get_strategy_effectiveness(self, strategy: str) -> Dict[str, Any]:
        """Get strategy effectiveness metrics"""
        if strategy in self.strategy_performance:
            strategy_perf = self.strategy_performance[strategy]
            return {
                'win_rate': strategy_perf.win_rate,
                'profit_factor': strategy_perf.profit_factor,
                'confidence_score': strategy_perf.confidence_score,
                'total_trades': strategy_perf.trades,
                'avg_duration': strategy_perf.avg_duration
            }
        else:
            return {
                'win_rate': 0.0,
                'profit_factor': 0.0,
                'confidence_score': 0.0,
                'total_trades': 0,
                'avg_duration': 0.0
            }

    def _analyze_market_timing(self, trade_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze market timing aspects of the trade"""
        entry_time = self._safe_datetime_extract(trade_data.get('entry_time'))

        if not entry_time:
            return {'market_condition': 'unknown', 'timing_score': 0.0}

        # Analyze entry timing
        hour = entry_time.hour
        day_of_week = entry_time.weekday()

        # Market session analysis
        if 0 <= hour < 8:
            session = 'asian'
        elif 8 <= hour < 16:
            session = 'european'
        else:
            session = 'american'

        # Weekend check
        is_weekend = day_of_week >= 5

        # Calculate timing score based on historical performance
        timing_score = self._calculate_timing_score(hour, day_of_week, session)

        return {
            'market_condition': self.current_market_condition.value,
            'session': session,
            'hour': hour,
            'day_of_week': day_of_week,
            'is_weekend': is_weekend,
            'timing_score': timing_score
        }

    def _calculate_timing_score(self, hour: int, day_of_week: int, session: str) -> float:
        """Calculate timing score based on historical performance"""
        try:
            # Analyze historical performance for this time
            if not self.trade_history:
                return 0.5  # Neutral score

            same_hour_trades = [
                t for t in self.trade_history
                if t['entry_time'] and t['entry_time'].hour == hour
            ]

            if len(same_hour_trades) < 5:  # Not enough data
                return 0.5

            # Calculate win rate for this hour
            wins = sum(1 for t in same_hour_trades if t['profit_loss'] > 0)
            win_rate = wins / len(same_hour_trades)

            # Normalize to 0-1 scale
            return min(1.0, max(0.0, win_rate))

        except Exception as e:
            self.logger.error(f"Error calculating timing score: {e}")
            return 0.5

    def _generate_trade_recommendations(self, analysis: Dict[str, Any], trade_data: Dict[str, Any]) -> List[str]:
        """Generate specific recommendations based on trade analysis"""
        recommendations = []

        # Quality-based recommendations
        if analysis['trade_quality'] == TradeQuality.TERRIBLE.value:
            recommendations.append("Review entry criteria - significant loss detected")
            recommendations.append("Consider tighter stop-loss levels")

        # Duration-based recommendations
        duration_info = analysis.get('duration_analysis', {})
        if duration_info.get('classification') == 'position' and analysis.get('profit_percentage', 0) < 1:
            recommendations.append("Consider shorter holding periods for better capital efficiency")

        # Strategy-based recommendations
        strategy_info = analysis.get('strategy_effectiveness', {})
        if strategy_info.get('confidence_score', 0) < 0.5:
            recommendations.append(f"Strategy showing low confidence - consider review or adjustment")

        # Market timing recommendations
        timing_info = analysis.get('market_timing', {})
        if timing_info.get('timing_score', 0.5) < 0.3:
            recommendations.append(f"Poor historical performance at {timing_info.get('hour', 0)}:00 - consider different timing")

        # Risk-reward recommendations
        if analysis.get('risk_reward_ratio', 0) < 0.01:
            recommendations.append("Consider wider price targets for better risk-reward ratio")

        return recommendations

    async def update_performance_metrics(self, trade_data: Dict[str, Any]) -> None:
        """Update performance metrics with new trade data"""
        try:
            # Extract and validate trade data
            extracted_trade = self._extract_trade_data(trade_data)

            if extracted_trade['profit_loss'] == 0:
                return  # Skip incomplete trades

            # Add to trade history
            self.trade_history.append(extracted_trade)

            # Update daily metrics
            await self._update_daily_metrics(extracted_trade)

            # Update strategy performance
            await self._update_strategy_performance(extracted_trade)

            # Update current metrics
            self.metrics.trades_today += 1
            self.metrics.total_pnl += extracted_trade['profit_loss']

            # Update streak
            if extracted_trade['profit_loss'] > 0:
                if self.current_streak >= 0:
                    self.current_streak += 1
                else:
                    self.current_streak = 1
                self.metrics.winning_trades += 1
            else:
                if self.current_streak <= 0:
                    self.current_streak -= 1
                else:
                    self.current_streak = -1
                self.metrics.losing_trades += 1

            # Recalculate key metrics
            self.metrics.trades_total = len(self.trade_history)
            if self.metrics.trades_total > 0:
                self.metrics.win_rate = self.metrics.winning_trades / self.metrics.trades_total

            # Save to database
            await self._save_trade_performance(extracted_trade)

            self.logger.info(f"Updated performance metrics - "
                           f"P&L: ${extracted_trade['profit_loss']:.2f}, "
                           f"Strategy: {extracted_trade['strategy']}, "
                           f"Current Streak: {self.current_streak}")

        except Exception as e:
            self.logger.error(f"Error updating performance metrics: {e}")

    async def _update_daily_metrics(self, trade: Dict[str, Any]) -> None:
        """Update daily performance metrics"""
        try:
            if not trade['entry_time']:
                return

            trade_date = trade['entry_time'].date()
            today = datetime.now(timezone.utc).date()

            # Find or create today's entry
            today_entry = None
            for day in self.daily_performance:
                if day['date'] == trade_date:
                    today_entry = day
                    break

            if not today_entry:
                today_entry = {
                    'date': trade_date,
                    'total_pnl': 0.0,
                    'trades': 0,
                    'wins': 0,
                    'losses': 0,
                    'volume': 0.0,
                    'fees': 0.0
                }
                self.daily_performance.append(today_entry)

            # Update metrics
            today_entry['total_pnl'] += trade['profit_loss']
            today_entry['trades'] += 1
            today_entry['volume'] += trade['quantity'] * trade['entry_price']
            today_entry['fees'] += trade['fees']

            if trade['profit_loss'] > 0:
                today_entry['wins'] += 1
            else:
                today_entry['losses'] += 1

        except Exception as e:
            self.logger.error(f"Error updating daily metrics: {e}")

    async def _update_strategy_performance(self, trade: Dict[str, Any]) -> None:
        """Update strategy-specific performance"""
        try:
            strategy = trade['strategy']

            if strategy not in self.strategy_performance:
                self.strategy_performance[strategy] = StrategyPerformance(name=strategy)

            strategy_perf = self.strategy_performance[strategy]
            strategy_perf.trades += 1
            strategy_perf.total_pnl += trade['profit_loss']
            strategy_perf.last_used = trade['entry_time']

            if trade['profit_loss'] > 0:
                strategy_perf.wins += 1
            else:
                strategy_perf.losses += 1

            # Recalculate metrics
            strategy_perf.win_rate = strategy_perf.wins / strategy_perf.trades

            # Update confidence score
            strategy_perf.confidence_score = self._calculate_strategy_confidence(strategy_perf)

        except Exception as e:
            self.logger.error(f"Error updating strategy performance: {e}")

    async def get_strategy_recommendations(self) -> Dict[str, Any]:
        """Get comprehensive strategy recommendations"""
        try:
            recommendations = {
                'strategy_weights': {},
                'parameter_adjustments': {},
                'market_conditions': self.current_market_condition.value,
                'risk_adjustments': {},
                'focus_symbols': [],
                'timing_recommendations': {},
                'overall_assessment': self._get_overall_assessment()
            }

            if not self.strategy_performance:
                return recommendations

            # Calculate dynamic strategy weights
            total_confidence = sum(s.confidence_score for s in self.strategy_performance.values())

            for strategy_name, strategy_perf in self.strategy_performance.items():
                if total_confidence > 0:
                    weight = strategy_perf.confidence_score / total_confidence
                    recommendations['strategy_weights'][strategy_name] = min(0.6, max(0.1, weight))
                else:
                    recommendations['strategy_weights'][strategy_name] = 1.0 / len(self.strategy_performance)

            # Parameter adjustments
            for strategy_name, strategy_perf in self.strategy_performance.items():
                adjustments = {}

                if strategy_perf.win_rate < 0.4:
                    adjustments['entry_threshold'] = 'increase'
                    adjustments['position_size'] = 'decrease'

                if strategy_perf.profit_factor < 1.0:
                    adjustments['stop_loss'] = 'tighten'
                    adjustments['take_profit'] = 'extend'

                if strategy_perf.confidence_score < 0.3:
                    adjustments['status'] = 'review_required'

                if adjustments:
                    recommendations['parameter_adjustments'][strategy_name] = adjustments

            # Risk adjustments
            if self.metrics.max_drawdown > self.benchmarks['max_acceptable_drawdown']:
                recommendations['risk_adjustments']['position_size'] = 'reduce_significantly'
                recommendations['risk_adjustments']['diversification'] = 'increase'

            if self.metrics.win_rate < 0.35:
                recommendations['risk_adjustments']['entry_criteria'] = 'strengthen'

            # Timing recommendations
            recommendations['timing_recommendations'] = await self._get_timing_recommendations()

            return recommendations

        except Exception as e:
            self.logger.error(f"Error generating strategy recommendations: {e}")
            return {'error': str(e)}

    def _get_overall_assessment(self) -> Dict[str, Any]:
        """Get overall performance assessment"""
        try:
            assessment = {
                'performance_grade': 'C',
                'key_strengths': [],
                'key_weaknesses': [],
                'priority_actions': []
            }

            # Grade calculation
            score = 0
            factors = 0

            # Win rate factor
            if self.metrics.win_rate >= self.benchmarks['excellent_win_rate']:
                score += 25
                assessment['key_strengths'].append('Excellent win rate')
            elif self.metrics.win_rate >= self.benchmarks['good_win_rate']:
                score += 20
                assessment['key_strengths'].append('Good win rate')
            else:
                assessment['key_weaknesses'].append('Low win rate')
            factors += 25

            # Profit factor
            if self.metrics.profit_factor >= self.benchmarks['excellent_profit_factor']:
                score += 25
                assessment['key_strengths'].append('Excellent profit factor')
            elif self.metrics.profit_factor >= self.benchmarks['good_profit_factor']:
                score += 20
                assessment['key_strengths'].append('Good profit factor')
            else:
                assessment['key_weaknesses'].append('Poor profit factor')
            factors += 25

            # Drawdown factor
            if self.metrics.max_drawdown <= 0.05:
                score += 25
                assessment['key_strengths'].append('Low drawdown')
            elif self.metrics.max_drawdown <= self.benchmarks['max_acceptable_drawdown']:
                score += 15
            else:
                score += 5
                assessment['key_weaknesses'].append('High drawdown')
            factors += 25

            # Consistency factor (Sharpe ratio)
            if self.metrics.sharpe_ratio >= 2.0:
                score += 25
                assessment['key_strengths'].append('Excellent consistency')
            elif self.metrics.sharpe_ratio >= 1.0:
                score += 15
                assessment['key_strengths'].append('Good consistency')
            else:
                assessment['key_weaknesses'].append('Inconsistent performance')
            factors += 25

            # Calculate grade
            percentage = (score / factors) * 100 if factors > 0 else 0

            if percentage >= 90:
                assessment['performance_grade'] = 'A+'
            elif percentage >= 85:
                assessment['performance_grade'] = 'A'
            elif percentage >= 80:
                assessment['performance_grade'] = 'A-'
            elif percentage >= 75:
                assessment['performance_grade'] = 'B+'
            elif percentage >= 70:
                assessment['performance_grade'] = 'B'
            elif percentage >= 65:
                assessment['performance_grade'] = 'B-'
            elif percentage >= 60:
                assessment['performance_grade'] = 'C+'
            elif percentage >= 55:
                assessment['performance_grade'] = 'C'
            elif percentage >= 50:
                assessment['performance_grade'] = 'C-'
            elif percentage >= 40:
                assessment['performance_grade'] = 'D'
            else:
                assessment['performance_grade'] = 'F'

            # Priority actions
            if self.metrics.max_drawdown > self.benchmarks['max_acceptable_drawdown']:
                assessment['priority_actions'].append('Reduce position sizes immediately')

            if self.metrics.win_rate < 0.4:
                assessment['priority_actions'].append('Review and strengthen entry criteria')

            if self.metrics.profit_factor < 1.0:
                assessment['priority_actions'].append('Improve risk-reward ratios')

            return assessment

        except Exception as e:
            self.logger.error(f"Error generating overall assessment: {e}")
            return {'performance_grade': 'Unknown', 'error': str(e)}

    async def _get_timing_recommendations(self) -> Dict[str, Any]:
        """Get timing-based recommendations"""
        try:
            if not self.trade_history:
                return {}

            # Analyze performance by hour
            hourly_performance = defaultdict(lambda: {'trades': 0, 'pnl': 0.0, 'wins': 0})

            for trade in self.trade_history:
                if trade['entry_time']:
                    hour = trade['entry_time'].hour
                    hourly_performance[hour]['trades'] += 1
                    hourly_performance[hour]['pnl'] += trade['profit_loss']
                    if trade['profit_loss'] > 0:
                        hourly_performance[hour]['wins'] += 1

            # Find best and worst hours
            best_hours = []
            worst_hours = []

            for hour, data in hourly_performance.items():
                if data['trades'] >= 5:  # Minimum sample size
                    win_rate = data['wins'] / data['trades']
                    avg_pnl = data['pnl'] / data['trades']

                    if win_rate >= 0.6 and avg_pnl > 0:
                        best_hours.append((hour, win_rate, avg_pnl))
                    elif win_rate <= 0.4 or avg_pnl < 0:
                        worst_hours.append((hour, win_rate, avg_pnl))

            return {
                'best_trading_hours': sorted(best_hours, key=lambda x: x[2], reverse=True)[:3],
                'worst_trading_hours': sorted(worst_hours, key=lambda x: x[2])[:3],
                'recommended_sessions': self._get_session_recommendations()
            }

        except Exception as e:
            self.logger.error(f"Error generating timing recommendations: {e}")
            return {}

    def _get_session_recommendations(self) -> List[str]:
        """Get trading session recommendations"""
        recommendations = []

        if self.current_market_condition == MarketCondition.VOLATILE:
            recommendations.append("Focus on Asian and European overlap (7-9 UTC) for volatility")
        elif self.current_market_condition == MarketCondition.SIDEWAYS:
            recommendations.append("Consider range trading during quieter sessions")

        if self.metrics.win_rate < 0.5:
            recommendations.append("Avoid trading during low-volume periods")

        return recommendations

    async def generate_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        try:
            report = {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'summary': {
                    'total_trades': self.metrics.trades_total,
                    'total_pnl': self.metrics.total_pnl,
                    'win_rate': self.metrics.win_rate,
                    'profit_factor': self.metrics.profit_factor,
                    'max_drawdown': self.metrics.max_drawdown,
                    'sharpe_ratio': self.metrics.sharpe_ratio,
                    'current_streak': self.current_streak
                },
                'detailed_metrics': self.metrics.to_dict(),
                'strategy_performance': {
                    name: strategy.to_dict()
                    for name, strategy in self.strategy_performance.items()
                },
                'market_analysis': {
                    'current_condition': self.current_market_condition.value,
                    'volatility': self.market_volatility,
                    'recent_performance': list(self.daily_performance)[-7:] if self.daily_performance else []
                },
                'recommendations': await self.get_strategy_recommendations(),
                'risk_metrics': {
                    'max_drawdown': self.metrics.max_drawdown,
                    'max_drawdown_duration': self.metrics.max_drawdown_duration,
                    'volatility': self.metrics.volatility,
                    'sortino_ratio': self.metrics.sortino_ratio,
                    'calmar_ratio': self.metrics.calmar_ratio
                },
                'trading_patterns': {
                    'average_trade_duration': self.metrics.average_trade_duration,
                    'largest_win': self.metrics.largest_win,
                    'largest_loss': self.metrics.largest_loss,
                    'max_consecutive_wins': self.metrics.max_consecutive_wins,
                    'max_consecutive_losses': self.metrics.max_consecutive_losses
                }
            }

            return report

        except Exception as e:
            self.logger.error(f"Error generating performance report: {e}")
            return {'error': str(e)}

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get quick performance summary"""
        try:
            return {
                'current_metrics': self.metrics.to_dict(),
                'strategy_count': len(self.strategy_performance),
                'best_strategy': self.metrics.best_strategy,
                'worst_strategy': self.metrics.worst_strategy,
                'market_condition': self.current_market_condition.value,
                'current_streak': self.current_streak,
                'session_pnl': sum(
                    t['profit_loss'] for t in self.trade_history
                    if t['entry_time'] and t['entry_time'].date() == datetime.now(timezone.utc).date()
                ),
                'daily_performance': list(self.daily_performance)[-30:] if self.daily_performance else [],
                'performance_grade': self._get_overall_assessment()['performance_grade']
            }

        except Exception as e:
            self.logger.error(f"Error getting performance summary: {e}")
            return {'error': str(e)}

    async def _save_trade_performance(self, trade: Dict[str, Any]) -> None:
        """Save trade performance data to database"""
        try:
            performance_data = {
                'timestamp': datetime.now(timezone.utc),
                'trade_data': trade,
                'analysis': await self.analyze_trade_performance(trade),
                'current_metrics': self.metrics.to_dict(),
                'market_condition': self.current_market_condition.value
            }

            if hasattr(self.db, 'save_performance_data'):
                await self.db.save_performance_data(performance_data)

        except Exception as e:
            self.logger.error(f"Error saving trade performance: {e}")

    async def _save_comprehensive_metrics(self) -> None:
        """Save comprehensive metrics to database"""
        try:
            metrics_data = {
                'timestamp': datetime.now(timezone.utc),
                'comprehensive_metrics': self.metrics.to_dict(),
                'strategy_performance': {
                    name: strategy.to_dict()
                    for name, strategy in self.strategy_performance.items()
                },
                'daily_performance': list(self.daily_performance),
                'market_analysis': {
                    'condition': self.current_market_condition.value,
                    'volatility': self.market_volatility
                }
            }

            if hasattr(self.db, 'save_comprehensive_performance'):
                await self.db.save_comprehensive_performance(metrics_data)
            elif hasattr(self.db, 'save_performance_data'):
                await self.db.save_performance_data(metrics_data)

        except Exception as e:
            self.logger.error(f"Error saving comprehensive metrics: {e}")

    def get_real_time_metrics(self) -> Dict[str, Any]:
        """Get real-time performance metrics for live monitoring"""
        try:
            session_duration = (datetime.now(timezone.utc) - self.session_start_time).total_seconds() / 3600

            return {
                'session_duration_hours': session_duration,
                'trades_today': self.metrics.trades_today,
                'session_pnl': sum(
                    t['profit_loss'] for t in self.trade_history
                    if t['entry_time'] and t['entry_time'] >= self.session_start_time
                ),
                'current_streak': self.current_streak,
                'win_rate_today': self._calculate_daily_win_rate(),
                'market_condition': self.current_market_condition.value,
                'active_strategies': len([
                    s for s in self.strategy_performance.values()
                    if s.last_used and (datetime.now(timezone.utc) - s.last_used).days < 1
                ]),
                'risk_level': self._assess_current_risk_level()
            }

        except Exception as e:
            self.logger.error(f"Error getting real-time metrics: {e}")
            return {}

    def _calculate_daily_win_rate(self) -> float:
        """Calculate win rate for today's trades"""
        try:
            today = datetime.now(timezone.utc).date()
            today_trades = [
                t for t in self.trade_history
                if t['entry_time'] and t['entry_time'].date() == today
            ]

            if not today_trades:
                return 0.0

            wins = sum(1 for t in today_trades if t['profit_loss'] > 0)
            return wins / len(today_trades)

        except Exception as e:
            self.logger.error(f"Error calculating daily win rate: {e}")
            return 0.0

    def _assess_current_risk_level(self) -> str:
        """Assess current risk level"""
        try:
            if self.metrics.max_drawdown > 0.2:
                return 'HIGH'
            elif self.metrics.max_drawdown > 0.1:
                return 'MEDIUM'
            elif self.current_streak < -3:
                return 'MEDIUM'
            else:
                return 'LOW'

        except Exception as e:
            self.logger.error(f"Error assessing risk level: {e}")
            return 'UNKNOWN'
        
    async def initialize(self):
        """Initialize performance analyzer"""
        try:
            self.logger.info("📊 Initializing Performance Analyzer...")
            
            # Load historical performance data
            await self._load_historical_performance()
            
            # Calculate comprehensive metrics
            await self._calculate_comprehensive_metrics()
            
            # Initialize strategy performance tracking
            await self._initialize_strategy_tracking()
            
            self.logger.info("✅ Performance Analyzer initialized")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Performance Analyzer: {e}")
            raise
    
    async def shutdown(self):
        """Shutdown performance analyzer"""
        try:
            # Save final performance metrics
            await self._save_performance_metrics()
            self.logger.info("✅ Performance Analyzer shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error shutting down Performance Analyzer: {e}")
    
    async def _load_historical_performance(self):
        """Load historical performance data from database"""
        try:
            # Load last 30 days of performance
            start_date = datetime.now(timezone.utc) - timedelta(days=30)
            
            # Check if the database method exists
            if hasattr(self.db, 'get_performance_data'):
                performance_data = await self.db.get_performance_data(
                    start_date=start_date,
                    end_date=datetime.now(timezone.utc)
                )
            else:
                # Fallback: try to get trades and calculate performance
                trades = await self.db.get_trades(start_date=start_date)
                performance_data = self._convert_trades_to_performance(trades)
            
            if performance_data:
                # Process daily performance
                daily_data = {}
                for perf in performance_data:
                    # Handle different data structures
                    if hasattr(perf, 'date'):
                        date_key = perf.date.date() if hasattr(perf.date, 'date') else perf.date
                    elif isinstance(perf, dict) and 'date' in perf:
                        date_key = perf['date'].date() if hasattr(perf['date'], 'date') else perf['date']
                    else:
                        continue
                    
                    if date_key not in daily_data:
                        daily_data[date_key] = {
                            "total_pnl": 0.0,
                            "trades": 0,
                            "wins": 0,
                            "losses": 0
                        }
                    
                    # Extract PnL safely, handling SQLAlchemy objects
                    pnl = 0.0
                    if hasattr(perf, 'total_pnl'):
                        pnl_value = perf.total_pnl
                        if hasattr(pnl_value, '__float__'):
                            pnl = float(pnl_value) if pnl_value is not None else 0.0
                        elif isinstance(pnl_value, (int, float)):
                            pnl = float(pnl_value) if pnl_value is not None else 0.0
                    elif isinstance(perf, dict) and 'total_pnl' in perf:
                        pnl_value = perf['total_pnl']
                        if hasattr(pnl_value, '__float__'):
                            pnl = float(pnl_value) if pnl_value is not None else 0.0
                        elif isinstance(pnl_value, (int, float)):
                            pnl = float(pnl_value) if pnl_value is not None else 0.0
                    elif hasattr(perf, 'profit_loss'):
                        pnl_value = perf.profit_loss
                        if hasattr(pnl_value, '__float__'):
                            pnl = float(pnl_value) if pnl_value is not None else 0.0
                        elif isinstance(pnl_value, (int, float)):
                            pnl = float(pnl_value) if pnl_value is not None else 0.0
                    elif isinstance(perf, dict) and 'profit_loss' in perf:
                        pnl_value = perf['profit_loss']
                        if hasattr(pnl_value, '__float__'):
                            pnl = float(pnl_value) if pnl_value is not None else 0.0
                        elif isinstance(pnl_value, (int, float)):
                            pnl = float(pnl_value) if pnl_value is not None else 0.0

                    daily_data[date_key]["total_pnl"] += pnl
                    daily_data[date_key]["trades"] += 1
                    
                    if pnl > 0:
                        daily_data[date_key]["wins"] += 1
                    else:
                        daily_data[date_key]["losses"] += 1
                
                self.daily_performance = list(daily_data.values())
                
                self.logger.info(f"📈 Loaded {len(self.daily_performance)} days of performance data")
            
        except Exception as e:
            self.logger.error(f"Error loading historical performance: {e}")
            # Initialize with empty data rather than failing
            self.daily_performance = []
    
    def _convert_trades_to_performance(self, trades: List[Any]) -> List[Dict]:
        """Convert trades to performance data format"""
        performance_data = []
        if trades:
            for trade in trades:
                # Safe date extraction
                trade_date = datetime.now(timezone.utc)
                if hasattr(trade, 'entry_time') and trade.entry_time:
                    trade_date = trade.entry_time
                elif hasattr(trade, 'executed_at') and trade.executed_at:
                    trade_date = trade.executed_at

                # Safe PnL extraction
                pnl = 0.0
                if hasattr(trade, 'profit_loss') and trade.profit_loss is not None:
                    pnl_value = trade.profit_loss
                    if hasattr(pnl_value, '__float__'):
                        pnl = float(pnl_value)
                    elif isinstance(pnl_value, (int, float)):
                        pnl = float(pnl_value)

                perf_entry = {
                    'date': trade_date,
                    'total_pnl': pnl
                }
                performance_data.append(perf_entry)
        return performance_data
    

    

    
    async def analyze_trade_performance(self, trade_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze individual trade performance and provide insights
        """
        try:
            analysis = {
                "trade_quality": "unknown",
                "profit_potential": 0.0,
                "risk_reward_ratio": 0.0,
                "duration_analysis": "normal",
                "strategy_effectiveness": 0.0,
                "recommendations": []
            }
            
            # Analyze profit/loss
            pnl = trade_data.get("profit_loss", 0.0)
            entry_price = trade_data.get("entry_price", 0.0)
            exit_price = trade_data.get("exit_price", 0.0)
            
            if pnl > 0:
                analysis["trade_quality"] = "profitable"
                if pnl > entry_price * 0.02:  # > 2% profit
                    analysis["trade_quality"] = "highly_profitable"
            else:
                analysis["trade_quality"] = "loss"
                if abs(pnl) > entry_price * 0.02:  # > 2% loss
                    analysis["trade_quality"] = "significant_loss"
            
            # Calculate risk-reward ratio
            if entry_price > 0 and exit_price > 0:
                price_change = abs(exit_price - entry_price)
                analysis["risk_reward_ratio"] = price_change / entry_price
            
            # Analyze duration
            entry_time = trade_data.get("entry_time")
            exit_time = trade_data.get("exit_time")
            
            if entry_time and exit_time:
                duration_hours = (exit_time - entry_time).total_seconds() / 3600
                
                if duration_hours < 1:
                    analysis["duration_analysis"] = "very_short"
                elif duration_hours < 4:
                    analysis["duration_analysis"] = "short"
                elif duration_hours > 24:
                    analysis["duration_analysis"] = "long"
                elif duration_hours > 72:
                    analysis["duration_analysis"] = "very_long"
            
            # Strategy effectiveness
            strategy = trade_data.get("strategy_used", "unknown")
            if strategy in self.strategy_performance:
                strategy_stats = self.strategy_performance[strategy]
                analysis["strategy_effectiveness"] = strategy_stats.get("win_rate", 0.0)
            
            # Generate recommendations
            recommendations = []
            
            if analysis["trade_quality"] == "significant_loss":
                recommendations.append("Review entry criteria - significant loss detected")
            
            if analysis["duration_analysis"] == "very_long":
                recommendations.append("Consider tighter time-based exit rules")
            
            if analysis["strategy_effectiveness"] < 0.4:  # < 40% win rate
                recommendations.append(f"Strategy '{strategy}' underperforming - consider adjustment")
            
            if analysis["risk_reward_ratio"] < 0.01:  # < 1% price movement
                recommendations.append("Consider wider price targets for better risk-reward")
            
            analysis["recommendations"] = recommendations
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"Error analyzing trade performance: {e}")
            return {"error": str(e)}
    
    async def get_strategy_recommendations(self) -> Dict[str, Any]:
        """
        Get adaptive strategy recommendations based on performance
        """
        try:
            recommendations = {
                "strategy_weights": {},
                "parameter_adjustments": {},
                "market_conditions": "unknown",
                "risk_adjustments": {},
                "focus_symbols": []
            }
            
            if not self.strategy_performance:
                return recommendations
            
            # Calculate dynamic strategy weights based on performance
            total_pnl = sum(stats["total_pnl"] for stats in self.strategy_performance.values())
            
            for strategy, stats in self.strategy_performance.items():
                if total_pnl != 0:
                    # Base weight on profit contribution and win rate
                    profit_weight = stats["total_pnl"] / total_pnl if total_pnl > 0 else 0.0
                    win_rate_weight = stats["win_rate"]
                    
                    # Combined weight with profit factor consideration
                    combined_weight = (profit_weight * 0.5 + win_rate_weight * 0.3 + 
                                     min(stats["profit_factor"] / 2.0, 1.0) * 0.2)
                    
                    recommendations["strategy_weights"][strategy] = max(0.1, min(0.6, combined_weight))
                else:
                    recommendations["strategy_weights"][strategy] = 1.0 / len(self.strategy_performance)
            
            # Normalize weights
            total_weight = sum(recommendations["strategy_weights"].values())
            if total_weight > 0:
                for strategy in recommendations["strategy_weights"]:
                    recommendations["strategy_weights"][strategy] /= total_weight
            
            # Parameter adjustments based on performance
            for strategy, stats in self.strategy_performance.items():
                adjustments = {}
                
                if stats["win_rate"] < 0.4:  # Low win rate
                    adjustments["entry_threshold"] = "increase"  # More selective entries
                    adjustments["position_size"] = "decrease"  # Smaller positions
                
                if stats["profit_factor"] < 1.0:  # Unprofitable
                    adjustments["stop_loss"] = "tighten"  # Tighter stops
                    adjustments["take_profit"] = "extend"  # Wider targets
                
                if stats["avg_duration"] > 24:  # Long holding periods
                    adjustments["time_exit"] = "implement"  # Time-based exits
                
                if adjustments:
                    recommendations["parameter_adjustments"][strategy] = adjustments
            
            # Market condition analysis
            recent_performance = self.daily_performance[-5:] if len(self.daily_performance) >= 5 else self.daily_performance
            
            if recent_performance:
                recent_pnl = []
                for day in recent_performance:
                    if isinstance(day, dict) and "total_pnl" in day:
                        pnl_value = day["total_pnl"]
                        if hasattr(pnl_value, '__float__'):
                            recent_pnl.append(float(pnl_value))
                        elif isinstance(pnl_value, (int, float)):
                            recent_pnl.append(float(pnl_value))

                if recent_pnl:
                    avg_recent_pnl = statistics.mean(recent_pnl)
                else:
                    avg_recent_pnl = 0.0
                
                if avg_recent_pnl > 0:
                    recommendations["market_conditions"] = "favorable"
                elif avg_recent_pnl < 0:
                    recommendations["market_conditions"] = "challenging"
                else:
                    recommendations["market_conditions"] = "neutral"
            
            # Risk adjustments
            if self.metrics.max_drawdown > 0.15:  # > 15% drawdown
                recommendations["risk_adjustments"]["position_size"] = "reduce"
                recommendations["risk_adjustments"]["diversification"] = "increase"

            if self.metrics.win_rate < 0.35:  # < 35% win rate
                recommendations["risk_adjustments"]["entry_criteria"] = "strengthen"
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"Error getting strategy recommendations: {e}")
            return {"error": str(e)}
    
    async def update_performance_metrics(self, trade_data: Dict[str, Any]):
        """
        Update performance metrics with new trade data
        """
        try:
            # Add trade to current metrics
            if trade_data.get("profit_loss"):
                self.metrics.total_pnl += trade_data["profit_loss"]

            self.metrics.trades_today += 1
            
            # Update strategy performance
            strategy = trade_data.get("strategy_used", "unknown")
            if strategy not in self.strategy_performance:
                self.strategy_performance[strategy] = {
                    "trades": 0,
                    "wins": 0,
                    "total_pnl": 0.0,
                    "win_rate": 0.0
                }
            
            stats = self.strategy_performance[strategy]
            stats["trades"] += 1
            stats["total_pnl"] += trade_data.get("profit_loss", 0.0)
            
            if trade_data.get("profit_loss", 0.0) > 0:
                stats["wins"] += 1
            
            stats["win_rate"] = stats["wins"] / stats["trades"]
            
            # Save performance data to database
            await self._save_performance_update(trade_data)
            
            self.logger.info(f"📊 Performance metrics updated for {strategy}")
            
        except Exception as e:
            self.logger.error(f"Error updating performance metrics: {e}")
    
    async def _save_performance_update(self, trade_data: Dict[str, Any]):
        """Save performance update to database"""
        try:
            performance_data = {
                "date": datetime.now(timezone.utc),
                "strategy": trade_data.get("strategy_used", "unknown"),
                "symbol": trade_data.get("symbol", "unknown"),
                "total_pnl": trade_data.get("profit_loss", 0.0),
                "trade_count": 1,
                "win_count": 1 if trade_data.get("profit_loss", 0.0) > 0 else 0,
                "details": {
                    "current_metrics": self.metrics.to_dict(),
                    "trade_analysis": await self.analyze_trade_performance(trade_data)
                }
            }
            
            # Check if save method exists
            if hasattr(self.db, 'save_performance_data'):
                await self.db.save_performance_data(performance_data)
            else:
                # Fallback: log the data
                self.logger.info(f"Performance data: {performance_data}")
            
        except Exception as e:
            self.logger.error(f"Error saving performance update: {e}")
    
    async def _save_performance_metrics(self):
        """Save current performance metrics to database"""
        try:
            metrics_data = {
                "date": datetime.now(timezone.utc),
                "strategy": "system_wide",
                "symbol": "ALL",
                "total_pnl": self.metrics.total_pnl,
                "trade_count": self.metrics.trades_today,
                "win_count": 0,  # Will be calculated from database
                "details": {
                    "all_metrics": self.metrics.to_dict(),
                    "strategy_performance": dict(self.strategy_performance),
                    "daily_performance": self.daily_performance[-7:] if self.daily_performance else []
                }
            }
            
            # Check if save method exists
            if hasattr(self.db, 'save_performance_data'):
                await self.db.save_performance_data(metrics_data)
            else:
                # Fallback: log the data
                self.logger.info(f"Performance metrics: {metrics_data}")
            
        except Exception as e:
            self.logger.error(f"Error saving performance metrics: {e}")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        try:
            # Safe extraction of daily performance data with SQLAlchemy handling
            daily_pnl_values = []
            profitable_days_count = 0

            for day in self.daily_performance:
                if isinstance(day, dict) and "total_pnl" in day:
                    pnl_value = day["total_pnl"]
                    if hasattr(pnl_value, '__float__'):
                        pnl = float(pnl_value)
                    elif isinstance(pnl_value, (int, float)):
                        pnl = float(pnl_value)
                    else:
                        continue

                    daily_pnl_values.append(pnl)
                    if pnl > 0:
                        profitable_days_count += 1

            # Calculate summary statistics safely
            summary_stats = {
                "total_trading_days": len(self.daily_performance),
                "profitable_days": profitable_days_count,
                "average_daily_pnl": 0.0,
                "best_day": None,
                "worst_day": None
            }

            if daily_pnl_values:
                summary_stats["average_daily_pnl"] = statistics.mean(daily_pnl_values)

                if self.daily_performance:
                    # Find best and worst days with safe value extraction
                    valid_days = []
                    for d in self.daily_performance:
                        if isinstance(d, dict) and "total_pnl" in d:
                            pnl_value = d["total_pnl"]
                            if hasattr(pnl_value, '__float__'):
                                pnl = float(pnl_value)
                            elif isinstance(pnl_value, (int, float)):
                                pnl = float(pnl_value)
                            else:
                                continue

                            # Create a safe copy with converted values
                            safe_day = dict(d)
                            safe_day["total_pnl"] = pnl
                            valid_days.append(safe_day)

                    if valid_days:
                        summary_stats["best_day"] = max(valid_days, key=lambda x: x["total_pnl"])
                        summary_stats["worst_day"] = min(valid_days, key=lambda x: x["total_pnl"])
            
            return {
                "current_metrics": self.metrics.to_dict(),
                "strategy_performance": dict(self.strategy_performance),
                "daily_performance": self.daily_performance[-30:] if self.daily_performance else [],
                "performance_trends": {
                    "last_7_days": self.daily_performance[-7:] if len(self.daily_performance) >= 7 else self.daily_performance,
                    "last_30_days": self.daily_performance[-30:] if len(self.daily_performance) >= 30 else self.daily_performance
                },
                "summary_stats": summary_stats
            }
            
        except Exception as e:
            self.logger.error(f"Error getting performance summary: {e}")
            return {
                "error": str(e),
                "current_metrics": self.metrics.to_dict(),
                "strategy_performance": dict(self.strategy_performance),
                "daily_performance": [],
                "performance_trends": {"last_7_days": [], "last_30_days": []},
                "summary_stats": {
                    "total_trading_days": 0,
                    "profitable_days": 0,
                    "average_daily_pnl": 0.0,
                    "best_day": None,
                    "worst_day": None
                }
            }

    async def analyze_performance(self) -> None:
        """Analyze current performance metrics"""
        try:
            # Perform performance analysis
            current_time = datetime.now()

            # Log current performance status
            self.logger.info(f"Performance analysis at {current_time}: "
                           f"Total PnL: {self.metrics.total_pnl:.2f}, "
                           f"Win Rate: {self.metrics.win_rate:.2f}%, "
                           f"Total Trades: {self.metrics.trades_total}")

        except Exception as e:
            self.logger.error(f"Error analyzing performance: {e}")

    async def record_trade(self, trade_record: dict) -> None:
        """Record a trade for performance analysis"""
        try:
            # Extract trade information
            profit = trade_record.get('profit', 0)
            symbol = trade_record.get('symbol', 'UNKNOWN')
            strategy = trade_record.get('strategy', 'UNKNOWN')

            # Update metrics
            self.metrics.trades_total += 1
            self.metrics.total_pnl += profit

            if profit > 0:
                self.metrics.winning_trades += 1
                if hasattr(self.metrics, 'total_profit'):
                    self.metrics.total_profit += profit
            else:
                self.metrics.losing_trades += 1
                if hasattr(self.metrics, 'total_loss'):
                    self.metrics.total_loss += abs(profit)

            # Update win rate
            if self.metrics.trades_total > 0:
                self.metrics.win_rate = (self.metrics.winning_trades / self.metrics.trades_total) * 100

            # Update strategy performance
            if strategy not in self.strategy_performance:
                self.strategy_performance[strategy] = StrategyPerformance(
                    name=strategy,
                    trades=0,
                    wins=0,
                    losses=0,
                    total_pnl=0.0,
                    win_rate=0.0,
                    profit_factor=0.0,
                    avg_duration=0.0,
                    max_drawdown=0.0,
                    sharpe_ratio=0.0,
                    last_used=datetime.now(),
                    confidence_score=0.0
                )

            strategy_perf = self.strategy_performance[strategy]
            strategy_perf.trades += 1
            strategy_perf.total_pnl += profit
            strategy_perf.last_used = datetime.now()

            if profit > 0:
                strategy_perf.wins += 1
                if hasattr(strategy_perf, 'max_profit'):
                    strategy_perf.max_profit = max(getattr(strategy_perf, 'max_profit', 0), profit)
            else:
                strategy_perf.losses += 1
                if hasattr(strategy_perf, 'max_loss'):
                    strategy_perf.max_loss = min(getattr(strategy_perf, 'max_loss', 0), profit)

            # Update win rate for strategy
            if strategy_perf.trades > 0:
                strategy_perf.win_rate = (strategy_perf.wins / strategy_perf.trades) * 100

            self.logger.info(f"Trade recorded: {symbol}, {strategy}, profit: {profit:.2f}")

        except Exception as e:
            self.logger.error(f"Error recording trade: {e}")
