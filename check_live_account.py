#!/usr/bin/env python3
"""
Live Account Balance and Trading Activity Checker
Verifies actual Bybit account balance and trading operations
"""

import asyncio
import sys
import os
from datetime import datetime

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
from bybit_bot.core.config import EnhancedBotConfig

async def check_live_account():
    """Check live Bybit account balance and trading activity"""
    try:
        print(f"LIVE ACCOUNT CHECK START: {datetime.now()}")
        print("=" * 60)
        
        # Initialize configuration
        config = EnhancedBotConfig()

        # Initialize enhanced Bybit client
        client = EnhancedBybitClient(config)
        await client.initialize()
        
        # Get account balance
        print("RETRIEVING LIVE ACCOUNT BALANCE...")
        balance = await client.get_account_balance()
        print(f"LIVE ACCOUNT BALANCE: {balance}")
        
        # Get active positions
        print("\nRETRIEVING ACTIVE POSITIONS...")
        positions = await client.get_positions()
        print(f"ACTIVE POSITIONS COUNT: {len(positions) if positions else 0}")
        
        if positions:
            for pos in positions[:5]:  # Show first 5 positions
                print(f"  Position: {pos}")
        
        # Get recent trades
        print("\nRETRIEVING RECENT TRADES...")
        trades = await client.get_trade_history(limit=10)
        print(f"RECENT TRADES COUNT: {len(trades) if trades else 0}")
        
        if trades:
            for trade in trades[:3]:  # Show first 3 trades
                print(f"  Trade: {trade}")
        
        # Get wallet balance
        print("\nRETRIEVING WALLET BALANCE...")
        wallet = await client.get_wallet_balance()
        print(f"WALLET BALANCE: {wallet}")
        
        print("=" * 60)
        print(f"LIVE ACCOUNT CHECK COMPLETE: {datetime.now()}")
        
        return {
            'balance': balance,
            'positions': len(positions) if positions else 0,
            'trades': len(trades) if trades else 0,
            'wallet': wallet
        }
        
    except Exception as e:
        print(f"ERROR checking live account: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = asyncio.run(check_live_account())
    if result:
        print(f"\nSUCCESS: Live account verification completed")
        print(f"Balance data: {result['balance']}")
        print(f"Active positions: {result['positions']}")
        print(f"Recent trades: {result['trades']}")
    else:
        print("FAILED: Could not verify live account")
