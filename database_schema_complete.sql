-- Complete Database Schema for Super GPT Bybit Bot (SQLite Compatible)
-- This script creates all required tables for the enhanced trading bot

-- ============================================================================
-- MEMORY MANAGEMENT TABLES
-- ============================================================================

-- Trading memories for persistent learning
CREATE TABLE IF NOT EXISTS trading_memories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    symbol VARCHAR(20) NOT NULL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    pattern_data TEXT NOT NULL,
    pattern_hash VARCHAR(64) NOT NULL,
    outcome TEXT NOT NULL,
    success BOOLEAN NOT NULL,
    profit_loss REAL DEFAULT 0,
    strategy VARCHAR(50),
    confidence REAL DEFAULT 0,
    market_conditions TEXT,
    technical_indicators TEXT,
    metadata TEXT DEFAULT '{}',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_trading_memories_symbol_timestamp ON trading_memories(symbol, timestamp);
CREATE INDEX IF NOT EXISTS idx_trading_memories_pattern_hash ON trading_memories(pattern_hash);
CREATE INDEX IF NOT EXISTS idx_trading_memories_success ON trading_memories(success);
CREATE INDEX IF NOT EXISTS idx_trading_memories_strategy ON trading_memories(strategy);

-- Strategy memories for adaptive learning
CREATE TABLE IF NOT EXISTS strategy_memories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    strategy_name VARCHAR(50) NOT NULL,
    memory_data TEXT NOT NULL,
    performance_score REAL DEFAULT 0,
    total_trades INTEGER DEFAULT 0,
    winning_trades INTEGER DEFAULT 0,
    total_pnl REAL DEFAULT 0,
    max_drawdown REAL DEFAULT 0,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX IF NOT EXISTS idx_strategy_memories_name ON strategy_memories(strategy_name);
CREATE INDEX IF NOT EXISTS idx_strategy_memories_updated ON strategy_memories(updated_at);
CREATE INDEX IF NOT EXISTS idx_strategy_memories_performance ON strategy_memories(performance_score);

-- ============================================================================
-- FIRECRAWL DATA TABLES
-- ============================================================================

-- Raw firecrawl data storage
CREATE TABLE IF NOT EXISTS firecrawl_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    url VARCHAR(2048) NOT NULL,
    source_type VARCHAR(50) NOT NULL, -- 'news', 'analysis', 'social', 'regulatory'
    title VARCHAR(500),
    content TEXT,
    extracted_data TEXT,
    metadata TEXT DEFAULT '{}',
    crawled_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    processed BOOLEAN DEFAULT FALSE,
    processing_errors TEXT,
    sentiment_score REAL,
    relevance_score REAL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_firecrawl_data_url ON firecrawl_data(url);
CREATE INDEX IF NOT EXISTS idx_firecrawl_data_source_type ON firecrawl_data(source_type);
CREATE INDEX IF NOT EXISTS idx_firecrawl_data_crawled_at ON firecrawl_data(crawled_at);
CREATE INDEX IF NOT EXISTS idx_firecrawl_data_processed ON firecrawl_data(processed);
CREATE INDEX IF NOT EXISTS idx_firecrawl_data_sentiment ON firecrawl_data(sentiment_score);

-- Processed market intelligence from firecrawl
CREATE TABLE IF NOT EXISTS market_intelligence (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    source_url VARCHAR(2048),
    source_type VARCHAR(50) NOT NULL,
    intelligence_type VARCHAR(50) NOT NULL, -- 'price_prediction', 'trend_analysis', 'sentiment', 'news_impact'
    symbol VARCHAR(20),
    content TEXT NOT NULL,
    extracted_insights TEXT,
    confidence_score REAL DEFAULT 0,
    impact_score REAL DEFAULT 0,
    sentiment_score REAL,
    relevance_score REAL DEFAULT 0,
    processing_metadata TEXT DEFAULT '{}',
    expires_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_market_intelligence_symbol ON market_intelligence(symbol);
CREATE INDEX IF NOT EXISTS idx_market_intelligence_type ON market_intelligence(intelligence_type);
CREATE INDEX IF NOT EXISTS idx_market_intelligence_source_type ON market_intelligence(source_type);
CREATE INDEX IF NOT EXISTS idx_market_intelligence_impact ON market_intelligence(impact_score);
CREATE INDEX IF NOT EXISTS idx_market_intelligence_created ON market_intelligence(created_at);
CREATE INDEX IF NOT EXISTS idx_market_intelligence_expires ON market_intelligence(expires_at);

-- ============================================================================
-- ENHANCED TIME MANAGEMENT TABLES
-- ============================================================================

-- Time-based performance tracking
CREATE TABLE IF NOT EXISTS time_performance_tracking (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    time_category VARCHAR(50) NOT NULL, -- 'market_hours', 'pre_market', 'post_market', 'weekend'
    market_session VARCHAR(50), -- 'asian', 'european', 'american', 'overlap'
    hour_of_day INTEGER NOT NULL CHECK (hour_of_day >= 0 AND hour_of_day <= 23),
    day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6),
    total_trades INTEGER DEFAULT 0,
    successful_trades INTEGER DEFAULT 0,
    total_pnl REAL DEFAULT 0,
    avg_trade_duration_minutes INTEGER,
    volatility REAL,
    volume_profile TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_time_performance_timestamp ON time_performance_tracking(timestamp);
CREATE INDEX IF NOT EXISTS idx_time_performance_category ON time_performance_tracking(time_category);
CREATE INDEX IF NOT EXISTS idx_time_performance_session ON time_performance_tracking(market_session);
CREATE INDEX IF NOT EXISTS idx_time_performance_hour ON time_performance_tracking(hour_of_day);
CREATE INDEX IF NOT EXISTS idx_time_performance_dow ON time_performance_tracking(day_of_week);

-- Optimal trading windows
CREATE TABLE IF NOT EXISTS optimal_trading_windows (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    symbol VARCHAR(20) NOT NULL,
    strategy VARCHAR(50) NOT NULL,
    start_hour INTEGER NOT NULL,
    end_hour INTEGER NOT NULL,
    days_of_week TEXT NOT NULL, -- JSON array of days (0=Sunday, 6=Saturday)
    timezone VARCHAR(50) NOT NULL DEFAULT 'UTC',
    win_rate REAL DEFAULT 0,
    avg_profit REAL DEFAULT 0,
    trade_count INTEGER DEFAULT 0,
    volatility_score REAL DEFAULT 0,
    confidence_score REAL DEFAULT 0,
    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_optimal_windows_symbol ON optimal_trading_windows(symbol);
CREATE INDEX IF NOT EXISTS idx_optimal_windows_strategy ON optimal_trading_windows(strategy);
CREATE INDEX IF NOT EXISTS idx_optimal_windows_hours ON optimal_trading_windows(start_hour, end_hour);
CREATE INDEX IF NOT EXISTS idx_optimal_windows_performance ON optimal_trading_windows(win_rate, avg_profit);

-- ============================================================================
-- ADVANCED RISK MANAGEMENT TABLES
-- ============================================================================

-- Risk scenario analysis
CREATE TABLE IF NOT EXISTS risk_scenarios (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    scenario_name VARCHAR(100) NOT NULL,
    scenario_type VARCHAR(50) NOT NULL, -- 'stress_test', 'monte_carlo', 'historical'
    parameters TEXT NOT NULL,
    results TEXT NOT NULL,
    max_loss REAL,
    probability REAL,
    var_95 REAL, -- Value at Risk 95%
    var_99 REAL, -- Value at Risk 99%
    expected_shortfall REAL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_risk_scenarios_name ON risk_scenarios(scenario_name);
CREATE INDEX IF NOT EXISTS idx_risk_scenarios_type ON risk_scenarios(scenario_type);
CREATE INDEX IF NOT EXISTS idx_risk_scenarios_max_loss ON risk_scenarios(max_loss);

-- Correlation matrices for risk management
CREATE TABLE IF NOT EXISTS correlation_matrices (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    calculation_date DATE NOT NULL,
    timeframe VARCHAR(20) NOT NULL, -- '1d', '7d', '30d', etc.
    correlation_data TEXT NOT NULL, -- Full correlation matrix
    eigenvalues TEXT, -- JSON array for PCA analysis
    max_correlation REAL,
    avg_correlation REAL,
    diversification_ratio REAL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_correlation_matrices_date ON correlation_matrices(calculation_date);
CREATE INDEX IF NOT EXISTS idx_correlation_matrices_timeframe ON correlation_matrices(timeframe);

-- ============================================================================
-- ADVANCED ANALYTICS TABLES
-- ============================================================================

-- Strategy performance attribution
CREATE TABLE IF NOT EXISTS strategy_attribution (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    strategy_name VARCHAR(50) NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    date DATE NOT NULL,
    pnl_attribution REAL NOT NULL,
    risk_attribution REAL NOT NULL,
    alpha REAL,
    beta REAL,
    tracking_error REAL,
    information_ratio REAL,
    factor_exposures TEXT, -- JSON for exposure to various market factors
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_strategy_attribution_strategy ON strategy_attribution(strategy_name);
CREATE INDEX IF NOT EXISTS idx_strategy_attribution_symbol ON strategy_attribution(symbol);
CREATE INDEX IF NOT EXISTS idx_strategy_attribution_date ON strategy_attribution(date);

-- Advanced portfolio metrics
CREATE TABLE IF NOT EXISTS portfolio_metrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    calculation_date DATE NOT NULL,
    total_value REAL NOT NULL,
    total_pnl REAL NOT NULL,
    daily_return REAL,
    volatility REAL,
    sharpe_ratio REAL,
    sortino_ratio REAL,
    calmar_ratio REAL,
    max_drawdown REAL,
    current_drawdown REAL,
    var_95 REAL,
    var_99 REAL,
    expected_shortfall REAL,
    beta_to_market REAL,
    correlation_to_market REAL,
    position_count INTEGER,
    concentration_risk REAL,
    leverage REAL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_portfolio_metrics_date ON portfolio_metrics(calculation_date);
CREATE INDEX IF NOT EXISTS idx_portfolio_metrics_performance ON portfolio_metrics(sharpe_ratio, total_pnl);

-- ============================================================================
-- MACHINE LEARNING ENHANCEMENT TABLES
-- ============================================================================

-- Feature engineering tracking
CREATE TABLE IF NOT EXISTS feature_engineering (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    symbol VARCHAR(20) NOT NULL,
    feature_name VARCHAR(100) NOT NULL,
    feature_type VARCHAR(50) NOT NULL, -- 'technical', 'fundamental', 'sentiment', 'macro'
    calculation_method TEXT NOT NULL,
    importance_score REAL DEFAULT 0,
    correlation_with_target REAL,
    stability_score REAL,
    last_calculated DATETIME,
    calculation_errors INTEGER DEFAULT 0,
    metadata TEXT DEFAULT '{}',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_feature_engineering_symbol ON feature_engineering(symbol);
CREATE INDEX IF NOT EXISTS idx_feature_engineering_name ON feature_engineering(feature_name);
CREATE INDEX IF NOT EXISTS idx_feature_engineering_importance ON feature_engineering(importance_score);

-- Model training history
CREATE TABLE IF NOT EXISTS model_training_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    model_name VARCHAR(100) NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    training_start DATETIME NOT NULL,
    training_end DATETIME,
    model_type VARCHAR(50) NOT NULL, -- 'xgboost', 'lstm', 'transformer', etc.
    hyperparameters TEXT NOT NULL,
    training_samples INTEGER NOT NULL,
    validation_samples INTEGER NOT NULL,
    test_samples INTEGER NOT NULL,
    training_accuracy REAL,
    validation_accuracy REAL,
    test_accuracy REAL,
    feature_importance TEXT,
    model_metrics TEXT,
    model_path VARCHAR(500),
    status VARCHAR(20) DEFAULT 'training', -- 'training', 'completed', 'failed', 'deployed'
    error_message TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_model_training_name ON model_training_history(model_name);
CREATE INDEX IF NOT EXISTS idx_model_training_symbol ON model_training_history(symbol);
CREATE INDEX IF NOT EXISTS idx_model_training_status ON model_training_history(status);
CREATE INDEX IF NOT EXISTS idx_model_training_accuracy ON model_training_history(test_accuracy);

-- ============================================================================
-- HUGGINGFACE DATA INTEGRATION TABLES
-- ============================================================================

-- HuggingFace datasets tracking
CREATE TABLE IF NOT EXISTS huggingface_datasets (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    dataset_name VARCHAR(200) NOT NULL,
    dataset_id VARCHAR(200) NOT NULL,
    description TEXT,
    categories TEXT, -- JSON array
    tags TEXT, -- JSON array
    download_count BIGINT,
    dataset_size_gb REAL,
    last_modified DATETIME,
    relevance_score REAL DEFAULT 0,
    quality_score REAL DEFAULT 0,
    usability_score REAL DEFAULT 0,
    status VARCHAR(20) DEFAULT 'discovered', -- 'discovered', 'downloaded', 'processed', 'integrated'
    local_path VARCHAR(500),
    integration_metadata TEXT DEFAULT '{}',
    error_messages TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_huggingface_datasets_name ON huggingface_datasets(dataset_name);
CREATE INDEX IF NOT EXISTS idx_huggingface_datasets_id ON huggingface_datasets(dataset_id);
CREATE INDEX IF NOT EXISTS idx_huggingface_datasets_relevance ON huggingface_datasets(relevance_score);
CREATE INDEX IF NOT EXISTS idx_huggingface_datasets_status ON huggingface_datasets(status);

-- Processed HuggingFace data
CREATE TABLE IF NOT EXISTS huggingface_processed_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    dataset_id INTEGER REFERENCES huggingface_datasets(id),
    symbol VARCHAR(20),
    data_type VARCHAR(50) NOT NULL, -- 'price', 'sentiment', 'news', 'social', 'fundamental'
    data_content TEXT NOT NULL,
    confidence_score REAL DEFAULT 0,
    processing_method VARCHAR(100),
    processing_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    valid_from DATETIME,
    valid_to DATETIME,
    metadata TEXT DEFAULT '{}',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_hf_processed_dataset ON huggingface_processed_data(dataset_id);
CREATE INDEX IF NOT EXISTS idx_hf_processed_symbol ON huggingface_processed_data(symbol);
CREATE INDEX IF NOT EXISTS idx_hf_processed_type ON huggingface_processed_data(data_type);
CREATE INDEX IF NOT EXISTS idx_hf_processed_timestamp ON huggingface_processed_data(processing_timestamp);

-- ============================================================================
-- SYSTEM MONITORING TABLES
-- ============================================================================

-- Enhanced system performance tracking
CREATE TABLE IF NOT EXISTS system_performance_detailed (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    component VARCHAR(50) NOT NULL, -- 'bot_manager', 'ml_engine', 'risk_manager', etc.
    operation VARCHAR(100) NOT NULL,
    execution_time_ms INTEGER NOT NULL,
    memory_usage_mb REAL,
    cpu_usage_percent REAL,
    success BOOLEAN NOT NULL,
    error_message TEXT,
    performance_metrics TEXT DEFAULT '{}',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Time-aware memories for advanced pattern recognition
CREATE TABLE IF NOT EXISTS time_aware_memories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    memory_id VARCHAR(100) UNIQUE NOT NULL,
    memory_data TEXT NOT NULL,
    temporal_relevance_score REAL DEFAULT 0,
    access_frequency INTEGER DEFAULT 0,
    performance_decay REAL DEFAULT 1.0,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Market correlations for cross-asset analysis
CREATE TABLE IF NOT EXISTS market_correlations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    symbol_pair VARCHAR(50) NOT NULL, -- 'BTCUSDT,ETHUSDT'
    correlation_coefficient REAL NOT NULL,
    time_window VARCHAR(20) NOT NULL, -- '1h', '1d', '1w'
    confidence REAL DEFAULT 0,
    correlation_data TEXT NOT NULL,
    last_updated DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol_pair, time_window)
);

-- Strategy performance memories with temporal context
CREATE TABLE IF NOT EXISTS strategy_performance_memories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    strategy_name VARCHAR(50) NOT NULL,
    time_period VARCHAR(50) NOT NULL, -- 'ASIAN', 'EUROPEAN', 'US', 'all'
    memory_data TEXT NOT NULL,
    avg_profit REAL DEFAULT 0,
    trade_count INTEGER DEFAULT 0,
    success_rate REAL DEFAULT 0,
    temporal_effectiveness TEXT DEFAULT '{}',
    last_updated DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(strategy_name, time_period)
);

-- Strategy states for continuation after system restart
CREATE TABLE IF NOT EXISTS strategy_states (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    strategy_name VARCHAR(50) UNIQUE NOT NULL,
    state_data TEXT NOT NULL,
    state_hash VARCHAR(64) NOT NULL,
    last_run_timestamp DATETIME NOT NULL,
    time_since_last_run INTEGER, -- seconds since last run
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- System state tracking for downtime calculation
CREATE TABLE IF NOT EXISTS system_state (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    component VARCHAR(50) UNIQUE NOT NULL,
    last_run_timestamp DATETIME NOT NULL,
    state_data TEXT DEFAULT '{}',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_system_perf_timestamp ON system_performance_detailed(timestamp);
CREATE INDEX IF NOT EXISTS idx_system_perf_component ON system_performance_detailed(component);
CREATE INDEX IF NOT EXISTS idx_system_perf_operation ON system_performance_detailed(operation);
CREATE INDEX IF NOT EXISTS idx_system_perf_execution_time ON system_performance_detailed(execution_time_ms);

-- API rate limiting tracking
CREATE TABLE IF NOT EXISTS api_rate_limits (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    api_name VARCHAR(50) NOT NULL, -- 'bybit', 'firecrawl', 'huggingface'
    endpoint VARCHAR(200) NOT NULL,
    request_timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    response_time_ms INTEGER,
    rate_limit_remaining INTEGER,
    rate_limit_reset DATETIME,
    status_code INTEGER,
    success BOOLEAN NOT NULL,
    error_message TEXT,
    request_metadata TEXT DEFAULT '{}'
);

CREATE INDEX IF NOT EXISTS idx_api_rate_limits_api ON api_rate_limits(api_name);
CREATE INDEX IF NOT EXISTS idx_api_rate_limits_endpoint ON api_rate_limits(endpoint);
CREATE INDEX IF NOT EXISTS idx_api_rate_limits_timestamp ON api_rate_limits(request_timestamp);

-- ============================================================================
-- SQLITE COMPATIBLE ANALYTICS (Views removed - use direct queries instead)
-- ============================================================================

-- Note: SQLite doesn't support CREATE OR REPLACE VIEW or DATE_TRUNC
-- Use direct SQL queries for analytics instead of views




-- ============================================================================
-- INDEXES FOR OPTIMAL PERFORMANCE
-- ============================================================================

-- Composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_trading_memories_composite ON trading_memories(symbol, strategy, timestamp, success);
CREATE INDEX IF NOT EXISTS idx_market_intelligence_composite ON market_intelligence(symbol, intelligence_type, created_at, impact_score);
CREATE INDEX IF NOT EXISTS idx_firecrawl_data_composite ON firecrawl_data(source_type, crawled_at, processed, sentiment_score);

-- Note: SQLite doesn't support partial indexes with complex WHERE clauses
-- Note: SQLite doesn't support PostgreSQL-style triggers and functions

-- ============================================================================
-- INITIAL DATA SETUP
-- ============================================================================

-- Insert initial strategy memory entries for common strategies
INSERT OR IGNORE INTO strategy_memories (strategy_name, memory_data, performance_score) VALUES
    ('momentum', '{"initialized": true, "default_params": {"lookback": 14, "threshold": 0.02}}', 0),
    ('mean_reversion', '{"initialized": true, "default_params": {"lookback": 20, "std_dev": 2.0}}', 0),
    ('trend_following', '{"initialized": true, "default_params": {"fast_ema": 12, "slow_ema": 26}}', 0),
    ('adaptive', '{"initialized": true, "default_params": {"adaptation_rate": 0.1}}', 0);

-- Note: SQLite doesn't support generate_series, so time performance tracking entries
-- would need to be created programmatically or with individual INSERT statements

-- ============================================================================
-- COMPLETION MESSAGE
-- ============================================================================
-- Schema creation complete - SQLite compatible version
-- All tables and indexes have been created for the enhanced trading bot
