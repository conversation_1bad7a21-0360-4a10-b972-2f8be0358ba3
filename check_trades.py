#!/usr/bin/env python3
"""
Check for actual trades in the database
"""

import sqlite3
import redis
from datetime import datetime

def check_trading_activity():
    """Check for real trading activity"""
    
    print("CHECKING FOR REAL TRADING ACTIVITY")
    print("=" * 40)
    
    # Check database
    conn = sqlite3.connect('bybit_trading_bot.db')
    cursor = conn.cursor()
    
    # Check trades
    cursor.execute('SELECT COUNT(*) FROM trades')
    total_trades = cursor.fetchone()[0]
    print(f"Total trades in database: {total_trades}")
    
    if total_trades > 0:
        cursor.execute('SELECT * FROM trades ORDER BY timestamp DESC LIMIT 5')
        recent_trades = cursor.fetchall()
        print("\nRecent trades:")
        for trade in recent_trades:
            print(f"  {trade}")
    
    # Check AI memories
    cursor.execute('SELECT COUNT(*) FROM ai_memories')
    ai_memories = cursor.fetchone()[0]
    print(f"\nAI memories: {ai_memories}")
    
    if ai_memories > 0:
        cursor.execute('SELECT * FROM ai_memories ORDER BY created_at DESC LIMIT 3')
        recent_memories = cursor.fetchall()
        print("Recent AI memories:")
        for memory in recent_memories:
            print(f"  {memory}")
    
    conn.close()
    
    # Check Redis
    r = redis.Redis(host='localhost', port=6379, decode_responses=True)
    redis_keys = len(r.keys())
    print(f"\nRedis keys: {redis_keys}")
    
    if redis_keys > 0:
        profit = r.get('profit:daily')
        trades_count = r.get('trades:count')
        print(f"Daily profit: {profit}")
        print(f"Trades count: {trades_count}")
        
        # Show some keys
        keys = r.keys()[:10]
        print(f"Sample keys: {keys}")
    
    print(f"\nCheck completed at: {datetime.now()}")

if __name__ == "__main__":
    check_trading_activity()
