"""
PROFIT TARGET ENFORCEMENT ENGINE - BYBIT TRADING BOT
Ensures the system actively works to reach profit targets and learns from failures
Never stops functioning - continuously adapts strategies to achieve targets

TARGET PROFIT RATES:
- Daily Target: $45,000/day
- Hourly Target: $1,875/hour  
- Per-Second Target: $0.52/second

ACTIVE LEARNING CAPABILITIES:
- Strategy performance analysis
- Failure pattern recognition
- Adaptive parameter optimization
- Real-time strategy switching
- Continuous improvement loops
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
from collections import deque
import statistics
import json

from bybit_bot.core.config import BotConfig
from bybit_bot.database.connection import DatabaseManager


class ProfitTargetStatus(Enum):
    """Profit target achievement status"""
    EXCEEDING = "exceeding"
    ON_TARGET = "on_target"
    BELOW_TARGET = "below_target"
    CRITICAL = "critical"


class LearningMode(Enum):
    """Learning modes for strategy adaptation"""
    AGGRESSIVE = "aggressive"
    MODERATE = "moderate"
    CONSERVATIVE = "conservative"
    EMERGENCY = "emergency"


@dataclass
class ProfitMetrics:
    """Real-time profit tracking metrics"""
    current_profit: float
    target_profit: float
    achievement_rate: float
    time_period: str
    status: ProfitTargetStatus
    strategies_active: int
    learning_mode: LearningMode
    adaptation_score: float
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class StrategyPerformance:
    """Strategy performance tracking"""
    strategy_name: str
    profit_generated: float
    trades_executed: int
    success_rate: float
    avg_profit_per_trade: float
    time_active: float
    efficiency_score: float
    last_updated: datetime = field(default_factory=datetime.now)


class ProfitTargetEnforcer:
    """
    Profit Target Enforcement Engine
    Ensures continuous operation and learning to achieve profit targets
    """
    
    def __init__(self, config: BotConfig, database_manager: DatabaseManager):
        self.config = config
        self.db = database_manager
        self.logger = logging.getLogger("profit_target_enforcer")
        
        # ULTRA-AGGRESSIVE PROFIT TARGETS (10x increase for maximum profit)
        self.targets = {
            'second': 1.74,      # $1.74/second (was $0.52 - 3.3x increase)
            'minute': 104.17,    # $104.17/minute (was $31.25 - 3.3x increase)
            'hour': 6250.0,      # $6,250/hour (was $1,875 - 3.3x increase)
            'day': 150000.0      # $150,000/day (was $45,000 - 3.3x increase)
        }
        
        # Real-time tracking
        self.profit_history = deque(maxlen=86400)  # 24 hours of second-by-second data
        self.strategy_performance = {}
        self.learning_data = deque(maxlen=10000)
        
        # AGGRESSIVE learning parameters for maximum profit
        self.learning_rate = 0.05  # INCREASED: Faster adaptation
        self.adaptation_threshold = 0.95  # INCREASED: Adapt if below 95% of target
        self.emergency_threshold = 0.8   # INCREASED: Emergency mode if below 80% of target
        
        # ULTRA-AGGRESSIVE strategy weights for MAXIMUM profit generation
        self.strategy_weights = {
            'ultra_scalping': 0.45,      # MASSIVELY INCREASED: Ultra-fast scalping
            'arbitrage': 0.30,           # INCREASED: High-frequency arbitrage
            'momentum_surfing': 0.15,    # Trend following and momentum
            'volatility_harvesting': 0.05, # REDUCED: Lower priority
            'grid_trading': 0.03,        # MINIMAL: Lowest priority
            'market_making': 0.02,       # MINIMAL: Lowest priority
            'correlation_trading': 0.0   # DISABLED: Remove lowest performer
        }
        
        # Performance tracking
        self.total_profit_today = 0.0
        self.start_time = time.time()
        self.last_adaptation = time.time()
        self.adaptation_count = 0
        
        # Learning state
        self.current_learning_mode = LearningMode.MODERATE
        self.failure_patterns = deque(maxlen=1000)
        self.success_patterns = deque(maxlen=1000)
        
        self.logger.info("Profit Target Enforcer initialized - ACTIVE LEARNING ENABLED")
    
    async def track_profit(self, profit_amount: float, strategy_name: str = "unknown"):
        """Track profit and update metrics"""
        current_time = time.time()
        
        # Record profit
        self.profit_history.append((current_time, profit_amount))
        self.total_profit_today += profit_amount
        
        # Update strategy performance
        if strategy_name not in self.strategy_performance:
            self.strategy_performance[strategy_name] = StrategyPerformance(
                strategy_name=strategy_name,
                profit_generated=0.0,
                trades_executed=0,
                success_rate=0.0,
                avg_profit_per_trade=0.0,
                time_active=0.0,
                efficiency_score=0.0
            )
        
        perf = self.strategy_performance[strategy_name]
        perf.profit_generated += profit_amount
        perf.trades_executed += 1
        perf.success_rate = (perf.success_rate * (perf.trades_executed - 1) + (1.0 if profit_amount > 0 else 0.0)) / perf.trades_executed
        perf.avg_profit_per_trade = perf.profit_generated / perf.trades_executed
        perf.efficiency_score = perf.profit_generated / max(perf.time_active, 1.0)
        perf.last_updated = datetime.now()
        
        # Check if adaptation is needed
        await self._check_adaptation_needed()
    
    async def get_current_metrics(self) -> ProfitMetrics:
        """Get current profit metrics and status"""
        current_time = time.time()
        
        # Calculate profit rates for different time periods
        profit_rates = {}
        for period, target in self.targets.items():
            if period == 'second':
                # Last second profit
                recent_profits = [p for t, p in self.profit_history if current_time - t <= 1.0]
                current_profit = sum(recent_profits)
            elif period == 'minute':
                # Last minute profit
                recent_profits = [p for t, p in self.profit_history if current_time - t <= 60.0]
                current_profit = sum(recent_profits)
            elif period == 'hour':
                # Last hour profit
                recent_profits = [p for t, p in self.profit_history if current_time - t <= 3600.0]
                current_profit = sum(recent_profits)
            else:  # day
                # Today's total profit
                current_profit = self.total_profit_today
            
            achievement_rate = current_profit / target if target > 0 else 0.0
            profit_rates[period] = {
                'current': current_profit,
                'target': target,
                'achievement_rate': achievement_rate
            }
        
        # Determine overall status based on hourly performance
        hourly_achievement = profit_rates['hour']['achievement_rate']
        if hourly_achievement >= 1.2:
            status = ProfitTargetStatus.EXCEEDING
        elif hourly_achievement >= 0.9:
            status = ProfitTargetStatus.ON_TARGET
        elif hourly_achievement >= self.emergency_threshold:
            status = ProfitTargetStatus.BELOW_TARGET
        else:
            status = ProfitTargetStatus.CRITICAL
        
        # Calculate adaptation score
        adaptation_score = self._calculate_adaptation_score()
        
        return ProfitMetrics(
            current_profit=profit_rates['hour']['current'],
            target_profit=profit_rates['hour']['target'],
            achievement_rate=hourly_achievement,
            time_period='hour',
            status=status,
            strategies_active=len([s for s in self.strategy_performance.values() if s.last_updated > datetime.now() - timedelta(minutes=5)]),
            learning_mode=self.current_learning_mode,
            adaptation_score=adaptation_score
        )
    
    async def _check_adaptation_needed(self):
        """Check if strategy adaptation is needed"""
        metrics = await self.get_current_metrics()
        current_time = time.time()
        
        # Don't adapt too frequently
        if current_time - self.last_adaptation < 300:  # 5 minutes minimum
            return
        
        adaptation_needed = False
        
        if metrics.status == ProfitTargetStatus.CRITICAL:
            # Emergency adaptation
            self.current_learning_mode = LearningMode.EMERGENCY
            adaptation_needed = True
            self.logger.warning(f"EMERGENCY MODE: Profit critically below target ({metrics.achievement_rate:.2%})")
            
        elif metrics.status == ProfitTargetStatus.BELOW_TARGET:
            # Aggressive adaptation
            self.current_learning_mode = LearningMode.AGGRESSIVE
            adaptation_needed = True
            self.logger.warning(f"AGGRESSIVE MODE: Profit below target ({metrics.achievement_rate:.2%})")
            
        elif metrics.status == ProfitTargetStatus.ON_TARGET:
            # Moderate learning
            self.current_learning_mode = LearningMode.MODERATE
            
        elif metrics.status == ProfitTargetStatus.EXCEEDING:
            # Conservative learning (don't change what's working)
            self.current_learning_mode = LearningMode.CONSERVATIVE
        
        if adaptation_needed:
            await self._adapt_strategies()
            self.last_adaptation = current_time
            self.adaptation_count += 1
    
    async def _adapt_strategies(self):
        """Adapt strategy weights based on performance"""
        self.logger.info(f"Adapting strategies in {self.current_learning_mode.value} mode")
        
        # Analyze strategy performance
        best_performers = []
        worst_performers = []
        
        for strategy_name, perf in self.strategy_performance.items():
            if perf.trades_executed > 0:
                score = perf.efficiency_score * perf.success_rate
                best_performers.append((strategy_name, score))
        
        # Sort by performance
        best_performers.sort(key=lambda x: x[1], reverse=True)
        
        if len(best_performers) >= 2:
            # Increase weight of best performers
            best_strategy = best_performers[0][0]
            worst_strategy = best_performers[-1][0]
            
            # Learning rate based on mode
            lr = {
                LearningMode.EMERGENCY: 0.1,
                LearningMode.AGGRESSIVE: 0.05,
                LearningMode.MODERATE: 0.02,
                LearningMode.CONSERVATIVE: 0.01
            }[self.current_learning_mode]
            
            # Adjust weights
            if best_strategy in self.strategy_weights and worst_strategy in self.strategy_weights:
                transfer_amount = min(self.strategy_weights[worst_strategy] * lr, 0.05)
                self.strategy_weights[worst_strategy] -= transfer_amount
                self.strategy_weights[best_strategy] += transfer_amount
                
                # Ensure weights stay within bounds
                for strategy in self.strategy_weights:
                    self.strategy_weights[strategy] = max(0.01, min(0.5, self.strategy_weights[strategy]))
                
                # Normalize weights
                total_weight = sum(self.strategy_weights.values())
                for strategy in self.strategy_weights:
                    self.strategy_weights[strategy] /= total_weight
                
                self.logger.info(f"Adapted weights: {best_strategy} +{transfer_amount:.3f}, {worst_strategy} -{transfer_amount:.3f}")
        
        # Store learning data
        self.learning_data.append({
            'timestamp': time.time(),
            'mode': self.current_learning_mode.value,
            'weights': self.strategy_weights.copy(),
            'performance': {name: perf.efficiency_score for name, perf in self.strategy_performance.items()}
        })
    
    def _calculate_adaptation_score(self) -> float:
        """Calculate how well the system is adapting"""
        if len(self.learning_data) < 2:
            return 0.5
        
        # Compare recent performance improvements
        recent_data = list(self.learning_data)[-10:]
        if len(recent_data) < 2:
            return 0.5
        
        # Calculate trend in performance
        performance_trend = []
        for data in recent_data:
            avg_performance = statistics.mean(data['performance'].values()) if data['performance'] else 0.0
            performance_trend.append(avg_performance)
        
        if len(performance_trend) >= 2:
            # Simple trend calculation
            trend = (performance_trend[-1] - performance_trend[0]) / len(performance_trend)
            # Normalize to 0-1 scale
            adaptation_score = max(0.0, min(1.0, 0.5 + trend * 10))
        else:
            adaptation_score = 0.5
        
        return adaptation_score
    
    def get_strategy_weights(self) -> Dict[str, float]:
        """Get current strategy weights for use by other components"""
        return self.strategy_weights.copy()
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        current_time = time.time()
        runtime_hours = (current_time - self.start_time) / 3600.0
        
        return {
            'total_profit_today': self.total_profit_today,
            'runtime_hours': runtime_hours,
            'profit_per_hour': self.total_profit_today / max(runtime_hours, 0.001),
            'target_achievement': self.total_profit_today / self.targets['day'],
            'adaptations_made': self.adaptation_count,
            'current_learning_mode': self.current_learning_mode.value,
            'strategy_weights': self.strategy_weights,
            'strategy_performance': {
                name: {
                    'profit': perf.profit_generated,
                    'trades': perf.trades_executed,
                    'success_rate': perf.success_rate,
                    'efficiency': perf.efficiency_score
                }
                for name, perf in self.strategy_performance.items()
            }
        }
    
    async def start(self):
        """Start the profit target enforcement engine"""
        self.logger.info("Profit Target Enforcer started - CONTINUOUS LEARNING ACTIVE")
        
        while True:
            try:
                # Continuous monitoring and learning
                await asyncio.sleep(10)  # Check every 10 seconds
                
                # Get current metrics
                metrics = await self.get_current_metrics()
                
                # Log status every minute
                if int(time.time()) % 60 == 0:
                    self.logger.info(f"Profit Status: {metrics.status.value} | Achievement: {metrics.achievement_rate:.2%} | Mode: {metrics.learning_mode.value}")
                
                # Emergency actions if critically below target
                if metrics.status == ProfitTargetStatus.CRITICAL:
                    await self._emergency_actions()
                
            except Exception as e:
                self.logger.error(f"Error in profit target enforcer: {e}")
                await asyncio.sleep(30)
    
    async def _emergency_actions(self):
        """Take emergency actions when profit is critically low"""
        # Skip emergency actions if no trades have been executed (API issues)
        total_trades = sum(perf.trades_executed for perf in self.strategy_performance.values())
        
        if total_trades == 0:
            self.logger.debug("Skipping emergency actions - No trades executed yet (likely API initialization)")
            return
            
        self.logger.warning("EMERGENCY ACTIONS: Implementing aggressive profit recovery")
        
        # Boost weights of historically best performing strategies
        if self.strategy_performance:
            best_strategy = max(self.strategy_performance.items(), 
                              key=lambda x: x[1].efficiency_score * x[1].success_rate)
            
            if best_strategy[0] in self.strategy_weights:
                # Temporarily boost best strategy weight
                self.strategy_weights[best_strategy[0]] = min(0.6, self.strategy_weights[best_strategy[0]] * 2.0)
                
                # Normalize other weights
                remaining_weight = 1.0 - self.strategy_weights[best_strategy[0]]
                other_strategies = [s for s in self.strategy_weights if s != best_strategy[0]]
                
                if other_strategies:
                    weight_per_other = remaining_weight / len(other_strategies)
                    for strategy in other_strategies:
                        self.strategy_weights[strategy] = weight_per_other
                
                self.logger.warning(f"Emergency boost: {best_strategy[0]} weight increased to {self.strategy_weights[best_strategy[0]]:.3f}")

    async def update_target_performance(self, achievement_rate: float, profit_velocity: float):
        """Update profit target enforcer with current performance metrics"""
        try:
            # Update current performance tracking
            self.current_performance = {
                'achievement_rate': achievement_rate,
                'profit_velocity': profit_velocity,
                'timestamp': datetime.now()
            }

            # Determine target status based on achievement rate
            if achievement_rate >= 120:
                status = ProfitTargetStatus.EXCEEDING
                learning_mode = LearningMode.CONSERVATIVE  # Maintain current approach
            elif achievement_rate >= 100:
                status = ProfitTargetStatus.ON_TARGET
                learning_mode = LearningMode.MODERATE
            elif achievement_rate >= 80:
                status = ProfitTargetStatus.BELOW_TARGET
                learning_mode = LearningMode.AGGRESSIVE
            else:
                status = ProfitTargetStatus.CRITICAL
                learning_mode = LearningMode.EMERGENCY

            # Update learning mode if changed
            if learning_mode != self.learning_mode:
                self.logger.info(f"LEARNING MODE CHANGE: {self.learning_mode.value} -> {learning_mode.value}")
                self.learning_mode = learning_mode

                # Trigger strategy weight adjustment based on new mode
                await self._adjust_strategy_weights_for_mode(learning_mode)

            # Log performance update
            self.logger.info(f"TARGET PERFORMANCE UPDATE: Achievement={achievement_rate:.1f}%, Velocity=${profit_velocity:.3f}/min, Status={status.value}")

            return True

        except Exception as e:
            self.logger.error(f"Error updating target performance: {e}")
            return False

    async def _adjust_strategy_weights_for_mode(self, mode: LearningMode):
        """Adjust strategy weights based on learning mode"""
        try:
            if mode == LearningMode.EMERGENCY:
                # MAXIMUM AGGRESSION - Focus on highest performing strategies
                self.strategy_weights = {
                    'ultra_scalping': 0.40,      # MAXIMUM: Primary profit driver
                    'arbitrage': 0.30,           # MAXIMUM: High-frequency opportunities
                    'momentum_surfing': 0.20,    # INCREASED: Trend following
                    'volatility_harvesting': 0.05, # REDUCED: Lower priority
                    'grid_trading': 0.03,        # MINIMAL: Lowest priority
                    'market_making': 0.02,       # MINIMAL: Lowest priority
                    'correlation_trading': 0.0   # DISABLED: Remove lowest performer
                }
                self.learning_rate = 0.08  # MAXIMUM learning rate

            elif mode == LearningMode.AGGRESSIVE:
                # HIGH AGGRESSION - Boost top performers
                self.strategy_weights = {
                    'ultra_scalping': 0.35,      # INCREASED: Primary profit driver
                    'arbitrage': 0.25,           # INCREASED: High-frequency opportunities
                    'momentum_surfing': 0.18,    # INCREASED: Trend following
                    'volatility_harvesting': 0.10, # Market volatility exploitation
                    'grid_trading': 0.07,        # REDUCED: Lower priority
                    'market_making': 0.03,       # REDUCED: Lower priority
                    'correlation_trading': 0.02  # REDUCED: Lowest priority
                }
                self.learning_rate = 0.06  # Increased learning rate

            elif mode == LearningMode.MODERATE:
                # BALANCED APPROACH - Default weights
                self.strategy_weights = {
                    'ultra_scalping': 0.30,      # Primary profit driver
                    'arbitrage': 0.22,           # High-frequency opportunities
                    'momentum_surfing': 0.15,    # Trend following
                    'volatility_harvesting': 0.12, # Market volatility exploitation
                    'grid_trading': 0.10,        # Grid trading
                    'market_making': 0.08,       # Market making
                    'correlation_trading': 0.03  # Correlation trading
                }
                self.learning_rate = 0.04  # Moderate learning rate

            else:  # CONSERVATIVE
                # MAINTAIN CURRENT - Don't change much when exceeding targets
                # Keep current weights but slightly reduce learning rate
                self.learning_rate = 0.02  # Reduced learning rate

            self.logger.info(f"STRATEGY WEIGHTS ADJUSTED for {mode.value} mode: Ultra={self.strategy_weights.get('ultra_scalping', 0):.2f}, Arbitrage={self.strategy_weights.get('arbitrage', 0):.2f}")

        except Exception as e:
            self.logger.error(f"Error adjusting strategy weights for mode {mode}: {e}")

    def get_current_performance(self) -> Dict[str, Any]:
        """Get current performance metrics"""
        return getattr(self, 'current_performance', {
            'achievement_rate': 0.0,
            'profit_velocity': 0.0,
            'timestamp': datetime.now()
        })
