#!/usr/bin/env python3
"""
Verify Symbol Fixes - Test that the symbol validation fixes are working
"""

import asyncio
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_symbol_fixes():
    print("=== VERIFYING SYMBOL FIXES ===")
    
    try:
        from bybit_bot.exchange.bybit_client import BybitClient
        
        client = BybitClient(
            api_key="WbQDRvmESPfUGgXQEj",
            api_secret="vdvi3Q34C7m65rHuzFw3I9kbGeyGr4oMFUga",
            testnet=False
        )
        
        print("1. Testing BTCUSDT spot klines...")
        klines = await client.get_klines("spot", "BTCUSDT", "1", 1)
        if klines.get('retCode') == 0:
            price = klines['result']['list'][0][4]
            print(f"   SUCCESS: BTCUSDT price = ${price}")
        else:
            print(f"   FAILED: {klines}")
            
        print("2. Testing ETHUSDT spot klines...")
        klines = await client.get_klines("spot", "ETHUSDT", "1", 1)
        if klines.get('retCode') == 0:
            price = klines['result']['list'][0][4]
            print(f"   SUCCESS: ETHUSDT price = ${price}")
        else:
            print(f"   FAILED: {klines}")
            
        print("3. Testing get_market_data method...")
        market_data = await client.get_market_data("BTCUSDT", "1", 5)
        if market_data and len(market_data) > 0:
            print(f"   SUCCESS: Got {len(market_data)} candles")
            print(f"   Latest close: ${market_data[0]['close']}")
        else:
            print("   FAILED: No market data returned")
            
        print("4. Testing account balance...")
        balance = await client.get_wallet_balance(accountType="UNIFIED")
        if balance.get('retCode') == 0:
            print("   SUCCESS: Account accessible")
            for account in balance['result']['list']:
                for coin in account.get('coin', []):
                    if coin['coin'] == 'USDT' and float(coin.get('walletBalance', 0)) > 0:
                        print(f"   Balance: {coin['walletBalance']} USDT")
        else:
            print(f"   FAILED: {balance}")
            
        await client.close()
        
        print("\n=== SYMBOL FIX VERIFICATION COMPLETE ===")
        print("If all tests show SUCCESS, the symbol fixes are working correctly.")
        print("The main system should now be able to retrieve live data and execute trades.")
        
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_symbol_fixes())
