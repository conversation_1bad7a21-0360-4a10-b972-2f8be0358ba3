# Bybit Trading Bot Mobile App - Installation Guide
## Motorola Moto G32 (XT2235-2) - Android 13

This guide will help you install and set up the enhanced mobile app on your Motorola G 32 with comprehensive logging controls and easy-to-use interface.

## Features Overview

### Enhanced Mobile UI
- **6 Main Pages**: Dashboard, Trading, Portfolio, AI Status, Logs, Settings
- **Logging Controls**: Toggle switches for 7 logging categories
- **Easy Interface**: Optimized for touch interaction with haptic feedback
- **Real-time Updates**: Live data streaming with auto-refresh
- **Dark Theme**: Battery-optimized design for mobile use

### Logging Categories
1. **Main System** - General system operations
2. **Trading** - Trade executions and orders
3. **Hardware** - System performance monitoring
4. **Performance** - Analytics and metrics
5. **AI Systems** - AI model operations
6. **Agents** - Agent orchestrator activities
7. **Errors** - Error tracking and debugging

## Prerequisites

### On Your Computer
1. **Node.js** (v18 or higher)
2. **Android Studio** with SDK
3. **Git** for version control
4. **USB Cable** for device connection

### On Your Motorola G 32
1. **Developer Options** enabled
2. **USB Debugging** enabled
3. **Install from Unknown Sources** enabled
4. **WiFi Connection** to ************** (or mobile data)

## Installation Steps

### Step 1: Enable Developer Options
1. Go to **Settings** > **About phone**
2. Tap **Build number** 7 times
3. Go back to **Settings** > **System** > **Developer options**
4. Enable **USB debugging**
5. Enable **Stay awake while charging**

### Step 2: Enable Unknown Sources
1. Go to **Settings** > **Security**
2. Enable **Install unknown apps**
3. Select your file manager and enable **Allow from this source**

### Step 3: Build the APK

#### Option A: Using the Build Script (Recommended)
```bash
cd mobile
chmod +x build-moto-g32.sh
./build-moto-g32.sh
```

#### Option B: Manual Build
```bash
cd mobile
npm install
npx react-native run-android --variant=release
```

### Step 4: Install on Device

#### Method 1: Direct Installation (USB)
1. Connect your Motorola G 32 via USB
2. Run the build script and select option 3 or 4
3. The app will be installed automatically

#### Method 2: APK Transfer
1. Locate the APK file in `mobile/android/app/build/outputs/apk/release/`
2. Transfer to your phone via:
   - USB file transfer
   - Email attachment
   - Cloud storage (Google Drive, etc.)
   - Wireless transfer apps
3. Open the APK file on your phone
4. Tap **Install** when prompted

## First-Time Setup

### 1. Network Configuration
- **Primary**: Connect to WiFi network **************
- **Fallback**: Use mobile data (4G LTE)
- **Trading System**: Connects to *************:8000

### 2. App Configuration
1. Open the app
2. Go to **Settings** > **Logging**
3. Configure logging categories as needed:
   - Enable/disable categories with toggle switches
   - Set log levels (DEBUG, INFO, WARNING, ERROR, CRITICAL)
   - Use master toggle for all categories

### 3. Verify Connection
1. Check **Dashboard** for system status
2. Verify **AI Status** shows components as active
3. Check **Logs** page for real-time log entries

## Using the App

### Dashboard Page
- System health overview
- Real-time profit/loss tracking
- Quick status indicators
- System alerts and notifications

### Trading Page
- Active positions and orders
- Strategy performance
- Real-time P&L updates
- Emergency stop button

### Portfolio Page
- Asset allocation overview
- Performance metrics
- Transaction history
- Portfolio analytics

### AI Status Page
- AI system health monitoring
- ML model performance
- Agent status and activities
- Confidence metrics

### Logs Page (NEW)
- **Live Logs**: Real-time log streaming
- **Category Filters**: Filter by logging category
- **Search**: Search through log entries
- **Export**: Export logs for analysis
- **Clear**: Clear logs by category

### Settings Page (ENHANCED)
- **Logging Controls**: Master and individual category toggles
- **Log Levels**: Set verbosity for each category
- **Trading Settings**: Risk management and parameters
- **Notifications**: Push notification preferences
- **Security**: Biometric authentication settings

## Logging Controls Usage

### Master Control
- **Enable All Logging**: Toggle all categories at once
- **Master Switch**: Quick enable/disable for all logging

### Individual Categories
Each category has:
- **Toggle Switch**: Enable/disable logging
- **Log Level Selector**: Choose verbosity level
- **Real-time Updates**: Changes apply immediately

### Log Levels Explained
- **DEBUG**: Most verbose, development information
- **INFO**: General operational information
- **WARNING**: Potential issues or important events
- **ERROR**: Error conditions that need attention
- **CRITICAL**: Critical failures requiring immediate action

## Troubleshooting

### App Won't Install
1. Check **Install from unknown sources** is enabled
2. Ensure sufficient storage space (>100MB)
3. Try restarting the device
4. Clear cache of file manager app

### Connection Issues
1. Verify WiFi connection to correct network
2. Check mobile data if WiFi unavailable
3. Ensure trading system is running at *************:8000
4. Check firewall settings

### Logging Not Working
1. Check master logging toggle is enabled
2. Verify individual category toggles
3. Check log level settings
4. Restart the app if needed

### Performance Issues
1. Close other apps to free memory
2. Enable **Stay awake while charging**
3. Disable battery optimization for the app
4. Restart the device if needed

## Advanced Features

### Auto-Refresh
- Enable in Logs page for real-time updates
- Configurable refresh intervals
- Battery-optimized background updates

### Export Functionality
- Export individual category logs
- Export all logs at once
- Share via email, cloud storage, or messaging

### Haptic Feedback
- Toggle switches provide tactile feedback
- Different patterns for ON/OFF states
- Enhances mobile user experience

### Dark Theme
- Optimized for battery life
- Reduces eye strain in low light
- Material Design 3 components

## Support

### Getting Help
1. Check logs for error messages
2. Use the **Help & Support** button in Settings
3. Export logs for technical support
4. Check system status in Dashboard

### Reporting Issues
1. Go to **Settings** > **About** > **Send Feedback**
2. Include relevant log exports
3. Describe steps to reproduce the issue
4. Include device and app version information

## Security Notes

- App uses secure HTTPS connections
- Biometric authentication available
- Auto-lock functionality
- Encrypted local data storage
- No sensitive data in logs

## Updates

The app will notify you of available updates. To update:
1. Download new APK
2. Install over existing app (data preserved)
3. Restart app to apply changes

---

**Device Optimized For**: Motorola Moto G32 (XT2235-2)  
**Android Version**: 13  
**Architecture**: ARM64  
**App Version**: 1.0.0  
**Build Date**: 2025-01-09
