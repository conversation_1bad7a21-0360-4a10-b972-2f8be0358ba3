import React from 'react';
import { StyleSheet, View, ScrollView } from 'react-native';
import { Text, Card, Chip, DataTable, Button } from 'react-native-paper';
import { RouteProp, useRoute } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { theme } from '../styles/theme';

type TradeDetailsRouteProp = RouteProp<{
    TradeDetails: {
        tradeId: string;
        trade?: any;
    };
}, 'TradeDetails'>;

const TradeDetailsScreen: React.FC = () => {
    const route = useRoute<TradeDetailsRouteProp>();
    const { tradeId, trade } = route.params;

    // Mock trade data - in real app, fetch from API using tradeId
    const tradeData = trade || {
        id: tradeId,
        symbol: 'BTCUSDT',
        side: 'Buy',
        type: 'Market',
        quantity: 0.1,
        price: 45230.50,
        executedPrice: 45235.20,
        status: 'Filled',
        timestamp: '2025-01-09 14:30:25',
        pnl: 125.50,
        fee: 2.26,
        strategy: 'AI Momentum Strategy',
        confidence: 85.2,
        riskScore: 3.2,
        notes: 'Strong bullish momentum detected by AI model',
    };

    const getStatusColor = (status: string) => {
        switch (status.toLowerCase()) {
            case 'filled':
                return theme.colors.primary;
            case 'pending':
                return '#FF9800';
            case 'cancelled':
                return theme.colors.error;
            default:
                return theme.colors.onSurface;
        }
    };

    const getSideColor = (side: string) => {
        return side.toLowerCase() === 'buy' ? theme.colors.primary : theme.colors.error;
    };

    return (
        <View style={styles.container}>
            <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
                {/* Trade Overview */}
                <Card style={styles.card}>
                    <Card.Content>
                        <View style={styles.header}>
                            <Text variant="headlineSmall" style={styles.symbol}>
                                {tradeData.symbol}
                            </Text>
                            <Chip
                                mode="outlined"
                                style={[
                                    styles.statusChip,
                                    { borderColor: getStatusColor(tradeData.status) }
                                ]}
                                textStyle={{ color: getStatusColor(tradeData.status) }}
                            >
                                {tradeData.status}
                            </Chip>
                        </View>

                        <View style={styles.tradeInfo}>
                            <View style={styles.infoRow}>
                                <Text variant="bodyMedium" style={styles.label}>
                                    Side:
                                </Text>
                                <Chip
                                    mode="outlined"
                                    style={[
                                        styles.sideChip,
                                        { borderColor: getSideColor(tradeData.side) }
                                    ]}
                                    textStyle={{ color: getSideColor(tradeData.side) }}
                                >
                                    {tradeData.side}
                                </Chip>
                            </View>
                            <View style={styles.infoRow}>
                                <Text variant="bodyMedium" style={styles.label}>
                                    Type:
                                </Text>
                                <Text variant="bodyMedium" style={styles.value}>
                                    {tradeData.type}
                                </Text>
                            </View>
                            <View style={styles.infoRow}>
                                <Text variant="bodyMedium" style={styles.label}>
                                    Quantity:
                                </Text>
                                <Text variant="bodyMedium" style={styles.value}>
                                    {tradeData.quantity}
                                </Text>
                            </View>
                            <View style={styles.infoRow}>
                                <Text variant="bodyMedium" style={styles.label}>
                                    Order Price:
                                </Text>
                                <Text variant="bodyMedium" style={styles.value}>
                                    ${tradeData.price.toFixed(2)}
                                </Text>
                            </View>
                            <View style={styles.infoRow}>
                                <Text variant="bodyMedium" style={styles.label}>
                                    Executed Price:
                                </Text>
                                <Text variant="bodyMedium" style={styles.value}>
                                    ${tradeData.executedPrice.toFixed(2)}
                                </Text>
                            </View>
                            <View style={styles.infoRow}>
                                <Text variant="bodyMedium" style={styles.label}>
                                    Timestamp:
                                </Text>
                                <Text variant="bodyMedium" style={styles.value}>
                                    {tradeData.timestamp}
                                </Text>
                            </View>
                        </View>
                    </Card.Content>
                </Card>

                {/* P&L and Fees */}
                <Card style={styles.card}>
                    <Card.Content>
                        <Text variant="titleMedium" style={styles.cardTitle}>
                            Profit & Loss
                        </Text>
                        <View style={styles.pnlContainer}>
                            <View style={styles.pnlItem}>
                                <Text variant="bodyMedium" style={styles.pnlLabel}>
                                    Realized P&L
                                </Text>
                                <Text
                                    variant="headlineSmall"
                                    style={[
                                        styles.pnlValue,
                                        { color: tradeData.pnl >= 0 ? theme.colors.primary : theme.colors.error }
                                    ]}
                                >
                                    ${tradeData.pnl.toFixed(2)}
                                </Text>
                            </View>
                            <View style={styles.pnlItem}>
                                <Text variant="bodyMedium" style={styles.pnlLabel}>
                                    Trading Fee
                                </Text>
                                <Text variant="headlineSmall" style={styles.feeValue}>
                                    ${tradeData.fee.toFixed(2)}
                                </Text>
                            </View>
                        </View>
                    </Card.Content>
                </Card>

                {/* Strategy Information */}
                <Card style={styles.card}>
                    <Card.Content>
                        <Text variant="titleMedium" style={styles.cardTitle}>
                            Strategy Information
                        </Text>
                        <View style={styles.strategyInfo}>
                            <View style={styles.infoRow}>
                                <Text variant="bodyMedium" style={styles.label}>
                                    Strategy:
                                </Text>
                                <Text variant="bodyMedium" style={styles.value}>
                                    {tradeData.strategy}
                                </Text>
                            </View>
                            <View style={styles.infoRow}>
                                <Text variant="bodyMedium" style={styles.label}>
                                    AI Confidence:
                                </Text>
                                <Text variant="bodyMedium" style={styles.value}>
                                    {tradeData.confidence}%
                                </Text>
                            </View>
                            <View style={styles.infoRow}>
                                <Text variant="bodyMedium" style={styles.label}>
                                    Risk Score:
                                </Text>
                                <Text variant="bodyMedium" style={styles.value}>
                                    {tradeData.riskScore}/10
                                </Text>
                            </View>
                        </View>
                        {tradeData.notes && (
                            <View style={styles.notesContainer}>
                                <Text variant="bodyMedium" style={styles.notesLabel}>
                                    Notes:
                                </Text>
                                <Text variant="bodyMedium" style={styles.notesText}>
                                    {tradeData.notes}
                                </Text>
                            </View>
                        )}
                    </Card.Content>
                </Card>

                {/* Trade Actions */}
                <Card style={styles.card}>
                    <Card.Content>
                        <Text variant="titleMedium" style={styles.cardTitle}>
                            Actions
                        </Text>
                        <View style={styles.actionsContainer}>
                            <Button
                                mode="outlined"
                                onPress={() => {
                                    // TODO: Export trade details
                                }}
                                style={styles.actionButton}
                                icon="download"
                            >
                                Export Details
                            </Button>
                            <Button
                                mode="outlined"
                                onPress={() => {
                                    // TODO: Share trade
                                }}
                                style={styles.actionButton}
                                icon="share"
                            >
                                Share Trade
                            </Button>
                        </View>
                    </Card.Content>
                </Card>
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: theme.colors.background,
    },
    scrollView: {
        flex: 1,
    },
    card: {
        marginHorizontal: 16,
        marginBottom: 16,
        backgroundColor: theme.colors.surface,
    },
    cardTitle: {
        color: theme.colors.onSurface,
        fontWeight: '700',
        marginBottom: 16,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 20,
    },
    symbol: {
        color: theme.colors.onSurface,
        fontWeight: '700',
    },
    statusChip: {
        height: 32,
    },
    tradeInfo: {
        gap: 12,
    },
    infoRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    label: {
        color: theme.colors.onSurfaceVariant,
        fontWeight: '500',
    },
    value: {
        color: theme.colors.onSurface,
        fontWeight: '600',
    },
    sideChip: {
        height: 28,
    },
    pnlContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    pnlItem: {
        alignItems: 'center',
        flex: 1,
    },
    pnlLabel: {
        color: theme.colors.onSurfaceVariant,
        marginBottom: 8,
    },
    pnlValue: {
        fontWeight: '700',
    },
    feeValue: {
        color: theme.colors.onSurface,
        fontWeight: '700',
    },
    strategyInfo: {
        gap: 12,
        marginBottom: 16,
    },
    notesContainer: {
        marginTop: 8,
        padding: 12,
        backgroundColor: theme.colors.surfaceVariant,
        borderRadius: 8,
    },
    notesLabel: {
        color: theme.colors.onSurfaceVariant,
        fontWeight: '500',
        marginBottom: 4,
    },
    notesText: {
        color: theme.colors.onSurface,
        lineHeight: 20,
    },
    actionsContainer: {
        flexDirection: 'row',
        gap: 12,
    },
    actionButton: {
        flex: 1,
    },
});

export default TradeDetailsScreen;
