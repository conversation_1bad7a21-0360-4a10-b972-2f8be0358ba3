import React, { useState } from 'react'
import {
    <PERSON>,
    <PERSON><PERSON>,
    <PERSON>,
    CardContent,
    <PERSON>er,
    FormControl,
    FormControlLabel,
    IconButton,
    InputLabel,
    MenuItem,
    Select,
    Slider,
    Switch,
    Typography,
    Divider,
    Chip,
    Grid,
    Tooltip
} from '@mui/material'
import {
    Palette,
    Close,
    RestartAlt,
    Brightness4,
    Brightness7,
    Animation,
    Speed,
    Visibility
} from '@mui/icons-material'
import { motion } from 'framer-motion'
import { useUserPreferences } from '../../hooks/useLocalStorage'

/**
 * Advanced theme customizer with real-time preview
 * Allows users to customize colors, layout, and visual preferences
 */
const ThemeCustomizer = ({ open, onClose }) => {
    const { preferences, updatePreference, resetPreferences } = useUserPreferences()
    
    // Theme presets
    const themePresets = [
        {
            name: 'Dark Professional',
            theme: 'dark',
            primary: '#00ff88',
            secondary: '#42a5f5',
            accent: '#ffa726'
        },
        {
            name: 'Light Clean',
            theme: 'light',
            primary: '#1976d2',
            secondary: '#dc004e',
            accent: '#ed6c02'
        },
        {
            name: 'Cyberpunk',
            theme: 'dark',
            primary: '#ff0080',
            secondary: '#00ffff',
            accent: '#ffff00'
        },
        {
            name: 'Forest',
            theme: 'dark',
            primary: '#4caf50',
            secondary: '#8bc34a',
            accent: '#cddc39'
        }
    ]
    
    // Color options
    const colorOptions = [
        { name: 'Green', value: '#00ff88' },
        { name: 'Blue', value: '#42a5f5' },
        { name: 'Purple', value: '#ab47bc' },
        { name: 'Orange', value: '#ffa726' },
        { name: 'Red', value: '#ff5252' },
        { name: 'Cyan', value: '#26c6da' },
        { name: 'Pink', value: '#ec407a' },
        { name: 'Lime', value: '#d4e157' }
    ]
    
    // Layout density options
    const densityOptions = [
        { label: 'Compact', value: 'compact' },
        { label: 'Standard', value: 'standard' },
        { label: 'Comfortable', value: 'comfortable' }
    ]
    
    // Animation speed options
    const animationSpeeds = [
        { label: 'Disabled', value: 0 },
        { label: 'Slow', value: 0.5 },
        { label: 'Normal', value: 1 },
        { label: 'Fast', value: 1.5 },
        { label: 'Very Fast', value: 2 }
    ]
    
    // Apply theme preset
    const applyPreset = (preset) => {
        updatePreference('theme', preset.theme)
        updatePreference('colors.primary', preset.primary)
        updatePreference('colors.secondary', preset.secondary)
        updatePreference('colors.accent', preset.accent)
    }
    
    // Color picker component
    const ColorPicker = ({ label, value, onChange, colors = colorOptions }) => (
        <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" sx={{ mb: 1, color: '#fff' }}>
                {label}
            </Typography>
            <Grid container spacing={1}>
                {colors.map((color) => (
                    <Grid item key={color.value}>
                        <Tooltip title={color.name}>
                            <Box
                                sx={{
                                    width: 32,
                                    height: 32,
                                    backgroundColor: color.value,
                                    borderRadius: '50%',
                                    cursor: 'pointer',
                                    border: value === color.value 
                                        ? '3px solid #fff' 
                                        : '2px solid rgba(255,255,255,0.2)',
                                    transition: 'all 0.2s ease',
                                    '&:hover': {
                                        transform: 'scale(1.1)',
                                        border: '3px solid rgba(255,255,255,0.5)'
                                    }
                                }}
                                onClick={() => onChange(color.value)}
                            />
                        </Tooltip>
                    </Grid>
                ))}
            </Grid>
        </Box>
    )
    
    return (
        <Drawer
            anchor="right"
            open={open}
            onClose={onClose}
            PaperProps={{
                sx: {
                    width: 400,
                    backgroundColor: 'rgba(0, 0, 0, 0.95)',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    backdropFilter: 'blur(10px)'
                }
            }}
        >
            <motion.div
                initial={{ x: 400, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                exit={{ x: 400, opacity: 0 }}
                transition={{ duration: 0.3 }}
            >
                <Box sx={{ p: 3 }}>
                    {/* Header */}
                    <Box
                        sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            mb: 3
                        }}
                    >
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <Palette sx={{ color: '#00ff88' }} />
                            <Typography variant="h6" sx={{ color: '#fff', fontWeight: 600 }}>
                                Theme Customizer
                            </Typography>
                        </Box>
                        
                        <Box sx={{ display: 'flex', gap: 1 }}>
                            <Tooltip title="Reset to Default">
                                <IconButton
                                    onClick={resetPreferences}
                                    sx={{ color: '#b3b3b3' }}
                                >
                                    <RestartAlt />
                                </IconButton>
                            </Tooltip>
                            <IconButton
                                onClick={onClose}
                                sx={{ color: '#b3b3b3' }}
                            >
                                <Close />
                            </IconButton>
                        </Box>
                    </Box>
                    
                    {/* Theme Presets */}
                    <Box sx={{ mb: 4 }}>
                        <Typography variant="subtitle1" sx={{ mb: 2, color: '#fff', fontWeight: 600 }}>
                            Quick Presets
                        </Typography>
                        <Grid container spacing={1}>
                            {themePresets.map((preset) => (
                                <Grid item xs={6} key={preset.name}>
                                    <Card
                                        sx={{
                                            cursor: 'pointer',
                                            background: 'rgba(255, 255, 255, 0.05)',
                                            border: '1px solid rgba(255, 255, 255, 0.1)',
                                            transition: 'all 0.2s ease',
                                            '&:hover': {
                                                background: 'rgba(255, 255, 255, 0.1)',
                                                transform: 'translateY(-2px)'
                                            }
                                        }}
                                        onClick={() => applyPreset(preset)}
                                    >
                                        <CardContent sx={{ p: 2 }}>
                                            <Typography
                                                variant="caption"
                                                sx={{ color: '#fff', fontWeight: 500 }}
                                            >
                                                {preset.name}
                                            </Typography>
                                            <Box sx={{ display: 'flex', gap: 0.5, mt: 1 }}>
                                                <Box
                                                    sx={{
                                                        width: 12,
                                                        height: 12,
                                                        backgroundColor: preset.primary,
                                                        borderRadius: '50%'
                                                    }}
                                                />
                                                <Box
                                                    sx={{
                                                        width: 12,
                                                        height: 12,
                                                        backgroundColor: preset.secondary,
                                                        borderRadius: '50%'
                                                    }}
                                                />
                                                <Box
                                                    sx={{
                                                        width: 12,
                                                        height: 12,
                                                        backgroundColor: preset.accent,
                                                        borderRadius: '50%'
                                                    }}
                                                />
                                            </Box>
                                        </CardContent>
                                    </Card>
                                </Grid>
                            ))}
                        </Grid>
                    </Box>
                    
                    <Divider sx={{ borderColor: 'rgba(255, 255, 255, 0.1)', mb: 3 }} />
                    
                    {/* Theme Mode */}
                    <Box sx={{ mb: 3 }}>
                        <Typography variant="subtitle1" sx={{ mb: 2, color: '#fff', fontWeight: 600 }}>
                            Appearance
                        </Typography>
                        
                        <FormControlLabel
                            control={
                                <Switch
                                    checked={preferences.theme === 'dark'}
                                    onChange={(e) => updatePreference('theme', e.target.checked ? 'dark' : 'light')}
                                    sx={{
                                        '& .MuiSwitch-switchBase.Mui-checked': {
                                            color: '#00ff88'
                                        },
                                        '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                                            backgroundColor: '#00ff88'
                                        }
                                    }}
                                />
                            }
                            label={
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                    {preferences.theme === 'dark' ? <Brightness4 /> : <Brightness7 />}
                                    <Typography sx={{ color: '#fff' }}>
                                        Dark Mode
                                    </Typography>
                                </Box>
                            }
                        />
                    </Box>
                    
                    {/* Color Customization */}
                    <ColorPicker
                        label="Primary Color"
                        value={preferences.colors?.primary || '#00ff88'}
                        onChange={(color) => updatePreference('colors.primary', color)}
                    />
                    
                    <ColorPicker
                        label="Secondary Color"
                        value={preferences.colors?.secondary || '#42a5f5'}
                        onChange={(color) => updatePreference('colors.secondary', color)}
                    />
                    
                    <Divider sx={{ borderColor: 'rgba(255, 255, 255, 0.1)', my: 3 }} />
                    
                    {/* Layout Settings */}
                    <Box sx={{ mb: 3 }}>
                        <Typography variant="subtitle1" sx={{ mb: 2, color: '#fff', fontWeight: 600 }}>
                            Layout & Behavior
                        </Typography>
                        
                        {/* Layout Density */}
                        <FormControl fullWidth sx={{ mb: 2 }}>
                            <InputLabel sx={{ color: '#b3b3b3' }}>Layout Density</InputLabel>
                            <Select
                                value={preferences.layout?.density || 'standard'}
                                onChange={(e) => updatePreference('layout.density', e.target.value)}
                                sx={{
                                    color: '#fff',
                                    '& .MuiOutlinedInput-notchedOutline': {
                                        borderColor: 'rgba(255, 255, 255, 0.2)'
                                    }
                                }}
                            >
                                {densityOptions.map(option => (
                                    <MenuItem key={option.value} value={option.value}>
                                        {option.label}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                        
                        {/* Animation Speed */}
                        <Box sx={{ mb: 2 }}>
                            <Typography variant="body2" sx={{ mb: 1, color: '#fff' }}>
                                Animation Speed
                            </Typography>
                            <Slider
                                value={preferences.animations?.speed || 1}
                                onChange={(_, value) => updatePreference('animations.speed', value)}
                                min={0}
                                max={2}
                                step={0.5}
                                marks={animationSpeeds}
                                valueLabelDisplay="auto"
                                sx={{
                                    color: '#00ff88',
                                    '& .MuiSlider-mark': {
                                        backgroundColor: '#b3b3b3'
                                    },
                                    '& .MuiSlider-markLabel': {
                                        color: '#b3b3b3',
                                        fontSize: '0.7rem'
                                    }
                                }}
                            />
                        </Box>
                        
                        {/* Sidebar Auto-collapse */}
                        <FormControlLabel
                            control={
                                <Switch
                                    checked={preferences.layout?.autoCollapseSidebar || false}
                                    onChange={(e) => updatePreference('layout.autoCollapseSidebar', e.target.checked)}
                                    sx={{
                                        '& .MuiSwitch-switchBase.Mui-checked': {
                                            color: '#00ff88'
                                        },
                                        '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                                            backgroundColor: '#00ff88'
                                        }
                                    }}
                                />
                            }
                            label={
                                <Typography sx={{ color: '#fff' }}>
                                    Auto-collapse Sidebar
                                </Typography>
                            }
                        />
                        
                        {/* Reduced Motion */}
                        <FormControlLabel
                            control={
                                <Switch
                                    checked={preferences.accessibility?.reducedMotion || false}
                                    onChange={(e) => updatePreference('accessibility.reducedMotion', e.target.checked)}
                                    sx={{
                                        '& .MuiSwitch-switchBase.Mui-checked': {
                                            color: '#00ff88'
                                        },
                                        '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                                            backgroundColor: '#00ff88'
                                        }
                                    }}
                                />
                            }
                            label={
                                <Typography sx={{ color: '#fff' }}>
                                    Reduce Motion
                                </Typography>
                            }
                        />
                    </Box>
                    
                    <Divider sx={{ borderColor: 'rgba(255, 255, 255, 0.1)', my: 3 }} />
                    
                    {/* Display Settings */}
                    <Box sx={{ mb: 3 }}>
                        <Typography variant="subtitle1" sx={{ mb: 2, color: '#fff', fontWeight: 600 }}>
                            Display Preferences
                        </Typography>
                        
                        {/* Compact Numbers */}
                        <FormControlLabel
                            control={
                                <Switch
                                    checked={preferences.display?.compactNumbers !== false}
                                    onChange={(e) => updatePreference('display.compactNumbers', e.target.checked)}
                                    sx={{
                                        '& .MuiSwitch-switchBase.Mui-checked': {
                                            color: '#00ff88'
                                        },
                                        '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                                            backgroundColor: '#00ff88'
                                        }
                                    }}
                                />
                            }
                            label={
                                <Typography sx={{ color: '#fff' }}>
                                    Compact Numbers (1.2K instead of 1,200)
                                </Typography>
                            }
                        />
                        
                        {/* High Contrast */}
                        <FormControlLabel
                            control={
                                <Switch
                                    checked={preferences.accessibility?.highContrast || false}
                                    onChange={(e) => updatePreference('accessibility.highContrast', e.target.checked)}
                                    sx={{
                                        '& .MuiSwitch-switchBase.Mui-checked': {
                                            color: '#00ff88'
                                        },
                                        '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                                            backgroundColor: '#00ff88'
                                        }
                                    }}
                                />
                            }
                            label={
                                <Typography sx={{ color: '#fff' }}>
                                    High Contrast Mode
                                </Typography>
                            }
                        />
                    </Box>
                    
                    {/* Apply Button */}
                    <Button
                        fullWidth
                        variant="contained"
                        sx={{
                            backgroundColor: '#00ff88',
                            color: '#000',
                            fontWeight: 600,
                            '&:hover': {
                                backgroundColor: '#00cc6a'
                            }
                        }}
                        onClick={onClose}
                    >
                        Apply Changes
                    </Button>
                </Box>
            </motion.div>
        </Drawer>
    )
}

export default ThemeCustomizer
