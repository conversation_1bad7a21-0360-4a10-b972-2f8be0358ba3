"""
High-performance MCP client for autonomous trading operations.
Optimized for minimal latency and maximum throughput.
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional
from datetime import datetime
import aiohttp
import time

class OptimizedMCPClient:
    """Ultra-fast MCP client with performance optimizations."""
    
    def __init__(self, server_endpoint: str = "localhost:8000"):
        self.server_endpoint = server_endpoint
        self.session: Optional[aiohttp.ClientSession] = None
        self._request_cache = {}
        self._cache_ttl = {}
        self.logger = logging.getLogger(__name__)
        
        # Performance metrics
        self.request_count = 0
        self.total_latency = 0.0
        self.max_latency = 0.0
        self.min_latency = float('inf')
        
    async def __aenter__(self):
        """Async context manager entry."""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.disconnect()
    
    async def connect(self):
        """Establish high-performance connection."""
        if not self.session:
            timeout = aiohttp.ClientTimeout(
                total=3.0,  # Total timeout
                connect=1.0,  # Connection timeout
                sock_read=0.5  # Socket read timeout
            )
            
            connector = aiohttp.TCPConnector(
                limit=100,  # Connection pool limit
                limit_per_host=50,  # Per-host limit
                keepalive_timeout=30,  # Keep-alive timeout
                enable_cleanup_closed=True,
                use_dns_cache=True,
                ttl_dns_cache=300
            )
            
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                connector=connector,
                json_serialize=json.dumps,
                raise_for_status=True
            )
    
    async def disconnect(self):
        """Clean disconnect."""
        if self.session:
            await self.session.close()
            self.session = None
    
    async def _make_request(
        self, 
        method: str, 
        endpoint: str, 
        data: Optional[Dict[str, Any]] = None,
        cache_key: Optional[str] = None,
        cache_ttl: float = 1.0
    ) -> Dict[str, Any]:
        """Make optimized request with caching."""
        start_time = time.perf_counter()
        
        # Check cache first
        if cache_key and self._is_cache_valid(cache_key):
            return self._request_cache[cache_key]
        
        try:
            if not self.session:
                await self.connect()
            
            url = f"http://{self.server_endpoint}/{endpoint}"
            
            if method.upper() == "GET":
                async with self.session.get(url, params=data) as response:
                    result = await response.json()
            else:
                async with self.session.post(url, json=data) as response:
                    result = await response.json()
            
            # Cache result if cache_key provided
            if cache_key:
                self._request_cache[cache_key] = result
                self._cache_ttl[cache_key] = time.time() + cache_ttl
            
            # Update performance metrics
            latency = time.perf_counter() - start_time
            self._update_metrics(latency)
            
            return result
            
        except Exception as e:
            self.logger.error(f"MCP request failed: {e}")
            return {"error": str(e), "timestamp": datetime.now().isoformat()}
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cache entry is still valid."""
        return (
            cache_key in self._request_cache and
            cache_key in self._cache_ttl and
            time.time() < self._cache_ttl[cache_key]
        )
    
    def _update_metrics(self, latency: float):
        """Update performance metrics."""
        self.request_count += 1
        self.total_latency += latency
        self.max_latency = max(self.max_latency, latency)
        self.min_latency = min(self.min_latency, latency)
    
    # Trading API methods with caching
    async def get_market_data(self, symbol: str = "BTCUSDT") -> Dict[str, Any]:
        """Get market data with sub-second caching."""
        return await self._make_request(
            "POST",
            "tools/get_market_data",
            {"symbol": symbol},
            cache_key=f"market_{symbol}",
            cache_ttl=0.1  # 100ms cache
        )
    
    async def execute_order(
        self,
        symbol: str,
        side: str,
        order_type: str,
        qty: float,
        price: Optional[float] = None
    ) -> Dict[str, Any]:
        """Execute order with no caching."""
        return await self._make_request(
            "POST",
            "tools/execute_order",
            {
                "symbol": symbol,
                "side": side,
                "order_type": order_type,
                "qty": qty,
                "price": price
            }
        )
    
    async def assess_risk(self, symbol: str, position_size: float) -> Dict[str, Any]:
        """Assess risk with minimal caching."""
        return await self._make_request(
            "POST",
            "tools/assess_risk",
            {"symbol": symbol, "position_size": position_size},
            cache_key=f"risk_{symbol}_{position_size}",
            cache_ttl=0.5  # 500ms cache
        )
    
    async def get_performance(self) -> Dict[str, Any]:
        """Get performance metrics with short caching."""
        return await self._make_request(
            "POST",
            "tools/get_performance",
            {},
            cache_key="performance",
            cache_ttl=1.0  # 1 second cache
        )
    
    async def optimize_portfolio(self) -> Dict[str, Any]:
        """Trigger portfolio optimization."""
        return await self._make_request(
            "POST",
            "tools/optimize_portfolio",
            {}
        )
    
    async def get_trading_status(self) -> Dict[str, Any]:
        """Get trading status with minimal caching."""
        return await self._make_request(
            "GET",
            "resources/trading_status",
            cache_key="trading_status",
            cache_ttl=2.0  # 2 second cache
        )
    
    async def get_market_overview(self) -> Dict[str, Any]:
        """Get market overview with caching."""
        return await self._make_request(
            "GET",
            "resources/market_overview",
            cache_key="market_overview",
            cache_ttl=5.0  # 5 second cache
        )
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get client performance statistics."""
        if self.request_count == 0:
            return {
                "requests": 0,
                "avg_latency": 0,
                "max_latency": 0,
                "min_latency": 0
            }
        
        return {
            "requests": self.request_count,
            "avg_latency": self.total_latency / self.request_count,
            "max_latency": self.max_latency,
            "min_latency": self.min_latency,
            "cache_entries": len(self._request_cache)
        }
    
    def clear_cache(self):
        """Clear all cached data."""
        self._request_cache.clear()
        self._cache_ttl.clear()

# Singleton instance for global use
_mcp_client_instance: Optional[OptimizedMCPClient] = None

async def get_mcp_client() -> OptimizedMCPClient:
    """Get or create MCP client singleton."""
    global _mcp_client_instance
    
    if _mcp_client_instance is None:
        _mcp_client_instance = OptimizedMCPClient()
        await _mcp_client_instance.connect()
    
    return _mcp_client_instance

async def cleanup_mcp_client():
    """Cleanup MCP client singleton."""
    global _mcp_client_instance
    
    if _mcp_client_instance:
        await _mcp_client_instance.disconnect()
        _mcp_client_instance = None
