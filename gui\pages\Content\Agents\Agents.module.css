.container {
    height: 100%;
    width: 100%;
    padding: 0 0 0 8px;
}

.title_box {
    width: 100%;
    padding: 8px;
    display: flex;
    align-items: center;
}

.title_text {
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 17px;
    display: flex;
    align-items: center;
    color: white;
}

.wrapper {
    margin-bottom: 5px;
    width: 100%;
}

.agent_active {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0;
}

.text_block {
    display:flex;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}

.form_label {
    font-size: 13px;
    margin-bottom: 4px;
    font-weight: 500;
    color: #888888;
    line-height: 17px;
}

.page_title {
    text-align: left;
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 17px;
    display: flex;
    align-items: center;
    color: white;
    margin-bottom: 25px;
}

.tool_text {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}

.detail_top {
    width: 100%;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 10px;
}

.detail_body {
    width: 100%;
    padding-right: 10px;
    margin-bottom: 20px;
}

.detail_content {
    height: calc(100vh - 140px);
    border-radius: 8px;
    overflow-y: scroll;
    padding-bottom: 0;
}

.tab_button {
    border: none;
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 15px;
    color: #FFFFFF;
    border-radius: 8px;
    padding: 8px 10px;
    display: -webkit-inline-flex;
    align-items: center;
    justify-content: center;
}

.tab_text {
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 15px;
    color: #FFFFFF;
    padding: 8px;
}

.tab_button:hover {
    background: #454254;
}
.run_history_button{
    background: rgba(255, 255, 255, 0.14);
    border: none;
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 12px;
    color: #FFFFFF;
    border-radius: 8px;
    padding: 8px 10px 8px 5px;
}
.run_button {
    background: #62A168;
    border: none;
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 15px;
    color: #FFFFFF;
    border-radius: 8px;
    padding: 7px 10px 8px 5px;
    height: 31px;
    margin-right: 7px;
}

.run_button:hover {
    background: #57825b;
}

.pause_button {
    background: #F78166;
    border: none;
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 15px;
    color: #FFFFFF;
    border-radius: 8px;
    padding: 8px 10px;
}

.pause_button:hover {
    background: #C95034;
}

.history_box {
    width: 100%;
    padding: 10px;
    color: white;
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 14px;
    border-radius: 8px;
    cursor: pointer;
    margin-bottom: 7px;
}

.notification_bubble {
    width: 14px;
    height: 14px;
    background: #DC6261;
    border-radius: 200px;
    display: flex;
    align-items: center;
    text-align: center;
    padding: 4px;
    font-size: 9px;
    order: 1;
}

.history_info {
    font-style: normal;
    font-weight: 400;
    font-size: 10px;
    line-height: 12px;
    color: #888888;
    margin-left: 4px;
    margin-top: 3px;
}

.feed_title {
    font-family: 'Source Code Pro';
    margin-left: 10px;
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 15px;
    color: white;
    white-space: pre-line;
    word-wrap: break-word;
    max-width: 95%;
}

.feed_icon {
    font-size: 20px;
    margin-top: 5px;
}

.custom_task_box {
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 14px;
    color: white;
    width: 100%;
    border-radius: 8px;
    margin-bottom: 7px;
    padding: 15px 20px;
}

.console_icons {
    margin: -3px 3px 0 0;
}

.detail_name {
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 19px;
    color: #FFFFFF;
    margin-bottom: 10px;
}

.separator {
    height: 15px;
    margin-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.agent_info_box {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 10px;
}

.agent_info_tools {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: flex-start;
}

.resources {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.agent_resources {
    width: 100%;
    margin-top: 10px;
}

.large_text_box {
    -webkit-line-clamp: 5;
    -webkit-box-orient: vertical;
}

.show_more_button {
    margin-top: 10px;
    cursor: pointer;
    width: fit-content;
    color: #888888;
}

.single_line_block {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 80%;
}

.three_dots {
    margin-left: 5px;
    background: transparent;
    border: none;
    border-radius: 8px;
}

.more_details {
    display: flex;
    align-items: center;
    margin-right: 30px;
}

.more_details_wrapper {
    display: flex;
    align-items: center;
    margin-top: 20px;
    justify-content: flex-start;
}

.task_header {
    color: rgb(255, 255, 255);
    padding: 7px;
    font-size: 13px;
    font-weight: 500;
    font-family: 'Source Code Pro';
}

.text_12_n
{
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 14px;
    color: #FFFFFF;
}

.notification_circle
{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 4px;
    width: fit-content;
    padding: 2px 5px;
    height: fit-content;
    background: #DC6161;
    border-radius: 16px;

    font-style: normal;
    font-weight: 500;
    font-size: 10px;
    line-height: 12px;
    color: #FFFFFF;
}
.delete_agent_modal_label
{
    color: #FFF;
    font-size: 16px;
    font-family: "Public Sans", sans-serif;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.delete_button
{
    display: flex;
    padding: 0px 12px;
    align-items: flex-start;
    gap: 4px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.08);
    background: #FFF;
    color: #000;
    font-size: 12px;
    font-family: "Public Sans", sans-serif;
    font-style: normal;
    font-weight: 500;
    line-height: normal;

}

.delete_modal_text
{
    color: #888;
    font-size: 12px;
    font-family: "Roboto Flex", sans-serif;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}
  
.rdtPicker input {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    padding: 8px 14px 8px 14px !important;
    gap: 4px !important;
    border-radius: 8px !important;
    width: 100% !important;
    height: 32px !important;
    background: #3B3B49 !important;
    border: 1px solid #4A4A55 !important;
    font-style: normal !important;
    font-weight: 400 !important;
    font-size: 12px !important;
    line-height: 16px !important;
    transition: 0.2s !important;
    color: white !important;
}

.rdtPicker input:focus {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 8px 14px 8px 14px;
    gap: 4px;
    border-radius: 8px;
    width: 100%;
    height: 32px;
    background: #3B3B49;
    border: 1px solid #4A4A55;
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    transition: 0.2s;
    color: white;
    outline: none;
}

.permission_changes {
    color: #888888 !important;
    text-decoration: line-through;
    pointerEvents: none !important;
}

.modal_buttons{
    display: flex;
    justify-content: flex-end;
    margin-top: 20px
}

.modal_info_class{
    margin-left: -5px;
    margin-right: 5px;
}

.table_contents{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 40px;
    width: 100%
}

.create_settings_button{
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 10px
}

.button_margin{
    margin-top: -10px;
}

.dropdown_separator{
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    margin-left:-5px;
    width: 180px
}

.dropdown_container_agent{
    bottom: 40px;
    z-index: 9999;
    padding: 0px;
    width: fit-content;
    height: fit-content;
    margin-right: 40px;
    background: #3B3B49;
    border-radius: 8px;
    position: absolute;
    box-shadow: 0 2px 7px rgba(0,0,0,.4), 0 0 2px rgba(0,0,0,.22);
}

.dropdown_item_agent{
    height:30px;
    paddingTop: 2px;
    paddingBottom: 2px;
}