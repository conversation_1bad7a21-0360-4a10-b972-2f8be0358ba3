#!/bin/bash

# Mobile App Build Script for Motorola Moto G32
# Optimized for XT2235-2 running Android 13

echo "📱 Building Bybit Trading Bot for Motorola Moto G32 (XT2235-2)"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Check prerequisites
check_prerequisites() {
    echo -e "${CYAN}🔍 Checking prerequisites...${NC}"
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js not found. Please install Node.js 18+${NC}"
        exit 1
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}❌ npm not found${NC}"
        exit 1
    fi
    
    # Check React Native CLI
    if ! command -v npx &> /dev/null; then
        echo -e "${RED}❌ npx not found${NC}"
        exit 1
    fi
    
    # Check Java (for Android builds)
    if ! command -v javac &> /dev/null; then
        echo -e "${YELLOW}⚠️  Java not found. Android builds may fail${NC}"
    fi
    
    echo -e "${GREEN}✅ Prerequisites check completed${NC}"
}

# Setup environment
setup_environment() {
    echo -e "${CYAN}⚙️  Setting up environment for Moto G32...${NC}"
    
    # Set Android environment variables
    export ANDROID_HOME=$HOME/Android/Sdk
    export PATH=$PATH:$ANDROID_HOME/emulator
    export PATH=$PATH:$ANDROID_HOME/tools
    export PATH=$PATH:$ANDROID_HOME/tools/bin
    export PATH=$PATH:$ANDROID_HOME/platform-tools
    
    # Optimize for Moto G32 hardware
    export ANDROID_AVD_HOME=$HOME/.android/avd
    export GRADLE_OPTS="-Xmx4g -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8"
    
    echo -e "${GREEN}✅ Environment configured${NC}"
}

# Install dependencies
install_dependencies() {
    echo -e "${CYAN}📦 Installing dependencies...${NC}"
    
    cd mobile
    
    # Clean install for consistent builds
    rm -rf node_modules
    rm -f package-lock.json
    
    # Install with optimizations
    npm install --legacy-peer-deps
    
    # Install additional dependencies for Moto G32
    npm install react-native-device-info@^10.11.0
    npm install @react-native-community/netinfo@^11.2.1
    
    echo -e "${GREEN}✅ Dependencies installed${NC}"
}

# Configure for Moto G32
configure_for_moto_g32() {
    echo -e "${CYAN}🔧 Configuring for Motorola Moto G32...${NC}"
    
    # Create android/app/src/main/res/values/strings.xml if not exists
    mkdir -p android/app/src/main/res/values
    
    cat > android/app/src/main/res/values/strings.xml << EOF
<resources>
    <string name="app_name">Bybit Trading Bot</string>
    
    <!-- Optimized for Moto G32 display -->
    <string name="density_dpi">420</string>
    <string name="screen_size">normal</string>
    
    <!-- Network configuration -->
    <string name="api_base_url">http://91.179.83.180:8000</string>
</resources>
EOF

    # Configure gradle.properties for Moto G32
    cat > android/gradle.properties << EOF
# Optimization for Moto G32 (4GB RAM)
org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
org.gradle.parallel=true
org.gradle.configureondemand=true
org.gradle.daemon=true

# Android configuration
android.useAndroidX=true
android.enableJetifier=true

# Disable unnecessary features for faster builds
android.enableR8.fullMode=false
android.bundle.enableUncompressedNativeLibs=false

# Optimize for ARM64 (Moto G32 architecture)
android.bundle.enableUncompressedNativeLibs=false
android.bundle.language.enableSplit=false
android.bundle.density.enableSplit=false
android.bundle.abi.enableSplit=true

# Enable Hermes for better performance
hermesEnabled=true
EOF

    echo -e "${GREEN}✅ Moto G32 configuration completed${NC}"
}

# Build debug APK
build_debug() {
    echo -e "${CYAN}🔨 Building debug APK for Moto G32...${NC}"
    
    cd android
    
    # Clean previous builds
    ./gradlew clean
    
    # Build debug APK
    ./gradlew assembleDebug
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Debug APK built successfully${NC}"
        echo -e "${YELLOW}📁 APK location: android/app/build/outputs/apk/debug/app-debug.apk${NC}"
    else
        echo -e "${RED}❌ Debug build failed${NC}"
        exit 1
    fi
}

# Build release APK
build_release() {
    echo -e "${CYAN}🔨 Building release APK for Moto G32...${NC}"
    
    cd android
    
    # Clean previous builds
    ./gradlew clean
    
    # Build release APK (unsigned)
    ./gradlew assembleRelease
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Release APK built successfully${NC}"
        echo -e "${YELLOW}📁 APK location: android/app/build/outputs/apk/release/app-release-unsigned.apk${NC}"
        echo -e "${YELLOW}⚠️  Note: This APK is unsigned. You'll need to sign it for production.${NC}"
    else
        echo -e "${RED}❌ Release build failed${NC}"
        exit 1
    fi
}

# Install on connected device
install_on_device() {
    echo -e "${CYAN}📱 Installing on connected Moto G32...${NC}"
    
    # Check if device is connected
    if ! command -v adb &> /dev/null; then
        echo -e "${RED}❌ ADB not found. Please install Android SDK platform-tools${NC}"
        exit 1
    fi
    
    # Check connected devices
    devices=$(adb devices | grep -v "List of devices attached" | grep "device$" | wc -l)
    
    if [ $devices -eq 0 ]; then
        echo -e "${RED}❌ No Android devices connected${NC}"
        echo -e "${YELLOW}📱 Please connect your Motorola Moto G32 and enable USB debugging${NC}"
        exit 1
    fi
    
    # Install debug APK
    echo -e "${CYAN}Installing debug APK...${NC}"
    adb install -r android/app/build/outputs/apk/debug/app-debug.apk
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ App installed successfully on Moto G32${NC}"
        echo -e "${CYAN}🚀 Launching app...${NC}"
        adb shell am start -n com.bybitbot.mobile/.MainActivity
    else
        echo -e "${RED}❌ Installation failed${NC}"
        exit 1
    fi
}

# Main build process
main() {
    echo -e "${GREEN}🚀 Starting build process for Motorola Moto G32${NC}"
    echo "Device: XT2235-2"
    echo "Android Version: 13"
    echo "Architecture: ARM64"
    echo "Enhanced Features: Logging Controls, Multiple Pages, Easy UI"
    echo ""
    
    check_prerequisites
    setup_environment
    install_dependencies
    configure_for_moto_g32
    
    # Ask user what to build
    echo -e "${CYAN}What would you like to build?${NC}"
    echo "1) Debug APK"
    echo "2) Release APK"
    echo "3) Build and install on connected device"
    echo "4) All of the above"
    
    read -p "Enter your choice (1-4): " choice
    
    case $choice in
        1)
            build_debug
            ;;
        2)
            build_release
            ;;
        3)
            build_debug
            install_on_device
            ;;
        4)
            build_debug
            build_release
            install_on_device
            ;;
        *)
            echo -e "${RED}❌ Invalid choice${NC}"
            exit 1
            ;;
    esac
    
    echo -e "${GREEN}🎉 Build process completed successfully!${NC}"
    echo -e "${CYAN}📱 Your Bybit Trading Bot is ready for Motorola Moto G32${NC}"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo -e "${RED}❌ Please run this script from the mobile directory${NC}"
    exit 1
fi

# Run main function
main "$@"
