FROM python:3.9

WORKDIR /app

#RUN apt-get update && apt-get install --no-install-recommends -y git wget libpq-dev gcc python3-dev && pip install psycopg2
RUN pip install --upgrade pip

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

# Downloads the tools
RUN python superagi/tool_manager.py

# Set executable permissions for install_tool_dependencies.sh
RUN chmod +x install_tool_dependencies.sh

# Install dependencies
RUN ./install_tool_dependencies.sh

# Downloads the tools
RUN python superagi/tool_manager.py

# Set executable permissions for install_tool_dependencies.sh
RUN chmod +x install_tool_dependencies.sh

# Install dependencies
RUN ./install_tool_dependencies.sh


CMD ["celery", "-A", "superagi.worker", "worker", "--beat","--loglevel=info"]
