#!/usr/bin/env python3
"""
SYSTEM VERIFICATION - Test all repaired components
"""
import os
import sys
import sqlite3
import asyncio
from pathlib import Path

async def verify_system_repairs():
    """Verify all system repairs are working"""
    
    print("SYSTEM VERIFICATION - TESTING ALL REPAIRS")
    print("=" * 50)
    
    # Change to correct directory
    try:
        os.chdir(r"e:\The_real_deal_copy\Bybit_Bot\BOT")
        print(f"✓ Working directory: {os.getcwd()}")
    except Exception as e:
        print(f"✗ Directory error: {e}")
        return False
    
    verification_results = []
    
    # Test 1: Database Tables
    print("\n[TEST 1] Database Table Verification")
    print("-" * 30)
    try:
        conn = sqlite3.connect("bybit_trading_bot.db")
        cursor = conn.cursor()
        
        # Check system_state table
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='system_state'")
        if cursor.fetchone():
            print("✓ system_state table exists")
            verification_results.append(True)
        else:
            print("✗ system_state table missing")
            verification_results.append(False)
        
        # Test insert into system_state
        cursor.execute("""
            INSERT OR REPLACE INTO system_state 
            (component, last_run_timestamp, state_data) 
            VALUES ('verification_test', CURRENT_TIMESTAMP, '{"status": "verified"}')
        """)
        conn.commit()
        print("✓ system_state table insert works")
        
        conn.close()
        
    except Exception as e:
        print(f"✗ Database test failed: {e}")
        verification_results.append(False)
    
    # Test 2: Enhanced Client Methods
    print("\n[TEST 2] Enhanced Client Method Verification")
    print("-" * 30)
    try:
        # Add bot directory to Python path
        bot_dir = Path.cwd()
        if str(bot_dir) not in sys.path:
            sys.path.insert(0, str(bot_dir))
        
        from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
        
        # Check if methods exist
        methods_to_check = ["get_account_balance", "get_market_data"]
        for method in methods_to_check:
            if hasattr(EnhancedBybitClient, method):
                print(f"✓ {method} method exists")
                verification_results.append(True)
            else:
                print(f"✗ {method} method missing")
                verification_results.append(False)
        
    except Exception as e:
        print(f"✗ Enhanced client test failed: {e}")
        verification_results.append(False)
    
    # Test 3: AI System Components
    print("\n[TEST 3] AI System Component Verification")
    print("-" * 30)
    try:
        from bybit_bot.ai.intelligent_ml_system import IntelligentMLSystem
        print("✓ IntelligentMLSystem import successful")
        verification_results.append(True)
        
        from bybit_bot.ai.openrouter_client import OpenRouterClient
        print("✓ OpenRouterClient import successful")
        verification_results.append(True)
        
    except Exception as e:
        print(f"✗ AI system test failed: {e}")
        verification_results.append(False)
    
    # Test 4: Configuration File
    print("\n[TEST 4] Configuration File Verification")
    print("-" * 30)
    try:
        config_path = Path("config.yaml")
        if config_path.exists():
            print("✓ config.yaml exists")
            
            with open(config_path, 'r') as f:
                content = f.read()
            
            if 'bybit:' in content or 'bybit ' in content:
                print("✓ bybit configuration found")
                verification_results.append(True)
            else:
                print("✗ bybit configuration missing")
                verification_results.append(False)
        else:
            print("✗ config.yaml not found")
            verification_results.append(False)
            
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        verification_results.append(False)
    
    # Test 5: Ultra Profit Amplification Engine
    print("\n[TEST 5] Ultra Profit Amplification Engine")
    print("-" * 30)
    try:
        from bybit_bot.strategies.ultra_profit_amplification_engine import UltraProfitAmplificationEngine
        print("✓ UltraProfitAmplificationEngine import successful")
        verification_results.append(True)
        
    except Exception as e:
        print(f"✗ Ultra Profit engine test failed: {e}")
        verification_results.append(False)
    
    # Final Results
    print("\n" + "=" * 50)
    print("VERIFICATION RESULTS")
    print("=" * 50)
    
    passed = sum(verification_results)
    total = len(verification_results)
    
    print(f"TESTS PASSED: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL VERIFICATIONS SUCCESSFUL!")
        print("System is fully operational and ready for trading!")
        return True
    else:
        failed = total - passed
        print(f"⚠️  {failed} verifications failed")
        print("Some components may need additional attention")
        return False

if __name__ == "__main__":
    print("BYBIT BOT SYSTEM VERIFICATION")
    print("Testing all repaired components...")
    
    try:
        success = asyncio.run(verify_system_repairs())
        
        if success:
            print("\n✅ SYSTEM VERIFICATION COMPLETE - ALL SYSTEMS GO!")
            sys.exit(0)
        else:
            print("\n⚠️  SYSTEM VERIFICATION INCOMPLETE - SOME ISSUES REMAIN")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ VERIFICATION ERROR: {e}")
        sys.exit(1)
