/**
 * Logging Service for Mobile App
 * Manages logging configuration and log retrieval for the trading bot
 */

export interface LogEntry {
    timestamp: string;
    level: string;
    message: string;
    category: string;
}

export interface LoggingCategory {
    id: string;
    name: string;
    description: string;
    icon: string;
}

export interface LoggingConfig {
    categories: {
        [key: string]: {
            enabled: boolean;
            level: string;
            logger_name: string;
        };
    };
    log_levels: string[];
    master_enabled: boolean;
}

export interface LogsResponse {
    logs: LogEntry[];
    total: number;
    category?: string;
    limit?: number;
    offset?: number;
}

export interface LogStats {
    categories: {
        [key: string]: {
            file_size: number;
            file_size_mb: number;
            line_count: number;
            exists: boolean;
        };
    };
    total_size: number;
    total_size_mb: number;
    total_lines: number;
}

class LoggingService {
    private baseURL: string;

    constructor() {
        this.baseURL = 'http://*************:8000';
    }

    private async request(endpoint: string, options: RequestInit = {}): Promise<any> {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 15000);

        try {
            const response = await fetch(`${this.baseURL}${endpoint}`, {
                ...options,
                signal: controller.signal,
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'BybitTradingBot-Mobile/1.0.0 (Android 13; Motorola Moto G32)',
                    'X-Device-Model': 'XT2235-2',
                    'X-Platform': 'android',
                    ...options.headers,
                },
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            clearTimeout(timeoutId);
            if (error instanceof Error) {
                if (error.name === 'AbortError') {
                    throw new Error('Request timeout - please check your connection');
                }
                if (error.message.includes('Network request failed')) {
                    throw new Error('Connection failed - check network connectivity');
                }
            }
            throw error;
        }
    }

    /**
     * Get current logging configuration
     */
    async getLoggingConfig(): Promise<LoggingConfig> {
        return this.request('/api/logging/config');
    }

    /**
     * Update logging configuration
     */
    async updateLoggingConfig(config: Partial<LoggingConfig>): Promise<{ status: string; message: string }> {
        return this.request('/api/logging/config', {
            method: 'PUT',
            body: JSON.stringify(config),
        });
    }

    /**
     * Get available logging categories
     */
    async getLoggingCategories(): Promise<{ categories: LoggingCategory[] }> {
        return this.request('/api/logging/categories');
    }

    /**
     * Get logs for a specific category
     */
    async getLogsByCategory(
        category: string,
        limit: number = 100,
        offset: number = 0
    ): Promise<LogsResponse> {
        return this.request(`/api/logs/${category}?limit=${limit}&offset=${offset}`);
    }

    /**
     * Get recent logs across all categories
     */
    async getRecentLogs(limit: number = 50): Promise<LogsResponse> {
        return this.request(`/api/logs/recent?limit=${limit}`);
    }

    /**
     * Clear logs for a specific category
     */
    async clearLogsByCategory(category: string): Promise<{ status: string; message: string }> {
        return this.request(`/api/logs/clear/${category}`, {
            method: 'POST',
        });
    }

    /**
     * Get logging statistics
     */
    async getLoggingStats(): Promise<LogStats> {
        return this.request('/api/logs/stats');
    }

    /**
     * Toggle logging for a specific category
     */
    async toggleCategoryLogging(category: string, enabled: boolean): Promise<void> {
        const config = await this.getLoggingConfig();
        
        if (config.categories[category]) {
            config.categories[category].enabled = enabled;
            await this.updateLoggingConfig(config);
        }
    }

    /**
     * Update log level for a specific category
     */
    async updateCategoryLogLevel(category: string, level: string): Promise<void> {
        const config = await this.getLoggingConfig();
        
        if (config.categories[category]) {
            config.categories[category].level = level;
            await this.updateLoggingConfig(config);
        }
    }

    /**
     * Toggle master logging (enable/disable all logging)
     */
    async toggleMasterLogging(enabled: boolean): Promise<void> {
        const config = await this.getLoggingConfig();
        config.master_enabled = enabled;
        
        // Also toggle all categories
        Object.keys(config.categories).forEach(category => {
            config.categories[category].enabled = enabled;
        });
        
        await this.updateLoggingConfig(config);
    }

    /**
     * Search logs by text
     */
    async searchLogs(
        query: string,
        category?: string,
        limit: number = 100
    ): Promise<LogEntry[]> {
        let logs: LogEntry[] = [];
        
        if (category) {
            const response = await this.getLogsByCategory(category, limit);
            logs = response.logs;
        } else {
            const response = await this.getRecentLogs(limit);
            logs = response.logs;
        }
        
        // Filter logs by search query
        return logs.filter(log =>
            log.message.toLowerCase().includes(query.toLowerCase()) ||
            log.level.toLowerCase().includes(query.toLowerCase())
        );
    }

    /**
     * Get logs by level
     */
    async getLogsByLevel(
        level: string,
        category?: string,
        limit: number = 100
    ): Promise<LogEntry[]> {
        let logs: LogEntry[] = [];
        
        if (category) {
            const response = await this.getLogsByCategory(category, limit);
            logs = response.logs;
        } else {
            const response = await this.getRecentLogs(limit);
            logs = response.logs;
        }
        
        // Filter logs by level
        return logs.filter(log => log.level === level);
    }

    /**
     * Export logs for a category (returns formatted text)
     */
    async exportLogs(category: string): Promise<string> {
        const response = await this.getLogsByCategory(category, 1000);
        
        let exportText = `# ${category.toUpperCase()} LOGS\n`;
        exportText += `# Exported: ${new Date().toISOString()}\n`;
        exportText += `# Total entries: ${response.total}\n\n`;
        
        response.logs.forEach(log => {
            exportText += `${log.timestamp} | ${log.level} | ${log.message}\n`;
        });
        
        return exportText;
    }
}

export default new LoggingService();
