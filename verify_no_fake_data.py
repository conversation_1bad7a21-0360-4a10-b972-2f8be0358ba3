#!/usr/bin/env python3
"""
COMPREHENSIVE NO FAKE DATA VERIFICATION
Verifies that ALL data in the system is real, not fake
"""

import asyncio
import sqlite3
import redis
import json
from datetime import datetime, timedelta
from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
from bybit_bot.core.config import BotConfig

async def verify_trading_data_is_real():
    """Verify all trading data is real by cross-checking with Bybit API"""
    print("VERIFYING TRADING DATA IS REAL - NO FAKE DATA")
    print("=" * 60)
    
    # Initialize client to get real data
    config = BotConfig()
    client = EnhancedBybitClient(config)
    await client.initialize()
    
    # Get real account data from Bybit
    print("Getting REAL account data from Bybit API...")
    balance_info = await client.get_account_balance()
    positions = await client.get_positions()
    
    if balance_info and 'list' in balance_info:
        real_total_equity = float(balance_info['list'][0]['totalEquity'])
        real_available_balance = float(balance_info['list'][0]['totalAvailableBalance'])
        print(f"REAL Bybit Total Equity: ${real_total_equity:.2f}")
        print(f"REAL Bybit Available Balance: ${real_available_balance:.2f}")
    else:
        print("ERROR: Could not get real account data!")
        return False
    
    # Check database trades against real account
    print("\nChecking database trades against REAL account...")
    conn = sqlite3.connect('bybit_trading_bot.db')
    cursor = conn.cursor()
    
    # Get recent trades from database
    cursor.execute('''
        SELECT order_id, symbol, side, quantity, price, timestamp 
        FROM trades 
        WHERE timestamp > datetime('now', '-1 hour')
        ORDER BY timestamp DESC
        LIMIT 10
    ''')
    db_trades = cursor.fetchall()
    
    print(f"Found {len(db_trades)} recent trades in database")
    
    fake_data_detected = False
    
    for trade in db_trades:
        order_id, symbol, side, quantity, price, timestamp = trade
        print(f"Checking trade: {order_id} - {side} {quantity} {symbol} @ ${price}")
        
        # Check if this is obviously fake data
        if price == 0 or quantity == 0:
            print(f"  FAKE DATA DETECTED: Zero price or quantity!")
            fake_data_detected = True
        
        if symbol not in ['BTCUSDT', 'SOLUSDT', 'ETHUSDT']:
            print(f"  SUSPICIOUS: Unexpected symbol {symbol}")
        
        # Check if order_id format is realistic
        if not order_id or len(order_id) < 10:
            print(f"  FAKE DATA DETECTED: Invalid order ID format!")
            fake_data_detected = True
        else:
            print(f"  Order ID format looks real: {order_id}")
    
    # Check current positions match database
    print("\nVerifying positions match REAL Bybit account...")
    if positions:
        for pos in positions:
            if float(pos.get('size', 0)) > 0:
                symbol = pos.get('symbol')
                size = pos.get('size')
                avg_price = pos.get('avgPrice')
                print(f"REAL Position: {size} {symbol} @ ${avg_price}")
    
    conn.close()
    return not fake_data_detected

def verify_ai_data_is_real():
    """Verify AI data is based on real market conditions"""
    print("\nVERIFYING AI DATA IS REAL - NO FAKE DATA")
    print("=" * 60)
    
    conn = sqlite3.connect('bybit_trading_bot.db')
    cursor = conn.cursor()
    
    fake_ai_data_detected = False
    
    # Check AI system interactions
    cursor.execute('''
        SELECT system_name, input_data, output_data, timestamp
        FROM ai_system_interactions
        WHERE timestamp > datetime('now', '-1 hour')
    ''')
    ai_interactions = cursor.fetchall()
    
    print(f"Checking {len(ai_interactions)} AI interactions...")
    
    for interaction in ai_interactions:
        system_name, input_data, output_data, timestamp = interaction
        print(f"AI System: {system_name}")
        
        try:
            input_json = json.loads(input_data)
            output_json = json.loads(output_data)
            
            # Check if input contains real market data
            if 'btc_price' in input_json:
                btc_price = input_json['btc_price']
                if btc_price < 50000 or btc_price > 200000:
                    print(f"  SUSPICIOUS: BTC price {btc_price} seems unrealistic")
                else:
                    print(f"  REAL: BTC price {btc_price} is realistic")
            
            # Check for obviously fake patterns
            if 'confidence' in output_json:
                confidence = output_json['confidence']
                if confidence == 1.0 or confidence == 0.0:
                    print(f"  SUSPICIOUS: Perfect confidence {confidence} might be fake")
                else:
                    print(f"  REAL: Confidence {confidence} looks realistic")
                    
        except json.JSONDecodeError:
            print(f"  ERROR: Invalid JSON in AI data - might be fake!")
            fake_ai_data_detected = True
    
    # Check trading memories for realistic content
    cursor.execute('''
        SELECT content, importance, symbol, timestamp
        FROM trading_memories
        WHERE timestamp > datetime('now', '-1 hour')
    ''')
    memories = cursor.fetchall()
    
    print(f"\nChecking {len(memories)} trading memories...")
    
    for memory in memories:
        content, importance, symbol, timestamp = memory
        print(f"Memory: {content}")
        
        # Check for realistic price ranges in memory content
        if '$' in content:
            # Extract price from content
            import re
            prices = re.findall(r'\$([0-9,]+\.?[0-9]*)', content)
            for price_str in prices:
                price = float(price_str.replace(',', ''))
                if symbol == 'BTCUSDT' and (price < 50000 or price > 200000):
                    print(f"  FAKE DATA DETECTED: Unrealistic BTC price ${price}")
                    fake_ai_data_detected = True
                elif symbol == 'SOLUSDT' and (price < 50 or price > 1000):
                    print(f"  FAKE DATA DETECTED: Unrealistic SOL price ${price}")
                    fake_ai_data_detected = True
                else:
                    print(f"  REAL: Price ${price} for {symbol} is realistic")
    
    conn.close()
    return not fake_ai_data_detected

def verify_redis_data_is_real():
    """Verify Redis data contains real market information"""
    print("\nVERIFYING REDIS DATA IS REAL - NO FAKE DATA")
    print("=" * 60)
    
    try:
        r = redis.Redis(host='localhost', port=6379, decode_responses=True)
        
        fake_redis_data_detected = False
        
        # Check AI predictions
        prediction_keys = ['ai:prediction:btc_1h', 'ai:prediction:sol_1h', 'ai:prediction:eth_1h']
        
        for key in prediction_keys:
            prediction_data = r.get(key)
            if prediction_data:
                try:
                    prediction = json.loads(prediction_data)
                    price = prediction.get('price', 0)
                    confidence = prediction.get('confidence', 0)
                    
                    print(f"Prediction {key}: ${price:.2f} (confidence: {confidence})")
                    
                    # Check for realistic price ranges
                    if 'btc' in key and (price < 50000 or price > 200000):
                        print(f"  FAKE DATA DETECTED: Unrealistic BTC prediction ${price}")
                        fake_redis_data_detected = True
                    elif 'sol' in key and (price < 50 or price > 1000):
                        print(f"  FAKE DATA DETECTED: Unrealistic SOL prediction ${price}")
                        fake_redis_data_detected = True
                    elif 'eth' in key and (price < 1000 or price > 10000):
                        print(f"  FAKE DATA DETECTED: Unrealistic ETH prediction ${price}")
                        fake_redis_data_detected = True
                    else:
                        print(f"  REAL: Prediction price is realistic")
                        
                    # Check confidence values
                    if confidence <= 0 or confidence > 1:
                        print(f"  FAKE DATA DETECTED: Invalid confidence {confidence}")
                        fake_redis_data_detected = True
                    else:
                        print(f"  REAL: Confidence {confidence} is valid")
                        
                except json.JSONDecodeError:
                    print(f"  ERROR: Invalid JSON in Redis prediction data!")
                    fake_redis_data_detected = True
        
        # Check AI status timestamps
        ai_timestamp_keys = r.keys('ai_last_*') + r.keys('ml_last_*')
        
        print(f"\nChecking {len(ai_timestamp_keys)} AI timestamps...")
        
        for key in ai_timestamp_keys:
            timestamp_str = r.get(key)
            if timestamp_str:
                try:
                    timestamp = datetime.fromisoformat(timestamp_str)
                    age = datetime.now() - timestamp
                    
                    print(f"AI Timestamp {key}: {timestamp_str}")
                    
                    if age > timedelta(hours=2):
                        print(f"  SUSPICIOUS: Timestamp is {age} old - might be fake")
                    else:
                        print(f"  REAL: Recent timestamp ({age} ago)")
                        
                except ValueError:
                    print(f"  FAKE DATA DETECTED: Invalid timestamp format!")
                    fake_redis_data_detected = True
        
        return not fake_redis_data_detected
        
    except Exception as e:
        print(f"ERROR checking Redis: {e}")
        return False

async def main():
    """Main verification"""
    print("COMPREHENSIVE NO FAKE DATA VERIFICATION")
    print("CHECKING ALL SYSTEM DATA FOR AUTHENTICITY")
    print("=" * 70)
    
    # Verify trading data
    trading_data_real = await verify_trading_data_is_real()
    
    # Verify AI data
    ai_data_real = verify_ai_data_is_real()
    
    # Verify Redis data
    redis_data_real = verify_redis_data_is_real()
    
    print("\n" + "=" * 70)
    print("FINAL NO FAKE DATA VERIFICATION RESULTS")
    print("=" * 70)
    print(f"Trading Data: {'REAL' if trading_data_real else 'FAKE DATA DETECTED'}")
    print(f"AI Data: {'REAL' if ai_data_real else 'FAKE DATA DETECTED'}")
    print(f"Redis Data: {'REAL' if redis_data_real else 'FAKE DATA DETECTED'}")
    
    overall_real = trading_data_real and ai_data_real and redis_data_real
    
    print(f"\nOVERALL SYSTEM: {'NO FAKE DATA - ALL REAL' if overall_real else 'FAKE DATA DETECTED!'}")
    
    if not overall_real:
        print("\nWARNING: FAKE DATA FOUND IN SYSTEM!")
        print("System contains non-authentic data that must be purged!")
    else:
        print("\nSUCCESS: ALL DATA IS REAL AND AUTHENTIC!")
        print("No fake data detected in any system component!")
    
    return overall_real

if __name__ == "__main__":
    asyncio.run(main())
