import React, { useState } from 'react';
import { StyleSheet, View, ScrollView, RefreshControl, Dimensions } from 'react-native';
import { Text, Card, DataTable, SegmentedButtons } from 'react-native-paper';
import { useQuery } from '@tanstack/react-query';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';
import apiService from '../services/api';
import { theme } from '../styles/theme';

const { width } = Dimensions.get('window');

const PortfolioScreen: React.FC = () => {
    const [refreshing, setRefreshing] = useState(false);
    const [activeTab, setActiveTab] = useState('overview');

    const {
        data: portfolioData,
        isLoading,
        error,
        refetch,
    } = useQuery({
        queryKey: ['portfolio'],
        queryFn: apiService.getPortfolioData,
        refetchInterval: 10000, // Refresh every 10 seconds
    });

    const handleRefresh = async () => {
        setRefreshing(true);
        await refetch();
        setRefreshing(false);
    };

    if (isLoading && !portfolioData) {
        return <LoadingSpinner message="Loading portfolio data..." />;
    }

    if (error) {
        return <ErrorMessage error={error} onRetry={refetch} />;
    }

    const balances = portfolioData?.balances || [];
    const transactions = portfolioData?.transactions || [];
    const performance = portfolioData?.performance || {};

    const renderOverview = () => (
        <View>
            {/* Portfolio Summary */}
            <Card style={styles.card}>
                <Card.Content>
                    <Text variant="titleMedium" style={styles.cardTitle}>
                        Portfolio Summary
                    </Text>
                    <View style={styles.summaryGrid}>
                        <View style={styles.summaryItem}>
                            <Text variant="headlineMedium" style={styles.summaryValue}>
                                ${portfolioData?.totalValue?.toFixed(2) || '0.00'}
                            </Text>
                            <Text variant="bodySmall" style={styles.summaryLabel}>
                                Total Portfolio Value
                            </Text>
                        </View>
                        <View style={styles.summaryItem}>
                            <Text 
                                variant="headlineSmall" 
                                style={[
                                    styles.summaryValue,
                                    { color: (portfolioData?.totalPnL || 0) >= 0 ? theme.colors.primary : theme.colors.error }
                                ]}
                            >
                                ${portfolioData?.totalPnL?.toFixed(2) || '0.00'}
                            </Text>
                            <Text variant="bodySmall" style={styles.summaryLabel}>
                                Total P&L
                            </Text>
                        </View>
                        <View style={styles.summaryItem}>
                            <Text 
                                variant="headlineSmall" 
                                style={[
                                    styles.summaryValue,
                                    { color: (portfolioData?.dailyPnL || 0) >= 0 ? theme.colors.primary : theme.colors.error }
                                ]}
                            >
                                ${portfolioData?.dailyPnL?.toFixed(2) || '0.00'}
                            </Text>
                            <Text variant="bodySmall" style={styles.summaryLabel}>
                                Daily P&L
                            </Text>
                        </View>
                        <View style={styles.summaryItem}>
                            <Text variant="headlineSmall" style={styles.summaryValue}>
                                {portfolioData?.availableBalance?.toFixed(2) || '0.00'}
                            </Text>
                            <Text variant="bodySmall" style={styles.summaryLabel}>
                                Available Balance (USDT)
                            </Text>
                        </View>
                    </View>
                </Card.Content>
            </Card>

            {/* Asset Allocation */}
            <Card style={styles.card}>
                <Card.Content>
                    <Text variant="titleMedium" style={styles.cardTitle}>
                        Asset Allocation
                    </Text>
                    {balances.length > 0 ? (
                        <DataTable>
                            <DataTable.Header>
                                <DataTable.Title>Asset</DataTable.Title>
                                <DataTable.Title numeric>Balance</DataTable.Title>
                                <DataTable.Title numeric>Value (USDT)</DataTable.Title>
                                <DataTable.Title numeric>%</DataTable.Title>
                            </DataTable.Header>
                            {balances.map((balance: any, index: number) => (
                                <DataTable.Row key={index}>
                                    <DataTable.Cell>
                                        <View style={styles.assetCell}>
                                            <Text variant="titleSmall" style={styles.assetSymbol}>
                                                {balance.coin}
                                            </Text>
                                        </View>
                                    </DataTable.Cell>
                                    <DataTable.Cell numeric>
                                        {parseFloat(balance.walletBalance).toFixed(4)}
                                    </DataTable.Cell>
                                    <DataTable.Cell numeric>
                                        ${(balance.usdValue || 0).toFixed(2)}
                                    </DataTable.Cell>
                                    <DataTable.Cell numeric>
                                        {((balance.usdValue / (portfolioData?.totalValue || 1)) * 100).toFixed(1)}%
                                    </DataTable.Cell>
                                </DataTable.Row>
                            ))}
                        </DataTable>
                    ) : (
                        <View style={styles.emptyState}>
                            <Icon name="account-balance-wallet" size={48} color={theme.colors.outline} />
                            <Text variant="bodyMedium" style={styles.emptyText}>
                                No assets found
                            </Text>
                        </View>
                    )}
                </Card.Content>
            </Card>
        </View>
    );

    const renderPerformance = () => (
        <View>
            {/* Performance Metrics */}
            <Card style={styles.card}>
                <Card.Content>
                    <Text variant="titleMedium" style={styles.cardTitle}>
                        Performance Metrics
                    </Text>
                    <View style={styles.performanceGrid}>
                        <View style={styles.performanceItem}>
                            <Text variant="headlineSmall" style={styles.performanceValue}>
                                {performance.winRate?.toFixed(1) || '0.0'}%
                            </Text>
                            <Text variant="bodySmall" style={styles.performanceLabel}>
                                Win Rate
                            </Text>
                        </View>
                        <View style={styles.performanceItem}>
                            <Text variant="headlineSmall" style={styles.performanceValue}>
                                {performance.totalTrades || 0}
                            </Text>
                            <Text variant="bodySmall" style={styles.performanceLabel}>
                                Total Trades
                            </Text>
                        </View>
                        <View style={styles.performanceItem}>
                            <Text variant="headlineSmall" style={styles.performanceValue}>
                                {performance.avgProfit?.toFixed(2) || '0.00'}
                            </Text>
                            <Text variant="bodySmall" style={styles.performanceLabel}>
                                Avg Profit (USDT)
                            </Text>
                        </View>
                        <View style={styles.performanceItem}>
                            <Text variant="headlineSmall" style={styles.performanceValue}>
                                {performance.maxDrawdown?.toFixed(2) || '0.00'}%
                            </Text>
                            <Text variant="bodySmall" style={styles.performanceLabel}>
                                Max Drawdown
                            </Text>
                        </View>
                        <View style={styles.performanceItem}>
                            <Text variant="headlineSmall" style={styles.performanceValue}>
                                {performance.sharpeRatio?.toFixed(2) || '0.00'}
                            </Text>
                            <Text variant="bodySmall" style={styles.performanceLabel}>
                                Sharpe Ratio
                            </Text>
                        </View>
                        <View style={styles.performanceItem}>
                            <Text variant="headlineSmall" style={styles.performanceValue}>
                                {performance.profitFactor?.toFixed(2) || '0.00'}
                            </Text>
                            <Text variant="bodySmall" style={styles.performanceLabel}>
                                Profit Factor
                            </Text>
                        </View>
                    </View>
                </Card.Content>
            </Card>
        </View>
    );

    const renderTransactions = () => (
        <View>
            {/* Recent Transactions */}
            <Card style={styles.card}>
                <Card.Content>
                    <Text variant="titleMedium" style={styles.cardTitle}>
                        Recent Transactions
                    </Text>
                    {transactions.length > 0 ? (
                        <DataTable>
                            <DataTable.Header>
                                <DataTable.Title>Type</DataTable.Title>
                                <DataTable.Title>Symbol</DataTable.Title>
                                <DataTable.Title numeric>Amount</DataTable.Title>
                                <DataTable.Title numeric>Price</DataTable.Title>
                            </DataTable.Header>
                            {transactions.slice(0, 10).map((transaction: any, index: number) => (
                                <DataTable.Row key={index}>
                                    <DataTable.Cell>
                                        <View style={styles.typeCell}>
                                            <Icon
                                                name={transaction.type === 'buy' ? 'trending-up' : 'trending-down'}
                                                size={16}
                                                color={transaction.type === 'buy' ? theme.colors.primary : theme.colors.error}
                                            />
                                            <Text style={[
                                                styles.typeText,
                                                { color: transaction.type === 'buy' ? theme.colors.primary : theme.colors.error }
                                            ]}>
                                                {transaction.type.toUpperCase()}
                                            </Text>
                                        </View>
                                    </DataTable.Cell>
                                    <DataTable.Cell>{transaction.symbol}</DataTable.Cell>
                                    <DataTable.Cell numeric>{transaction.amount}</DataTable.Cell>
                                    <DataTable.Cell numeric>${transaction.price}</DataTable.Cell>
                                </DataTable.Row>
                            ))}
                        </DataTable>
                    ) : (
                        <View style={styles.emptyState}>
                            <Icon name="receipt" size={48} color={theme.colors.outline} />
                            <Text variant="bodyMedium" style={styles.emptyText}>
                                No transactions found
                            </Text>
                        </View>
                    )}
                </Card.Content>
            </Card>
        </View>
    );

    const renderContent = () => {
        switch (activeTab) {
            case 'overview':
                return renderOverview();
            case 'performance':
                return renderPerformance();
            case 'transactions':
                return renderTransactions();
            default:
                return renderOverview();
        }
    };

    return (
        <View style={styles.container}>
            {/* Tab Navigation */}
            <View style={styles.tabContainer}>
                <SegmentedButtons
                    value={activeTab}
                    onValueChange={setActiveTab}
                    buttons={[
                        {
                            value: 'overview',
                            label: 'Overview',
                            icon: 'dashboard',
                        },
                        {
                            value: 'performance',
                            label: 'Performance',
                            icon: 'trending-up',
                        },
                        {
                            value: 'transactions',
                            label: 'History',
                            icon: 'history',
                        },
                    ]}
                    style={styles.segmentedButtons}
                />
            </View>

            {/* Content */}
            <ScrollView
                style={styles.scrollView}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={handleRefresh}
                        colors={[theme.colors.primary]}
                    />
                }
                showsVerticalScrollIndicator={false}
            >
                {renderContent()}
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: theme.colors.background,
    },
    tabContainer: {
        padding: 16,
        backgroundColor: theme.colors.surface,
        elevation: 2,
    },
    segmentedButtons: {
        backgroundColor: theme.colors.surfaceVariant,
    },
    scrollView: {
        flex: 1,
    },
    card: {
        marginHorizontal: 16,
        marginBottom: 16,
        backgroundColor: theme.colors.surface,
    },
    cardTitle: {
        color: theme.colors.onSurface,
        fontWeight: '700',
        marginBottom: 16,
    },
    summaryGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
    },
    summaryItem: {
        width: '48%',
        alignItems: 'center',
        marginBottom: 16,
        padding: 16,
        backgroundColor: theme.colors.surfaceVariant,
        borderRadius: 12,
    },
    summaryValue: {
        fontWeight: '700',
        marginBottom: 4,
        color: theme.colors.onSurface,
    },
    summaryLabel: {
        color: theme.colors.onSurfaceVariant,
        textAlign: 'center',
    },
    performanceGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
    },
    performanceItem: {
        width: '48%',
        alignItems: 'center',
        marginBottom: 16,
        padding: 12,
        backgroundColor: theme.colors.surfaceVariant,
        borderRadius: 8,
    },
    performanceValue: {
        color: theme.colors.primary,
        fontWeight: '700',
        marginBottom: 4,
    },
    performanceLabel: {
        color: theme.colors.onSurfaceVariant,
        textAlign: 'center',
        fontSize: 12,
    },
    assetCell: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    assetSymbol: {
        fontWeight: '600',
        color: theme.colors.onSurface,
    },
    typeCell: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 4,
    },
    typeText: {
        fontSize: 12,
        fontWeight: '600',
    },
    emptyState: {
        alignItems: 'center',
        padding: 32,
    },
    emptyText: {
        color: theme.colors.onSurfaceVariant,
        marginTop: 16,
    },
});

export default PortfolioScreen;
