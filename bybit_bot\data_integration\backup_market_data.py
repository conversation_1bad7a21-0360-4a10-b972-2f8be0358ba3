"""
Backup Market Data Sources for Real-Time Trading
Provides fallback data sources when primary Bybit API fails
"""

import asyncio
import aiohttp
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import time

class BackupMarketDataManager:
    """Manages backup market data sources for continuous trading"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.session: Optional[aiohttp.ClientSession] = None
        self.cache = {}
        self.cache_ttl = 5  # 5 seconds cache
        
        # API endpoints for backup data
        self.sources = {
            'coingecko': {
                'base_url': 'https://api.coingecko.com/api/v3',
                'rate_limit': 0.1,  # 10 requests per second
                'last_request': 0
            },
            'cryptocompare': {
                'base_url': 'https://min-api.cryptocompare.com/data',
                'rate_limit': 0.1,  # 10 requests per second
                'last_request': 0
            },
            'binance': {
                'base_url': 'https://api.binance.com/api/v3',
                'rate_limit': 0.05,  # 20 requests per second
                'last_request': 0
            }
        }
        
        # Symbol mapping for different exchanges
        self.symbol_mapping = {
            'BTCUSDT': {
                'coingecko': 'bitcoin',
                'cryptocompare': 'BTC',
                'binance': 'BTCUSDT'
            },
            'ETHUSDT': {
                'coingecko': 'ethereum',
                'cryptocompare': 'ETH',
                'binance': 'ETHUSDT'
            },
            'SOLUSDT': {
                'coingecko': 'solana',
                'cryptocompare': 'SOL',
                'binance': 'SOLUSDT'
            },
            'ADAUSDT': {
                'coingecko': 'cardano',
                'cryptocompare': 'ADA',
                'binance': 'ADAUSDT'
            },
            'DOTUSDT': {
                'coingecko': 'polkadot',
                'cryptocompare': 'DOT',
                'binance': 'DOTUSDT'
            },
            'LINKUSDT': {
                'coingecko': 'chainlink',
                'cryptocompare': 'LINK',
                'binance': 'LINKUSDT'
            },
            'AVAXUSDT': {
                'coingecko': 'avalanche-2',
                'cryptocompare': 'AVAX',
                'binance': 'AVAXUSDT'
            },
            'BNBUSDT': {
                'coingecko': 'binancecoin',
                'cryptocompare': 'BNB',
                'binance': 'BNBUSDT'
            },
            'XRPUSDT': {
                'coingecko': 'ripple',
                'cryptocompare': 'XRP',
                'binance': 'XRPUSDT'
            }
        }
    
    async def initialize(self):
        """Initialize the backup data manager"""
        self.session = aiohttp.ClientSession()
        self.logger.info("Backup market data manager initialized")
    
    async def close(self):
        """Close the session"""
        if self.session:
            await self.session.close()
    
    async def get_backup_price(self, symbol: str) -> Optional[float]:
        """Get backup price from multiple sources"""
        try:
            # Check cache first
            cache_key = f"price_{symbol}"
            if cache_key in self.cache:
                cached_data, timestamp = self.cache[cache_key]
                if time.time() - timestamp < self.cache_ttl:
                    return cached_data
            
            # Try multiple sources in order of preference
            price = None
            
            # Try Binance first (most reliable for crypto)
            price = await self._get_binance_price(symbol)
            if price:
                self.cache[cache_key] = (price, time.time())
                return price
            
            # Try CoinGecko
            price = await self._get_coingecko_price(symbol)
            if price:
                self.cache[cache_key] = (price, time.time())
                return price
            
            # Try CryptoCompare
            price = await self._get_cryptocompare_price(symbol)
            if price:
                self.cache[cache_key] = (price, time.time())
                return price
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting backup price for {symbol}: {e}")
            return None
    
    async def get_backup_order_book(self, symbol: str) -> Optional[Dict]:
        """Get backup order book data"""
        try:
            # Only Binance provides order book data in our backup sources
            return await self._get_binance_order_book(symbol)
        except Exception as e:
            self.logger.error(f"Error getting backup order book for {symbol}: {e}")
            return None
    
    async def _get_binance_price(self, symbol: str) -> Optional[float]:
        """Get price from Binance API"""
        try:
            if symbol not in self.symbol_mapping:
                return None
            
            binance_symbol = self.symbol_mapping[symbol]['binance']
            await self._rate_limit('binance')
            
            url = f"{self.sources['binance']['base_url']}/ticker/price"
            params = {'symbol': binance_symbol}
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return float(data['price'])
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting Binance price for {symbol}: {e}")
            return None
    
    async def _get_binance_order_book(self, symbol: str) -> Optional[Dict]:
        """Get order book from Binance API"""
        try:
            if symbol not in self.symbol_mapping:
                return None
            
            binance_symbol = self.symbol_mapping[symbol]['binance']
            await self._rate_limit('binance')
            
            url = f"{self.sources['binance']['base_url']}/depth"
            params = {'symbol': binance_symbol, 'limit': 20}
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    # Convert to Bybit V5 format
                    return {
                        'b': data['bids'],
                        'a': data['asks'],
                        's': symbol,
                        'ts': int(time.time() * 1000)
                    }
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting Binance order book for {symbol}: {e}")
            return None
    
    async def _get_coingecko_price(self, symbol: str) -> Optional[float]:
        """Get price from CoinGecko API"""
        try:
            if symbol not in self.symbol_mapping:
                return None
            
            coin_id = self.symbol_mapping[symbol]['coingecko']
            await self._rate_limit('coingecko')
            
            url = f"{self.sources['coingecko']['base_url']}/simple/price"
            params = {'ids': coin_id, 'vs_currencies': 'usd'}
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return float(data[coin_id]['usd'])
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting CoinGecko price for {symbol}: {e}")
            return None
    
    async def _get_cryptocompare_price(self, symbol: str) -> Optional[float]:
        """Get price from CryptoCompare API"""
        try:
            if symbol not in self.symbol_mapping:
                return None
            
            crypto_symbol = self.symbol_mapping[symbol]['cryptocompare']
            await self._rate_limit('cryptocompare')
            
            url = f"{self.sources['cryptocompare']['base_url']}/price"
            params = {'fsym': crypto_symbol, 'tsyms': 'USD'}
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return float(data['USD'])
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting CryptoCompare price for {symbol}: {e}")
            return None
    
    async def _rate_limit(self, source: str):
        """Implement rate limiting for API calls"""
        current_time = time.time()
        last_request = self.sources[source]['last_request']
        rate_limit = self.sources[source]['rate_limit']
        
        time_since_last = current_time - last_request
        if time_since_last < rate_limit:
            await asyncio.sleep(rate_limit - time_since_last)
        
        self.sources[source]['last_request'] = time.time()
    
    async def get_comprehensive_backup_data(self, symbol: str) -> Dict[str, Any]:
        """Get comprehensive backup market data"""
        try:
            price = await self.get_backup_price(symbol)
            order_book = await self.get_backup_order_book(symbol)
            
            return {
                'symbol': symbol,
                'price': price,
                'order_book': order_book,
                'timestamp': int(time.time() * 1000),
                'source': 'backup_apis'
            }
            
        except Exception as e:
            self.logger.error(f"Error getting comprehensive backup data for {symbol}: {e}")
            return {}
