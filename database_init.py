"""
Database Setup and Initialization Script for Super-GPT Trading Bot
Creates all required tables, indexes, and initial data
"""
import asyncio
import os
import sys
from datetime import datetime
from pathlib import Path
import logging

# Add the parent directory to the path
sys.path.append(str(Path(__file__).parent.parent))

# Database imports - SQLite compatible
try:
    import psycopg2
    from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
    POSTGRES_AVAILABLE = True
except ImportError:
    POSTGRES_AVAILABLE = False
from bybit_bot.core.config import BotConfig
from bybit_bot.core.logger import TradingBotLogger
from bybit_bot.database.connection import DatabaseManager


class DatabaseInitializer:
    """Database initialization and setup"""
    
    def __init__(self, config: BotConfig):
        self.config = config
        self.logger = TradingBotLogger("DatabaseInitializer")
        self.db_manager = DatabaseManager(config)
        
        # Get database configuration
        self.db_config = {
            'host': config.database.get('host', 'localhost'),
            'port': config.database.get('port', 5432),
            'database': config.database.get('database', 'trading_bot'),
            'user': config.database.get('user', 'postgres'),
            'password': config.database.get('password', 'password')
        }
    
    async def initialize_database(self):
        """Initialize the complete database"""
        try:
            self.logger.info("Starting database initialization")
            
            # Create database if it doesn't exist
            await self._create_database_if_not_exists()
            
            # Create tables
            await self._create_tables()
            
            # Create indexes
            await self._create_indexes()
            
            # Create views
            await self._create_views()
            
            # Create functions and procedures
            await self._create_functions()
            
            # Create triggers
            await self._create_triggers()
            
            # Insert initial data
            await self._insert_initial_data()
            
            # Verify setup
            await self._verify_database_setup()
            
            self.logger.info("Database initialization completed successfully")
            
        except Exception as e:
            self.logger.error(f"Database initialization failed: {e}")
            raise
    
    async def _create_database_if_not_exists(self):
        """Create database if it doesn't exist (SQLite compatible)"""
        try:
            # For SQLite, database file is created automatically by DatabaseManager
            # No need to create database explicitly like PostgreSQL
            self.logger.info("Database will be created automatically by SQLite")

        except Exception as e:
            self.logger.error(f"Error in database setup: {e}")
            raise
    
    async def _create_tables(self):
        """Create all tables"""
        try:
            self.logger.info("Creating database tables")
            
            # Core tables
            await self._create_core_tables()
            
            # Trading tables
            await self._create_trading_tables()
            
            # AI and ML tables
            await self._create_ai_tables()
            
            # Risk management tables
            await self._create_risk_tables()
            
            # Analytics tables
            await self._create_analytics_tables()
            
            # System tables
            await self._create_system_tables()
            
        except Exception as e:
            self.logger.error(f"Error creating tables: {e}")
            raise
    
    async def _create_core_tables(self):
        """Create core system tables"""
        async with self.db_manager.get_connection() as conn:
            
            # Symbols table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS symbols (
                    symbol VARCHAR(20) PRIMARY KEY,
                    name VARCHAR(100),
                    base_currency VARCHAR(10),
                    quote_currency VARCHAR(10),
                    status VARCHAR(20) DEFAULT 'active',
                    price_precision INTEGER DEFAULT 8,
                    quantity_precision INTEGER DEFAULT 8,
                    min_order_size REAL,
                    max_order_size REAL,
                    tick_size REAL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Market data table (SQLite compatible)
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS market_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol VARCHAR(20),
                    timestamp DATETIME,
                    open_price REAL,
                    high_price REAL,
                    low_price REAL,
                    close_price REAL,
                    volume REAL,
                    turnover REAL,
                    interval VARCHAR(10),
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(symbol, timestamp, interval)
                )
            """)
            
            # Order book table (SQLite compatible)
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS order_book (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol VARCHAR(20),
                    timestamp DATETIME,
                    side VARCHAR(4),
                    price REAL,
                    quantity REAL,
                    count INTEGER,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Trades table (SQLite compatible)
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol VARCHAR(20),
                    timestamp DATETIME,
                    price REAL,
                    quantity REAL,
                    side VARCHAR(4),
                    trade_id VARCHAR(50),
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(symbol, trade_id)
                )
            """)
    
    async def _create_trading_tables(self):
        """Create trading-related tables"""
        async with self.db_manager.get_connection() as conn:
            
            # Positions table (SQLite compatible)
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS positions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol VARCHAR(20),
                    side VARCHAR(4),
                    size REAL,
                    entry_price REAL,
                    current_price REAL,
                    unrealized_pnl REAL,
                    realized_pnl REAL,
                    margin REAL,
                    leverage INTEGER,
                    stop_loss REAL,
                    take_profit REAL,
                    status VARCHAR(20) DEFAULT 'open',
                    opened_at DATETIME,
                    closed_at DATETIME,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Orders table (SQLite compatible)
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS orders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    order_id VARCHAR(50) UNIQUE,
                    symbol VARCHAR(20),
                    side VARCHAR(4),
                    type VARCHAR(20),
                    quantity REAL,
                    price REAL,
                    filled_quantity REAL DEFAULT 0,
                    average_price REAL,
                    status VARCHAR(20) DEFAULT 'pending',
                    time_in_force VARCHAR(10) DEFAULT 'GTC',
                    reduce_only BOOLEAN DEFAULT 0,
                    close_on_trigger BOOLEAN DEFAULT 0,
                    stop_price REAL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    filled_at DATETIME,
                    cancelled_at DATETIME
                )
            """)
            
            # Executions table (SQLite compatible)
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS executions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    execution_id VARCHAR(50) UNIQUE,
                    order_id VARCHAR(50),
                    symbol VARCHAR(20),
                    side VARCHAR(4),
                    quantity REAL,
                    price REAL,
                    fee REAL,
                    fee_currency VARCHAR(10),
                    is_maker BOOLEAN,
                    executed_at DATETIME,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Strategies table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS strategies (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name VARCHAR(100) UNIQUE,
                    description TEXT,
                    type VARCHAR(50),
                    parameters TEXT,
                    status VARCHAR(20) DEFAULT 'inactive',
                    performance TEXT,
                    risk_metrics TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Strategy executions table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS strategy_executions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    strategy_id INTEGER,
                    symbol VARCHAR(20),
                    action VARCHAR(20),
                    parameters TEXT,
                    result TEXT,
                    success BOOLEAN,
                    error_message TEXT,
                    execution_time INTEGER,
                    executed_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
    
    async def _create_ai_tables(self):
        """Create AI and ML related tables (SQLite compatible)"""
        async with self.db_manager.get_connection() as conn:

            # AI models table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS ai_models (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name VARCHAR(100) UNIQUE,
                    type VARCHAR(50),
                    version VARCHAR(20),
                    parameters TEXT,
                    performance_metrics TEXT,
                    training_data_info TEXT,
                    status VARCHAR(20) DEFAULT 'inactive',
                    file_path VARCHAR(500),
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    last_trained_at DATETIME
                )
            """)
            
            # Model predictions table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS model_predictions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    model_id INTEGER,
                    symbol VARCHAR(20),
                    prediction_type VARCHAR(50),
                    prediction_value REAL,
                    confidence REAL,
                    features TEXT,
                    actual_value REAL,
                    accuracy REAL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Memory store table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS memory_store (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key VARCHAR(255) UNIQUE,
                    value TEXT,
                    type VARCHAR(50),
                    importance REAL DEFAULT 0.5,
                    access_count INTEGER DEFAULT 0,
                    last_accessed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    expires_at DATETIME
                )
            """)
            
            # Learning patterns table (SQLite compatible)
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS learning_patterns (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    pattern_type VARCHAR(50),
                    pattern_data TEXT,
                    frequency INTEGER DEFAULT 1,
                    success_rate REAL,
                    confidence REAL,
                    last_seen_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Agent actions table (SQLite compatible)
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS agent_actions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    agent_name VARCHAR(100),
                    action_type VARCHAR(50),
                    action_data TEXT,
                    context TEXT,
                    result TEXT,
                    success BOOLEAN,
                    execution_time INTEGER,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
    
    async def _create_risk_tables(self):
        """Create risk management tables"""
        async with self.db_manager.get_connection() as conn:
            
            # Risk metrics table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS risk_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol VARCHAR(20),
                    timestamp DATETIME,
                    var_1d REAL,
                    var_7d REAL,
                    var_30d REAL,
                    expected_shortfall REAL,
                    beta REAL,
                    sharpe_ratio REAL,
                    max_drawdown REAL,
                    volatility REAL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Risk limits table (SQLite compatible)
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS risk_limits (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol VARCHAR(20),
                    limit_type VARCHAR(50),
                    limit_value REAL,
                    current_value REAL,
                    utilization REAL,
                    breach_count INTEGER DEFAULT 0,
                    last_breach_at DATETIME,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Risk events table (SQLite compatible)
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS risk_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    event_type VARCHAR(50),
                    severity VARCHAR(20),
                    symbol VARCHAR(20),
                    description TEXT,
                    impact TEXT,
                    mitigation_actions TEXT,
                    status VARCHAR(20) DEFAULT 'open',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    resolved_at DATETIME
                )
            """)
    
    async def _create_analytics_tables(self):
        """Create analytics tables"""
        async with self.db_manager.get_connection() as conn:
            
            # Performance metrics table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS performance_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    metric_type VARCHAR(50),
                    symbol VARCHAR(20),
                    period VARCHAR(20),
                    value REAL,
                    benchmark_value REAL,
                    rank INTEGER,
                    percentile REAL,
                    calculated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Market analysis table (SQLite compatible)
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS market_analysis (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol VARCHAR(20),
                    analysis_type VARCHAR(50),
                    timeframe VARCHAR(20),
                    indicators TEXT,
                    signals TEXT,
                    confidence REAL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # News and sentiment table (SQLite compatible)
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS news_sentiment (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol VARCHAR(20),
                    headline TEXT,
                    content TEXT,
                    source VARCHAR(100),
                    sentiment_score REAL,
                    impact_score REAL,
                    relevance_score REAL,
                    published_at DATETIME,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
    
    async def _create_system_tables(self):
        """Create system tables"""
        async with self.db_manager.get_connection() as conn:
            
            # System logs table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS system_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    level VARCHAR(20),
                    logger VARCHAR(100),
                    message TEXT,
                    module VARCHAR(100),
                    function VARCHAR(100),
                    line_number INTEGER,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # System metrics table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS system_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    metric_name VARCHAR(100),
                    metric_value REAL,
                    unit VARCHAR(20),
                    tags TEXT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Configuration table (SQLite compatible)
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS configuration (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key VARCHAR(255) UNIQUE,
                    value TEXT,
                    type VARCHAR(50),
                    description TEXT,
                    is_sensitive BOOLEAN DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Backups table (SQLite compatible)
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS backups (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    backup_type VARCHAR(50),
                    file_path VARCHAR(500),
                    size_bytes INTEGER,
                    checksum VARCHAR(64),
                    status VARCHAR(20),
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    completed_at DATETIME
                )
            """)
    
    async def _create_indexes(self):
        """Create database indexes"""
        async with self.db_manager.get_connection() as conn:
            
            # Market data indexes
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_market_data_symbol_timestamp ON market_data(symbol, timestamp)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_market_data_timestamp ON market_data(timestamp)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_market_data_symbol_interval ON market_data(symbol, interval)")
            
            # Order book indexes
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_order_book_symbol_timestamp ON order_book(symbol, timestamp)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_order_book_timestamp ON order_book(timestamp)")
            
            # Trades indexes
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_trades_symbol_timestamp ON trades(symbol, timestamp)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_trades_timestamp ON trades(timestamp)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_trades_trade_id ON trades(trade_id)")
            
            # Positions indexes
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_positions_symbol ON positions(symbol)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_positions_status ON positions(status)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_positions_opened_at ON positions(opened_at)")
            
            # Orders indexes
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_orders_order_id ON orders(order_id)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_orders_symbol ON orders(symbol)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at)")
            
            # Executions indexes
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_executions_order_id ON executions(order_id)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_executions_symbol ON executions(symbol)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_executions_executed_at ON executions(executed_at)")
            
            # AI models indexes
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_ai_models_name ON ai_models(name)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_ai_models_type ON ai_models(type)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_ai_models_status ON ai_models(status)")
            
            # Model predictions indexes
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_model_predictions_model_id ON model_predictions(model_id)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_model_predictions_symbol ON model_predictions(symbol)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_model_predictions_created_at ON model_predictions(created_at)")
            
            # Memory store indexes
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_memory_store_key ON memory_store(key)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_memory_store_type ON memory_store(type)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_memory_store_importance ON memory_store(importance)")
            
            # Performance indexes
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_performance_metrics_type ON performance_metrics(metric_type)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_performance_metrics_symbol ON performance_metrics(symbol)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_performance_metrics_calculated_at ON performance_metrics(calculated_at)")
            
            # System logs indexes
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_system_logs_level ON system_logs(level)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_system_logs_logger ON system_logs(logger)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_system_logs_timestamp ON system_logs(timestamp)")
    
    async def _create_views(self):
        """Create database views"""
        async with self.db_manager.get_connection() as conn:
            
            # Active positions view
            await conn.execute("""
                CREATE OR REPLACE VIEW active_positions AS
                SELECT * FROM positions WHERE status = 'open'
            """)
            
            # Open orders view
            await conn.execute("""
                CREATE OR REPLACE VIEW open_orders AS
                SELECT * FROM orders WHERE status IN ('pending', 'partially_filled')
            """)
            
            # Recent trades view (SQLite compatible)
            await conn.execute("""
                CREATE VIEW IF NOT EXISTS recent_trades AS
                SELECT * FROM executions
                WHERE executed_at >= datetime('now', '-24 hours')
                ORDER BY executed_at DESC
            """)

            # Performance summary view (SQLite compatible)
            await conn.execute("""
                CREATE VIEW IF NOT EXISTS performance_summary AS
                SELECT
                    symbol,
                    COUNT(*) as trade_count,
                    SUM(CASE WHEN realized_pnl > 0 THEN 1 ELSE 0 END) as winning_trades,
                    SUM(CASE WHEN realized_pnl <= 0 THEN 1 ELSE 0 END) as losing_trades,
                    SUM(realized_pnl) as total_pnl,
                    AVG(realized_pnl) as avg_pnl,
                    MAX(realized_pnl) as max_win,
                    MIN(realized_pnl) as max_loss
                FROM positions
                WHERE status = 'closed' AND closed_at >= datetime('now', '-30 days')
                GROUP BY symbol
            """)
    
    async def _create_functions(self):
        """Create database functions (SQLite compatible - using application logic instead)"""
        # SQLite doesn't support stored procedures/functions like PostgreSQL
        # PnL calculations will be handled in application code
        pass
    
    async def _create_triggers(self):
        """Create database triggers (SQLite compatible - simplified)"""
        # SQLite doesn't support complex triggers like PostgreSQL
        # Timestamp updates will be handled in application code
        pass
    
    async def _insert_initial_data(self):
        """Insert initial data"""
        async with self.db_manager.get_connection() as conn:
            
            # Insert common trading symbols
            symbols_data = [
                ('BTCUSDT', 'Bitcoin', 'BTC', 'USDT', 'active', 2, 6, 0.00001, 1000, 0.01),
                ('ETHUSDT', 'Ethereum', 'ETH', 'USDT', 'active', 2, 5, 0.0001, 10000, 0.01),
                ('ADAUSDT', 'Cardano', 'ADA', 'USDT', 'active', 4, 1, 1, 1000000, 0.0001),
                ('DOTUSDT', 'Polkadot', 'DOT', 'USDT', 'active', 3, 2, 0.01, 100000, 0.001),
                ('LINKUSDT', 'Chainlink', 'LINK', 'USDT', 'active', 3, 2, 0.01, 100000, 0.001),
                ('LTCUSDT', 'Litecoin', 'LTC', 'USDT', 'active', 2, 5, 0.0001, 10000, 0.01),
                ('BCHUSDT', 'Bitcoin Cash', 'BCH', 'USDT', 'active', 2, 5, 0.0001, 10000, 0.01),
                ('XRPUSDT', 'Ripple', 'XRP', 'USDT', 'active', 4, 1, 1, 1000000, 0.0001),
                ('EOSUSDT', 'EOS', 'EOS', 'USDT', 'active', 3, 2, 0.01, 100000, 0.001),
                ('TRXUSDT', 'Tron', 'TRX', 'USDT', 'active', 5, 0, 1, 10000000, 0.00001)
            ]
            
            for symbol_data in symbols_data:
                await conn.execute("""
                    INSERT INTO symbols (symbol, name, base_currency, quote_currency, status, 
                                       price_precision, quantity_precision, min_order_size, 
                                       max_order_size, tick_size)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                    ON CONFLICT (symbol) DO NOTHING
                """, symbol_data)
            
            # Insert initial configuration
            config_data = [
                ('max_position_size', '0.1', 'decimal', 'Maximum position size as fraction of portfolio'),
                ('max_daily_loss', '0.05', 'decimal', 'Maximum daily loss as fraction of portfolio'),
                ('max_drawdown', '0.2', 'decimal', 'Maximum drawdown threshold'),
                ('risk_free_rate', '0.02', 'decimal', 'Risk-free rate for calculations'),
                ('default_leverage', '1', 'integer', 'Default leverage for new positions'),
                ('stop_loss_percentage', '0.05', 'decimal', 'Default stop loss percentage'),
                ('take_profit_percentage', '0.1', 'decimal', 'Default take profit percentage'),
                ('rebalance_threshold', '0.05', 'decimal', 'Portfolio rebalance threshold'),
                ('min_trade_size', '10', 'decimal', 'Minimum trade size in USD'),
                ('max_trade_size', '10000', 'decimal', 'Maximum trade size in USD')
            ]
            
            for config_item in config_data:
                await conn.execute("""
                    INSERT INTO configuration (key, value, type, description)
                    VALUES ($1, $2, $3, $4)
                    ON CONFLICT (key) DO NOTHING
                """, config_item)
            
            # Insert initial risk limits
            risk_limits_data = [
                ('BTCUSDT', 'position_size', 100000, 0, 0.0),
                ('ETHUSDT', 'position_size', 50000, 0, 0.0),
                ('ADAUSDT', 'position_size', 10000, 0, 0.0),
                ('portfolio', 'daily_loss', 0.05, 0, 0.0),
                ('portfolio', 'total_exposure', 1.0, 0, 0.0),
                ('portfolio', 'max_drawdown', 0.2, 0, 0.0)
            ]
            
            for limit_data in risk_limits_data:
                await conn.execute("""
                    INSERT INTO risk_limits (symbol, limit_type, limit_value, current_value, utilization)
                    VALUES ($1, $2, $3, $4, $5)
                """, limit_data)
    
    async def _verify_database_setup(self):
        """Verify database setup (SQLite compatible)"""
        async with self.db_manager.get_connection() as conn:

            # Check if all tables exist (SQLite compatible)
            result = await conn.fetch("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
                ORDER BY name
            """)

            table_count = len(result)
            self.logger.info(f"Created {table_count} tables")

            # Check if all indexes exist (SQLite compatible)
            result = await conn.fetch("""
                SELECT name FROM sqlite_master
                WHERE type='index' AND name NOT LIKE 'sqlite_%'
                ORDER BY name
            """)

            index_count = len(result)
            self.logger.info(f"Created {index_count} indexes")

            # Check if all views exist (SQLite compatible)
            result = await conn.fetch("""
                SELECT name FROM sqlite_master
                WHERE type='view'
                ORDER BY name
            """)

            view_count = len(result)
            self.logger.info(f"Created {view_count} views")

            # SQLite doesn't have stored functions like PostgreSQL
            self.logger.info("Database verification completed (SQLite mode)")
            
            # Verify data integrity
            symbol_count = await conn.fetchval("SELECT COUNT(*) FROM symbols")
            config_count = await conn.fetchval("SELECT COUNT(*) FROM configuration")
            limit_count = await conn.fetchval("SELECT COUNT(*) FROM risk_limits")
            
            self.logger.info(f"Inserted {symbol_count} symbols, {config_count} config items, {limit_count} risk limits")
            
            self.logger.info("Database setup verification completed successfully")


async def main():
    """Main function to run database initialization"""
    try:
        # Load configuration
        config = BotConfig()
        
        # Initialize database
        db_initializer = DatabaseInitializer(config)
        await db_initializer.initialize_database()
        
        print("Database initialization completed successfully!")
        
    except Exception as e:
        print(f"Database initialization failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
