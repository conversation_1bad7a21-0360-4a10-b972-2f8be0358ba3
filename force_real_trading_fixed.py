#!/usr/bin/env python3
"""
FORCE REAL TRADING - FIXED AUTHENTICATION
Bypasses all complex conditions and forces actual trades with corrected API signature
"""

import asyncio
import sys
import os
from datetime import datetime

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
from bybit_bot.core.config import EnhancedBotConfig

async def force_real_trading_fixed():
    """Force actual trading with real money - FIXED AUTHENTICATION"""
    print(f"FORCE REAL TRADING START (FIXED AUTH): {datetime.now()}")
    print("=" * 80)
    print("WARNING: LIVE MONEY TRADING - REAL ORDERS WILL BE PLACED")
    print("AUTHENTICATION FIXED - TIMESTAMP CORRECTED")
    print("=" * 80)
    
    try:
        # Initialize client
        config = EnhancedBotConfig()
        client = EnhancedBybitClient(config)
        await client.initialize()
        
        print("CLIENT INITIALIZED - READY FOR LIVE TRADING")
        
        # Get account status
        balance = await client.get_account_balance()
        if balance and 'list' in balance and balance['list']:
            account = balance['list'][0]
            total_equity = float(account.get('totalEquity', 0))
            available_balance = float(account.get('totalAvailableBalance', 0))
            margin_ratio = float(account.get('accountIMRate', 0)) * 100
            
            print(f"ACCOUNT STATUS:")
            print(f"  Total Equity: ${total_equity:.2f}")
            print(f"  Available Balance: ${available_balance:.2f}")
            print(f"  Margin Ratio: {margin_ratio:.2f}%")
            
            if margin_ratio > 95:
                print("ERROR: Margin ratio too high - cannot trade safely")
                return False
                
            if available_balance < 1:
                print("ERROR: Available balance too low")
                return False
            
            # Test with BTCUSDT first
            symbol = "BTCUSDT"
            print(f"\nFORCING TRADE FOR {symbol}:")
            
            # Get current price using ticker endpoint
            try:
                # Use direct API call to get ticker data
                ticker_params = {"category": "linear", "symbol": symbol}
                ticker_response = await client._make_request("GET", "/v5/market/tickers", ticker_params, False)
                
                if ticker_response.get("retCode") == 0 and ticker_response.get("result"):
                    ticker_list = ticker_response["result"].get("list", [])
                    if ticker_list:
                        ticker_data = ticker_list[0]
                        current_price = float(ticker_data.get('lastPrice', 0))
                    else:
                        print(f"  ERROR: No ticker data for {symbol}")
                        return False
                else:
                    print(f"  ERROR: Ticker API failed for {symbol}")
                    return False
                    
            except Exception as e:
                print(f"  ERROR: Exception getting ticker for {symbol}: {e}")
                return False
            
            if current_price == 0:
                print(f"  ERROR: Invalid price for {symbol}")
                return False
            
            print(f"  Current Price: ${current_price:.2f}")
            
            # Calculate minimum viable position - USE BYBIT MINIMUM ORDER SIZE
            # For BTCUSDT linear, minimum is typically 0.001 BTC
            quantity = 0.001  # Use fixed minimum quantity for BTCUSDT
            quantity_str = "0.001"  # Force exact format
            
            actual_position_usd = quantity * current_price
            
            print(f"  FORCING ORDER: {quantity_str} {symbol} (${actual_position_usd:.2f})")
            print(f"  PLACING REAL BUY ORDER WITH FIXED AUTHENTICATION...")
            
            try:
                order_result = await client.place_order(
                    symbol=symbol,
                    side="Buy",
                    order_type="Market",
                    qty=quantity_str,  # Use formatted string to avoid scientific notation
                    category="linear"  # Derivatives with leverage
                )
                
                if order_result and order_result.get('orderId'):
                    order_id = order_result.get('orderId')
                    print(f"  SUCCESS: REAL ORDER PLACED!")
                    print(f"    Order ID: {order_id}")
                    print(f"    Symbol: {symbol}")
                    print(f"    Side: Buy")
                    print(f"    Quantity: {quantity_str}")
                    print(f"    Value: ${actual_position_usd:.2f}")
                    print(f"    Time: {datetime.now()}")
                    
                    # Wait a moment then check if order was filled
                    await asyncio.sleep(3)
                    
                    # Verify the order exists
                    positions = await client.get_positions()
                    if positions:
                        for pos in positions:
                            if pos.get('symbol') == symbol:
                                size = float(pos.get('size', 0))
                                if size > 0:
                                    print(f"  VERIFIED: Position exists - {size} {symbol}")
                                    break
                    
                    print("\nSUCCESS: AUTHENTICATION FIXED - REAL TRADE EXECUTED")
                    print("CHECK YOUR BYBIT ACCOUNT FOR BALANCE CHANGES")
                    return True  # Success - real trade executed
                    
                else:
                    print(f"  FAILED: Order not placed - {order_result}")
                    return False
                    
            except Exception as e:
                print(f"  ERROR placing order: {e}")
                return False
            
        else:
            print("ERROR: Could not get account balance")
            return False
            
    except Exception as e:
        print(f"CRITICAL ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main execution"""
    print("BYPASSING ALL FAKE CONDITIONS - FORCING REAL TRADES")
    print("AUTHENTICATION FIXED - TIMESTAMP CORRECTED TO < server_time + 1000")
    print("THIS WILL PLACE ACTUAL ORDERS WITH REAL MONEY")
    
    success = await force_real_trading_fixed()
    
    if success:
        print("\nSUCCESS: REAL TRADE EXECUTED WITH FIXED AUTHENTICATION")
        print("ACCOUNT BALANCE SHOULD NOW SHOW CHANGES")
        print("THE FAKE DATA PROBLEM IS SOLVED")
    else:
        print("\nFAILED: AUTHENTICATION STILL FAILING")
        print("NEED TO INVESTIGATE FURTHER")

if __name__ == "__main__":
    asyncio.run(main())
