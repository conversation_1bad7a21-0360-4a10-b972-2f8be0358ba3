#!/usr/bin/env python3
"""
Continuous Trading Bot Monitoring Script
Runs continuous monitoring of the trading bot system
"""
import asyncio
import logging
import json
from datetime import datetime, timedelta
from deploy_monitoring_and_alerting import TradingBotMonitor

async def main():
    """Main monitoring loop"""
    monitor = TradingBotMonitor()
    
    print("STARTING CONTINUOUS TRADING BOT MONITORING")
    print("==========================================")
    
    while True:
        try:
            # Perform health check
            health_status = await monitor.perform_health_check()
            
            # Log status
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"[{timestamp}] System Health: {health_status['overall_health']:.1f}%")
            
            # Check for alerts
            if health_status['overall_health'] < 80:
                print(f"[{timestamp}] ALERT: System health below threshold!")
            
            # Wait for next check (5 minutes)
            await asyncio.sleep(300)
            
        except KeyboardInterrupt:
            print("Monitoring stopped by user")
            break
        except Exception as e:
            print(f"Monitoring error: {e}")
            await asyncio.sleep(60)  # Wait 1 minute before retry

if __name__ == "__main__":
    asyncio.run(main())
