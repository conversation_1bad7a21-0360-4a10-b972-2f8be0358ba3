"""
Firecrawl Integration for Enhanced Data Collection
Provides web scraping and data extraction capabilities
"""

import asyncio
import aiohttp
import json
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import time

from ..core.config import BotConfig
from ..core.logger import TradingBotLogger
from ..database.connection import DatabaseManager


@dataclass
class ScrapedData:
    """Scraped data from Firecrawl"""
    url: str
    title: str
    content: str
    metadata: Dict[str, Any]
    timestamp: datetime
    extraction_type: str
    relevance_score: float


class FirecrawlCrawler:
    """
    Firecrawl integration for advanced web scraping and data extraction
    """
    
    def __init__(self, config: BotConfig, database_manager: DatabaseManager):
        self.config = config
        self.db = database_manager
        self.logger = TradingBotLogger(config)
        
        # Firecrawl API configuration
        self.api_key = "fc-611319452f0e4e1db2197b70078df8ad"
        self.base_url = "https://api.firecrawl.dev/v1"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        # Target websites for crypto data
        self.target_websites = [
            "https://cointelegraph.com",
            "https://coindesk.com",
            "https://decrypt.co",
            "https://theblock.co",
            "https://bitcoinist.com",
            "https://u.today",
            "https://cryptoslate.com",
            "https://cryptonews.com",
            "https://newsbtc.com",
            "https://ambcrypto.com"
        ]
        
        # Search keywords for relevant content
        self.crypto_keywords = [
            "bitcoin", "ethereum", "crypto", "cryptocurrency", "blockchain",
            "bybit", "binance", "trading", "market", "price", "analysis",
            "technical", "fundamental", "defi", "nft", "altcoin", "bull", "bear"
        ]
        
        # Rate limiting
        self.rate_limit = 10  # requests per minute
        self.last_request_time = 0
        self.request_count = 0
        self.request_window_start = time.time()
        
        self.running = False
        
    async def initialize(self):
        """Initialize the Firecrawl crawler"""
        try:
            self.logger.info("🔥 Initializing Firecrawl Crawler...")
            
            # Test API connection
            await self._test_api_connection()
            
            self.logger.info("✅ Firecrawl Crawler initialized successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Firecrawl Crawler: {e}")
            raise
    
    async def start(self):
        """Start the crawler"""
        if self.running:
            self.logger.warning("Firecrawl Crawler is already running")
            return
            
        self.running = True
        self.logger.info("🔥 Starting Firecrawl data collection...")
        
        # Start background crawling task
        asyncio.create_task(self._crawling_loop())
    
    async def stop(self):
        """Stop the crawler"""
        self.running = False
        self.logger.info("🛑 Firecrawl Crawler stopped")
    
    async def scrape_url(self, url: str, options: Optional[Dict[str, Any]] = None) -> Optional[ScrapedData]:
        """Scrape a single URL using Firecrawl"""
        try:
            await self._check_rate_limit()
            
            payload = {
                "url": url,
                "formats": ["markdown", "html"],
                "onlyMainContent": True,
                "includeTags": ["title", "meta", "h1", "h2", "h3", "p", "article"],
                "excludeTags": ["nav", "footer", "script", "style", "advertisement"],
                "waitFor": 2000,
                "timeout": 30000
            }
            
            if options:
                payload.update(options)
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/scrape",
                    headers=self.headers,
                    json=payload
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return await self._process_scraped_data(url, data)
                    else:
                        self.logger.error(f"Failed to scrape {url}: {response.status}")
                        return None
                        
        except Exception as e:
            self.logger.error(f"Error scraping {url}: {e}")
            return None
    
    async def crawl_website(self, base_url: str, max_pages: int = 10) -> List[ScrapedData]:
        """Crawl multiple pages of a website"""
        try:
            await self._check_rate_limit()
            
            payload = {
                "url": base_url,
                "limit": max_pages,
                "scrapeOptions": {
                    "formats": ["markdown"],
                    "onlyMainContent": True,
                    "includeTags": ["title", "meta", "h1", "h2", "h3", "p", "article"],
                    "excludeTags": ["nav", "footer", "script", "style"],
                    "waitFor": 2000
                },
                "allowBackwardLinks": False,
                "allowExternalLinks": False,
                "maxDepth": 3
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/crawl",
                    headers=self.headers,
                    json=payload
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        crawl_id = data.get("id")
                        
                        if crawl_id:
                            return await self._wait_for_crawl_completion(crawl_id)
                    else:
                        self.logger.error(f"Failed to start crawl of {base_url}: {response.status}")
                        return []
                        
        except Exception as e:
            self.logger.error(f"Error crawling {base_url}: {e}")
            return []
    
    async def search_content(self, query: str, num_results: int = 20) -> List[ScrapedData]:
        """Search for content using Firecrawl search capabilities"""
        try:
            await self._check_rate_limit()
            
            payload = {
                "query": query,
                "pageOptions": {
                    "onlyMainContent": True,
                    "includeHtml": False,
                    "includeRawHtml": False
                },
                "searchOptions": {
                    "limit": num_results
                }
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/search",
                    headers=self.headers,
                    json=payload
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        results = []
                        
                        for item in data.get("data", []):
                            scraped_data = await self._process_search_result(query, item)
                            if scraped_data:
                                results.append(scraped_data)
                        
                        return results
                    else:
                        self.logger.error(f"Search failed for query '{query}': {response.status}")
                        return []
                        
        except Exception as e:
            self.logger.error(f"Error searching for '{query}': {e}")
            return []
    
    async def get_latest_crypto_news(self, hours_back: int = 24) -> List[ScrapedData]:
        """Get latest cryptocurrency news from multiple sources"""
        try:
            all_news = []
            
            # Search for recent crypto news
            for keyword in self.crypto_keywords[:5]:  # Limit to avoid rate limiting
                query = f"{keyword} news latest cryptocurrency trading market"
                results = await self.search_content(query, num_results=5)
                all_news.extend(results)
                
                # Add delay between searches
                await asyncio.sleep(2)
            
            # Remove duplicates and sort by relevance
            unique_news = {}
            for news in all_news:
                if news.url not in unique_news:
                    unique_news[news.url] = news
            
            sorted_news = sorted(
                unique_news.values(),
                key=lambda x: x.relevance_score,
                reverse=True
            )
            
            # Store in database
            for news in sorted_news[:20]:  # Top 20 results
                await self._store_scraped_data(news)
            
            self.logger.info(f"🔥 Collected {len(sorted_news)} latest crypto news articles")
            return sorted_news[:20]
            
        except Exception as e:
            self.logger.error(f"Error getting latest crypto news: {e}")
            return []
    
    async def extract_market_sentiment(self, content: str) -> Dict[str, Any]:
        """Extract market sentiment from scraped content"""
        try:
            # Simple sentiment analysis based on keywords
            bullish_keywords = [
                "bullish", "bull", "surge", "rally", "pump", "moon", "up", "rise",
                "gain", "profit", "positive", "optimistic", "strong", "buy"
            ]
            
            bearish_keywords = [
                "bearish", "bear", "crash", "dump", "down", "fall", "drop",
                "loss", "negative", "pessimistic", "weak", "sell", "decline"
            ]
            
            content_lower = content.lower()
            
            bullish_count = sum(1 for keyword in bullish_keywords if keyword in content_lower)
            bearish_count = sum(1 for keyword in bearish_keywords if keyword in content_lower)
            
            total_sentiment_words = bullish_count + bearish_count
            
            if total_sentiment_words == 0:
                sentiment_score = 0.5  # Neutral
            else:
                sentiment_score = bullish_count / total_sentiment_words
            
            # Classify sentiment
            if sentiment_score > 0.6:
                sentiment = "bullish"
            elif sentiment_score < 0.4:
                sentiment = "bearish"
            else:
                sentiment = "neutral"
            
            return {
                "sentiment": sentiment,
                "score": sentiment_score,
                "bullish_signals": bullish_count,
                "bearish_signals": bearish_count,
                "confidence": min(total_sentiment_words / 10, 1.0)
            }
            
        except Exception as e:
            self.logger.error(f"Error extracting sentiment: {e}")
            return {"sentiment": "neutral", "score": 0.5, "confidence": 0.0}
    
    async def _crawling_loop(self):
        """Main crawling loop"""
        try:
            while self.running:
                # Get latest news
                await self.get_latest_crypto_news(hours_back=6)
                
                # Crawl specific websites
                for website in self.target_websites[:3]:  # Limit to avoid overwhelming
                    try:
                        results = await self.crawl_website(website, max_pages=5)
                        for result in results:
                            await self._store_scraped_data(result)
                        
                        # Delay between websites
                        await asyncio.sleep(10)
                        
                    except Exception as e:
                        self.logger.error(f"Error crawling {website}: {e}")
                
                # Wait before next cycle (30 minutes)
                await asyncio.sleep(1800)
                
        except Exception as e:
            self.logger.error(f"Error in crawling loop: {e}")
    
    async def _test_api_connection(self):
        """Test Firecrawl API connection"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}/status",
                    headers=self.headers
                ) as response:
                    if response.status == 200:
                        self.logger.info("✅ Firecrawl API connection successful")
                    else:
                        raise Exception(f"API test failed with status {response.status}")
                        
        except Exception as e:
            self.logger.error(f"Firecrawl API connection test failed: {e}")
            raise
    
    async def _check_rate_limit(self):
        """Check and enforce rate limiting"""
        current_time = time.time()
        
        # Reset counter if window has passed
        if current_time - self.request_window_start >= 60:
            self.request_count = 0
            self.request_window_start = current_time
        
        # Check if we've exceeded rate limit
        if self.request_count >= self.rate_limit:
            wait_time = 60 - (current_time - self.request_window_start)
            if wait_time > 0:
                self.logger.info(f"Rate limit reached, waiting {wait_time:.1f} seconds")
                await asyncio.sleep(wait_time)
                self.request_count = 0
                self.request_window_start = time.time()
        
        self.request_count += 1
        self.last_request_time = current_time
    
    async def _process_scraped_data(self, url: str, data: Dict[str, Any]) -> Optional[ScrapedData]:
        """Process raw scraped data"""
        try:
            content_data = data.get("data", {})
            metadata = content_data.get("metadata", {})
            
            title = metadata.get("title", "")
            content = content_data.get("markdown", content_data.get("html", ""))
            
            # Calculate relevance score
            relevance_score = self._calculate_relevance_score(title, content)
            
            return ScrapedData(
                url=url,
                title=title,
                content=content,
                metadata=metadata,
                timestamp=datetime.now(timezone.utc),
                extraction_type="scrape",
                relevance_score=relevance_score
            )
            
        except Exception as e:
            self.logger.error(f"Error processing scraped data: {e}")
            return None
    
    async def _process_search_result(self, query: str, item: Dict[str, Any]) -> Optional[ScrapedData]:
        """Process search result data"""
        try:
            url = item.get("url", "")
            title = item.get("title", "")
            content = item.get("content", "")
            
            # Calculate relevance score based on query match
            relevance_score = self._calculate_search_relevance(query, title, content)
            
            return ScrapedData(
                url=url,
                title=title,
                content=content,
                metadata=item.get("metadata", {}),
                timestamp=datetime.now(timezone.utc),
                extraction_type="search",
                relevance_score=relevance_score
            )
            
        except Exception as e:
            self.logger.error(f"Error processing search result: {e}")
            return None
    
    async def _wait_for_crawl_completion(self, crawl_id: str, max_wait: int = 300) -> List[ScrapedData]:
        """Wait for crawl to complete and get results"""
        try:
            start_time = time.time()
            results = []
            
            while time.time() - start_time < max_wait:
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        f"{self.base_url}/crawl/{crawl_id}",
                        headers=self.headers
                    ) as response:
                        if response.status == 200:
                            data = await response.json()
                            status = data.get("status")
                            
                            if status == "completed":
                                for item in data.get("data", []):
                                    scraped_data = await self._process_scraped_data(
                                        item.get("url", ""), 
                                        {"data": item}
                                    )
                                    if scraped_data:
                                        results.append(scraped_data)
                                break
                            elif status == "failed":
                                self.logger.error(f"Crawl {crawl_id} failed")
                                break
                            
                            # Wait before checking again
                            await asyncio.sleep(5)
                        else:
                            self.logger.error(f"Failed to check crawl status: {response.status}")
                            break
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error waiting for crawl completion: {e}")
            return []
    
    def _calculate_relevance_score(self, title: str, content: str) -> float:
        """Calculate relevance score for crypto trading"""
        try:
            text = f"{title} {content}".lower()
            
            # Trading-specific keywords with weights
            trading_keywords = {
                "bitcoin": 1.0, "ethereum": 1.0, "crypto": 0.8, "trading": 1.2,
                "price": 1.0, "analysis": 1.2, "market": 1.0, "technical": 1.1,
                "fundamental": 1.1, "bybit": 1.5, "binance": 1.0, "exchange": 0.8,
                "bullish": 1.0, "bearish": 1.0, "trend": 1.0, "support": 1.0,
                "resistance": 1.0, "breakout": 1.1, "volume": 0.9, "momentum": 1.0
            }
            
            score = 0.0
            total_weight = 0.0
            
            for keyword, weight in trading_keywords.items():
                if keyword in text:
                    # Count occurrences
                    count = text.count(keyword)
                    score += min(count * weight, weight * 3)  # Cap at 3 occurrences
                    total_weight += weight
            
            # Normalize score
            if total_weight > 0:
                score = min(score / total_weight, 1.0)
            
            return score
            
        except Exception:
            return 0.5
    
    def _calculate_search_relevance(self, query: str, title: str, content: str) -> float:
        """Calculate relevance score for search results"""
        try:
            query_words = query.lower().split()
            text = f"{title} {content}".lower()
            
            matches = sum(1 for word in query_words if word in text)
            relevance = matches / len(query_words) if query_words else 0
            
            # Boost for title matches
            title_matches = sum(1 for word in query_words if word in title.lower())
            title_boost = title_matches * 0.2
            
            return min(relevance + title_boost, 1.0)
            
        except Exception:
            return 0.5
    
    async def _store_scraped_data(self, data: ScrapedData):
        """Store scraped data in database"""
        try:
            # Extract sentiment
            sentiment = await self.extract_market_sentiment(data.content)
            
            await self.db.execute(
                """
                INSERT INTO scraped_data 
                (url, title, content, metadata, timestamp, extraction_type, 
                 relevance_score, sentiment, sentiment_score)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                ON CONFLICT (url) DO UPDATE SET
                content = $2, metadata = $4, timestamp = $5,
                relevance_score = $7, sentiment = $8, sentiment_score = $9
                """,
                data.url, data.title, data.content, json.dumps(data.metadata),
                data.timestamp, data.extraction_type, data.relevance_score,
                sentiment["sentiment"], sentiment["score"]
            )
            
        except Exception as e:
            self.logger.error(f"Error storing scraped data: {e}")
