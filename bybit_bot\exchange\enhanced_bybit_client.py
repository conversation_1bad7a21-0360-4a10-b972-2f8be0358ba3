"""
ENHANCED BYBIT V5 CLIENT - MAXIMUM PROFIT OPTIMIZATION
Advanced client utilizing ALL Bybit V5 API endpoints for profit generation
Implements comprehensive copy trading, social signals, and derivatives mastery

FEATURES:
- Copy Trading API Integration
- Social Trading Signals
- Derivatives Trading (Perpetuals, Futures, Options)
- Advanced Order Types
- Cross-Margin Optimization
- Funding Rate Arbitrage
- Index and Basket Trading
- Ultra-Fast Execution
- Real-time WebSocket Streams
"""

import asyncio
import aiohttp
import time
import hmac
import hashlib
import json
import logging
from typing import Dict, Any, List, Optional, Union, Tuple
from decimal import Decimal
from datetime import datetime, timezone
import urllib.parse
from dataclasses import dataclass
from enum import Enum
import websockets.client
import numpy as np
from concurrent.futures import ThreadPoolExecutor

from bybit_bot.core.config import BotConfig
from bybit_bot.data_integration.backup_market_data import BackupMarketDataManager
from bybit_bot.exchange.bybit_client import BybitClient as BaseBybitClient


class OrderType(Enum):
    """Order type enumeration"""
    MARKET = "Market"
    LIMIT = "Limit"
    STOP = "Stop"
    STOP_LIMIT = "StopLimit"
    TAKE_PROFIT = "TakeProfit"
    TAKE_PROFIT_LIMIT = "TakeProfitLimit"
    CONDITIONAL = "Conditional"


class OrderSide(Enum):
    """Order side enumeration"""
    BUY = "Buy"
    SELL = "Sell"


class TimeInForce(Enum):
    """Time in force enumeration"""
    GTC = "GTC"  # Good Till Cancel
    IOC = "IOC"  # Immediate or Cancel
    FOK = "FOK"  # Fill or Kill
    POST_ONLY = "PostOnly"


@dataclass
class EnhancedPosition:
    """Enhanced position with comprehensive data"""
    symbol: str
    side: str
    size: float
    entry_price: float
    mark_price: float
    unrealized_pnl: float
    leverage: float
    margin_type: str
    position_value: float
    funding_fee: float
    auto_add_margin: bool
    position_idx: int = 0
    hedge_mode: bool = False
    
    # Copy trading data
    copied_from: Optional[str] = None
    trader_uid: Optional[str] = None
    copy_ratio: float = 1.0
    
    # Advanced metrics
    roi: float = 0.0
    drawdown: float = 0.0
    holding_time: int = 0


@dataclass
class TraderPerformance:
    """Copy trading trader performance metrics"""
    trader_uid: str
    nickname: str
    avatar_url: str
    followers_count: int
    roi_7d: float
    roi_30d: float
    roi_total: float
    pnl_7d: float
    pnl_30d: float
    pnl_total: float
    win_rate: float
    avg_hold_time: int
    max_drawdown: float
    copiers_pnl: float
    total_trades: int
    ranking: int
    is_verified: bool = False


class EnhancedBybitClient(BaseBybitClient):
    """
    Enhanced Bybit V5 client with ALL profit generation capabilities
    Optimized for maximum profit generation and ultra-fast execution
    """
    
    def __init__(self, config: BotConfig):
        # Initialize parent class
        super().__init__(config)

        # Enhanced client specific configuration (inherit from parent, add enhanced features)
        # Parent already sets api_key, api_secret, testnet, base_url

        # Enhanced WebSocket URLs
        if self.testnet:
            self.ws_public_url = "wss://stream-testnet.bybit.com/v5/public"
            self.ws_private_url = "wss://stream-testnet.bybit.com/v5/private"
        else:
            self.ws_public_url = "wss://stream.bybit.com/v5/public"
            self.ws_private_url = "wss://stream.bybit.com/v5/private"
        
        # Session and rate limiting
        self.session = None
        self.rate_limiter = asyncio.Semaphore(50)  # 50 requests per second
        self.last_request_time = 0
        self.request_delay = 0.02  # 20ms between requests
        
        # WebSocket connections
        self.ws_connections: Dict[str, Any] = {}
        self.ws_subscriptions: set[str] = set()

        # Copy trading data
        self.followed_traders: Dict[str, Any] = {}
        self.trader_performance: Dict[str, Any] = {}
        self.copy_positions: Dict[str, Any] = {}

        # Backup market data manager
        self.backup_data_manager = BackupMarketDataManager()
        
        # Social signals
        self.social_signals = {}
        self.sentiment_cache = {}
        
        # Advanced features
        self.cross_margin_enabled = False
        self.position_hedge_mode = False
        self.unified_margin_account = False
        
        # Performance tracking
        self.execution_times = []
        self.api_call_count = 0
        self.error_count = 0
        
        self.logger = logging.getLogger("enhanced_bybit_client")

    async def __aenter__(self):
        """Async context manager entry"""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()

    async def initialize(self):
        """Initialize the enhanced client"""
        try:
            self.logger.info("Initializing Enhanced Bybit V5 Client...")
            
            # Initialize parent class
            await super().initialize()

            # Initialize backup data manager
            await self.backup_data_manager.initialize()

            # Initialize enhanced features
            await self._initialize_advanced_features()

            # Setup copy trading
            await self._initialize_copy_trading()

            # Start WebSocket connections
            await self._initialize_enhanced_websockets()
            
            self.logger.info("Enhanced Bybit V5 Client initialized successfully!")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Enhanced Bybit client: {e}")
            raise

    async def close(self):
        """Close the client and cleanup resources"""
        try:
            # Close WebSocket connections
            for ws in self.ws_connections.values():
                if ws and not ws.closed:
                    await ws.close()

            # Close backup data manager
            await self.backup_data_manager.close()

            # Close parent resources
            await super().close()
            
            self.logger.info("Enhanced Bybit client closed successfully")
            
        except Exception as e:
            self.logger.error(f"Error closing Enhanced Bybit client: {e}")

    # =====================================
    # COPY TRADING API METHODS
    # =====================================
    
    async def get_copy_trading_traders(self, 
                                     sort_type: str = "roi",
                                     order_by: str = "desc",
                                     limit: int = 50) -> List[Dict]:
        """Get list of top copy trading traders"""
        try:
            params = {
                "sortType": sort_type,
                "orderBy": order_by,
                "limit": limit
            }
            
            response = await self._make_request(
                "GET",
                "/v5/copy-trading/get-leaderboard",
                params,
                signed=True
            )
            
            # Handle None response
            if response is None:
                self.logger.warning("Copy trading API returned None response")
                return []
            
            if response.get("retCode") == 0:
                result = response.get("result", {})
                if result is None:
                    self.logger.warning("Copy trading API result is None")
                    return []
                return result.get("list", [])
            
            return []
            
        except Exception as e:
            self.logger.error(f"Error getting copy trading traders: {e}")
            return []

    async def get_trader_performance(self, trader_uid: str) -> Optional[TraderPerformance]:
        """Get detailed performance data for a specific trader"""
        try:
            params = {"traderUid": trader_uid}
            
            response = await self._make_request(
                "GET",
                "/v5/copy-trading/get-trader-performance",
                params,
                signed=True
            )
            
            if response.get("retCode") == 0:
                data = response.get("result", {})
                return TraderPerformance(
                    trader_uid=data.get("traderUid", ""),
                    nickname=data.get("nickname", ""),
                    avatar_url=data.get("avatarUrl", ""),
                    followers_count=data.get("followersCount", 0),
                    roi_7d=float(data.get("roi7d", 0)),
                    roi_30d=float(data.get("roi30d", 0)),
                    roi_total=float(data.get("roiTotal", 0)),
                    pnl_7d=float(data.get("pnl7d", 0)),
                    pnl_30d=float(data.get("pnl30d", 0)),
                    pnl_total=float(data.get("pnlTotal", 0)),
                    win_rate=float(data.get("winRate", 0)),
                    avg_hold_time=int(data.get("avgHoldTime", 0)),
                    max_drawdown=float(data.get("maxDrawdown", 0)),
                    copiers_pnl=float(data.get("copiersPnl", 0)),
                    total_trades=int(data.get("totalTrades", 0)),
                    ranking=int(data.get("ranking", 0)),
                    is_verified=data.get("isVerified", False)
                )
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting trader performance: {e}")
            return None

    async def follow_trader(self, trader_uid: str, copy_ratio: float = 1.0) -> bool:
        """Follow a trader for copy trading"""
        try:
            data = {
                "traderUid": trader_uid,
                "copyRatio": copy_ratio
            }
            
            response = await self._make_request(
                "POST",
                "/v5/copy-trading/follow-trader",
                data,
                signed=True
            )
            
            if response.get("retCode") == 0:
                self.followed_traders[trader_uid] = copy_ratio
                self.logger.info(f"Successfully following trader {trader_uid} with ratio {copy_ratio}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error following trader: {e}")
            return False

    async def unfollow_trader(self, trader_uid: str) -> bool:
        """Unfollow a trader"""
        try:
            data = {"traderUid": trader_uid}
            
            response = await self._make_request(
                "POST",
                "/v5/copy-trading/unfollow-trader",
                data,
                signed=True
            )
            
            if response.get("retCode") == 0:
                self.followed_traders.pop(trader_uid, None)
                self.logger.info(f"Successfully unfollowed trader {trader_uid}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error unfollowing trader: {e}")
            return False

    async def get_copy_positions(self) -> List[EnhancedPosition]:
        """Get all copy trading positions"""
        try:
            response = await self._make_request(
                "GET",
                "/v5/copy-trading/get-positions",
                signed=True
            )
            
            positions = []
            if response.get("retCode") == 0:
                for pos_data in response.get("result", {}).get("list", []):
                    position = EnhancedPosition(
                        symbol=pos_data.get("symbol", ""),
                        side=pos_data.get("side", ""),
                        size=float(pos_data.get("size", 0)),
                        entry_price=float(pos_data.get("entryPrice", 0)),
                        mark_price=float(pos_data.get("markPrice", 0)),
                        unrealized_pnl=float(pos_data.get("unrealisedPnl", 0)),
                        leverage=float(pos_data.get("leverage", 1)),
                        margin_type=pos_data.get("marginType", ""),
                        position_value=float(pos_data.get("positionValue", 0)),
                        funding_fee=float(pos_data.get("fundingFee", 0)),
                        auto_add_margin=pos_data.get("autoAddMargin", False),
                        copied_from=pos_data.get("traderUid"),
                        trader_uid=pos_data.get("traderUid"),
                        copy_ratio=float(pos_data.get("copyRatio", 1.0))
                    )
                    positions.append(position)
            
            return positions
            
        except Exception as e:
            self.logger.error(f"Error getting copy positions: {e}")
            return []

    # =====================================
    # MARGIN TRADING METHODS
    # =====================================
    
    async def enable_cross_margin(self) -> bool:
        """Enable cross margin trading"""
        try:
            data = {
                "category": "linear",
                "marginMode": "REGULAR_MARGIN"
            }
            
            response = await self._make_request(
                "POST",
                "/v5/account/set-margin-mode",
                data
            )
            
            if response.get("retCode") == 0:
                self.cross_margin_enabled = True
                self.logger.info("Cross margin enabled")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error enabling cross margin: {e}")
            return False

    async def set_leverage(self, symbol: str, buy_leverage: float, sell_leverage: float) -> bool:
        """Set leverage for a symbol"""
        try:
            data = {
                "category": "linear",
                "symbol": symbol,
                "buyLeverage": str(buy_leverage),
                "sellLeverage": str(sell_leverage)
            }
            
            response = await self._make_request(
                "POST",
                "/v5/position/set-leverage",
                data
            )
            
            return response.get("retCode") == 0
            
        except Exception as e:
            self.logger.error(f"Error setting leverage: {e}")
            return False

    async def add_or_reduce_margin(self, symbol: str, margin: float, is_add: bool = True) -> bool:
        """Add or reduce margin for a position"""
        try:
            data = {
                "category": "linear",
                "symbol": symbol,
                "margin": str(margin),
                "operationType": "add" if is_add else "reduce"
            }
            
            response = await self._make_request(
                "POST",
                "/v5/position/add-margin",
                data
            )
            
            return response.get("retCode") == 0
            
        except Exception as e:
            self.logger.error(f"Error adding/reducing margin: {e}")
            return False

    async def get_margin_balance(self) -> Dict[str, Any]:
        """Get margin account balance"""
        try:
            response = await self._make_request(
                "GET",
                "/v5/account/wallet-balance",
                {"accountType": "UNIFIED"},
                signed=True
            )
            
            if response.get("retCode") == 0:
                return response.get("result", {})
            
            return {}
            
        except Exception as e:
            self.logger.error(f"Error getting margin balance: {e}")
            return {}

    # =====================================
    # DERIVATIVES TRADING METHODS
    # =====================================
    
    async def place_derivatives_order(self,
                                    symbol: str,
                                    side: str,
                                    order_type: str,
                                    qty: float,
                                    price: Optional[float] = None,
                                    trigger_price: Optional[float] = None,
                                    stop_loss: Optional[float] = None,
                                    take_profit: Optional[float] = None,
                                    time_in_force: str = "GTC",
                                    reduce_only: bool = False,
                                    close_on_trigger: bool = False) -> Optional[Dict]:
        """Place a derivatives order (futures/perpetuals)"""
        try:
            data = {
                "category": "linear",
                "symbol": symbol,
                "side": side,
                "orderType": order_type,
                "qty": str(qty),
                "timeInForce": time_in_force,
                "reduceOnly": reduce_only,
                "closeOnTrigger": close_on_trigger
            }
            
            if price:
                data["price"] = str(price)
            if trigger_price:
                data["triggerPrice"] = str(trigger_price)
            if stop_loss:
                data["stopLoss"] = str(stop_loss)
            if take_profit:
                data["takeProfit"] = str(take_profit)
            
            response = await self._make_request(
                "POST",
                "/v5/order/create",
                data
            )
            
            if response.get("retCode") == 0:
                return response.get("result", {})
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error placing derivatives order: {e}")
            return None

    async def get_derivatives_positions(self, symbol: Optional[str] = None) -> List[EnhancedPosition]:
        """Get all derivatives positions"""
        try:
            params = {"category": "linear", "settleCoin": "USDT"}  # Add required settleCoin parameter
            if symbol:
                params["symbol"] = symbol
            
            response = await self._make_request(
                "GET",
                "/v5/position/list",
                params,
                signed=True
            )
            
            positions = []
            if response.get("retCode") == 0:
                for pos_data in response.get("result", {}).get("list", []):
                    if float(pos_data.get("size", 0)) > 0:  # Only active positions
                        position = EnhancedPosition(
                            symbol=pos_data.get("symbol", ""),
                            side=pos_data.get("side", ""),
                            size=float(pos_data.get("size", 0)),
                            entry_price=float(pos_data.get("avgPrice", 0)),
                            mark_price=float(pos_data.get("markPrice", 0)),
                            unrealized_pnl=float(pos_data.get("unrealisedPnl", 0)),
                            leverage=float(pos_data.get("leverage", 1)),
                            margin_type=pos_data.get("marginMode", ""),
                            position_value=float(pos_data.get("positionValue", 0)),
                            funding_fee=float(pos_data.get("cumRealisedPnl", 0)),
                            auto_add_margin=pos_data.get("autoAddMargin", 0) == 1,
                            position_idx=int(pos_data.get("positionIdx", 0))
                        )
                        positions.append(position)
            
            return positions
            
        except Exception as e:
            self.logger.error(f"Error getting derivatives positions: {e}")
            return []

    async def close_derivatives_position(self, symbol: str, position_idx: int = 0) -> bool:
        """Close a derivatives position"""
        try:
            # Get current position to determine close parameters
            positions = await self.get_derivatives_positions(symbol)
            target_position = None
            
            for pos in positions:
                if pos.position_idx == position_idx:
                    target_position = pos
                    break
            
            if not target_position:
                self.logger.warning(f"No position found for {symbol} with idx {position_idx}")
                return False
            
            # Close position with market order
            close_side = "Sell" if target_position.side == "Buy" else "Buy"
            
            data = {
                "category": "linear",
                "symbol": symbol,
                "side": close_side,
                "orderType": "Market",
                "qty": str(target_position.size),
                "timeInForce": "IOC",
                "reduceOnly": True,
                "positionIdx": position_idx
            }
            
            response = await self._make_request(
                "POST",
                "/v5/order/create",
                data
            )
            
            return response.get("retCode") == 0
            
        except Exception as e:
            self.logger.error(f"Error closing derivatives position: {e}")
            return False

    # =====================================
    # FUNDING RATE ARBITRAGE
    # =====================================
    
    async def get_funding_rate(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get funding rate for a specific symbol - SINGULAR method for compatibility"""
        try:
            params = {
                "category": "linear",
                "symbol": symbol,
                "limit": 1
            }
            
            response = await self._make_request(
                "GET",
                "/v5/market/funding/history",
                params
            )
            
            if response.get("retCode") == 0 and response.get("result", {}).get("list"):
                funding_data = response["result"]["list"][0]
                return {
                    "symbol": symbol,
                    "fundingRate": float(funding_data.get("fundingRate", 0)),
                    "fundingTime": funding_data.get("fundingRateTimestamp", ""),
                    "nextFundingTime": funding_data.get("nextFundingTime", "")
                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting funding rate for {symbol}: {e}")
            return None

    async def get_funding_rates(self, symbols: Optional[List[str]] = None) -> Dict[str, float]:
        """Get current funding rates for arbitrage"""
        try:
            params = {"category": "linear"}
            
            response = await self._make_request(
                "GET",
                "/v5/market/funding/history",
                params
            )
            
            funding_rates = {}
            if response.get("retCode") == 0:
                for rate_data in response.get("result", {}).get("list", []):
                    symbol = rate_data.get("symbol", "")
                    rate = float(rate_data.get("fundingRate", 0))
                    
                    if not symbols or symbol in symbols:
                        funding_rates[symbol] = rate
            
            return funding_rates
            
        except Exception as e:
            self.logger.error(f"Error getting funding rates: {e}")
            return {}

    async def detect_funding_arbitrage_opportunities(self) -> List[Dict]:
        """Detect profitable funding rate arbitrage opportunities"""
        try:
            funding_rates = await self.get_funding_rates()
            
            opportunities = []
            for symbol, rate in funding_rates.items():
                # Look for high funding rates (profitable for short positions)
                if abs(rate) > 0.0001:  # 0.01% threshold
                    opportunity = {
                        "symbol": symbol,
                        "funding_rate": rate,
                        "annual_rate": rate * 3 * 365,  # 3 times daily
                        "recommended_side": "Short" if rate > 0 else "Long",
                        "profit_potential": abs(rate) * 100  # Percentage
                    }
                    opportunities.append(opportunity)
            
            # Sort by profit potential
            opportunities.sort(key=lambda x: x["profit_potential"], reverse=True)
            
            return opportunities[:10]  # Top 10 opportunities
            
        except Exception as e:
            self.logger.error(f"Error detecting funding arbitrage: {e}")
            return []

    # =====================================
    # ADVANCED ORDER MANAGEMENT
    # =====================================
    
    async def place_conditional_order(self,
                                    symbol: str,
                                    side: str,
                                    qty: float,
                                    trigger_price: float,
                                    order_price: Optional[float] = None,
                                    stop_loss: Optional[float] = None,
                                    take_profit: Optional[float] = None) -> Optional[Dict]:
        """Place a conditional order"""
        try:
            data = {
                "category": "linear",
                "symbol": symbol,
                "side": side,
                "orderType": "Limit" if order_price else "Market",
                "qty": str(qty),
                "triggerDirection": 1 if side == "Buy" else 2,
                "triggerPrice": str(trigger_price),
                "triggerBy": "LastPrice"
            }
            
            if order_price:
                data["price"] = str(order_price)
            if stop_loss:
                data["stopLoss"] = str(stop_loss)
            if take_profit:
                data["takeProfit"] = str(take_profit)
            
            response = await self._make_request(
                "POST",
                "/v5/order/create-conditional",
                data
            )
            
            if response.get("retCode") == 0:
                return response.get("result", {})
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error placing conditional order: {e}")
            return None

    async def place_bracket_order(self,
                                symbol: str,
                                side: str,
                                qty: float,
                                entry_price: float,
                                stop_loss: float,
                                take_profit: float) -> List[Optional[Dict]]:
        """Place a bracket order (entry + stop loss + take profit)"""
        try:
            results = []
            
            # 1. Place main entry order
            entry_order = await self.place_derivatives_order(
                symbol=symbol,
                side=side,
                order_type="Limit",
                qty=qty,
                price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit
            )
            results.append(entry_order)
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error placing bracket order: {e}")
            return [None]

    async def emergency_close_positions(self, margin_ratio_threshold: float) -> bool:
        """Emergency close all positions when margin ratio is too high"""
        try:
            self.logger.warning(f"EMERGENCY: Closing all positions due to margin ratio > {margin_ratio_threshold}%")

            # Get all open positions
            positions = await self.get_positions()
            if not positions:
                self.logger.info("No positions to close")
                return True

            closed_count = 0
            for position in positions:
                if float(position.get('size', 0)) > 0:  # Only close positions with size > 0
                    symbol = position['symbol']
                    size = position['size']
                    side = "Sell" if position['side'] == "Buy" else "Buy"  # Opposite side to close

                    try:
                        # Close position with market order
                        result = await self.place_derivatives_order(
                            symbol=symbol,
                            side=side,
                            order_type="Market",
                            qty=float(size),
                            reduce_only=True
                        )

                        if result:
                            closed_count += 1
                            self.logger.info(f"Emergency closed position: {symbol} {size}")
                        else:
                            self.logger.error(f"Failed to close position: {symbol}")

                    except Exception as e:
                        self.logger.error(f"Error closing position {symbol}: {e}")

            self.logger.info(f"Emergency closure complete: {closed_count} positions closed")
            return closed_count > 0

        except Exception as e:
            self.logger.error(f"Emergency close positions failed: {e}")
            return False

    # =====================================
    # SOCIAL TRADING SIGNALS
    # =====================================
    
    async def get_social_sentiment(self, symbol: str) -> Dict[str, Any]:
        """Get social sentiment data for a symbol"""
        try:
            # This would integrate with social media APIs
            # For now, return mock data structure
            sentiment_data = {
                "symbol": symbol,
                "sentiment_score": 0.0,  # -1 to 1
                "mention_count": 0,
                "trend": "neutral",  # bullish, bearish, neutral
                "confidence": 0.0,  # 0 to 1
                "sources": []
            }
            
            return sentiment_data
            
        except Exception as e:
            self.logger.error(f"Error getting social sentiment: {e}")
            return {}

    # =====================================
    # PERFORMANCE OPTIMIZATION
    # =====================================
    
    async def get_enhanced_performance_stats(self) -> Dict[str, Any]:
        """Get comprehensive performance statistics"""
        try:
            stats = {
                "api_calls": self.api_call_count,
                "error_count": self.error_count,
                "error_rate": self.error_count / max(self.api_call_count, 1),
                "avg_execution_time": np.mean(self.execution_times) if self.execution_times else 0,
                "active_positions": len(await self.get_derivatives_positions()),
                "followed_traders": len(self.followed_traders),
                "websocket_connections": len(self.ws_connections),
                "features": {
                    "cross_margin": self.cross_margin_enabled,
                    "hedge_mode": self.position_hedge_mode,
                    "unified_margin": self.unified_margin_account
                }
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Error getting performance stats: {e}")
            return {}

    # =====================================
    # PRIVATE HELPER METHODS
    # =====================================
    
    async def _initialize_advanced_features(self):
        """Initialize advanced trading features"""
        try:
            # Enable cross margin
            await self.enable_cross_margin()
            
            # Setup unified margin account
            await self._setup_unified_margin()
            
            self.logger.info("Advanced features initialized")
            
        except Exception as e:
            self.logger.error(f"Error initializing advanced features: {e}")

    async def _setup_unified_margin(self):
        """Setup unified margin account"""
        try:
            # Implementation for unified margin setup
            self.unified_margin_account = True
            
        except Exception as e:
            self.logger.error(f"Error setting up unified margin: {e}")

    async def _initialize_copy_trading(self):
        """Initialize copy trading features"""
        try:
            # Load existing followed traders
            traders = await self.get_copy_trading_traders()
            self.logger.info(f"Found {len(traders)} available traders for copy trading")
            
        except Exception as e:
            self.logger.error(f"Error initializing copy trading: {e}")

    async def _initialize_enhanced_websockets(self):
        """Initialize enhanced WebSocket connections"""
        try:
            # Start enhanced WebSocket streams for real-time data
            await self._start_enhanced_streams()
            
        except Exception as e:
            self.logger.error(f"Error initializing enhanced WebSockets: {e}")

    async def _start_enhanced_streams(self):
        """Start enhanced real-time data streams"""
        try:
            # Implementation for enhanced WebSocket streams
            pass
            
        except Exception as e:
            self.logger.error(f"Error starting enhanced streams: {e}")

    async def get_account_balance(self, account_type: str = "UNIFIED") -> Dict[str, Any]:
        """Get account balance information"""
        try:
            params = {"accountType": account_type}
            result = await self._make_request("GET", "/v5/account/wallet-balance", params, True)
            
            if result.get("retCode") == 0:
                return result.get("result", {})
            else:
                self.logger.error(f"Failed to get account balance: {result}")
                return {}
                
        except Exception as e:
            self.logger.error(f"Error getting account balance: {e}")
            return {}

    async def get_market_data(self, symbol: str, timeframe: str = "1", limit: int = 100, category: str = "linear") -> List[Dict[str, Any]]:
        """Get market data for a symbol with timeframe and limit support for compatibility"""
        try:
            # Get kline data with specified timeframe and limit
            kline_params = {"category": category, "symbol": symbol, "interval": timeframe, "limit": limit}
            kline_result = await self._make_request("GET", "/v5/market/kline", kline_params, False)
            
            if kline_result.get("retCode") == 0 and kline_result.get("result"):
                kline_list = kline_result["result"].get("list", [])
                
                # Convert kline data to OHLCV format
                market_data = []
                for kline in reversed(kline_list):  # Reverse to get chronological order
                    market_data.append({
                        "timestamp": int(kline[0]),
                        "open": float(kline[1]),
                        "high": float(kline[2]),
                        "low": float(kline[3]),
                        "close": float(kline[4]),
                        "volume": float(kline[5])
                    })
                
                return market_data
            
            return []
            
        except Exception as e:
            self.logger.error(f"Error getting market data for {symbol}: {e}")
            return []

    async def get_current_price(self, symbol: str, category: str = "linear") -> float:
        """Get current price for a symbol with backup data fallback"""
        try:
            ticker_params = {"category": category, "symbol": symbol}
            ticker_result = await self._make_request("GET", "/v5/market/tickers", ticker_params, False)

            if ticker_result.get("retCode") == 0 and ticker_result.get("result"):
                ticker_list = ticker_result["result"].get("list", [])
                if ticker_list:
                    return float(ticker_list[0].get("lastPrice", 0))

            # Primary API failed, try backup data
            self.logger.warning(f"Primary price API failed for {symbol}, trying backup sources...")
            backup_price = await self.backup_data_manager.get_backup_price(symbol)
            if backup_price:
                self.logger.info(f"Successfully retrieved backup price for {symbol}: {backup_price}")
                return backup_price

            return 0.0

        except Exception as e:
            self.logger.error(f"Error getting current price for {symbol}: {e}")
            # Try backup data on exception
            try:
                backup_price = await self.backup_data_manager.get_backup_price(symbol)
                if backup_price:
                    self.logger.info(f"Successfully retrieved backup price for {symbol} after exception: {backup_price}")
                    return backup_price
            except Exception as backup_e:
                self.logger.error(f"Backup price also failed for {symbol}: {backup_e}")
            return 0.0

    async def get_ticker(self, symbol: str, category: str = "linear") -> Dict[str, Any]:
        """Get ticker data for a symbol"""
        try:
            params = {"category": category, "symbol": symbol}
            result = await self._make_request("GET", "/v5/market/tickers", params, False)

            if result and result.get("retCode") == 0:
                ticker_list = result.get("result", {}).get("list", [])
                if ticker_list:
                    return ticker_list[0]
            return {}
        except Exception as e:
            self.logger.error(f"Error getting ticker for {symbol}: {e}")
            return {}

    async def get_order_book(self, symbol: str, limit: int = 25) -> Dict[str, Any]:
        """Get order book data for a symbol with backup data fallback"""
        try:
            params = {"category": "linear", "symbol": symbol, "limit": limit}
            result = await self._make_request("GET", "/v5/market/orderbook", params, False)

            if result and result.get("retCode") == 0:
                return result.get("result", {})

            # Primary API failed, try backup data
            self.logger.warning(f"Primary order book API failed for {symbol}, trying backup sources...")
            backup_data = await self.backup_data_manager.get_backup_order_book(symbol)
            if backup_data:
                self.logger.info(f"Successfully retrieved backup order book data for {symbol}")
                return backup_data

            return {}
        except Exception as e:
            self.logger.error(f"Error getting order book for {symbol}: {e}")
            # Try backup data on exception
            try:
                backup_data = await self.backup_data_manager.get_backup_order_book(symbol)
                if backup_data:
                    self.logger.info(f"Successfully retrieved backup order book data for {symbol} after exception")
                    return backup_data
            except Exception as backup_e:
                self.logger.error(f"Backup order book also failed for {symbol}: {backup_e}")
            return {}

    async def get_recent_trades(self, symbol: str, category: str = "linear", limit: int = 60) -> List[Dict[str, Any]]:
        """Get recent trades for a symbol"""
        try:
            params = {"category": category, "symbol": symbol, "limit": limit}
            result = await self._make_request("GET", "/v5/market/recent-trade", params, False)

            if result and result.get("retCode") == 0:
                return result.get("result", {}).get("list", [])
            return []
        except Exception as e:
            self.logger.error(f"Error getting recent trades for {symbol}: {e}")
            return []

    async def calculate_position_size(self, symbol: str, price: float, confidence: float) -> float:
        """Calculate optimal position size based on risk parameters"""
        try:
            # Get account balance
            account_info = await self.get_account_info()
            if not account_info:
                return 0.0

            # Basic position sizing (1% of account per trade)
            base_risk = 0.01
            confidence_multiplier = min(confidence, 1.0)

            # Calculate position size
            position_size = (base_risk * confidence_multiplier) / price

            # Minimum position size
            min_size = 0.001  # Minimum BTC quantity
            return max(position_size, min_size)

        except Exception as e:
            self.logger.error(f"Error calculating position size: {e}")
            return 0.0

    async def validate_order_risk(self, symbol: str, side: str, qty: float, price: float) -> bool:
        """Validate order against risk parameters"""
        try:
            # Get current positions
            positions = await self.get_positions()

            # Calculate order value
            order_value = qty * price

            # Basic risk checks
            if order_value < 1.0:  # Minimum $1 order
                return False

            if order_value > 10000.0:  # Maximum $10k order
                return False

            return True

        except Exception as e:
            self.logger.error(f"Error validating order risk: {e}")
            return False

    async def modify_order(self, symbol: str, order_id: str, qty: Optional[float] = None,
                          price: Optional[float] = None) -> Optional[Dict[str, Any]]:
        """Modify an existing order"""
        try:
            params = {
                "category": "linear",
                "symbol": symbol,
                "orderId": order_id
            }

            if qty is not None:
                params["qty"] = str(qty)
            if price is not None:
                params["price"] = str(price)

            response = await self._make_request("POST", "/v5/order/amend", params, signed=True)

            if response.get("retCode") == 0:
                return response.get("result", {})

            return None

        except Exception as e:
            self.logger.error(f"Error modifying order: {e}")
            return None

    async def cancel_all_orders(self, symbol: Optional[str] = None) -> bool:
        """Cancel all orders for a symbol or all symbols"""
        try:
            params = {"category": "linear"}
            if symbol:
                params["symbol"] = symbol
            else:
                params["settleCoin"] = "USDT"

            response = await self._make_request("POST", "/v5/order/cancel-all", params, signed=True)

            return response.get("retCode") == 0

        except Exception as e:
            self.logger.error(f"Error cancelling all orders: {e}")
            return False

    async def get_comprehensive_market_data(self, symbol: str, category: str = "linear") -> Dict[str, Any]:
        """Get comprehensive market data including ticker, kline, and orderbook"""
        try:
            # Get ticker data
            ticker_params = {"category": category, "symbol": symbol}
            ticker_result = await self._make_request("GET", "/v5/market/tickers", ticker_params, False)
            
            # Get kline data
            kline_params = {"category": category, "symbol": symbol, "interval": "1", "limit": 1}
            kline_result = await self._make_request("GET", "/v5/market/kline", kline_params, False)
            
            # Get orderbook data
            orderbook_params = {"category": category, "symbol": symbol, "limit": 25}
            orderbook_result = await self._make_request("GET", "/v5/market/orderbook", orderbook_params, False)
            
            return {
                "ticker": ticker_result.get("result", {}),
                "kline": kline_result.get("result", {}),
                "orderbook": orderbook_result.get("result", {})
            }
            
        except Exception as e:
            self.logger.error(f"Error getting comprehensive market data for {symbol}: {e}")
            return {}

    async def _make_request(self, method: str, endpoint: str, params: Optional[Dict] = None, signed: bool = True) -> Dict:
        """Make HTTP request to Bybit API with enhanced error handling"""
        try:
            # Track API calls
            self.api_call_count += 1
            start_time = time.time()
            
            # Use parent class method
            result = await super()._make_request(method, endpoint, params, signed)
            
            # Track execution time
            execution_time = time.time() - start_time
            self.execution_times.append(execution_time)
            
            # Keep only last 1000 execution times for memory efficiency
            if len(self.execution_times) > 1000:
                self.execution_times = self.execution_times[-1000:]
            
            return result
            
        except Exception as e:
            self.error_count += 1
            self.logger.error(f"Enhanced API request error: {e}")
            raise
