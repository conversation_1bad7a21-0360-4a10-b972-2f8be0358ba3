import { DefaultTheme } from '@react-navigation/native';
import { MD3DarkTheme } from 'react-native-paper';

export const colors = {
    primary: '#00ff88',
    primaryDark: '#00cc6a',
    primaryLight: '#33ff9a',
    secondary: '#ff4757',
    success: '#00ff88',
    warning: '#ffa726',
    error: '#ff4757',
    info: '#42a5f5',

    background: '#0a0a0a',
    surface: '#1a1a1a',
    surfaceVariant: '#2a2a2a',

    text: '#ffffff',
    textSecondary: '#b3b3b3',
    textTertiary: '#666666',

    border: '#333333',
    borderLight: '#444444',

    shadow: 'rgba(0, 0, 0, 0.3)',
    overlay: 'rgba(0, 0, 0, 0.5)',

    gradient: {
        primary: ['#00ff88', '#00cc6a'],
        secondary: ['#ff4757', '#ff3742'],
        background: ['#0a0a0a', '#1a1a1a'],
    },
};

export const spacing = {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
};

export const borderRadius = {
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    round: 50,
};

export const typography = {
    sizes: {
        xs: 10,
        sm: 12,
        md: 14,
        lg: 16,
        xl: 18,
        xxl: 20,
        title: 24,
        heading: 32,
    },
    weights: {
        light: '300' as const,
        regular: '400' as const,
        medium: '500' as const,
        semibold: '600' as const,
        bold: '700' as const,
    },
};

export const shadows = {
    small: {
        shadowColor: colors.shadow,
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    medium: {
        shadowColor: colors.shadow,
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.30,
        shadowRadius: 4.65,
        elevation: 8,
    },
    large: {
        shadowColor: colors.shadow,
        shadowOffset: {
            width: 0,
            height: 8,
        },
        shadowOpacity: 0.44,
        shadowRadius: 10.32,
        elevation: 16,
    },
};

// React Navigation theme
export const navigationTheme = {
    ...DefaultTheme,
    colors: {
        ...DefaultTheme.colors,
        primary: colors.primary,
        background: colors.background,
        card: colors.surface,
        text: colors.text,
        border: colors.border,
        notification: colors.secondary,
    },
};

// React Native Paper theme
export const paperTheme = {
    ...MD3DarkTheme,
    colors: {
        ...MD3DarkTheme.colors,
        primary: colors.primary,
        primaryContainer: colors.primaryDark,
        secondary: colors.secondary,
        background: colors.background,
        surface: colors.surface,
        surfaceVariant: colors.surfaceVariant,
        onPrimary: colors.background,
        onSecondary: colors.background,
        onBackground: colors.text,
        onSurface: colors.text,
        error: colors.error,
    },
};

export const theme = {
    colors,
    spacing,
    borderRadius,
    typography,
    shadows,
    navigation: navigationTheme,
    paper: paperTheme,
};
