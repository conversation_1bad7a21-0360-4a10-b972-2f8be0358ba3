import {
    Analytics,
    AutoAwesome,
    CheckCircle,
    Error,
    Lightbulb,
    Memory,
    Psychology,
    SmartToy,
    Speed,
    Timeline,
    TrendingUp,
    Warning,
} from '@mui/icons-material'
import {
    Avatar,
    Box,
    Button,
    Card,
    CardContent,
    Chip,
    Container,
    Divider,
    FormControlLabel,
    Grid,
    LinearProgress,
    List,
    ListItem,
    ListItemAvatar,
    ListItemText,
    Switch,
    Typography
} from '@mui/material'
import { motion } from 'framer-motion'
import React, { useState } from 'react'
import { Area, AreaChart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts'
import { useRealTimeData } from '../../hooks/useRealTimeData'

const AIStatus = () => {
    const [autoMode, setAutoMode] = useState(true)
    const [mlPredictions, setMlPredictions] = useState(true)
    const [sentimentAnalysis, setSentimentAnalysis] = useState(true)

    // Real-time data hook
    const {
        performanceMetrics,
        systemHealth,
        isConnected
    } = useRealTimeData()

    // Use real-time AI metrics or fallback to mock data
    const aiMetrics = {
        confidence: performanceMetrics?.aiConfidence || 87.3,
        accuracy: performanceMetrics?.accuracy || 94.2,
        learningRate: 0.001,
        modelVersion: 'v2.4.1',
        lastTrained: '2024-01-15 09:30:15',
        predictionSuccess: performanceMetrics?.predictionSuccess || 78.6,
        processingSpeed: 1.2, // ms
        memoryUsage: 68.4,
    }

    const aiModules = [
        {
            name: 'Market Predictor',
            status: 'active',
            confidence: 92.1,
            accuracy: 89.3,
            lastUpdate: '2 min ago',
            description: 'Advanced ML model for price prediction',
            icon: <TrendingUp />,
            color: '#00ff88',
        },
        {
            name: 'Sentiment Analyzer',
            status: 'active',
            confidence: 85.7,
            accuracy: 91.8,
            lastUpdate: '1 min ago',
            description: 'Real-time news and social sentiment analysis',
            icon: <Psychology />,
            color: '#42a5f5',
        },
        {
            name: 'Risk Manager',
            status: 'active',
            confidence: 96.4,
            accuracy: 94.7,
            lastUpdate: '30 sec ago',
            description: 'Dynamic risk assessment and portfolio optimization',
            icon: <Analytics />,
            color: '#ffa726',
        },
        {
            name: 'Pattern Recognition',
            status: 'learning',
            confidence: 78.2,
            accuracy: 82.1,
            lastUpdate: '5 min ago',
            description: 'Technical analysis pattern detection',
            icon: <AutoAwesome />,
            color: '#8e24aa',
        },
        {
            name: 'Arbitrage Detector',
            status: 'active',
            confidence: 91.8,
            accuracy: 96.2,
            lastUpdate: '15 sec ago',
            description: 'Cross-exchange arbitrage opportunity detection',
            icon: <SmartToy />,
            color: '#26a69a',
        },
        {
            name: 'News Impact Analyzer',
            status: 'training',
            confidence: 73.5,
            accuracy: 76.8,
            lastUpdate: '1 hour ago',
            description: 'News event impact prediction on market movements',
            icon: <Lightbulb />,
            color: '#ff7043',
        },
    ]

    const confidenceHistory = [
        { time: '00:00', confidence: 84.2, accuracy: 89.1 },
        { time: '04:00', confidence: 86.7, accuracy: 91.3 },
        { time: '08:00', confidence: 85.1, accuracy: 88.7 },
        { time: '12:00', confidence: 89.4, accuracy: 93.2 },
        { time: '16:00', confidence: 87.8, accuracy: 90.8 },
        { time: '20:00', confidence: 91.2, accuracy: 94.5 },
    ]

    const recentPredictions = [
        {
            timestamp: '14:30:25',
            asset: 'BTCUSDT',
            prediction: 'Bullish',
            confidence: 89.2,
            actual: 'Bullish',
            accuracy: true,
            impact: 'High',
        },
        {
            timestamp: '14:25:10',
            asset: 'ETHUSDT',
            prediction: 'Bearish',
            confidence: 76.8,
            actual: 'Neutral',
            accuracy: false,
            impact: 'Medium',
        },
        {
            timestamp: '14:20:45',
            asset: 'ADAUSDT',
            prediction: 'Bullish',
            confidence: 92.5,
            actual: 'Bullish',
            accuracy: true,
            impact: 'Medium',
        },
        {
            timestamp: '14:15:30',
            asset: 'SOLUSDT',
            prediction: 'Neutral',
            confidence: 83.1,
            actual: 'Neutral',
            accuracy: true,
            impact: 'Low',
        },
    ]

    const getStatusColor = (status) => {
        switch (status) {
            case 'active':
                return '#00ff88'
            case 'learning':
                return '#42a5f5'
            case 'training':
                return '#ffa726'
            case 'error':
                return '#ff4757'
            default:
                return '#666'
        }
    }

    const getStatusIcon = (status) => {
        switch (status) {
            case 'active':
                return <CheckCircle />
            case 'learning':
                return <Timeline />
            case 'training':
                return <Memory />
            case 'error':
                return <Error />
            default:
                return <Warning />
        }
    }

    return (
        <Container maxWidth="xl" sx={{ py: 3 }}>
            <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
            >
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
                    <Typography
                        variant="h4"
                        sx={{
                            fontWeight: 700,
                            color: '#ffffff',
                            background: 'linear-gradient(45deg, #00ff88, #00ccff)',
                            WebkitBackgroundClip: 'text',
                            WebkitTextFillColor: 'transparent',
                        }}
                    >
                        AI System Status
                    </Typography>

                    <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                        <FormControlLabel
                            control={
                                <Switch
                                    checked={autoMode}
                                    onChange={(e) => setAutoMode(e.target.checked)}
                                    sx={{
                                        '& .MuiSwitch-switchBase.Mui-checked': {
                                            color: '#00ff88',
                                        },
                                        '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                                            backgroundColor: '#00ff88',
                                        },
                                    }}
                                />
                            }
                            label={
                                <Typography sx={{ color: '#ffffff', fontWeight: 600 }}>
                                    Auto Learning
                                </Typography>
                            }
                        />

                        <Chip
                            label="SuperGPT AI Active"
                            icon={<Psychology />}
                            sx={{
                                backgroundColor: 'rgba(0, 255, 136, 0.1)',
                                color: '#00ff88',
                                border: '1px solid rgba(0, 255, 136, 0.3)',
                                fontWeight: 600,
                            }}
                        />
                    </Box>
                </Box>
            </motion.div>

            <Grid container spacing={3}>
                {/* AI Overview Metrics */}
                <Grid item xs={12} md={3}>
                    <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.6, delay: 0.1 }}
                    >
                        <Card
                            sx={{
                                background: 'rgba(255, 255, 255, 0.03)',
                                backdropFilter: 'blur(20px)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: 3,
                            }}
                        >
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                                    <Psychology sx={{ color: '#00ff88' }} />
                                    <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                        AI Confidence
                                    </Typography>
                                </Box>
                                <Typography variant="h3" sx={{ color: '#00ff88', fontWeight: 700, mb: 1 }}>
                                    {aiMetrics.confidence}%
                                </Typography>
                                <LinearProgress
                                    variant="determinate"
                                    value={aiMetrics.confidence}
                                    sx={{
                                        height: 8,
                                        borderRadius: 4,
                                        backgroundColor: 'rgba(255, 255, 255, 0.1)',
                                        '& .MuiLinearProgress-bar': {
                                            background: 'linear-gradient(90deg, #00ff88, #00cc6a)',
                                            borderRadius: 4,
                                        },
                                    }}
                                />
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>

                <Grid item xs={12} md={3}>
                    <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.6, delay: 0.2 }}
                    >
                        <Card
                            sx={{
                                background: 'rgba(255, 255, 255, 0.03)',
                                backdropFilter: 'blur(20px)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: 3,
                            }}
                        >
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                                    <Speed sx={{ color: '#42a5f5' }} />
                                    <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                        Accuracy
                                    </Typography>
                                </Box>
                                <Typography variant="h3" sx={{ color: '#42a5f5', fontWeight: 700, mb: 1 }}>
                                    {aiMetrics.accuracy}%
                                </Typography>
                                <LinearProgress
                                    variant="determinate"
                                    value={aiMetrics.accuracy}
                                    sx={{
                                        height: 8,
                                        borderRadius: 4,
                                        backgroundColor: 'rgba(255, 255, 255, 0.1)',
                                        '& .MuiLinearProgress-bar': {
                                            background: 'linear-gradient(90deg, #42a5f5, #1e88e5)',
                                            borderRadius: 4,
                                        },
                                    }}
                                />
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>

                <Grid item xs={12} md={3}>
                    <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.6, delay: 0.3 }}
                    >
                        <Card
                            sx={{
                                background: 'rgba(255, 255, 255, 0.03)',
                                backdropFilter: 'blur(20px)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: 3,
                            }}
                        >
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                                    <Memory sx={{ color: '#ffa726' }} />
                                    <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                        Memory Usage
                                    </Typography>
                                </Box>
                                <Typography variant="h3" sx={{ color: '#ffa726', fontWeight: 700, mb: 1 }}>
                                    {aiMetrics.memoryUsage}%
                                </Typography>
                                <LinearProgress
                                    variant="determinate"
                                    value={aiMetrics.memoryUsage}
                                    sx={{
                                        height: 8,
                                        borderRadius: 4,
                                        backgroundColor: 'rgba(255, 255, 255, 0.1)',
                                        '& .MuiLinearProgress-bar': {
                                            background: 'linear-gradient(90deg, #ffa726, #ff9800)',
                                            borderRadius: 4,
                                        },
                                    }}
                                />
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>

                <Grid item xs={12} md={3}>
                    <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.6, delay: 0.4 }}
                    >
                        <Card
                            sx={{
                                background: 'rgba(255, 255, 255, 0.03)',
                                backdropFilter: 'blur(20px)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: 3,
                            }}
                        >
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                                    <AutoAwesome sx={{ color: '#8e24aa' }} />
                                    <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                        Processing Speed
                                    </Typography>
                                </Box>
                                <Typography variant="h3" sx={{ color: '#8e24aa', fontWeight: 700, mb: 1 }}>
                                    {aiMetrics.processingSpeed}ms
                                </Typography>
                                <Typography variant="body2" sx={{ color: '#b3b3b3' }}>
                                    Ultra-fast inference
                                </Typography>
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>

                {/* AI Performance Chart */}
                <Grid item xs={12} lg={8}>
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.5 }}
                    >
                        <Card
                            sx={{
                                background: 'rgba(255, 255, 255, 0.03)',
                                backdropFilter: 'blur(20px)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: 3,
                                height: '400px',
                            }}
                        >
                            <CardContent sx={{ height: '100%' }}>
                                <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600, mb: 3 }}>
                                    AI Performance Over Time
                                </Typography>

                                <ResponsiveContainer width="100%" height="85%">
                                    <AreaChart data={confidenceHistory}>
                                        <defs>
                                            <linearGradient id="colorConfidence" x1="0" y1="0" x2="0" y2="1">
                                                <stop offset="5%" stopColor="#00ff88" stopOpacity={0.3} />
                                                <stop offset="95%" stopColor="#00ff88" stopOpacity={0} />
                                            </linearGradient>
                                            <linearGradient id="colorAccuracy" x1="0" y1="0" x2="0" y2="1">
                                                <stop offset="5%" stopColor="#42a5f5" stopOpacity={0.3} />
                                                <stop offset="95%" stopColor="#42a5f5" stopOpacity={0} />
                                            </linearGradient>
                                        </defs>
                                        <CartesianGrid strokeDasharray="3 3" stroke="rgba(255, 255, 255, 0.1)" />
                                        <XAxis dataKey="time" stroke="#b3b3b3" />
                                        <YAxis stroke="#b3b3b3" domain={[70, 100]} />
                                        <Tooltip
                                            contentStyle={{
                                                backgroundColor: 'rgba(20, 20, 20, 0.9)',
                                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                                borderRadius: '8px',
                                                color: '#ffffff',
                                            }}
                                        />
                                        <Area
                                            type="monotone"
                                            dataKey="confidence"
                                            stroke="#00ff88"
                                            strokeWidth={2}
                                            fillOpacity={1}
                                            fill="url(#colorConfidence)"
                                        />
                                        <Area
                                            type="monotone"
                                            dataKey="accuracy"
                                            stroke="#42a5f5"
                                            strokeWidth={2}
                                            fillOpacity={1}
                                            fill="url(#colorAccuracy)"
                                        />
                                    </AreaChart>
                                </ResponsiveContainer>
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>

                {/* AI Model Info */}
                <Grid item xs={12} lg={4}>
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.6 }}
                    >
                        <Card
                            sx={{
                                background: 'rgba(255, 255, 255, 0.03)',
                                backdropFilter: 'blur(20px)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: 3,
                                height: '400px',
                            }}
                        >
                            <CardContent sx={{ height: '100%' }}>
                                <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600, mb: 3 }}>
                                    Model Information
                                </Typography>

                                <Box sx={{ mb: 3 }}>
                                    <Typography variant="body2" sx={{ color: '#b3b3b3', mb: 1 }}>
                                        Model Version
                                    </Typography>
                                    <Typography variant="h6" sx={{ color: '#00ff88', fontWeight: 600 }}>
                                        {aiMetrics.modelVersion}
                                    </Typography>
                                </Box>

                                <Box sx={{ mb: 3 }}>
                                    <Typography variant="body2" sx={{ color: '#b3b3b3', mb: 1 }}>
                                        Last Training
                                    </Typography>
                                    <Typography variant="body1" sx={{ color: '#ffffff' }}>
                                        {aiMetrics.lastTrained}
                                    </Typography>
                                </Box>

                                <Box sx={{ mb: 3 }}>
                                    <Typography variant="body2" sx={{ color: '#b3b3b3', mb: 1 }}>
                                        Learning Rate
                                    </Typography>
                                    <Typography variant="body1" sx={{ color: '#ffffff' }}>
                                        {aiMetrics.learningRate}
                                    </Typography>
                                </Box>

                                <Box sx={{ mb: 3 }}>
                                    <Typography variant="body2" sx={{ color: '#b3b3b3', mb: 1 }}>
                                        Prediction Success Rate
                                    </Typography>
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                        <LinearProgress
                                            variant="determinate"
                                            value={aiMetrics.predictionSuccess}
                                            sx={{
                                                flexGrow: 1,
                                                height: 8,
                                                borderRadius: 4,
                                                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                                                '& .MuiLinearProgress-bar': {
                                                    background: 'linear-gradient(90deg, #8e24aa, #7b1fa2)',
                                                    borderRadius: 4,
                                                },
                                            }}
                                        />
                                        <Typography variant="body2" sx={{ color: '#8e24aa', fontWeight: 600 }}>
                                            {aiMetrics.predictionSuccess}%
                                        </Typography>
                                    </Box>
                                </Box>

                                <Button
                                    variant="outlined"
                                    fullWidth
                                    sx={{
                                        borderColor: '#00ff88',
                                        color: '#00ff88',
                                        '&:hover': {
                                            borderColor: '#00ff88',
                                            backgroundColor: 'rgba(0, 255, 136, 0.1)',
                                        },
                                    }}
                                >
                                    Retrain Model
                                </Button>
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>

                {/* AI Modules Status */}
                <Grid item xs={12} lg={8}>
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.7 }}
                    >
                        <Card
                            sx={{
                                background: 'rgba(255, 255, 255, 0.03)',
                                backdropFilter: 'blur(20px)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: 3,
                            }}
                        >
                            <CardContent>
                                <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600, mb: 3 }}>
                                    AI Modules Status
                                </Typography>

                                <Grid container spacing={2}>
                                    {aiModules.map((module, index) => (
                                        <Grid item xs={12} md={6} key={module.name}>
                                            <Box
                                                sx={{
                                                    p: 2,
                                                    background: 'rgba(255, 255, 255, 0.02)',
                                                    borderRadius: 2,
                                                    border: '1px solid rgba(255, 255, 255, 0.05)',
                                                }}
                                            >
                                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                        <Avatar
                                                            sx={{
                                                                width: 32,
                                                                height: 32,
                                                                backgroundColor: `${module.color}20`,
                                                                color: module.color,
                                                            }}
                                                        >
                                                            {module.icon}
                                                        </Avatar>
                                                        <Typography variant="body1" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                                            {module.name}
                                                        </Typography>
                                                    </Box>
                                                    <Chip
                                                        label={module.status}
                                                        size="small"
                                                        icon={getStatusIcon(module.status)}
                                                        sx={{
                                                            backgroundColor: `${getStatusColor(module.status)}20`,
                                                            color: getStatusColor(module.status),
                                                        }}
                                                    />
                                                </Box>

                                                <Typography variant="caption" sx={{ color: '#b3b3b3', display: 'block', mb: 2 }}>
                                                    {module.description}
                                                </Typography>

                                                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                                    <Typography variant="caption" sx={{ color: '#b3b3b3' }}>
                                                        Confidence: {module.confidence}%
                                                    </Typography>
                                                    <Typography variant="caption" sx={{ color: '#b3b3b3' }}>
                                                        Accuracy: {module.accuracy}%
                                                    </Typography>
                                                </Box>

                                                <LinearProgress
                                                    variant="determinate"
                                                    value={module.confidence}
                                                    sx={{
                                                        height: 4,
                                                        borderRadius: 2,
                                                        backgroundColor: 'rgba(255, 255, 255, 0.1)',
                                                        '& .MuiLinearProgress-bar': {
                                                            backgroundColor: module.color,
                                                            borderRadius: 2,
                                                        },
                                                    }}
                                                />

                                                <Typography variant="caption" sx={{ color: '#666', mt: 1, display: 'block' }}>
                                                    Last update: {module.lastUpdate}
                                                </Typography>
                                            </Box>
                                        </Grid>
                                    ))}
                                </Grid>
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>

                {/* Recent Predictions */}
                <Grid item xs={12} lg={4}>
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.8 }}
                    >
                        <Card
                            sx={{
                                background: 'rgba(255, 255, 255, 0.03)',
                                backdropFilter: 'blur(20px)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: 3,
                                height: '400px',
                            }}
                        >
                            <CardContent sx={{ height: '100%' }}>
                                <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600, mb: 3 }}>
                                    Recent Predictions
                                </Typography>

                                <List sx={{ overflow: 'auto', maxHeight: 'calc(100% - 60px)' }}>
                                    {recentPredictions.map((prediction, index) => (
                                        <React.Fragment key={index}>
                                            <ListItem sx={{ px: 0 }}>
                                                <ListItemAvatar>
                                                    <Avatar
                                                        sx={{
                                                            width: 32,
                                                            height: 32,
                                                            backgroundColor: prediction.accuracy ? '#00ff8820' : '#ff475720',
                                                            color: prediction.accuracy ? '#00ff88' : '#ff4757',
                                                        }}
                                                    >
                                                        {prediction.accuracy ? <CheckCircle /> : <Error />}
                                                    </Avatar>
                                                </ListItemAvatar>
                                                <ListItemText
                                                    primary={
                                                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                            <Typography variant="body2" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                                                {prediction.asset}
                                                            </Typography>
                                                            <Chip
                                                                label={prediction.prediction}
                                                                size="small"
                                                                sx={{
                                                                    backgroundColor: prediction.prediction === 'Bullish' ? '#00ff8820' :
                                                                        prediction.prediction === 'Bearish' ? '#ff475720' : '#ffa72620',
                                                                    color: prediction.prediction === 'Bullish' ? '#00ff88' :
                                                                        prediction.prediction === 'Bearish' ? '#ff4757' : '#ffa726',
                                                                    fontSize: '0.7rem',
                                                                }}
                                                            />
                                                        </Box>
                                                    }
                                                    secondary={
                                                        <Box>
                                                            <Typography variant="caption" sx={{ color: '#b3b3b3' }}>
                                                                Confidence: {prediction.confidence}% • Impact: {prediction.impact}
                                                            </Typography>
                                                            <Typography variant="caption" sx={{ color: '#666', display: 'block' }}>
                                                                {prediction.timestamp}
                                                            </Typography>
                                                        </Box>
                                                    }
                                                />
                                            </ListItem>
                                            {index < recentPredictions.length - 1 && <Divider sx={{ backgroundColor: 'rgba(255, 255, 255, 0.05)' }} />}
                                        </React.Fragment>
                                    ))}
                                </List>
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>
            </Grid>
        </Container>
    )
}

export default AIStatus
