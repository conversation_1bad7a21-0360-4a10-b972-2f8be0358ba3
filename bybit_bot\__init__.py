"""
Bybit Trading Bot - Advanced Autonomous Trading System

A comprehensive trading bot for Bybit exchange with AI-enhanced features:
- Ultra Profit Amplification Engine (3.3x profit targets)
- AI-enhanced margin trading with cross-margin optimization
- Advanced risk management and position sizing
- Real-time market analysis and strategy execution
- Autonomous trading with self-healing capabilities
"""

__version__ = "1.0.0"
__author__ = "Bybit Trading Bot Team"

# Core modules - imports deferred to avoid circular dependencies
# Note: Modules will be imported on demand to avoid circular dependencies

# Import core modules when needed
def get_core():
    try:
        from . import core
        return core
    except ImportError:
        return None

def get_exchange():
    try:
        from . import exchange
        return exchange
    except ImportError:
        return None

def get_ai():
    try:
        from . import ai
        return ai
    except ImportError:
        return None

def get_strategies():
    try:
        from . import strategies
        return strategies
    except ImportError:
        return None

def get_utils():
    try:
        from . import utils
        return utils
    except ImportError:
        return None
