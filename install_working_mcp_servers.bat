@echo off
echo [INFO] Installing REAL MCP Servers (verified packages only)
echo ============================================================

echo [STEP 1] Initializing NPM project on E drive...
cd /d E:\The_real_deal_copy\Bybit_Bot\BOT
if not exist package.json (
    npm init -y > nul
    echo [OK] NPM project initialized
) else (
    echo [OK] NPM project already exists
)

echo.
echo [STEP 2] Installing verified MCP packages...

echo [1/14] Installing Playwright MCP server...
npm install @playwright/mcp

echo [2/14] Installing Context7 MCP server...
npm install @upstash/context7-mcp

echo [3/14] Installing MCP Framework...
npm install mcp-framework

echo [4/14] Installing Supabase MCP utilities...
npm install @supabase/mcp-utils

echo [5/14] Installing Composio MCP...
npm install @composio/mcp

echo [6/14] Installing Browser MCP...
npm install @browsermcp/mcp

echo [7/14] Installing Mastra MCP...
npm install @mastra/mcp

echo [8/14] Installing MCP Proxy...
npm install mcp-proxy

echo [9/14] Installing Vercel MCP Adapter...
npm install @vercel/mcp-adapter

echo [10/14] Installing LangChain MCP Adapters...
npm install @langchain/mcp-adapters

echo [11/14] Installing Notion MCP Server...
npm install @notionhq/notion-mcp-server

echo [12/14] Installing Tavily MCP (Web Search)...
npm install tavily-mcp

echo [13/14] Installing Code Runner MCP...
npm install mcp-server-code-runner

echo [14/14] Installing n8n MCP Workflow...
npm install n8n-mcp

echo.
echo [STEP 3] Installing additional useful packages...
npm install @translated/lara-mcp
npm install @currents/mcp

echo.
echo [INFO] All verified MCP servers installed successfully!
echo [INFO] Packages installed:
echo   - Playwright automation
echo   - Context7 for enhanced context
echo   - Supabase database utilities
echo   - Browser automation
echo   - Web search with Tavily
echo   - Code execution capabilities
echo   - Notion integration
echo   - LangChain adapters
echo   - n8n workflow automation
echo   - Translation services
echo.
echo [NEXT] Please restart VS Code to activate all MCP servers
pause
