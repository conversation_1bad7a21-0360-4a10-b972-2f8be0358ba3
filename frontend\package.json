{"name": "autonomous-trader-dashboard", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/material": "^5.14.19", "@mui/icons-material": "^5.14.19", "@mui/x-charts": "^6.18.1", "@mui/x-data-grid": "^6.18.2", "recharts": "^2.8.0", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "framer-motion": "^10.16.5", "react-query": "^3.39.3", "react-hot-toast": "^2.4.1", "date-fns": "^2.30.0", "numeral": "^2.0.6", "react-countup": "^6.5.0", "react-intersection-observer": "^9.5.3", "@tremor/react": "^3.14.1", "react-grid-layout": "^1.4.4", "@tanstack/react-table": "^8.11.2", "react-window": "^1.8.8", "@headlessui/react": "^1.7.17", "react-hook-form": "^7.48.2", "zustand": "^4.4.7", "@floating-ui/react": "^0.26.4", "react-hotkeys-hook": "^4.4.1", "react-use": "^17.4.2", "lodash": "^4.17.21", "clsx": "^2.0.0", "react-beautiful-dnd": "^13.1.1", "react-virtualized-auto-sizer": "^1.0.20", "react-window-infinite-loader": "^1.0.9"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "vite": "^4.5.0"}}