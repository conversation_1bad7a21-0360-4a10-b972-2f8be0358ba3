#!/usr/bin/env python3
"""
Monitor live trading activity and verify real trades are being executed
"""

import asyncio
import sqlite3
import redis
import time
from datetime import datetime, timedelta
from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient

async def check_live_trading_activity():
    """Check for actual trading activity and account changes"""
    
    print("LIVE TRADING ACTIVITY MONITOR")
    print("=" * 50)
    print(f"Monitor started at: {datetime.now()}")
    print()
    
    # Initialize Bybit client
    client = EnhancedBybitClient()
    await client.initialize()
    
    # Get initial account balance
    initial_balance = await client.get_account_balance()
    if initial_balance and 'list' in initial_balance and len(initial_balance['list']) > 0:
        initial_equity = float(initial_balance['list'][0]['totalEquity'])
        initial_available = float(initial_balance['list'][0]['totalAvailableBalance'])
        print(f"INITIAL ACCOUNT STATUS:")
        print(f"  Total Equity: ${initial_equity:.2f}")
        print(f"  Available Balance: ${initial_available:.2f}")
    else:
        print("ERROR: Could not get initial account balance")
        return
    
    # Connect to databases
    db_conn = sqlite3.connect('bybit_trading_bot.db')
    redis_conn = redis.Redis(host='localhost', port=6379, decode_responses=True)
    
    print()
    print("MONITORING FOR LIVE TRADING ACTIVITY...")
    print("Checking every 30 seconds for:")
    print("  - Account balance changes")
    print("  - New trades in database")
    print("  - Redis trading signals")
    print("  - Active positions")
    print()
    
    monitor_count = 0
    
    while monitor_count < 20:  # Monitor for 10 minutes (20 * 30 seconds)
        monitor_count += 1
        
        print(f"[{datetime.now().strftime('%H:%M:%S')}] Check #{monitor_count}/20")
        
        try:
            # Check current account balance
            current_balance = await client.get_account_balance()
            if current_balance and 'list' in current_balance and len(current_balance['list']) > 0:
                current_equity = float(current_balance['list'][0]['totalEquity'])
                current_available = float(current_balance['list'][0]['totalAvailableBalance'])
                
                equity_change = current_equity - initial_equity
                available_change = current_available - initial_available
                
                print(f"  Account: ${current_equity:.2f} equity (${equity_change:+.2f}), ${current_available:.2f} available (${available_change:+.2f})")
                
                # Check for balance changes (indicating real trading)
                if abs(equity_change) > 0.01 or abs(available_change) > 0.01:
                    print(f"  *** BALANCE CHANGE DETECTED! Real trading activity confirmed! ***")
                    print(f"      Equity change: ${equity_change:+.2f}")
                    print(f"      Available change: ${available_change:+.2f}")
            
            # Check database for new trades
            cursor = db_conn.cursor()
            cursor.execute('SELECT COUNT(*) FROM trades')
            trade_count = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM ai_memories')
            ai_count = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM trading_memories')
            memory_count = cursor.fetchone()[0]
            
            print(f"  Database: {trade_count} trades, {ai_count} AI memories, {memory_count} trading memories")
            
            # Check Redis for trading signals
            redis_keys = len(redis_conn.keys())
            profit_data = redis_conn.get('profit:daily')
            trading_signals = redis_conn.get('trading:signal:trend')
            
            print(f"  Redis: {redis_keys} keys, profit: {profit_data}, signals: {trading_signals}")
            
            # Check for active positions
            positions = await client.get_positions()
            if positions:
                active_positions = [p for p in positions if float(p.get('size', 0)) > 0]
                print(f"  Positions: {len(active_positions)} active positions")
                
                if active_positions:
                    print("  *** ACTIVE POSITIONS DETECTED! ***")
                    for pos in active_positions[:3]:  # Show first 3 positions
                        symbol = pos.get('symbol', 'Unknown')
                        side = pos.get('side', 'Unknown')
                        size = pos.get('size', '0')
                        unrealized_pnl = pos.get('unrealisedPnl', '0')
                        print(f"      {symbol} {side} {size} (PnL: ${unrealized_pnl})")
            else:
                print(f"  Positions: 0 active positions")
            
            # Check for recent trades in database
            cursor.execute('SELECT * FROM trades WHERE timestamp > datetime("now", "-1 minute") ORDER BY timestamp DESC LIMIT 3')
            recent_trades = cursor.fetchall()
            
            if recent_trades:
                print(f"  *** RECENT TRADES DETECTED! ***")
                for trade in recent_trades:
                    print(f"      {trade[1]} {trade[2]} {trade[3]} {trade[4]} @ ${trade[5]}")
            
            print()
            
        except Exception as e:
            print(f"  ERROR during monitoring: {e}")
            print()
        
        # Wait 30 seconds before next check
        if monitor_count < 20:
            await asyncio.sleep(30)
    
    print("MONITORING COMPLETE")
    print(f"Monitor ended at: {datetime.now()}")
    
    # Final summary
    final_balance = await client.get_account_balance()
    if final_balance and 'list' in final_balance and len(final_balance['list']) > 0:
        final_equity = float(final_balance['list'][0]['totalEquity'])
        final_available = float(final_balance['list'][0]['totalAvailableBalance'])
        
        total_equity_change = final_equity - initial_equity
        total_available_change = final_available - initial_available
        
        print()
        print("FINAL SUMMARY:")
        print(f"  Initial Equity: ${initial_equity:.2f}")
        print(f"  Final Equity: ${final_equity:.2f}")
        print(f"  Total Change: ${total_equity_change:+.2f}")
        print()
        
        if abs(total_equity_change) > 0.01:
            print("*** LIVE TRADING CONFIRMED - ACCOUNT BALANCE CHANGED ***")
        else:
            print("*** NO BALANCE CHANGES DETECTED - VERIFY TRADING EXECUTION ***")
    
    db_conn.close()

if __name__ == "__main__":
    asyncio.run(check_live_trading_activity())
