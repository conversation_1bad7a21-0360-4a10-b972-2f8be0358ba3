import { useEffect, useCallback } from 'react'
import { useHotkeys } from 'react-hotkeys-hook'
import { useNavigate } from 'react-router-dom'
import toast from 'react-hot-toast'

/**
 * Custom hook for managing keyboard shortcuts throughout the application
 * Provides consistent keyboard navigation and quick actions
 */
export const useKeyboardShortcuts = () => {
    const navigate = useNavigate()
    
    // Navigation shortcuts
    useHotkeys('ctrl+1', () => navigate('/'), { preventDefault: true })
    useHotkeys('ctrl+2', () => navigate('/trading'), { preventDefault: true })
    useHotkeys('ctrl+3', () => navigate('/portfolio'), { preventDefault: true })
    useHotkeys('ctrl+4', () => navigate('/analytics'), { preventDefault: true })
    useHotkeys('ctrl+5', () => navigate('/ai-status'), { preventDefault: true })
    useHotkeys('ctrl+6', () => navigate('/settings'), { preventDefault: true })
    
    // Quick actions
    useHotkeys('ctrl+r', (e) => {
        e.preventDefault()
        window.location.reload()
    })
    
    useHotkeys('ctrl+shift+r', (e) => {
        e.preventDefault()
        // Trigger data refresh without page reload
        window.dispatchEvent(new CustomEvent('refreshData'))
        toast.info('Refreshing data...')
    })
    
    // Emergency stop
    useHotkeys('ctrl+shift+s', (e) => {
        e.preventDefault()
        window.dispatchEvent(new CustomEvent('emergencyStop'))
        toast.error('Emergency stop triggered!')
    })
    
    // Help modal
    useHotkeys('ctrl+shift+h', (e) => {
        e.preventDefault()
        window.dispatchEvent(new CustomEvent('showHelp'))
    })
    
    // Search/Command palette
    useHotkeys('ctrl+k', (e) => {
        e.preventDefault()
        window.dispatchEvent(new CustomEvent('showCommandPalette'))
    })
    
    return {
        shortcuts: {
            navigation: {
                'Ctrl+1': 'Go to Dashboard',
                'Ctrl+2': 'Go to Trading',
                'Ctrl+3': 'Go to Portfolio',
                'Ctrl+4': 'Go to Analytics',
                'Ctrl+5': 'Go to AI Status',
                'Ctrl+6': 'Go to Settings'
            },
            actions: {
                'Ctrl+R': 'Refresh Page',
                'Ctrl+Shift+R': 'Refresh Data',
                'Ctrl+Shift+S': 'Emergency Stop',
                'Ctrl+Shift+H': 'Show Help',
                'Ctrl+K': 'Command Palette'
            }
        }
    }
}

/**
 * Hook for trading-specific keyboard shortcuts
 */
export const useTradingShortcuts = (callbacks = {}) => {
    const {
        onBuy,
        onSell,
        onCloseAll,
        onToggleOrderForm,
        onCancelOrders
    } = callbacks
    
    // Trading shortcuts (only active on trading page)
    useHotkeys('b', () => {
        if (onBuy) onBuy()
    }, { scopes: ['trading'] })
    
    useHotkeys('s', () => {
        if (onSell) onSell()
    }, { scopes: ['trading'] })
    
    useHotkeys('c', () => {
        if (onCloseAll) onCloseAll()
    }, { scopes: ['trading'] })
    
    useHotkeys('o', () => {
        if (onToggleOrderForm) onToggleOrderForm()
    }, { scopes: ['trading'] })
    
    useHotkeys('escape', () => {
        if (onCancelOrders) onCancelOrders()
    }, { scopes: ['trading'] })
    
    return {
        shortcuts: {
            'B': 'Quick Buy',
            'S': 'Quick Sell',
            'C': 'Close All Positions',
            'O': 'Toggle Order Form',
            'Escape': 'Cancel Orders'
        }
    }
}

/**
 * Hook for chart-specific keyboard shortcuts
 */
export const useChartShortcuts = (callbacks = {}) => {
    const {
        onZoomIn,
        onZoomOut,
        onResetZoom,
        onToggleFullscreen,
        onChangeTimeframe
    } = callbacks
    
    useHotkeys('plus', () => {
        if (onZoomIn) onZoomIn()
    }, { scopes: ['chart'] })
    
    useHotkeys('minus', () => {
        if (onZoomOut) onZoomOut()
    }, { scopes: ['chart'] })
    
    useHotkeys('0', () => {
        if (onResetZoom) onResetZoom()
    }, { scopes: ['chart'] })
    
    useHotkeys('f', () => {
        if (onToggleFullscreen) onToggleFullscreen()
    }, { scopes: ['chart'] })
    
    // Timeframe shortcuts
    useHotkeys('1', () => {
        if (onChangeTimeframe) onChangeTimeframe('1m')
    }, { scopes: ['chart'] })
    
    useHotkeys('5', () => {
        if (onChangeTimeframe) onChangeTimeframe('5m')
    }, { scopes: ['chart'] })
    
    useHotkeys('shift+1', () => {
        if (onChangeTimeframe) onChangeTimeframe('1h')
    }, { scopes: ['chart'] })
    
    useHotkeys('shift+4', () => {
        if (onChangeTimeframe) onChangeTimeframe('4h')
    }, { scopes: ['chart'] })
    
    useHotkeys('d', () => {
        if (onChangeTimeframe) onChangeTimeframe('1d')
    }, { scopes: ['chart'] })
    
    return {
        shortcuts: {
            '+': 'Zoom In',
            '-': 'Zoom Out',
            '0': 'Reset Zoom',
            'F': 'Toggle Fullscreen',
            '1': '1 Minute',
            '5': '5 Minutes',
            'Shift+1': '1 Hour',
            'Shift+4': '4 Hours',
            'D': '1 Day'
        }
    }
}

/**
 * Hook for accessibility keyboard navigation
 */
export const useAccessibilityShortcuts = () => {
    useEffect(() => {
        const handleKeyDown = (e) => {
            // Skip to main content
            if (e.altKey && e.key === 'm') {
                e.preventDefault()
                const mainContent = document.querySelector('main')
                if (mainContent) {
                    mainContent.focus()
                    mainContent.scrollIntoView()
                }
            }
            
            // Skip to navigation
            if (e.altKey && e.key === 'n') {
                e.preventDefault()
                const navigation = document.querySelector('nav')
                if (navigation) {
                    navigation.focus()
                    navigation.scrollIntoView()
                }
            }
            
            // Announce current page
            if (e.altKey && e.key === 'a') {
                e.preventDefault()
                const pageTitle = document.title
                const announcement = `Current page: ${pageTitle}`
                
                // Create temporary element for screen reader announcement
                const announcer = document.createElement('div')
                announcer.setAttribute('aria-live', 'polite')
                announcer.setAttribute('aria-atomic', 'true')
                announcer.style.position = 'absolute'
                announcer.style.left = '-10000px'
                announcer.textContent = announcement
                
                document.body.appendChild(announcer)
                setTimeout(() => document.body.removeChild(announcer), 1000)
            }
        }
        
        document.addEventListener('keydown', handleKeyDown)
        return () => document.removeEventListener('keydown', handleKeyDown)
    }, [])
    
    return {
        shortcuts: {
            'Alt+M': 'Skip to Main Content',
            'Alt+N': 'Skip to Navigation',
            'Alt+A': 'Announce Current Page'
        }
    }
}

export default useKeyboardShortcuts
