"""
Economic Data Crawler
Collects and analyzes macroeconomic indicators
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional
import aiohttp
import pandas as pd
from fredapi import Fred

from ..core.config import BotConfig
from ..database.connection import DatabaseManager


class EconomicDataCrawler:
    """
    Advanced economic data crawler that:
    - Tracks macroeconomic indicators
    - Monitors central bank announcements
    - Analyzes economic calendar events
    - Correlates economic data with crypto markets
    """
    
    def __init__(self, config: BotConfig, db_manager: DatabaseManager):
        self.config = config
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
        self.running = False
        
        # Initialize FRED API for economic data
        self.fred_client = self._initialize_fred()
        
        # Economic indicators to track
        self.economic_indicators = {
            # US Economic Indicators
            'GDP': 'GDP',
            'INFLATION': 'CPIAUCSL',  # Consumer Price Index
            'UNEMPLOYMENT': 'UNRATE',
            'INTEREST_RATE': 'FEDFUNDS',  # Federal Funds Rate
            'M2_MONEY_SUPPLY': 'M2SL',
            'DXY': 'DEXUSEU',  # Dollar Index
            'VIX': 'VIXCLS',  # Volatility Index
            'GOLD_PRICE': 'GOLDAMGBD228NLBM',
            'OIL_PRICE': 'DCOILWTICO',
            'TREASURY_10Y': 'DGS10',
            'TREASURY_2Y': 'DGS2',
            'CORPORATE_BONDS': 'BAMLC0A0CM',
            'CONSUMER_SENTIMENT': 'UMCSENT',
            'INDUSTRIAL_PRODUCTION': 'INDPRO',
            'RETAIL_SALES': 'RSAFS',
            'HOUSING_STARTS': 'HOUST',
            'PAYROLL': 'PAYEMS',
        }
        
        # Calendar event sources
        self.calendar_sources = {
            'economic_calendar': 'https://api.tradingeconomics.com/calendar',
            'fed_calendar': 'https://api.stlouisfed.org/fred/releases',
        }
        
        self.crawl_tasks = []
        
    def _initialize_fred(self) -> Optional[Fred]:
        """Initialize FRED API client"""
        try:
            fred_config = self.config.get_api_key("fred")
            api_key = fred_config.get("api_key")
            
            if not api_key:
                self.logger.warning("FRED API key not configured")
                return None
            
            return Fred(api_key=api_key)
            
        except Exception as e:
            self.logger.error(f"Failed to initialize FRED client: {e}")
            return None
    
    async def start(self):
        """Start the economic data crawler"""
        if self.running:
            return
            
        self.running = True
        self.logger.info("📊 Starting economic data crawler...")
        
        # Start crawling tasks
        self.crawl_tasks = [
            asyncio.create_task(self._crawl_economic_indicators()),
            asyncio.create_task(self._crawl_economic_calendar()),
            asyncio.create_task(self._analyze_economic_correlations()),
            asyncio.create_task(self._monitor_central_bank_events()),
            asyncio.create_task(self._calculate_economic_indexes()),
        ]
        
        await asyncio.gather(*self.crawl_tasks, return_exceptions=True)
    
    async def stop(self):
        """Stop the economic data crawler"""
        self.running = False
        
        # Cancel all tasks
        for task in self.crawl_tasks:
            task.cancel()
            
        self.logger.info("🛑 Economic data crawler stopped")
    
    async def _crawl_economic_indicators(self):
        """Crawl economic indicators from FRED API"""
        while self.running:
            try:
                if not self.fred_client:
                    await asyncio.sleep(3600)
                    continue
                
                for indicator_name, series_id in self.economic_indicators.items():
                    try:
                        # Get latest data (last 30 days)
                        end_date = datetime.now()
                        start_date = end_date - timedelta(days=30)
                        
                        # Fetch data from FRED
                        data = self.fred_client.get_series(
                            series_id, 
                            start=start_date.strftime('%Y-%m-%d'),
                            end=end_date.strftime('%Y-%m-%d')
                        )
                        
                        if not data.empty:
                            # Process the data
                            for date, value in data.items():
                                if pd.notna(value):
                                    await self._store_economic_indicator({
                                        'indicator_name': indicator_name,
                                        'series_id': series_id,
                                        'date': date.to_pydatetime(),
                                        'value': float(value),
                                        'timestamp': datetime.now(timezone.utc)
                                    })
                        
                        # Calculate indicator analysis
                        await self._analyze_indicator_trend(indicator_name, series_id)
                        
                    except Exception as e:
                        self.logger.error(f"Error fetching {indicator_name}: {e}")
                    
                    await asyncio.sleep(60)  # Wait between indicators
                
                await asyncio.sleep(self.config.data_crawler.economic_data_interval)
                
            except Exception as e:
                self.logger.error(f"Error in economic indicators crawler: {e}")
                await asyncio.sleep(1800)
    
    async def _crawl_economic_calendar(self):
        """Crawl economic calendar events"""
        while self.running:
            try:
                # Get upcoming economic events
                events = await self._fetch_economic_events()
                
                for event in events:
                    await self._process_economic_event(event)
                
                await asyncio.sleep(self.config.data_crawler.economic_data_interval * 2)
                
            except Exception as e:
                self.logger.error(f"Error crawling economic calendar: {e}")
                await asyncio.sleep(3600)
    
    async def _analyze_economic_correlations(self):
        """Analyze correlations between economic data and crypto prices"""
        while self.running:
            try:
                # Get crypto price data for analysis
                symbols = self.config.get_trading_pairs()
                
                for symbol in symbols:
                    correlations = await self._calculate_economic_crypto_correlations(symbol)
                    if correlations:
                        await self._store_economic_correlations(symbol, correlations)
                
                await asyncio.sleep(7200)  # Analyze every 2 hours
                
            except Exception as e:
                self.logger.error(f"Error analyzing economic correlations: {e}")
                await asyncio.sleep(3600)
    
    async def _monitor_central_bank_events(self):
        """Monitor central bank announcements and meetings"""
        while self.running:
            try:
                # Monitor Fed events
                fed_events = await self._fetch_fed_events()
                
                for event in fed_events:
                    await self._process_central_bank_event(event, 'FED')
                
                # Monitor other central banks (ECB, BOJ, etc.)
                other_cb_events = await self._fetch_other_central_bank_events()
                
                for event in other_cb_events:
                    await self._process_central_bank_event(event, event.get('bank', 'UNKNOWN'))
                
                await asyncio.sleep(self.config.data_crawler.economic_data_interval * 3)
                
            except Exception as e:
                self.logger.error(f"Error monitoring central bank events: {e}")
                await asyncio.sleep(3600)
    
    async def _calculate_economic_indexes(self):
        """Calculate custom economic indexes for trading"""
        while self.running:
            try:
                # Calculate Fear & Greed Index based on economic data
                fear_greed_index = await self._calculate_fear_greed_index()
                
                # Calculate Inflation Pressure Index
                inflation_index = await self._calculate_inflation_pressure_index()
                
                # Calculate Liquidity Index
                liquidity_index = await self._calculate_liquidity_index()
                
                # Calculate Economic Momentum Index
                momentum_index = await self._calculate_economic_momentum_index()
                
                # Store indexes
                await self._store_economic_indexes({
                    'timestamp': datetime.now(timezone.utc),
                    'fear_greed_index': fear_greed_index,
                    'inflation_index': inflation_index,
                    'liquidity_index': liquidity_index,
                    'momentum_index': momentum_index
                })
                
                await asyncio.sleep(3600)  # Update every hour
                
            except Exception as e:
                self.logger.error(f"Error calculating economic indexes: {e}")
                await asyncio.sleep(1800)
    
    async def _fetch_economic_events(self) -> List[Dict]:
        """Fetch upcoming economic events"""
        events = []
        
        try:
            # Use Trading Economics API or similar
            async with aiohttp.ClientSession() as session:
                # Example: Fetch from economic calendar API
                te_config = self.config.get_api_key("trading_economics")
                api_key = te_config.get("api_key")
                
                if api_key:
                    url = f"https://api.tradingeconomics.com/calendar?c={api_key}&f=json"
                    
                    async with session.get(url) as response:
                        if response.status == 200:
                            data = await response.json()
                            events.extend(data)
                
                # Fetch from additional sources
                events.extend(await self._fetch_manual_economic_events())
                
        except Exception as e:
            self.logger.error(f"Error fetching economic events: {e}")
            
        return events
    
    async def _fetch_manual_economic_events(self) -> List[Dict]:
        """Fetch economic events from manual/backup sources"""
        # Define known recurring events
        upcoming_events = []
        now = datetime.now(timezone.utc)
        
        # FOMC meetings (8 times per year)
        fomc_dates = [
            '2024-01-31', '2024-03-20', '2024-05-01', '2024-06-12',
            '2024-07-31', '2024-09-18', '2024-11-07', '2024-12-18'
        ]
        
        for date_str in fomc_dates:
            event_date = datetime.strptime(date_str, '%Y-%m-%d')
            if event_date > now:
                upcoming_events.append({
                    'event': 'FOMC Meeting',
                    'date': event_date,
                    'country': 'US',
                    'importance': 'High',
                    'previous': None,
                    'forecast': None,
                    'actual': None
                })
                break  # Only add the next upcoming meeting
        
        # NFP (Non-Farm Payrolls) - First Friday of every month
        for month in range(now.month, 13):
            # Find first Friday of the month
            first_day = datetime(now.year, month, 1)
            days_to_friday = (4 - first_day.weekday()) % 7
            first_friday = first_day + timedelta(days=days_to_friday)
            
            if first_friday > now:
                upcoming_events.append({
                    'event': 'Non-Farm Payrolls',
                    'date': first_friday,
                    'country': 'US',
                    'importance': 'High',
                    'previous': None,
                    'forecast': None,
                    'actual': None
                })
                break
        
        return upcoming_events
    
    async def _fetch_fed_events(self) -> List[Dict]:
        """Fetch Federal Reserve events"""
        events = []
        
        try:
            if not self.fred_client:
                return events
            
            # Get Fed releases
            releases = self.fred_client.get_releases()
            
            for _, release in releases.head(10).iterrows():
                events.append({
                    'event': release['name'],
                    'date': datetime.now() + timedelta(days=1),  # Placeholder
                    'bank': 'FED',
                    'importance': 'Medium',
                    'release_id': release['id']
                })
                
        except Exception as e:
            self.logger.error(f"Error fetching Fed events: {e}")
            
        return events
    
    async def _fetch_other_central_bank_events(self) -> List[Dict]:
        """Fetch other central bank events"""
        events = []
        
        # Add known central bank meeting dates
        central_banks = {
            'ECB': ['2024-01-25', '2024-03-07', '2024-04-11', '2024-06-06'],
            'BOJ': ['2024-01-23', '2024-03-19', '2024-04-26', '2024-06-14'],
            'BOE': ['2024-02-01', '2024-03-21', '2024-05-09', '2024-06-20']
        }
        
        now = datetime.now(timezone.utc)
        
        for bank, dates in central_banks.items():
            for date_str in dates:
                event_date = datetime.strptime(date_str, '%Y-%m-%d')
                if event_date > now:
                    events.append({
                        'event': f'{bank} Policy Meeting',
                        'date': event_date,
                        'bank': bank,
                        'importance': 'High'
                    })
        
        return events
    
    async def _analyze_indicator_trend(self, indicator_name: str, series_id: str):
        """Analyze trend for an economic indicator"""
        try:
            # Get recent data for trend analysis
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=90)
            
            query = """
            SELECT date, value FROM economic_indicators 
            WHERE indicator_name = $1 AND date > $2 
            ORDER BY date ASC
            """
            
            data = await self.db_manager.fetch_all(query, indicator_name, cutoff_date)
            
            if len(data) >= 2:
                values = [row['value'] for row in data]
                dates = [row['date'] for row in data]
                
                # Calculate trend metrics
                recent_value = values[-1]
                previous_value = values[-2] if len(values) > 1 else values[-1]
                change_pct = ((recent_value - previous_value) / previous_value * 100) if previous_value != 0 else 0
                
                # Calculate moving averages
                ma_short = sum(values[-5:]) / min(5, len(values))
                ma_long = sum(values[-20:]) / min(20, len(values))
                
                # Determine trend direction
                if ma_short > ma_long:
                    trend = 'UPTREND'
                elif ma_short < ma_long:
                    trend = 'DOWNTREND'
                else:
                    trend = 'SIDEWAYS'
                
                # Store trend analysis
                await self._store_indicator_analysis({
                    'indicator_name': indicator_name,
                    'series_id': series_id,
                    'current_value': recent_value,
                    'change_pct': change_pct,
                    'trend': trend,
                    'ma_short': ma_short,
                    'ma_long': ma_long,
                    'analysis_date': datetime.now(timezone.utc)
                })
                
        except Exception as e:
            self.logger.error(f"Error analyzing trend for {indicator_name}: {e}")
    
    async def _calculate_economic_crypto_correlations(self, symbol: str) -> Dict:
        """Calculate correlations between economic data and crypto prices"""
        try:
            # Get crypto price data
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=30)
            
            crypto_query = """
            SELECT date_trunc('day', timestamp) as date, AVG(close_price) as price
            FROM market_data 
            WHERE symbol = $1 AND timestamp > $2 
            GROUP BY date_trunc('day', timestamp)
            ORDER BY date
            """
            
            crypto_data = await self.db_manager.fetch_all(crypto_query, symbol, cutoff_date)
            
            if len(crypto_data) < 10:
                return {}
            
            crypto_df = pd.DataFrame(crypto_data)
            crypto_df['date'] = pd.to_datetime(crypto_df['date'])
            crypto_df.set_index('date', inplace=True)
            
            correlations = {}
            
            # Calculate correlation with each economic indicator
            for indicator_name in self.economic_indicators.keys():
                econ_query = """
                SELECT date, value FROM economic_indicators 
                WHERE indicator_name = $1 AND date > $2 
                ORDER BY date
                """
                
                econ_data = await self.db_manager.fetch_all(econ_query, indicator_name, cutoff_date)
                
                if len(econ_data) >= 5:
                    econ_df = pd.DataFrame(econ_data)
                    econ_df['date'] = pd.to_datetime(econ_df['date'])
                    econ_df.set_index('date', inplace=True)
                    
                    # Merge and calculate correlation
                    merged = crypto_df.join(econ_df, how='inner', rsuffix='_econ')
                    
                    if len(merged) >= 5:
                        correlation = merged['price'].corr(merged['value'])
                        if not pd.isna(correlation):
                            correlations[indicator_name] = correlation
            
            return correlations
            
        except Exception as e:
            self.logger.error(f"Error calculating correlations for {symbol}: {e}")
            return {}
    
    async def _calculate_fear_greed_index(self) -> float:
        """Calculate custom Fear & Greed Index based on economic data"""
        try:
            # Get recent values for key indicators
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=7)
            
            components = {}
            
            # VIX component (fear indicator)
            vix_data = await self._get_latest_indicator('VIX', cutoff_date)
            if vix_data:
                # Normalize VIX (higher = more fear)
                vix_normalized = max(0, min(100, (vix_data - 10) / 30 * 100))
                components['vix'] = (100 - vix_normalized) * 0.25  # Invert for greed index
            
            # Dollar Index component
            dxy_data = await self._get_latest_indicator('DXY', cutoff_date)
            if dxy_data:
                # Strong dollar can be negative for crypto
                dxy_normalized = max(0, min(100, (dxy_data - 90) / 20 * 100))
                components['dxy'] = (100 - dxy_normalized) * 0.15
            
            # Interest rates component
            rates_data = await self._get_latest_indicator('INTEREST_RATE', cutoff_date)
            if rates_data:
                # Higher rates typically negative for crypto
                rates_normalized = max(0, min(100, rates_data / 6 * 100))
                components['rates'] = (100 - rates_normalized) * 0.20
            
            # Gold prices component
            gold_data = await self._get_latest_indicator('GOLD_PRICE', cutoff_date)
            if gold_data:
                # Rising gold can indicate uncertainty
                gold_ma = await self._get_indicator_ma('GOLD_PRICE', 30)
                if gold_ma:
                    gold_trend = (gold_data - gold_ma) / gold_ma * 100
                    gold_normalized = max(0, min(100, (gold_trend + 10) / 20 * 100))
                    components['gold'] = gold_normalized * 0.15
            
            # Money supply component
            m2_data = await self._get_latest_indicator('M2_MONEY_SUPPLY', cutoff_date)
            if m2_data:
                # Increasing money supply can be positive for crypto
                m2_ma = await self._get_indicator_ma('M2_MONEY_SUPPLY', 90)
                if m2_ma:
                    m2_trend = (m2_data - m2_ma) / m2_ma * 100
                    m2_normalized = max(0, min(100, (m2_trend + 5) / 10 * 100))
                    components['m2'] = m2_normalized * 0.25
            
            # Calculate weighted average
            if components:
                total_weight = sum([0.25, 0.15, 0.20, 0.15, 0.25][:len(components)])
                fear_greed = sum(components.values()) / total_weight * 100
                return max(0, min(100, fear_greed))
            else:
                return 50.0  # Neutral if no data
                
        except Exception as e:
            self.logger.error(f"Error calculating fear & greed index: {e}")
            return 50.0
    
    async def _calculate_inflation_pressure_index(self) -> float:
        """Calculate inflation pressure index"""
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=30)
            
            # Get CPI data
            cpi_data = await self._get_latest_indicator('INFLATION', cutoff_date)
            cpi_ma = await self._get_indicator_ma('INFLATION', 180)  # 6-month average
            
            # Get oil price trend
            oil_data = await self._get_latest_indicator('OIL_PRICE', cutoff_date)
            oil_ma = await self._get_indicator_ma('OIL_PRICE', 30)
            
            # Get money supply trend
            m2_data = await self._get_latest_indicator('M2_MONEY_SUPPLY', cutoff_date)
            m2_ma = await self._get_indicator_ma('M2_MONEY_SUPPLY', 180)
            
            components = []
            
            if cpi_data and cpi_ma:
                cpi_pressure = (cpi_data - cpi_ma) / cpi_ma * 100
                components.append(cpi_pressure * 0.5)
            
            if oil_data and oil_ma:
                oil_pressure = (oil_data - oil_ma) / oil_ma * 100
                components.append(oil_pressure * 0.3)
            
            if m2_data and m2_ma:
                m2_pressure = (m2_data - m2_ma) / m2_ma * 100
                components.append(m2_pressure * 0.2)
            
            if components:
                inflation_index = sum(components)
                return max(0, min(100, (inflation_index + 10) / 20 * 100))
            else:
                return 50.0
                
        except Exception as e:
            self.logger.error(f"Error calculating inflation pressure: {e}")
            return 50.0
    
    async def _calculate_liquidity_index(self) -> float:
        """Calculate market liquidity index"""
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=7)
            
            # Get Federal Funds Rate
            fed_rate = await self._get_latest_indicator('INTEREST_RATE', cutoff_date)
            
            # Get yield curve (10Y - 2Y spread)
            treasury_10y = await self._get_latest_indicator('TREASURY_10Y', cutoff_date)
            treasury_2y = await self._get_latest_indicator('TREASURY_2Y', cutoff_date)
            
            # Get M2 money supply trend
            m2_data = await self._get_latest_indicator('M2_MONEY_SUPPLY', cutoff_date)
            m2_ma = await self._get_indicator_ma('M2_MONEY_SUPPLY', 90)
            
            components = []
            
            # Lower rates = higher liquidity
            if fed_rate is not None:
                rate_component = (6 - fed_rate) / 6 * 100  # Normalize 0-6% range
                components.append(rate_component * 0.4)
            
            # Positive yield curve = normal liquidity
            if treasury_10y and treasury_2y:
                yield_spread = treasury_10y - treasury_2y
                spread_component = max(0, min(100, (yield_spread + 1) / 3 * 100))
                components.append(spread_component * 0.3)
            
            # Growing money supply = higher liquidity
            if m2_data and m2_ma:
                m2_growth = (m2_data - m2_ma) / m2_ma * 100
                m2_component = max(0, min(100, (m2_growth + 5) / 10 * 100))
                components.append(m2_component * 0.3)
            
            if components:
                return sum(components) / len(components)
            else:
                return 50.0
                
        except Exception as e:
            self.logger.error(f"Error calculating liquidity index: {e}")
            return 50.0
    
    async def _calculate_economic_momentum_index(self) -> float:
        """Calculate economic momentum index"""
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=30)
            
            indicators_data = {}
            
            # Key momentum indicators
            momentum_indicators = ['GDP', 'UNEMPLOYMENT', 'CONSUMER_SENTIMENT', 
                                 'INDUSTRIAL_PRODUCTION', 'RETAIL_SALES']
            
            for indicator in momentum_indicators:
                current = await self._get_latest_indicator(indicator, cutoff_date)
                ma = await self._get_indicator_ma(indicator, 90)
                
                if current is not None and ma is not None:
                    momentum = (current - ma) / ma * 100
                    indicators_data[indicator] = momentum
            
            if indicators_data:
                # Weight the indicators
                weights = {
                    'GDP': 0.3,
                    'UNEMPLOYMENT': -0.25,  # Negative weight (lower unemployment = better)
                    'CONSUMER_SENTIMENT': 0.2,
                    'INDUSTRIAL_PRODUCTION': 0.15,
                    'RETAIL_SALES': 0.1
                }
                
                weighted_momentum = 0
                total_weight = 0
                
                for indicator, momentum in indicators_data.items():
                    weight = weights.get(indicator, 0)
                    weighted_momentum += momentum * abs(weight)
                    total_weight += abs(weight)
                
                if total_weight > 0:
                    momentum_index = weighted_momentum / total_weight
                    return max(0, min(100, (momentum_index + 10) / 20 * 100))
            
            return 50.0
            
        except Exception as e:
            self.logger.error(f"Error calculating economic momentum: {e}")
            return 50.0
    
    async def _get_latest_indicator(self, indicator_name: str, cutoff_date: datetime) -> Optional[float]:
        """Get latest value for an economic indicator"""
        query = """
        SELECT value FROM economic_indicators 
        WHERE indicator_name = $1 AND date > $2 
        ORDER BY date DESC LIMIT 1
        """
        
        result = await self.db_manager.fetch_one(query, indicator_name, cutoff_date)
        return result['value'] if result else None
    
    async def _get_indicator_ma(self, indicator_name: str, days: int) -> Optional[float]:
        """Get moving average for an economic indicator"""
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
        
        query = """
        SELECT AVG(value) as ma FROM economic_indicators 
        WHERE indicator_name = $1 AND date > $2
        """
        
        result = await self.db_manager.fetch_one(query, indicator_name, cutoff_date)
        return result['ma'] if result and result['ma'] else None
    
    async def _process_economic_event(self, event: Dict):
        """Process an economic calendar event"""
        try:
            # Check if event already exists
            existing = await self._check_event_exists(event)
            if existing:
                return
            
            # Store economic event
            await self._store_economic_event({
                'event_name': event.get('event', 'Unknown'),
                'country': event.get('country', 'Unknown'),
                'date': event.get('date'),
                'importance': event.get('importance', 'Medium'),
                'previous': event.get('previous'),
                'forecast': event.get('forecast'),
                'actual': event.get('actual'),
                'timestamp': datetime.now(timezone.utc)
            })
            
        except Exception as e:
            self.logger.error(f"Error processing economic event: {e}")
    
    async def _process_central_bank_event(self, event: Dict, bank: str):
        """Process central bank event"""
        try:
            # Store central bank event with higher importance
            await self._store_central_bank_event({
                'bank': bank,
                'event_name': event.get('event', 'Unknown'),
                'date': event.get('date'),
                'importance': 'High',
                'description': event.get('description', ''),
                'timestamp': datetime.now(timezone.utc)
            })
            
        except Exception as e:
            self.logger.error(f"Error processing central bank event: {e}")
    
    async def _check_event_exists(self, event: Dict) -> bool:
        """Check if economic event already exists"""
        query = """
        SELECT id FROM economic_events 
        WHERE event_name = $1 AND date = $2
        """
        
        result = await self.db_manager.fetch_one(
            query, 
            event.get('event', 'Unknown'),
            event.get('date')
        )
        return result is not None
    
    async def _store_economic_indicator(self, data: Dict):
        """Store economic indicator data"""
        query = """
        INSERT INTO economic_indicators (indicator_name, series_id, date, value, timestamp)
        VALUES ($1, $2, $3, $4, $5)
        ON CONFLICT (indicator_name, date) DO UPDATE SET
        value = $4, timestamp = $5
        """
        
        await self.db_manager.execute(
            query,
            data['indicator_name'], data['series_id'], data['date'],
            data['value'], data['timestamp']
        )
    
    async def _store_indicator_analysis(self, data: Dict):
        """Store indicator trend analysis"""
        query = """
        INSERT INTO economic_indicator_analysis (indicator_name, series_id, current_value,
                                               change_pct, trend, ma_short, ma_long, analysis_date)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        ON CONFLICT (indicator_name, analysis_date) DO UPDATE SET
        current_value = $3, change_pct = $4, trend = $5, ma_short = $6, ma_long = $7
        """
        
        await self.db_manager.execute(
            query,
            data['indicator_name'], data['series_id'], data['current_value'],
            data['change_pct'], data['trend'], data['ma_short'], data['ma_long'],
            data['analysis_date']
        )
    
    async def _store_economic_correlations(self, symbol: str, correlations: Dict):
        """Store economic-crypto correlations"""
        for indicator, correlation in correlations.items():
            query = """
            INSERT INTO economic_crypto_correlations (symbol, indicator_name, correlation, timestamp)
            VALUES ($1, $2, $3, $4)
            ON CONFLICT (symbol, indicator_name, timestamp) DO UPDATE SET
            correlation = $3
            """
            
            await self.db_manager.execute(
                query, symbol, indicator, correlation, datetime.now(timezone.utc)
            )
    
    async def _store_economic_event(self, data: Dict):
        """Store economic calendar event"""
        query = """
        INSERT INTO economic_events (event_name, country, date, importance,
                                   previous, forecast, actual, timestamp)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        ON CONFLICT (event_name, date) DO NOTHING
        """
        
        await self.db_manager.execute(
            query,
            data['event_name'], data['country'], data['date'], data['importance'],
            data['previous'], data['forecast'], data['actual'], data['timestamp']
        )
    
    async def _store_central_bank_event(self, data: Dict):
        """Store central bank event"""
        query = """
        INSERT INTO central_bank_events (bank, event_name, date, importance, description, timestamp)
        VALUES ($1, $2, $3, $4, $5, $6)
        ON CONFLICT (bank, event_name, date) DO NOTHING
        """
        
        await self.db_manager.execute(
            query,
            data['bank'], data['event_name'], data['date'], data['importance'],
            data['description'], data['timestamp']
        )
    
    async def _store_economic_indexes(self, data: Dict):
        """Store calculated economic indexes"""
        query = """
        INSERT INTO economic_indexes (timestamp, fear_greed_index, inflation_index,
                                    liquidity_index, momentum_index)
        VALUES ($1, $2, $3, $4, $5)
        """
        
        await self.db_manager.execute(
            query,
            data['timestamp'], data['fear_greed_index'], data['inflation_index'],
            data['liquidity_index'], data['momentum_index']
        )
    
    async def get_economic_summary(self, days: int = 7) -> Dict:
        """Get economic data summary"""
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
        
        # Get latest economic indexes
        indexes_query = """
        SELECT * FROM economic_indexes 
        ORDER BY timestamp DESC LIMIT 1
        """
        indexes = await self.db_manager.fetch_one(indexes_query)
        
        # Get upcoming events
        events_query = """
        SELECT * FROM economic_events 
        WHERE date > $1 
        ORDER BY date ASC LIMIT 10
        """
        events = await self.db_manager.fetch_all(events_query, datetime.now(timezone.utc))
        
        # Get recent indicator changes
        indicators_query = """
        SELECT indicator_name, current_value, change_pct, trend
        FROM economic_indicator_analysis 
        WHERE analysis_date > $1
        ORDER BY ABS(change_pct) DESC
        LIMIT 10
        """
        indicators = await self.db_manager.fetch_all(indicators_query, cutoff_date)
        
        return {
            'economic_indexes': dict(indexes) if indexes else {},
            'upcoming_events': [dict(event) for event in events],
            'key_indicators': [dict(indicator) for indicator in indicators],
            'timeframe_days': days
        }
    
    async def get_correlation_analysis(self, symbol: str) -> Dict:
        """Get correlation analysis for a trading symbol"""
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=30)
        
        query = """
        SELECT indicator_name, correlation, timestamp
        FROM economic_crypto_correlations 
        WHERE symbol = $1 AND timestamp > $2
        ORDER BY ABS(correlation) DESC, timestamp DESC
        """
        
        correlations = await self.db_manager.fetch_all(query, symbol, cutoff_date)
        
        return {
            'symbol': symbol,
            'correlations': [dict(corr) for corr in correlations],
            'timeframe_days': 30
        }
