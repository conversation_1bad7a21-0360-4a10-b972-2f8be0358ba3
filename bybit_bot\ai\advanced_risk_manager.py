"""
ADVANCED RISK MANAGER - SOPHISTICATED RISK LEARNING
Advanced risk management with pattern learning and adaptive strategies
"""

import asyncio
import logging
import json
import time
import math
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from collections import deque, defaultdict
import statistics

logger = logging.getLogger(__name__)

@dataclass
class RiskAssessment:
    """Risk assessment result"""
    symbol: str
    risk_level: str  # 'very_low', 'low', 'medium', 'high', 'very_high'
    risk_score: float  # 0.0 to 1.0
    max_position_size: float
    stop_loss_level: float
    take_profit_level: float
    confidence: float
    risk_factors: List[str]
    margin_impact: float

class AdvancedRiskManager:
    """
    Advanced risk management with machine learning capabilities
    Learns from market patterns and adapts risk parameters
    """
    
    def __init__(self, config: Dict[str, Any], database_manager=None, bybit_client=None):
        self.config = config
        self.database_manager = database_manager
        self.bybit_client = bybit_client
        self.logger = logging.getLogger(__name__)
        
        # Risk learning data
        self.risk_history = defaultdict(lambda: deque(maxlen=1000))
        self.loss_patterns = defaultdict(lambda: deque(maxlen=500))
        self.volatility_patterns = defaultdict(lambda: deque(maxlen=500))
        self.correlation_matrix = defaultdict(lambda: defaultdict(float))
        
        # Adaptive risk parameters
        self.base_risk_tolerance = 0.02  # 2% base risk per trade
        self.dynamic_risk_multiplier = defaultdict(lambda: 1.0)
        self.volatility_adjustment = defaultdict(lambda: 1.0)
        self.correlation_adjustment = defaultdict(lambda: 1.0)
        
        # Learning parameters
        self.learning_rate = 0.01
        self.risk_memory_decay = 0.95
        
        # Performance tracking
        self.risk_prediction_accuracy = defaultdict(lambda: deque(maxlen=100))
        self.drawdown_prevention_score = deque(maxlen=100)
        
        self.logger.info("Advanced Risk Manager initialized - FULL LEARNING CAPABILITIES")
    
    async def initialize(self):
        """Initialize the advanced risk manager"""
        try:
            self.logger.info("Initializing Advanced Risk Manager...")
            # Load historical risk data if available
            await self._load_risk_history()
            self.logger.info("Advanced Risk Manager initialization complete")
        except Exception as e:
            self.logger.error(f"Failed to initialize Advanced Risk Manager: {e}")
            raise
    
    async def _load_risk_history(self):
        """Load historical risk data from database"""
        try:
            if self.database_manager:
                # Load any existing risk history
                pass
        except Exception as e:
            self.logger.warning(f"Could not load risk history: {e}")
    
    async def assess_risk(self, symbol: str, market_data: Dict[str, Any], 
                         account_info: Dict[str, Any]) -> RiskAssessment:
        """
        Comprehensive risk assessment using advanced learning algorithms
        """
        try:
            current_price = float(market_data.get('price', 0))
            volume = float(market_data.get('volume', 0))
            margin_ratio = float(account_info.get('marginRatio', 0))
            available_balance = float(account_info.get('totalAvailableBalance', 0))
            
            # Multi-factor risk analysis
            volatility_risk = await self._assess_volatility_risk(symbol, market_data)
            margin_risk = self._assess_margin_risk(margin_ratio)
            correlation_risk = await self._assess_correlation_risk(symbol)
            liquidity_risk = self._assess_liquidity_risk(symbol, volume)
            pattern_risk = await self._assess_pattern_risk(symbol, market_data)
            
            # Combine risk factors with learned weights
            risk_factors = [
                ('volatility', volatility_risk, 0.25),
                ('margin', margin_risk, 0.30),
                ('correlation', correlation_risk, 0.15),
                ('liquidity', liquidity_risk, 0.15),
                ('pattern', pattern_risk, 0.15)
            ]
            
            # Calculate weighted risk score
            total_risk_score = sum(risk * weight for _, risk, weight in risk_factors)
            
            # Apply dynamic adjustments based on learning
            total_risk_score *= self.dynamic_risk_multiplier[symbol]
            total_risk_score *= self.volatility_adjustment[symbol]
            total_risk_score *= self.correlation_adjustment[symbol]
            
            # Determine risk level
            if total_risk_score < 0.2:
                risk_level = 'very_low'
            elif total_risk_score < 0.4:
                risk_level = 'low'
            elif total_risk_score < 0.6:
                risk_level = 'medium'
            elif total_risk_score < 0.8:
                risk_level = 'high'
            else:
                risk_level = 'very_high'
            
            # Calculate position sizing based on risk
            max_position_size = self._calculate_position_size(
                total_risk_score, available_balance, margin_ratio
            )
            
            # Calculate stop loss and take profit levels
            stop_loss_level, take_profit_level = self._calculate_levels(
                current_price, total_risk_score, volatility_risk
            )
            
            # Calculate confidence in risk assessment
            confidence = self._calculate_confidence(symbol, risk_factors)
            
            # Estimate margin impact
            margin_impact = max_position_size / available_balance if available_balance > 0 else 0.0
            
            # Compile risk factors list
            active_risk_factors = [name for name, risk, _ in risk_factors if risk > 0.5]
            
            result = RiskAssessment(
                symbol=symbol,
                risk_level=risk_level,
                risk_score=total_risk_score,
                max_position_size=max_position_size,
                stop_loss_level=stop_loss_level,
                take_profit_level=take_profit_level,
                confidence=confidence,
                risk_factors=active_risk_factors,
                margin_impact=margin_impact
            )
            
            # Store for learning
            self.risk_history[symbol].append((time.time(), result))
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in risk assessment: {e}")
            return RiskAssessment(
                symbol=symbol,
                risk_level='very_high',
                risk_score=1.0,
                max_position_size=0.0,
                stop_loss_level=current_price * 0.95,
                take_profit_level=current_price * 1.05,
                confidence=0.0,
                risk_factors=['error'],
                margin_impact=0.0
            )
    
    async def _assess_volatility_risk(self, symbol: str, market_data: Dict[str, Any]) -> float:
        """Assess volatility risk using advanced volatility models"""
        try:
            # Get historical volatility data
            if symbol not in self.volatility_patterns or len(self.volatility_patterns[symbol]) < 10:
                return 0.5  # Default medium risk
            
            recent_volatilities = list(self.volatility_patterns[symbol])[-20:]
            current_volatility = float(market_data.get('volatility', 0))
            
            # Calculate volatility percentile
            if recent_volatilities:
                sorted_vols = sorted(recent_volatilities)
                percentile = sum(1 for v in sorted_vols if v <= current_volatility) / len(sorted_vols)
                
                # High percentile = high volatility = high risk
                volatility_risk = percentile
                
                # Apply GARCH-like volatility clustering detection
                if len(recent_volatilities) >= 5:
                    recent_avg = statistics.mean(recent_volatilities[-5:])
                    historical_avg = statistics.mean(recent_volatilities)
                    
                    if recent_avg > historical_avg * 1.5:
                        volatility_risk *= 1.3  # Volatility clustering detected
                
                return min(1.0, volatility_risk)
            
            return 0.5
            
        except Exception as e:
            self.logger.error(f"Error assessing volatility risk: {e}")
            return 0.7  # Conservative default
    
    def _assess_margin_risk(self, margin_ratio: float) -> float:
        """Assess margin risk based on current margin levels"""
        if margin_ratio > 95:
            return 1.0  # Maximum risk
        elif margin_ratio > 90:
            return 0.9
        elif margin_ratio > 80:
            return 0.7
        elif margin_ratio > 70:
            return 0.5
        elif margin_ratio > 50:
            return 0.3
        else:
            return 0.1  # Low risk
    
    async def _assess_correlation_risk(self, symbol: str) -> float:
        """Assess correlation risk with other positions"""
        try:
            # Calculate correlation with other active symbols
            correlations = []
            
            for other_symbol in self.correlation_matrix[symbol]:
                if other_symbol != symbol:
                    correlation = abs(self.correlation_matrix[symbol][other_symbol])
                    correlations.append(correlation)
            
            if correlations:
                avg_correlation = statistics.mean(correlations)
                max_correlation = max(correlations)
                
                # High correlation = high risk (portfolio concentration)
                correlation_risk = (avg_correlation * 0.7 + max_correlation * 0.3)
                return min(1.0, correlation_risk)
            
            return 0.2  # Low risk if no correlations
            
        except Exception as e:
            self.logger.error(f"Error assessing correlation risk: {e}")
            return 0.3
    
    def _assess_liquidity_risk(self, symbol: str, volume: float) -> float:
        """Assess liquidity risk based on volume patterns"""
        try:
            # Get historical volume data
            if symbol not in self.risk_history or len(self.risk_history[symbol]) < 10:
                return 0.3  # Default low-medium risk

            # Calculate volume percentile - FIX: Use proper indexing instead of slice
            historical_data = list(self.risk_history[symbol])[-50:]  # Get last 50 entries
            historical_volumes = []

            for timestamp, risk_assessment in historical_data:
                # Extract volume data from risk assessment if available
                if hasattr(risk_assessment, 'margin_impact'):
                    historical_volumes.append(float(risk_assessment.margin_impact))

            # If no historical volumes, use current volume as baseline
            if not historical_volumes:
                historical_volumes = [volume] if volume > 0 else [1000000]  # Default volume

            if historical_volumes and volume > 0:
                avg_volume = statistics.mean(historical_volumes)

                if volume < avg_volume * 0.5:
                    return 0.8  # Low volume = high liquidity risk
                elif volume < avg_volume * 0.8:
                    return 0.5  # Medium liquidity risk
                else:
                    return 0.2  # Good liquidity

            return 0.3

        except Exception as e:
            self.logger.error(f"Error assessing liquidity risk: {e}")
            return 0.4
    
    async def _assess_pattern_risk(self, symbol: str, market_data: Dict[str, Any]) -> float:
        """Assess risk based on learned loss patterns"""
        try:
            current_price = float(market_data.get('price', 0))
            
            # Analyze historical loss patterns
            if symbol not in self.loss_patterns or len(self.loss_patterns[symbol]) < 5:
                return 0.4  # Default medium risk
            
            loss_events = list(self.loss_patterns[symbol])
            
            # Check for similar market conditions that led to losses
            pattern_risk = 0.0
            pattern_count = 0
            
            for loss_event in loss_events[-20:]:  # Recent loss patterns
                loss_price = loss_event.get('price', 0)
                loss_conditions = loss_event.get('conditions', {})
                
                # Calculate similarity to current conditions
                price_similarity = 1.0 - abs(current_price - loss_price) / max(current_price, loss_price)
                
                if price_similarity > 0.8:  # Similar price levels
                    pattern_risk += loss_event.get('severity', 0.5)
                    pattern_count += 1
            
            if pattern_count > 0:
                pattern_risk /= pattern_count
                return min(1.0, pattern_risk)
            
            return 0.3  # Low risk if no similar patterns
            
        except Exception as e:
            self.logger.error(f"Error assessing pattern risk: {e}")
            return 0.4
    
    def _calculate_position_size(self, risk_score: float, available_balance: float, 
                               margin_ratio: float) -> float:
        """Calculate optimal position size based on risk assessment"""
        # Base position size as percentage of available balance
        base_percentage = self.base_risk_tolerance
        
        # Adjust based on risk score
        risk_adjustment = 1.0 - risk_score
        
        # Adjust based on margin ratio
        if margin_ratio > 90:
            margin_adjustment = 0.1
        elif margin_ratio > 80:
            margin_adjustment = 0.3
        elif margin_ratio > 70:
            margin_adjustment = 0.5
        elif margin_ratio > 50:
            margin_adjustment = 0.7
        else:
            margin_adjustment = 1.0
        
        # Calculate final position size
        position_percentage = base_percentage * risk_adjustment * margin_adjustment
        max_position_size = available_balance * position_percentage
        
        # Apply absolute limits
        max_position_size = min(max_position_size, available_balance * 0.1)  # Never more than 10%
        max_position_size = max(max_position_size, 0.01)  # Minimum $0.01
        
        return max_position_size
    
    def _calculate_levels(self, current_price: float, risk_score: float, 
                         volatility_risk: float) -> Tuple[float, float]:
        """Calculate stop loss and take profit levels"""
        # Base stop loss percentage
        base_stop_loss = 0.02  # 2%
        
        # Adjust based on risk and volatility
        stop_loss_percentage = base_stop_loss * (1.0 + risk_score) * (1.0 + volatility_risk)
        stop_loss_percentage = min(stop_loss_percentage, 0.1)  # Max 10% stop loss
        
        # Calculate levels
        stop_loss_level = current_price * (1.0 - stop_loss_percentage)
        
        # Take profit is typically 2-3x the stop loss distance
        profit_distance = stop_loss_percentage * 2.5
        take_profit_level = current_price * (1.0 + profit_distance)
        
        return stop_loss_level, take_profit_level
    
    def _calculate_confidence(self, symbol: str, risk_factors: List[Tuple[str, float, float]]) -> float:
        """Calculate confidence in risk assessment"""
        # Base confidence on historical accuracy
        if symbol in self.risk_prediction_accuracy and self.risk_prediction_accuracy[symbol]:
            historical_accuracy = statistics.mean(self.risk_prediction_accuracy[symbol])
        else:
            historical_accuracy = 0.5  # Default
        
        # Adjust based on data availability
        data_availability = min(1.0, len(self.risk_history[symbol]) / 100.0)
        
        # Adjust based on risk factor agreement
        risk_values = [risk for _, risk, _ in risk_factors]
        if len(risk_values) > 1:
            risk_variance = statistics.variance(risk_values)
            agreement_factor = max(0.5, 1.0 - risk_variance)
        else:
            agreement_factor = 0.7
        
        confidence = historical_accuracy * 0.5 + data_availability * 0.3 + agreement_factor * 0.2
        return min(1.0, max(0.1, confidence))
    
    async def learn_from_outcome(self, symbol: str, trade_result: Dict[str, Any]):
        """Learn from trade outcomes to improve risk assessment"""
        try:
            profit = trade_result.get('profit', 0.0)
            loss = trade_result.get('loss', 0.0)
            actual_risk = trade_result.get('actual_risk', 0.0)
            
            # Update loss patterns if trade was a loss
            if loss > 0:
                loss_event = {
                    'price': trade_result.get('price', 0.0),
                    'severity': min(1.0, loss / trade_result.get('position_size', 1.0)),
                    'conditions': {
                        'volatility': trade_result.get('volatility', 0.0),
                        'volume': trade_result.get('volume', 0.0),
                        'margin_ratio': trade_result.get('margin_ratio', 0.0)
                    },
                    'timestamp': time.time()
                }
                self.loss_patterns[symbol].append(loss_event)
            
            # Update risk prediction accuracy
            if 'predicted_risk' in trade_result:
                predicted_risk = trade_result['predicted_risk']
                accuracy = 1.0 - abs(predicted_risk - actual_risk)
                self.risk_prediction_accuracy[symbol].append(accuracy)
            
            # Adjust dynamic risk multiplier based on outcomes
            if profit > 0 and actual_risk < 0.3:
                # Good outcome with low risk - can be slightly more aggressive
                self.dynamic_risk_multiplier[symbol] *= 0.98
            elif loss > 0 and actual_risk > 0.7:
                # Bad outcome with high risk - be more conservative
                self.dynamic_risk_multiplier[symbol] *= 1.05
            
            # Keep multiplier within reasonable bounds
            self.dynamic_risk_multiplier[symbol] = max(0.5, min(2.0, self.dynamic_risk_multiplier[symbol]))
            
        except Exception as e:
            self.logger.error(f"Error learning from outcome: {e}")
    
    async def update_correlations(self, symbols: List[str], price_data: Dict[str, List[float]]):
        """Update correlation matrix between symbols"""
        try:
            for symbol1 in symbols:
                for symbol2 in symbols:
                    if symbol1 != symbol2 and symbol1 in price_data and symbol2 in price_data:
                        prices1 = price_data[symbol1]
                        prices2 = price_data[symbol2]
                        
                        if len(prices1) >= 10 and len(prices2) >= 10:
                            # Calculate correlation coefficient
                            correlation = self._calculate_correlation(prices1[-20:], prices2[-20:])
                            
                            # Update with exponential smoothing
                            old_correlation = self.correlation_matrix[symbol1][symbol2]
                            self.correlation_matrix[symbol1][symbol2] = (
                                old_correlation * 0.9 + correlation * 0.1
                            )
                            
        except Exception as e:
            self.logger.error(f"Error updating correlations: {e}")
    
    def _calculate_correlation(self, prices1: List[float], prices2: List[float]) -> float:
        """Calculate correlation coefficient between two price series"""
        try:
            if len(prices1) != len(prices2) or len(prices1) < 2:
                return 0.0
            
            # Calculate returns
            returns1 = [(prices1[i] - prices1[i-1]) / prices1[i-1] for i in range(1, len(prices1))]
            returns2 = [(prices2[i] - prices2[i-1]) / prices2[i-1] for i in range(1, len(prices2))]
            
            if len(returns1) < 2:
                return 0.0
            
            # Calculate correlation
            mean1 = statistics.mean(returns1)
            mean2 = statistics.mean(returns2)
            
            numerator = sum((returns1[i] - mean1) * (returns2[i] - mean2) for i in range(len(returns1)))
            
            sum_sq1 = sum((r - mean1) ** 2 for r in returns1)
            sum_sq2 = sum((r - mean2) ** 2 for r in returns2)
            
            denominator = math.sqrt(sum_sq1 * sum_sq2)
            
            if denominator == 0:
                return 0.0
            
            correlation = numerator / denominator
            return max(-1.0, min(1.0, correlation))
            
        except Exception as e:
            self.logger.error(f"Error calculating correlation: {e}")
            return 0.0
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get risk manager performance metrics"""
        metrics = {
            'total_assessments': sum(len(history) for history in self.risk_history.values()),
            'average_accuracy': 0.0,
            'symbols_tracked': len(self.risk_history),
            'loss_patterns_learned': sum(len(patterns) for patterns in self.loss_patterns.values()),
            'correlation_pairs': sum(len(corrs) for corrs in self.correlation_matrix.values())
        }
        
        # Calculate average accuracy across all symbols
        all_accuracies = []
        for symbol_accuracies in self.risk_prediction_accuracy.values():
            all_accuracies.extend(symbol_accuracies)
        
        if all_accuracies:
            metrics['average_accuracy'] = statistics.mean(all_accuracies)
        
        return metrics

    async def start(self):
        """Start the advanced risk manager"""
        self.logger.info("Advanced Risk Manager started - FULL LEARNING ACTIVE")

        while True:
            try:
                # Continuous learning and optimization
                await asyncio.sleep(120)  # Update every 2 minutes

                # Decay old patterns to adapt to changing market conditions
                for symbol in list(self.dynamic_risk_multiplier.keys()):
                    self.dynamic_risk_multiplier[symbol] *= self.risk_memory_decay

                    # Reset if too far from baseline
                    if self.dynamic_risk_multiplier[symbol] < 0.7 or self.dynamic_risk_multiplier[symbol] > 1.5:
                        self.dynamic_risk_multiplier[symbol] = 1.0

            except Exception as e:
                self.logger.error(f"Error in risk manager: {e}")
                await asyncio.sleep(60)
