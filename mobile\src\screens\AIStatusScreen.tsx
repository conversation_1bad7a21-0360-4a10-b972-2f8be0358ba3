import React, { useState } from 'react';
import { StyleSheet, View, ScrollView, RefreshControl } from 'react-native';
import { Text, Card, ProgressBar, Chip, List, SegmentedButtons } from 'react-native-paper';
import { useQuery } from '@tanstack/react-query';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';
import apiService from '../services/api';
import { theme } from '../styles/theme';

const AIStatusScreen: React.FC = () => {
    const [refreshing, setRefreshing] = useState(false);
    const [activeTab, setActiveTab] = useState('overview');

    const {
        data: aiData,
        isLoading,
        error,
        refetch,
    } = useQuery({
        queryKey: ['ai-status'],
        queryFn: apiService.getAIStatus,
        refetchInterval: 5000, // Refresh every 5 seconds
    });

    const handleRefresh = async () => {
        setRefreshing(true);
        await refetch();
        setRefreshing(false);
    };

    if (isLoading && !aiData) {
        return <LoadingSpinner message="Loading AI system status..." />;
    }

    if (error) {
        return <ErrorMessage error={error} onRetry={refetch} />;
    }

    const getStatusColor = (status: string) => {
        switch (status.toLowerCase()) {
            case 'active':
            case 'running':
            case 'healthy':
                return theme.colors.primary;
            case 'warning':
            case 'degraded':
                return '#FF9800';
            case 'error':
            case 'failed':
            case 'critical':
                return theme.colors.error;
            case 'inactive':
            case 'stopped':
                return theme.colors.outline;
            default:
                return theme.colors.onSurface;
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status.toLowerCase()) {
            case 'active':
            case 'running':
            case 'healthy':
                return 'check-circle';
            case 'warning':
            case 'degraded':
                return 'warning';
            case 'error':
            case 'failed':
            case 'critical':
                return 'error';
            case 'inactive':
            case 'stopped':
                return 'pause-circle';
            default:
                return 'help';
        }
    };

    const renderOverview = () => (
        <View>
            {/* System Health */}
            <Card style={styles.card}>
                <Card.Content>
                    <Text variant="titleMedium" style={styles.cardTitle}>
                        AI System Health
                    </Text>
                    <View style={styles.healthContainer}>
                        <View style={styles.healthScore}>
                            <Text variant="displaySmall" style={styles.healthValue}>
                                {aiData?.systemHealth || 0}%
                            </Text>
                            <Text variant="bodyMedium" style={styles.healthLabel}>
                                Overall Health
                            </Text>
                        </View>
                        <View style={styles.healthProgress}>
                            <ProgressBar
                                progress={(aiData?.systemHealth || 0) / 100}
                                color={
                                    (aiData?.systemHealth || 0) >= 80
                                        ? theme.colors.primary
                                        : (aiData?.systemHealth || 0) >= 60
                                        ? '#FF9800'
                                        : theme.colors.error
                                }
                                style={styles.progressBar}
                            />
                        </View>
                    </View>
                </Card.Content>
            </Card>

            {/* AI Components Status */}
            <Card style={styles.card}>
                <Card.Content>
                    <Text variant="titleMedium" style={styles.cardTitle}>
                        AI Components
                    </Text>
                    {aiData?.components ? (
                        Object.entries(aiData.components).map(([key, component]: [string, any]) => (
                            <List.Item
                                key={key}
                                title={component.name || key.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
                                description={component.description || `${component.status} - Last updated: ${component.lastUpdate || 'Unknown'}`}
                                left={() => (
                                    <Icon
                                        name={getStatusIcon(component.status)}
                                        size={24}
                                        color={getStatusColor(component.status)}
                                        style={styles.componentIcon}
                                    />
                                )}
                                right={() => (
                                    <Chip
                                        mode="outlined"
                                        style={[
                                            styles.statusChip,
                                            { borderColor: getStatusColor(component.status) }
                                        ]}
                                        textStyle={{ color: getStatusColor(component.status) }}
                                    >
                                        {component.status}
                                    </Chip>
                                )}
                                style={styles.componentItem}
                            />
                        ))
                    ) : (
                        <View style={styles.emptyState}>
                            <Icon name="psychology" size={48} color={theme.colors.outline} />
                            <Text variant="bodyMedium" style={styles.emptyText}>
                                No AI components data available
                            </Text>
                        </View>
                    )}
                </Card.Content>
            </Card>
        </View>
    );

    const renderModels = () => (
        <View>
            {/* ML Models */}
            <Card style={styles.card}>
                <Card.Content>
                    <Text variant="titleMedium" style={styles.cardTitle}>
                        Machine Learning Models
                    </Text>
                    {aiData?.models ? (
                        aiData.models.map((model: any, index: number) => (
                            <View key={index} style={styles.modelItem}>
                                <View style={styles.modelHeader}>
                                    <Text variant="titleSmall" style={styles.modelName}>
                                        {model.name}
                                    </Text>
                                    <Chip
                                        mode="outlined"
                                        style={[
                                            styles.statusChip,
                                            { borderColor: getStatusColor(model.status) }
                                        ]}
                                        textStyle={{ color: getStatusColor(model.status) }}
                                    >
                                        {model.status}
                                    </Chip>
                                </View>
                                <Text variant="bodySmall" style={styles.modelDescription}>
                                    {model.description}
                                </Text>
                                <View style={styles.modelMetrics}>
                                    <View style={styles.metric}>
                                        <Text variant="bodySmall" style={styles.metricLabel}>
                                            Accuracy
                                        </Text>
                                        <Text variant="titleSmall" style={styles.metricValue}>
                                            {model.accuracy?.toFixed(2) || 'N/A'}%
                                        </Text>
                                    </View>
                                    <View style={styles.metric}>
                                        <Text variant="bodySmall" style={styles.metricLabel}>
                                            Confidence
                                        </Text>
                                        <Text variant="titleSmall" style={styles.metricValue}>
                                            {model.confidence?.toFixed(2) || 'N/A'}%
                                        </Text>
                                    </View>
                                    <View style={styles.metric}>
                                        <Text variant="bodySmall" style={styles.metricLabel}>
                                            Last Prediction
                                        </Text>
                                        <Text variant="titleSmall" style={styles.metricValue}>
                                            {model.lastPrediction || 'None'}
                                        </Text>
                                    </View>
                                </View>
                            </View>
                        ))
                    ) : (
                        <View style={styles.emptyState}>
                            <Icon name="model-training" size={48} color={theme.colors.outline} />
                            <Text variant="bodyMedium" style={styles.emptyText}>
                                No ML models data available
                            </Text>
                        </View>
                    )}
                </Card.Content>
            </Card>
        </View>
    );

    const renderAgents = () => (
        <View>
            {/* AI Agents */}
            <Card style={styles.card}>
                <Card.Content>
                    <Text variant="titleMedium" style={styles.cardTitle}>
                        AI Agents
                    </Text>
                    {aiData?.agents ? (
                        aiData.agents.map((agent: any, index: number) => (
                            <List.Item
                                key={index}
                                title={agent.name}
                                description={`${agent.type} - ${agent.description}`}
                                left={() => (
                                    <Icon
                                        name={agent.type === 'trading' ? 'trending-up' : 
                                              agent.type === 'research' ? 'search' :
                                              agent.type === 'risk' ? 'shield' : 'smart-toy'}
                                        size={24}
                                        color={getStatusColor(agent.status)}
                                        style={styles.componentIcon}
                                    />
                                )}
                                right={() => (
                                    <View style={styles.agentStats}>
                                        <Chip
                                            mode="outlined"
                                            style={[
                                                styles.statusChip,
                                                { borderColor: getStatusColor(agent.status) }
                                            ]}
                                            textStyle={{ color: getStatusColor(agent.status) }}
                                        >
                                            {agent.status}
                                        </Chip>
                                        <Text variant="bodySmall" style={styles.agentMetric}>
                                            Tasks: {agent.tasksCompleted || 0}
                                        </Text>
                                    </View>
                                )}
                                style={styles.componentItem}
                            />
                        ))
                    ) : (
                        <View style={styles.emptyState}>
                            <Icon name="smart-toy" size={48} color={theme.colors.outline} />
                            <Text variant="bodyMedium" style={styles.emptyText}>
                                No AI agents data available
                            </Text>
                        </View>
                    )}
                </Card.Content>
            </Card>
        </View>
    );

    const renderContent = () => {
        switch (activeTab) {
            case 'overview':
                return renderOverview();
            case 'models':
                return renderModels();
            case 'agents':
                return renderAgents();
            default:
                return renderOverview();
        }
    };

    return (
        <View style={styles.container}>
            {/* Tab Navigation */}
            <View style={styles.tabContainer}>
                <SegmentedButtons
                    value={activeTab}
                    onValueChange={setActiveTab}
                    buttons={[
                        {
                            value: 'overview',
                            label: 'Overview',
                            icon: 'dashboard',
                        },
                        {
                            value: 'models',
                            label: 'Models',
                            icon: 'model-training',
                        },
                        {
                            value: 'agents',
                            label: 'Agents',
                            icon: 'smart-toy',
                        },
                    ]}
                    style={styles.segmentedButtons}
                />
            </View>

            {/* Content */}
            <ScrollView
                style={styles.scrollView}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={handleRefresh}
                        colors={[theme.colors.primary]}
                    />
                }
                showsVerticalScrollIndicator={false}
            >
                {renderContent()}
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: theme.colors.background,
    },
    tabContainer: {
        padding: 16,
        backgroundColor: theme.colors.surface,
        elevation: 2,
    },
    segmentedButtons: {
        backgroundColor: theme.colors.surfaceVariant,
    },
    scrollView: {
        flex: 1,
    },
    card: {
        marginHorizontal: 16,
        marginBottom: 16,
        backgroundColor: theme.colors.surface,
    },
    cardTitle: {
        color: theme.colors.onSurface,
        fontWeight: '700',
        marginBottom: 16,
    },
    healthContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 20,
    },
    healthScore: {
        alignItems: 'center',
    },
    healthValue: {
        color: theme.colors.primary,
        fontWeight: '700',
    },
    healthLabel: {
        color: theme.colors.onSurfaceVariant,
        marginTop: 4,
    },
    healthProgress: {
        flex: 1,
    },
    progressBar: {
        height: 8,
        borderRadius: 4,
    },
    componentItem: {
        paddingVertical: 8,
        borderBottomWidth: 1,
        borderBottomColor: theme.colors.outline + '20',
    },
    componentIcon: {
        marginTop: 8,
    },
    statusChip: {
        height: 28,
    },
    modelItem: {
        marginBottom: 16,
        padding: 16,
        backgroundColor: theme.colors.surfaceVariant,
        borderRadius: 12,
    },
    modelHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
    },
    modelName: {
        color: theme.colors.onSurface,
        fontWeight: '600',
    },
    modelDescription: {
        color: theme.colors.onSurfaceVariant,
        marginBottom: 12,
    },
    modelMetrics: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    metric: {
        alignItems: 'center',
    },
    metricLabel: {
        color: theme.colors.onSurfaceVariant,
        marginBottom: 4,
    },
    metricValue: {
        color: theme.colors.onSurface,
        fontWeight: '600',
    },
    agentStats: {
        alignItems: 'flex-end',
        gap: 4,
    },
    agentMetric: {
        color: theme.colors.onSurfaceVariant,
    },
    emptyState: {
        alignItems: 'center',
        padding: 32,
    },
    emptyText: {
        color: theme.colors.onSurfaceVariant,
        marginTop: 16,
    },
});

export default AIStatusScreen;
