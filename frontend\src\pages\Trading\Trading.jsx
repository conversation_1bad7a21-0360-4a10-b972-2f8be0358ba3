import {
    Pause,
    PlayArrow,
    Security,
    Settings,
    Speed,
    Stop,
    Timeline,
    TrendingDown,
    TrendingUp
} from '@mui/icons-material'
import {
    <PERSON>ert,
    Box,
    Button,
    Card,
    CardContent,
    Chip,
    Container,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    FormControlLabel,
    Grid,
    IconButton,
    MenuItem,
    Switch,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    TextField,
    Typography,
} from '@mui/material'
import { motion } from 'framer-motion'
import { useState } from 'react'
import { useRealTimeData } from '../../hooks/useRealTimeData'
import toast from 'react-hot-toast'

const Trading = () => {
    const [isTrading, setIsTrading] = useState(true)
    const [emergencyStopDialog, setEmergencyStopDialog] = useState(false)
    const [newStrategyDialog, setNewStrategyDialog] = useState(false)
    const [selectedStrategy, setSelectedStrategy] = useState('')

    // Real-time data hook
    const {
        connectionStatus,
        trades,
        balance,
        performanceMetrics,
        isConnected
    } = useRealTimeData()

    const strategies = [
        {
            id: 1,
            name: 'Arbitrage Scanner',
            status: 'active',
            profit: 2340.50,
            trades: 23,
            winRate: 78.2,
            risk: 'low',
            performance: 15.4,
        },
        {
            id: 2,
            name: 'Grid Trading Bot',
            status: 'active',
            profit: 1890.23,
            trades: 45,
            winRate: 71.9,
            risk: 'medium',
            performance: 12.7,
        },
        {
            id: 3,
            name: 'Momentum Scalper',
            status: 'paused',
            profit: 567.89,
            trades: 12,
            winRate: 83.3,
            risk: 'high',
            performance: 8.9,
        },
        {
            id: 4,
            name: 'News Trader',
            status: 'active',
            profit: 3240.67,
            trades: 8,
            winRate: 87.5,
            risk: 'medium',
            performance: 22.1,
        },
    ]

    const positions = [
        {
            symbol: 'BTCUSDT',
            side: 'LONG',
            size: 0.045,
            entryPrice: 42150.50,
            currentPrice: 42350.50,
            pnl: 145.32,
            pnlPercent: 2.34,
            timestamp: '2024-01-15 14:30:25',
        },
        {
            symbol: 'ETHUSDT',
            side: 'SHORT',
            size: 0.823,
            entryPrice: 2580.20,
            currentPrice: 2543.20,
            pnl: 67.89,
            pnlPercent: 1.43,
            timestamp: '2024-01-15 13:45:12',
        },
        {
            symbol: 'ADAUSDT',
            side: 'LONG',
            size: 2847.5,
            entryPrice: 0.4423,
            currentPrice: 0.4523,
            pnl: 234.56,
            pnlPercent: 2.26,
            timestamp: '2024-01-15 12:15:08',
        },
    ]

    const getStatusColor = (status) => {
        switch (status) {
            case 'active':
                return '#00ff88'
            case 'paused':
                return '#ffa726'
            case 'stopped':
                return '#ff4757'
            default:
                return '#666'
        }
    }

    const getRiskColor = (risk) => {
        switch (risk) {
            case 'low':
                return '#00ff88'
            case 'medium':
                return '#ffa726'
            case 'high':
                return '#ff4757'
            default:
                return '#666'
        }
    }

    const handleEmergencyStop = () => {
        setIsTrading(false)
        setEmergencyStopDialog(false)
        // Here you would call the emergency stop API
    }

    return (
        <Container maxWidth="xl" sx={{ py: 3 }}>
            <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
            >
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
                    <Typography
                        variant="h4"
                        sx={{
                            fontWeight: 700,
                            color: '#ffffff',
                            background: 'linear-gradient(45deg, #00ff88, #00ccff)',
                            WebkitBackgroundClip: 'text',
                            WebkitTextFillColor: 'transparent',
                        }}
                    >
                        Trading Control Center
                    </Typography>

                    <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                        <FormControlLabel
                            control={
                                <Switch
                                    checked={isTrading}
                                    onChange={(e) => setIsTrading(e.target.checked)}
                                    sx={{
                                        '& .MuiSwitch-switchBase.Mui-checked': {
                                            color: '#00ff88',
                                        },
                                        '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                                            backgroundColor: '#00ff88',
                                        },
                                    }}
                                />
                            }
                            label={
                                <Typography sx={{ color: '#ffffff', fontWeight: 600 }}>
                                    {isTrading ? 'Trading Active' : 'Trading Paused'}
                                </Typography>
                            }
                        />

                        <Button
                            variant="contained"
                            color="error"
                            startIcon={<Stop />}
                            onClick={() => setEmergencyStopDialog(true)}
                            sx={{
                                background: 'linear-gradient(45deg, #ff4757, #ff3742)',
                                fontWeight: 600,
                            }}
                        >
                            Emergency Stop
                        </Button>
                    </Box>
                </Box>
            </motion.div>

            <Grid container spacing={3}>
                {/* Trading Status Cards */}
                <Grid item xs={12} md={3}>
                    <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.6, delay: 0.1 }}
                    >
                        <Card
                            sx={{
                                background: 'rgba(255, 255, 255, 0.03)',
                                backdropFilter: 'blur(20px)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: 3,
                            }}
                        >
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                                    <PlayArrow sx={{ color: '#00ff88' }} />
                                    <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                        Active Strategies
                                    </Typography>
                                </Box>
                                <Typography variant="h3" sx={{ color: '#00ff88', fontWeight: 700 }}>
                                    3
                                </Typography>
                                <Typography variant="body2" sx={{ color: '#b3b3b3' }}>
                                    Out of 4 total strategies
                                </Typography>
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>

                <Grid item xs={12} md={3}>
                    <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.6, delay: 0.2 }}
                    >
                        <Card
                            sx={{
                                background: 'rgba(255, 255, 255, 0.03)',
                                backdropFilter: 'blur(20px)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: 3,
                            }}
                        >
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                                    <Timeline sx={{ color: '#42a5f5' }} />
                                    <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                        Open Positions
                                    </Typography>
                                </Box>
                                <Typography variant="h3" sx={{ color: '#42a5f5', fontWeight: 700 }}>
                                    {positions.length}
                                </Typography>
                                <Typography variant="body2" sx={{ color: '#b3b3b3' }}>
                                    Total exposure: $45,678
                                </Typography>
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>

                <Grid item xs={12} md={3}>
                    <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.6, delay: 0.3 }}
                    >
                        <Card
                            sx={{
                                background: 'rgba(255, 255, 255, 0.03)',
                                backdropFilter: 'blur(20px)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: 3,
                            }}
                        >
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                                    <Speed sx={{ color: '#ffa726' }} />
                                    <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                        System Load
                                    </Typography>
                                </Box>
                                <Typography variant="h3" sx={{ color: '#ffa726', fontWeight: 700 }}>
                                    67%
                                </Typography>
                                <Typography variant="body2" sx={{ color: '#b3b3b3' }}>
                                    Optimal performance
                                </Typography>
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>

                <Grid item xs={12} md={3}>
                    <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.6, delay: 0.4 }}
                    >
                        <Card
                            sx={{
                                background: 'rgba(255, 255, 255, 0.03)',
                                backdropFilter: 'blur(20px)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: 3,
                            }}
                        >
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                                    <Security sx={{ color: '#8e24aa' }} />
                                    <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                        Risk Level
                                    </Typography>
                                </Box>
                                <Typography variant="h3" sx={{ color: '#8e24aa', fontWeight: 700 }}>
                                    Low
                                </Typography>
                                <Typography variant="body2" sx={{ color: '#b3b3b3' }}>
                                    Within safety limits
                                </Typography>
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>

                {/* Strategies Table */}
                <Grid item xs={12} lg={8}>
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.5 }}
                    >
                        <Card
                            sx={{
                                background: 'rgba(255, 255, 255, 0.03)',
                                backdropFilter: 'blur(20px)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: 3,
                            }}
                        >
                            <CardContent>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                                    <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                        Trading Strategies
                                    </Typography>
                                    <Button
                                        variant="outlined"
                                        startIcon={<Settings />}
                                        onClick={() => setNewStrategyDialog(true)}
                                        sx={{
                                            borderColor: '#00ff88',
                                            color: '#00ff88',
                                            '&:hover': {
                                                borderColor: '#00ff88',
                                                backgroundColor: 'rgba(0, 255, 136, 0.1)',
                                            },
                                        }}
                                    >
                                        Configure Strategy
                                    </Button>
                                </Box>

                                <TableContainer>
                                    <Table>
                                        <TableHead>
                                            <TableRow>
                                                <TableCell sx={{ color: '#b3b3b3', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
                                                    Strategy
                                                </TableCell>
                                                <TableCell sx={{ color: '#b3b3b3', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
                                                    Status
                                                </TableCell>
                                                <TableCell sx={{ color: '#b3b3b3', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
                                                    Profit
                                                </TableCell>
                                                <TableCell sx={{ color: '#b3b3b3', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
                                                    Trades
                                                </TableCell>
                                                <TableCell sx={{ color: '#b3b3b3', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
                                                    Win Rate
                                                </TableCell>
                                                <TableCell sx={{ color: '#b3b3b3', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
                                                    Risk
                                                </TableCell>
                                                <TableCell sx={{ color: '#b3b3b3', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
                                                    Actions
                                                </TableCell>
                                            </TableRow>
                                        </TableHead>
                                        <TableBody>
                                            {strategies.map((strategy) => (
                                                <TableRow key={strategy.id}>
                                                    <TableCell sx={{ color: '#ffffff', borderBottom: '1px solid rgba(255, 255, 255, 0.05)' }}>
                                                        {strategy.name}
                                                    </TableCell>
                                                    <TableCell sx={{ borderBottom: '1px solid rgba(255, 255, 255, 0.05)' }}>
                                                        <Chip
                                                            label={strategy.status}
                                                            size="small"
                                                            sx={{
                                                                backgroundColor: `${getStatusColor(strategy.status)}20`,
                                                                color: getStatusColor(strategy.status),
                                                            }}
                                                        />
                                                    </TableCell>
                                                    <TableCell sx={{ color: '#00ff88', borderBottom: '1px solid rgba(255, 255, 255, 0.05)' }}>
                                                        ${strategy.profit.toFixed(2)}
                                                    </TableCell>
                                                    <TableCell sx={{ color: '#ffffff', borderBottom: '1px solid rgba(255, 255, 255, 0.05)' }}>
                                                        {strategy.trades}
                                                    </TableCell>
                                                    <TableCell sx={{ color: '#ffffff', borderBottom: '1px solid rgba(255, 255, 255, 0.05)' }}>
                                                        {strategy.winRate}%
                                                    </TableCell>
                                                    <TableCell sx={{ borderBottom: '1px solid rgba(255, 255, 255, 0.05)' }}>
                                                        <Chip
                                                            label={strategy.risk}
                                                            size="small"
                                                            sx={{
                                                                backgroundColor: `${getRiskColor(strategy.risk)}20`,
                                                                color: getRiskColor(strategy.risk),
                                                            }}
                                                        />
                                                    </TableCell>
                                                    <TableCell sx={{ borderBottom: '1px solid rgba(255, 255, 255, 0.05)' }}>
                                                        <Box sx={{ display: 'flex', gap: 1 }}>
                                                            <IconButton
                                                                size="small"
                                                                sx={{ color: strategy.status === 'active' ? '#ffa726' : '#00ff88' }}
                                                            >
                                                                {strategy.status === 'active' ? <Pause /> : <PlayArrow />}
                                                            </IconButton>
                                                            <IconButton size="small" sx={{ color: '#666' }}>
                                                                <Settings />
                                                            </IconButton>
                                                        </Box>
                                                    </TableCell>
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                    </Table>
                                </TableContainer>
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>

                {/* Open Positions */}
                <Grid item xs={12} lg={4}>
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.6 }}
                    >
                        <Card
                            sx={{
                                background: 'rgba(255, 255, 255, 0.03)',
                                backdropFilter: 'blur(20px)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: 3,
                                height: '400px',
                            }}
                        >
                            <CardContent sx={{ height: '100%' }}>
                                <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600, mb: 3 }}>
                                    Open Positions
                                </Typography>

                                <Box sx={{ overflow: 'auto', maxHeight: 'calc(100% - 60px)' }}>
                                    {positions.map((position, index) => (
                                        <Box
                                            key={index}
                                            sx={{
                                                p: 2,
                                                mb: 2,
                                                background: 'rgba(255, 255, 255, 0.02)',
                                                borderRadius: 2,
                                                border: '1px solid rgba(255, 255, 255, 0.05)',
                                            }}
                                        >
                                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                                                <Typography variant="body1" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                                    {position.symbol}
                                                </Typography>
                                                <Chip
                                                    label={position.side}
                                                    size="small"
                                                    sx={{
                                                        backgroundColor: position.side === 'LONG' ? '#00ff8820' : '#ff475720',
                                                        color: position.side === 'LONG' ? '#00ff88' : '#ff4757',
                                                    }}
                                                />
                                            </Box>

                                            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                                <Typography variant="caption" sx={{ color: '#b3b3b3' }}>
                                                    Size: {position.size}
                                                </Typography>
                                                <Typography variant="caption" sx={{ color: '#b3b3b3' }}>
                                                    Entry: ${position.entryPrice.toLocaleString()}
                                                </Typography>
                                            </Box>

                                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                <Typography variant="caption" sx={{ color: '#b3b3b3' }}>
                                                    Current: ${position.currentPrice.toLocaleString()}
                                                </Typography>
                                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                                    {position.pnl > 0 ? (
                                                        <TrendingUp sx={{ color: '#00ff88', fontSize: 16 }} />
                                                    ) : (
                                                        <TrendingDown sx={{ color: '#ff4757', fontSize: 16 }} />
                                                    )}
                                                    <Typography
                                                        variant="body2"
                                                        sx={{
                                                            color: position.pnl > 0 ? '#00ff88' : '#ff4757',
                                                            fontWeight: 600,
                                                        }}
                                                    >
                                                        ${position.pnl.toFixed(2)} ({position.pnlPercent > 0 ? '+' : ''}{position.pnlPercent}%)
                                                    </Typography>
                                                </Box>
                                            </Box>
                                        </Box>
                                    ))}
                                </Box>
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>
            </Grid>

            {/* Emergency Stop Dialog */}
            <Dialog
                open={emergencyStopDialog}
                onClose={() => setEmergencyStopDialog(false)}
                PaperProps={{
                    sx: {
                        background: 'rgba(20, 20, 20, 0.95)',
                        backdropFilter: 'blur(20px)',
                        border: '1px solid rgba(255, 255, 255, 0.1)',
                        borderRadius: 3,
                    },
                }}
            >
                <DialogTitle sx={{ color: '#ffffff' }}>
                    Emergency Stop Confirmation
                </DialogTitle>
                <DialogContent>
                    <Alert severity="warning" sx={{ mb: 2 }}>
                        This will immediately stop all trading activities and close open positions.
                    </Alert>
                    <Typography sx={{ color: '#b3b3b3' }}>
                        Are you sure you want to execute an emergency stop? This action cannot be undone.
                    </Typography>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setEmergencyStopDialog(false)} sx={{ color: '#b3b3b3' }}>
                        Cancel
                    </Button>
                    <Button onClick={handleEmergencyStop} color="error" variant="contained">
                        Execute Emergency Stop
                    </Button>
                </DialogActions>
            </Dialog>

            {/* New Strategy Dialog */}
            <Dialog
                open={newStrategyDialog}
                onClose={() => setNewStrategyDialog(false)}
                maxWidth="sm"
                fullWidth
                PaperProps={{
                    sx: {
                        background: 'rgba(20, 20, 20, 0.95)',
                        backdropFilter: 'blur(20px)',
                        border: '1px solid rgba(255, 255, 255, 0.1)',
                        borderRadius: 3,
                    },
                }}
            >
                <DialogTitle sx={{ color: '#ffffff' }}>
                    Configure Trading Strategy
                </DialogTitle>
                <DialogContent>
                    <Grid container spacing={2} sx={{ mt: 1 }}>
                        <Grid item xs={12}>
                            <TextField
                                select
                                fullWidth
                                label="Strategy Type"
                                value={selectedStrategy}
                                onChange={(e) => setSelectedStrategy(e.target.value)}
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        color: '#ffffff',
                                        '& fieldset': {
                                            borderColor: 'rgba(255, 255, 255, 0.3)',
                                        },
                                        '&:hover fieldset': {
                                            borderColor: '#00ff88',
                                        },
                                        '&.Mui-focused fieldset': {
                                            borderColor: '#00ff88',
                                        },
                                    },
                                    '& .MuiInputLabel-root': {
                                        color: '#b3b3b3',
                                    },
                                }}
                            >
                                <MenuItem value="arbitrage">Arbitrage Scanner</MenuItem>
                                <MenuItem value="grid">Grid Trading</MenuItem>
                                <MenuItem value="momentum">Momentum Scalping</MenuItem>
                                <MenuItem value="news">News Trading</MenuItem>
                                <MenuItem value="mean_reversion">Mean Reversion</MenuItem>
                            </TextField>
                        </Grid>
                        <Grid item xs={6}>
                            <TextField
                                fullWidth
                                label="Risk Level"
                                defaultValue="Medium"
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        color: '#ffffff',
                                        '& fieldset': {
                                            borderColor: 'rgba(255, 255, 255, 0.3)',
                                        },
                                        '&:hover fieldset': {
                                            borderColor: '#00ff88',
                                        },
                                        '&.Mui-focused fieldset': {
                                            borderColor: '#00ff88',
                                        },
                                    },
                                    '& .MuiInputLabel-root': {
                                        color: '#b3b3b3',
                                    },
                                }}
                            />
                        </Grid>
                        <Grid item xs={6}>
                            <TextField
                                fullWidth
                                label="Max Allocation (%)"
                                defaultValue="25"
                                type="number"
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        color: '#ffffff',
                                        '& fieldset': {
                                            borderColor: 'rgba(255, 255, 255, 0.3)',
                                        },
                                        '&:hover fieldset': {
                                            borderColor: '#00ff88',
                                        },
                                        '&.Mui-focused fieldset': {
                                            borderColor: '#00ff88',
                                        },
                                    },
                                    '& .MuiInputLabel-root': {
                                        color: '#b3b3b3',
                                    },
                                }}
                            />
                        </Grid>
                    </Grid>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setNewStrategyDialog(false)} sx={{ color: '#b3b3b3' }}>
                        Cancel
                    </Button>
                    <Button
                        variant="contained"
                        sx={{
                            background: 'linear-gradient(45deg, #00ff88, #00cc6a)',
                            fontWeight: 600,
                        }}
                    >
                        Configure Strategy
                    </Button>
                </DialogActions>
            </Dialog>
        </Container>
    )
}

export default Trading
