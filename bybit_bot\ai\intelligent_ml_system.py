"""
INTELLIGENT ML SYSTEM - NO EXTERNAL DEPENDENCIES
Advanced machine learning using only Python standard library and numpy
Designed for maximum profit generation with margin awareness
"""

import asyncio
import logging
import json
import time
import math
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from collections import deque, defaultdict
import statistics

logger = logging.getLogger(__name__)

@dataclass
class MarketSignal:
    """Market signal with confidence and risk assessment"""
    symbol: str
    signal_type: str  # 'buy', 'sell', 'hold'
    confidence: float  # 0.0 to 1.0
    risk_level: str  # 'low', 'medium', 'high'
    expected_return: float
    time_horizon: int  # seconds
    margin_impact: float  # estimated margin usage
    reasoning: str

@dataclass
class TradingPattern:
    """Identified trading pattern"""
    pattern_type: str
    strength: float
    duration: int
    success_rate: float
    avg_return: float
    margin_efficiency: float

class IntelligentMLSystem:
    """
    Advanced ML system using only standard Python libraries
    Focuses on margin-aware profit generation
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Learning data storage
        self.price_history = defaultdict(lambda: deque(maxlen=1000))
        self.volume_history = defaultdict(lambda: deque(maxlen=1000))
        self.margin_history = deque(maxlen=500)
        self.trade_history = deque(maxlen=1000)
        self.pattern_library = {}
        
        # Performance tracking
        self.prediction_accuracy = defaultdict(float)
        self.profit_by_strategy = defaultdict(list)
        self.margin_efficiency_scores = deque(maxlen=100)
        
        # Adaptive parameters
        self.learning_rate = 0.01
        self.confidence_threshold = 0.6
        self.risk_tolerance = 0.5

        # PROFIT TARGET TRACKING AND OPTIMIZATION
        self.profit_targets = {
            'hourly_target': 625.0,     # $625/hour for $15,000/day
            'current_achievement_rate': 0.0,
            'profit_velocity': 0.0,
            'target_velocity': 10.42,   # $10.42/minute for $625/hour
            'performance_gap': 0.0,
            'hours_above_target': 0,
            'hours_below_target': 0,
            'average_hourly_profit': 0.0
        }

        # ML STRATEGY ADJUSTMENTS BASED ON TARGET PERFORMANCE
        self.strategy_adjustments = {
            'aggression_level': 1.0,      # 0.5 = conservative, 1.0 = normal, 1.5 = aggressive
            'risk_tolerance': 1.0,        # Risk multiplier based on performance
            'frequency_multiplier': 1.0,  # Trade frequency adjustment
            'profit_threshold_adj': 1.0,  # Profit threshold adjustment
            'confidence_threshold': 0.6   # Minimum confidence for trades
        }

        self.logger.info("Intelligent ML System initialized - NO EXTERNAL DEPENDENCIES")
    
    async def analyze_market_data(self, symbol: str, market_data: Dict[str, Any]) -> MarketSignal:
        """
        Analyze market data and generate intelligent trading signals
        """
        try:
            current_price = float(market_data.get('price', 0))
            volume = float(market_data.get('volume', 0))
            timestamp = time.time()
            
            # Store data for learning
            self.price_history[symbol].append((timestamp, current_price))
            self.volume_history[symbol].append((timestamp, volume))
            
            # Generate signal using multiple analysis methods
            trend_signal = self._analyze_trend(symbol)
            momentum_signal = self._analyze_momentum(symbol)
            volume_signal = self._analyze_volume(symbol)
            pattern_signal = self._analyze_patterns(symbol)
            
            # Combine signals with intelligent weighting
            combined_signal = self._combine_signals([
                trend_signal, momentum_signal, volume_signal, pattern_signal
            ])
            
            # Add margin awareness
            margin_adjusted_signal = self._adjust_for_margin_risk(combined_signal)
            
            return margin_adjusted_signal
            
        except Exception as e:
            self.logger.error(f"Error in market analysis: {e}")
            return MarketSignal(
                symbol=symbol,
                signal_type='hold',
                confidence=0.0,
                risk_level='high',
                expected_return=0.0,
                time_horizon=0,
                margin_impact=0.0,
                reasoning=f"Analysis error: {e}"
            )
    
    def _analyze_trend(self, symbol: str) -> MarketSignal:
        """Analyze price trend using moving averages and regression"""
        prices = list(self.price_history[symbol])
        if len(prices) < 20:
            return self._neutral_signal(symbol, "Insufficient data for trend analysis")
        
        # Calculate multiple timeframe moving averages
        short_ma = self._moving_average([p[1] for p in prices[-10:]])
        medium_ma = self._moving_average([p[1] for p in prices[-20:]])
        long_ma = self._moving_average([p[1] for p in prices[-50:]] if len(prices) >= 50 else [p[1] for p in prices])
        
        # Trend strength calculation
        current_price = prices[-1][1]
        trend_strength = 0.0
        signal_type = 'hold'
        
        if current_price > short_ma > medium_ma:
            trend_strength = min(1.0, (current_price - medium_ma) / medium_ma * 10)
            signal_type = 'buy'
        elif current_price < short_ma < medium_ma:
            trend_strength = min(1.0, (medium_ma - current_price) / medium_ma * 10)
            signal_type = 'sell'
        
        return MarketSignal(
            symbol=symbol,
            signal_type=signal_type,
            confidence=trend_strength,
            risk_level='medium',
            expected_return=trend_strength * 0.02,  # 2% max expected return
            time_horizon=300,  # 5 minutes
            margin_impact=0.1,
            reasoning=f"Trend analysis: {signal_type} with {trend_strength:.2f} strength"
        )
    
    def _analyze_momentum(self, symbol: str) -> MarketSignal:
        """Analyze price momentum using rate of change"""
        prices = list(self.price_history[symbol])
        if len(prices) < 10:
            return self._neutral_signal(symbol, "Insufficient data for momentum analysis")
        
        # Calculate momentum indicators
        recent_prices = [p[1] for p in prices[-10:]]
        price_changes = [recent_prices[i] - recent_prices[i-1] for i in range(1, len(recent_prices))]
        
        # Momentum strength
        avg_change = statistics.mean(price_changes)
        momentum_strength = abs(avg_change) / recent_prices[-1] * 100  # Percentage momentum
        
        signal_type = 'buy' if avg_change > 0 else 'sell' if avg_change < 0 else 'hold'
        confidence = min(1.0, momentum_strength * 5)  # Scale momentum to confidence
        
        return MarketSignal(
            symbol=symbol,
            signal_type=signal_type,
            confidence=confidence,
            risk_level='medium',
            expected_return=confidence * 0.015,  # 1.5% max expected return
            time_horizon=180,  # 3 minutes
            margin_impact=0.08,
            reasoning=f"Momentum analysis: {avg_change:.6f} change, {momentum_strength:.4f}% strength"
        )
    
    def _analyze_volume(self, symbol: str) -> MarketSignal:
        """Analyze volume patterns for confirmation"""
        volumes = list(self.volume_history[symbol])
        if len(volumes) < 5:
            return self._neutral_signal(symbol, "Insufficient volume data")
        
        recent_volumes = [v[1] for v in volumes[-5:]]
        avg_volume = statistics.mean(recent_volumes)
        current_volume = recent_volumes[-1]
        
        # Volume surge detection
        volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
        
        if volume_ratio > 1.5:  # Volume surge
            confidence = min(1.0, (volume_ratio - 1.0) * 2)
            return MarketSignal(
                symbol=symbol,
                signal_type='buy',  # Volume surge often indicates buying interest
                confidence=confidence,
                risk_level='low',
                expected_return=confidence * 0.01,
                time_horizon=120,
                margin_impact=0.05,
                reasoning=f"Volume surge: {volume_ratio:.2f}x average volume"
            )
        
        return self._neutral_signal(symbol, f"Normal volume: {volume_ratio:.2f}x average")
    
    def _analyze_patterns(self, symbol: str) -> MarketSignal:
        """Analyze price patterns for trading opportunities"""
        prices = list(self.price_history[symbol])
        if len(prices) < 15:
            return self._neutral_signal(symbol, "Insufficient data for pattern analysis")
        
        recent_prices = [p[1] for p in prices[-15:]]
        
        # Pattern detection
        pattern_score = 0.0
        pattern_type = "none"
        
        # Support/Resistance levels
        support_level = min(recent_prices)
        resistance_level = max(recent_prices)
        current_price = recent_prices[-1]
        
        # Breakout detection
        price_range = resistance_level - support_level
        if price_range > 0:
            if current_price > resistance_level * 0.999:  # Near resistance breakout
                pattern_score = 0.8
                pattern_type = "resistance_breakout"
            elif current_price < support_level * 1.001:  # Near support breakdown
                pattern_score = 0.8
                pattern_type = "support_breakdown"
        
        signal_type = 'buy' if pattern_type == "resistance_breakout" else 'sell' if pattern_type == "support_breakdown" else 'hold'
        
        return MarketSignal(
            symbol=symbol,
            signal_type=signal_type,
            confidence=pattern_score,
            risk_level='medium',
            expected_return=pattern_score * 0.025,  # 2.5% max expected return
            time_horizon=600,  # 10 minutes
            margin_impact=0.12,
            reasoning=f"Pattern: {pattern_type}, score: {pattern_score:.2f}"
        )
    
    def _combine_signals(self, signals: List[MarketSignal]) -> MarketSignal:
        """Intelligently combine multiple signals"""
        if not signals:
            return self._neutral_signal("UNKNOWN", "No signals to combine")
        
        # Weight signals by confidence and expected return
        buy_score = 0.0
        sell_score = 0.0
        total_weight = 0.0
        
        for signal in signals:
            weight = signal.confidence * (1.0 + signal.expected_return)
            total_weight += weight
            
            if signal.signal_type == 'buy':
                buy_score += weight
            elif signal.signal_type == 'sell':
                sell_score += weight
        
        # Determine final signal
        if total_weight == 0:
            return self._neutral_signal(signals[0].symbol, "No weighted signals")
        
        buy_strength = buy_score / total_weight
        sell_strength = sell_score / total_weight
        
        if buy_strength > sell_strength and buy_strength > 0.3:
            signal_type = 'buy'
            confidence = buy_strength
        elif sell_strength > buy_strength and sell_strength > 0.3:
            signal_type = 'sell'
            confidence = sell_strength
        else:
            signal_type = 'hold'
            confidence = 0.0
        
        # Calculate combined metrics
        avg_return = statistics.mean([s.expected_return for s in signals])
        avg_margin_impact = statistics.mean([s.margin_impact for s in signals])
        avg_time_horizon = int(statistics.mean([s.time_horizon for s in signals]))
        
        return MarketSignal(
            symbol=signals[0].symbol,
            signal_type=signal_type,
            confidence=confidence,
            risk_level='medium',
            expected_return=avg_return,
            time_horizon=avg_time_horizon,
            margin_impact=avg_margin_impact,
            reasoning=f"Combined signal: {len(signals)} indicators, {confidence:.2f} confidence"
        )
    
    def _adjust_for_margin_risk(self, signal: MarketSignal) -> MarketSignal:
        """Adjust signal based on current margin risk"""
        if not self.margin_history:
            return signal
        
        current_margin_ratio = self.margin_history[-1] if self.margin_history else 0
        
        # Reduce confidence and expected return based on margin risk
        if current_margin_ratio > 90:
            # Very high margin - be extremely conservative
            signal.confidence *= 0.1
            signal.expected_return *= 0.1
            signal.risk_level = 'high'
            signal.reasoning += " | MARGIN RISK: Very high margin ratio"
        elif current_margin_ratio > 80:
            # High margin - be conservative
            signal.confidence *= 0.3
            signal.expected_return *= 0.3
            signal.risk_level = 'high'
            signal.reasoning += " | MARGIN RISK: High margin ratio"
        elif current_margin_ratio > 70:
            # Moderate margin - slight reduction
            signal.confidence *= 0.7
            signal.expected_return *= 0.7
            signal.reasoning += " | MARGIN RISK: Moderate margin ratio"
        
        return signal
    
    def _neutral_signal(self, symbol: str, reason: str) -> MarketSignal:
        """Create a neutral/hold signal"""
        return MarketSignal(
            symbol=symbol,
            signal_type='hold',
            confidence=0.0,
            risk_level='low',
            expected_return=0.0,
            time_horizon=0,
            margin_impact=0.0,
            reasoning=reason
        )
    
    def _moving_average(self, values: List[float]) -> float:
        """Calculate simple moving average"""
        return statistics.mean(values) if values else 0.0
    
    async def update_margin_status(self, margin_ratio: float):
        """Update margin status for risk adjustment"""
        self.margin_history.append(margin_ratio)
        
        # Adjust risk tolerance based on margin
        if margin_ratio > 90:
            self.risk_tolerance = 0.1  # Very conservative
        elif margin_ratio > 80:
            self.risk_tolerance = 0.3  # Conservative
        elif margin_ratio > 70:
            self.risk_tolerance = 0.5  # Moderate
        else:
            self.risk_tolerance = 0.8  # More aggressive
    
    async def learn_from_trade(self, trade_result: Dict[str, Any]):
        """Learn from trade results to improve future predictions"""
        self.trade_history.append(trade_result)
        
        # Update prediction accuracy
        if 'predicted_return' in trade_result and 'actual_return' in trade_result:
            symbol = trade_result.get('symbol', 'UNKNOWN')
            accuracy = 1.0 - abs(trade_result['predicted_return'] - trade_result['actual_return'])
            self.prediction_accuracy[symbol] = (
                self.prediction_accuracy[symbol] * 0.9 + accuracy * 0.1
            )
        
        # Update strategy performance
        strategy = trade_result.get('strategy', 'unknown')
        profit = trade_result.get('profit', 0.0)
        self.profit_by_strategy[strategy].append(profit)
        
        # Keep only recent performance data
        if len(self.profit_by_strategy[strategy]) > 100:
            self.profit_by_strategy[strategy] = self.profit_by_strategy[strategy][-100:]
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get current ML system performance metrics"""
        return {
            'prediction_accuracy': dict(self.prediction_accuracy),
            'total_trades_learned': len(self.trade_history),
            'margin_efficiency': statistics.mean(self.margin_efficiency_scores) if self.margin_efficiency_scores else 0.0,
            'risk_tolerance': self.risk_tolerance,
            'patterns_identified': len(self.pattern_library),
            'data_points': {
                'price_history_size': sum(len(h) for h in self.price_history.values()),
                'volume_history_size': sum(len(h) for h in self.volume_history.values()),
                'margin_history_size': len(self.margin_history)
            }
        }

    async def update_profit_targets(self, target_performance_data: Dict[str, Any]):
        """Update ML system with profit target performance data for strategy optimization"""
        try:
            # Update profit target tracking
            self.profit_targets.update(target_performance_data)

            # Calculate performance-based strategy adjustments
            achievement_rate = target_performance_data.get('current_achievement_rate', 0.0)
            performance_gap = target_performance_data.get('performance_gap', 0.0)

            # ADAPTIVE STRATEGY ADJUSTMENTS BASED ON TARGET PERFORMANCE
            if achievement_rate >= 120:  # Exceeding target by 20%
                # Maintain current aggressive approach
                self.strategy_adjustments['aggression_level'] = 1.2
                self.strategy_adjustments['risk_tolerance'] = 1.1
                self.strategy_adjustments['frequency_multiplier'] = 1.0
                self.strategy_adjustments['profit_threshold_adj'] = 0.9  # Lower thresholds for more trades
                self.strategy_adjustments['confidence_threshold'] = 0.55  # Slightly lower confidence needed

            elif achievement_rate >= 100:  # Meeting target
                # Maintain balanced approach
                self.strategy_adjustments['aggression_level'] = 1.0
                self.strategy_adjustments['risk_tolerance'] = 1.0
                self.strategy_adjustments['frequency_multiplier'] = 1.0
                self.strategy_adjustments['profit_threshold_adj'] = 1.0
                self.strategy_adjustments['confidence_threshold'] = 0.6

            elif achievement_rate >= 80:  # 80-99% of target
                # Increase aggression moderately
                self.strategy_adjustments['aggression_level'] = 1.1
                self.strategy_adjustments['risk_tolerance'] = 1.05
                self.strategy_adjustments['frequency_multiplier'] = 1.1
                self.strategy_adjustments['profit_threshold_adj'] = 0.95
                self.strategy_adjustments['confidence_threshold'] = 0.58

            elif achievement_rate >= 60:  # 60-79% of target
                # Increase aggression significantly
                self.strategy_adjustments['aggression_level'] = 1.3
                self.strategy_adjustments['risk_tolerance'] = 1.15
                self.strategy_adjustments['frequency_multiplier'] = 1.2
                self.strategy_adjustments['profit_threshold_adj'] = 0.85
                self.strategy_adjustments['confidence_threshold'] = 0.55

            else:  # Below 60% of target - MAXIMUM AGGRESSION
                self.strategy_adjustments['aggression_level'] = 1.5
                self.strategy_adjustments['risk_tolerance'] = 1.25
                self.strategy_adjustments['frequency_multiplier'] = 1.3
                self.strategy_adjustments['profit_threshold_adj'] = 0.75
                self.strategy_adjustments['confidence_threshold'] = 0.5

            # Update adaptive parameters based on adjustments
            self.confidence_threshold = self.strategy_adjustments['confidence_threshold']
            self.risk_tolerance = min(1.0, self.risk_tolerance * self.strategy_adjustments['risk_tolerance'])

            # Log strategy adjustments
            self.logger.info(f"ML STRATEGY ADJUSTMENT: Achievement={achievement_rate:.1f}%, Aggression={self.strategy_adjustments['aggression_level']:.2f}, Risk={self.strategy_adjustments['risk_tolerance']:.2f}")

            return True

        except Exception as e:
            self.logger.error(f"Error updating profit targets in ML system: {e}")
            return False

    def get_strategy_adjustments(self) -> Dict[str, float]:
        """Get current strategy adjustments for use by trading strategies"""
        return self.strategy_adjustments.copy()

    def get_profit_performance(self) -> Dict[str, Any]:
        """Get current profit performance metrics"""
        return {
            'achievement_rate': self.profit_targets.get('current_achievement_rate', 0.0),
            'profit_velocity': self.profit_targets.get('profit_velocity', 0.0),
            'target_velocity': self.profit_targets.get('target_velocity', 0.83),
            'performance_gap': self.profit_targets.get('performance_gap', 0.0),
            'aggression_level': self.strategy_adjustments['aggression_level'],
            'confidence_threshold': self.strategy_adjustments['confidence_threshold']
        }
