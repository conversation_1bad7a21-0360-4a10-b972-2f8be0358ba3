{"folders": [{"name": "Bybit Trading Bot", "path": "."}], "settings": {"python.defaultInterpreterPath": "C:\\Users\\<USER>\\.conda\\envs\\bybit-trader\\python.exe", "python.terminal.activateEnvironment": true, "python.terminal.activateEnvInCurrentTerminal": true, "terminal.integrated.defaultProfile.windows": "PowerShell", "terminal.integrated.cwd": "E:\\The_real_deal_copy\\Bybit_Bot\\BOT", "debug.console.fontSize": 12, "debug.internalConsoleOptions": "openOnSessionStart", "debug.terminal.clearBeforeReusing": true, "debug.allowBreakpointsEverywhere": true, "files.associations": {"*.py": "python", "*.json": "jsonc"}}, "launch": {"version": "0.2.0", "configurations": [{"name": "🧪 Debug Simple Test", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/simple_debug_test.py", "console": "integratedTerminal", "python": "C:\\Users\\<USER>\\.conda\\envs\\bybit-trader\\python.exe", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "CONDA_DEFAULT_ENV": "bybit-trader"}, "justMyCode": false, "stopOnEntry": false, "showReturnValue": true}, {"name": "📄 Debug Current File", "type": "debugpy", "request": "launch", "program": "${file}", "console": "integratedTerminal", "python": "C:\\Users\\<USER>\\.conda\\envs\\bybit-trader\\python.exe", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "CONDA_DEFAULT_ENV": "bybit-trader"}, "justMyCode": false, "stopOnEntry": false, "showReturnValue": true}, {"name": "🚀 Debug Main Trading Bot", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/main.py", "console": "integratedTerminal", "python": "C:\\Users\\<USER>\\.conda\\envs\\bybit-trader\\python.exe", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "CONDA_DEFAULT_ENV": "bybit-trader", "TRADING_ENV": "development"}, "args": ["--test-mode"], "justMyCode": false, "stopOnEntry": false, "showReturnValue": true}]}, "extensions": {"recommendations": ["ms-python.python", "ms-python.debugpy", "ms-vscode.vscode-json"]}}