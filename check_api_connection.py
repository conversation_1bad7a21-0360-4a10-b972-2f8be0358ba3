#!/usr/bin/env python3
"""
Bybit API Connection Diagnostic Tool
Checks if API credentials are valid and can connect to Bybit
"""

import asyncio
import aiohttp
import hmac
import hashlib
import time
import json
from pathlib import Path
import yaml
from urllib.parse import urlencode

async def test_bybit_connection():
    """Test Bybit API connection with current credentials"""
    print("BYBIT API CONNECTION DIAGNOSTIC")
    print("=" * 50)
    
    # Load config
    config_path = Path('config.yaml')
    if not config_path.exists():
        print("ERROR: config.yaml not found")
        return False
    
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # Get API credentials
    api_key = config.get('bybit', {}).get('api_key', '')
    api_secret = config.get('bybit', {}).get('api_secret', '')
    
    print(f"API Key: {api_key[:10]}...{api_key[-5:] if len(api_key) > 15 else api_key}")
    print(f"API Secret: {api_secret[:10]}...{api_secret[-5:] if len(api_secret) > 15 else api_secret}")
    print(f"API Key Length: {len(api_key)} characters")
    print(f"API Secret Length: {len(api_secret)} characters")
    print()
    
    # Check credential format
    if len(api_key) < 20:
        print("WARNING: API Key appears too short for valid Bybit credentials")
        print("Real Bybit API keys are typically 20+ characters")
    
    if len(api_secret) < 40:
        print("WARNING: API Secret appears too short for valid Bybit credentials")
        print("Real Bybit API secrets are typically 40+ characters")
    
    if not api_key or not api_secret:
        print("ERROR: Missing API credentials")
        print("Please configure valid Bybit API credentials in config.yaml")
        return False
    
    print("Testing API connection...")
    
    # Test public endpoint first (no authentication required)
    try:
        async with aiohttp.ClientSession() as session:
            url = "https://api.bybit.com/v5/market/time"
            async with session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    print("SUCCESS: Public API connection working")
                    print(f"Server time: {data.get('result', {}).get('timeSecond', 'Unknown')}")
                else:
                    print(f"ERROR: Public API failed with status {response.status}")
                    return False
    except Exception as e:
        print(f"ERROR: Public API connection failed: {e}")
        return False
    
    # Test private endpoint (requires authentication)
    try:
        async with aiohttp.ClientSession() as session:
            timestamp = str(int(time.time() * 1000))
            recv_window = "5000"

            # Bybit V5 API signature method - EXACT match to main system implementation
            # Create parameters dict
            params = {
                'timestamp': timestamp,
                'recv_window': recv_window
            }

            # Sort parameters but ensure timestamp comes before recv_window as Bybit expects
            sorted_params = []
            for key in sorted(params.keys()):
                if key not in ['timestamp', 'recv_window']:
                    sorted_params.append((key, params[key]))
            # Add timestamp and recv_window in the correct order
            sorted_params.append(('timestamp', params['timestamp']))
            sorted_params.append(('recv_window', params['recv_window']))

            query_string = urlencode(sorted_params)
            signing_string = f"{timestamp}{api_key}{recv_window}{query_string}"

            print(f"DEBUG: Signing string: {signing_string}")

            signature = hmac.new(
                api_secret.encode('utf-8'),
                signing_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()

            print(f"DEBUG: Generated signature: {signature}")

            headers = {
                'X-BAPI-API-KEY': api_key,
                'X-BAPI-SIGN': signature,
                'X-BAPI-SIGN-TYPE': '2',
                'X-BAPI-TIMESTAMP': timestamp,
                'X-BAPI-RECV-WINDOW': recv_window,
                'Content-Type': 'application/json'
            }

            url = f"https://api.bybit.com/v5/account/wallet-balance?{query_string}"

            print(f"DEBUG: Request URL: {url}")
            print(f"DEBUG: Headers: {headers}")
            
            async with session.get(url, headers=headers) as response:
                data = await response.json()
                
                if response.status == 200 and data.get('retCode') == 0:
                    print("SUCCESS: Private API authentication working")
                    print("Account access confirmed")
                    
                    # Show account info if available
                    result = data.get('result', {})
                    if 'list' in result and result['list']:
                        account = result['list'][0]
                        total_equity = account.get('totalEquity', '0')
                        available_balance = account.get('totalAvailableBalance', '0')
                        print(f"Total Equity: ${float(total_equity):.2f}")
                        print(f"Available Balance: ${float(available_balance):.2f}")
                    
                    return True
                else:
                    print(f"ERROR: Private API authentication failed")
                    print(f"Status: {response.status}")
                    print(f"Response: {data}")
                    
                    # Check for common error codes
                    ret_code = data.get('retCode')
                    ret_msg = data.get('retMsg', '')
                    
                    if ret_code == 10003:
                        print("DIAGNOSIS: Invalid API key")
                    elif ret_code == 10004:
                        print("DIAGNOSIS: Invalid API signature")
                    elif ret_code == 10005:
                        print("DIAGNOSIS: Permission denied - check API key permissions")
                    elif ret_code == 10006:
                        print("DIAGNOSIS: Too many requests - rate limited")
                    else:
                        print(f"DIAGNOSIS: Unknown error code {ret_code}: {ret_msg}")
                    
                    return False
                    
    except Exception as e:
        print(f"ERROR: Private API test failed: {e}")
        return False

async def main():
    """Main diagnostic function"""
    print("Starting Bybit API connection diagnostic...")
    print()
    
    success = await test_bybit_connection()
    
    print()
    print("=" * 50)
    if success:
        print("RESULT: API CONNECTION SUCCESSFUL")
        print("Your Bybit API credentials are valid and working")
        print("The trading bot should be able to execute real trades")
    else:
        print("RESULT: API CONNECTION FAILED")
        print("REQUIRED ACTIONS:")
        print("1. Obtain valid Bybit API credentials from your Bybit account")
        print("2. Go to https://www.bybit.com/app/user/api-management")
        print("3. Create new API key with trading permissions")
        print("4. Update config.yaml with the real API key and secret")
        print("5. Ensure API key has the following permissions:")
        print("   - Read access")
        print("   - Trade access")
        print("   - Derivatives trading (for margin/futures)")
        print("6. Make sure to use MAINNET credentials (not testnet)")
    print("=" * 50)

if __name__ == "__main__":
    asyncio.run(main())
