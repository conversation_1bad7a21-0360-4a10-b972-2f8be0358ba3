#!/usr/bin/env python3
"""
Test script for the adaptive learning system
Verifies that the learning system can adapt parameters based on trade outcomes
"""

import asyncio
import logging
import sqlite3
from datetime import datetime
from bybit_bot.ai.adaptive_learning_engine import AdaptiveLearningEngine
from bybit_bot.ai.real_time_learning_monitor import RealTimeLearningMonitor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_learning_system():
    """Test the adaptive learning system with simulated trades"""
    
    logger.info("TESTING: Starting adaptive learning system test")
    
    # Initialize learning components
    learning_engine = AdaptiveLearningEngine()
    learning_monitor = RealTimeLearningMonitor()
    
    # Initialize database tables
    await learning_engine._initialize_database()
    
    # Test 1: Simulate a losing trade
    logger.info("TEST 1: Simulating losing trade")
    losing_trade = {
        'symbol': 'BTCUSDT',
        'side': 'Buy',
        'quantity': 0.001,
        'profit_loss': -2.5,  # 2.5 EUR loss
        'timestamp': datetime.now().isoformat(),
        'execution_time': 0.5
    }
    
    # Get initial parameters
    initial_params = learning_engine.get_adapted_parameters()
    logger.info(f"INITIAL PARAMS: {initial_params}")
    
    # Learn from losing trade
    learning_result = await learning_engine.learn_from_trade(losing_trade)
    logger.info(f"LEARNING RESULT: {learning_result}")
    
    # Get adapted parameters
    adapted_params = learning_engine.get_adapted_parameters()
    logger.info(f"ADAPTED PARAMS: {adapted_params}")
    
    # Verify adaptation occurred
    if adapted_params['position_size_multiplier'] < initial_params['position_size_multiplier']:
        logger.info("SUCCESS: Position size multiplier reduced after loss")
    else:
        logger.error("FAILURE: Position size multiplier not reduced after loss")
    
    # Test 2: Simulate multiple consecutive losses
    logger.info("TEST 2: Simulating consecutive losses")
    for i in range(3):
        consecutive_loss = {
            'symbol': 'ETHUSDT',
            'side': 'Sell',
            'quantity': 0.01,
            'profit_loss': -1.0,  # 1 EUR loss each
            'timestamp': datetime.now().isoformat(),
            'execution_time': 0.3
        }
        
        result = await learning_engine.learn_from_trade(consecutive_loss)
        logger.info(f"CONSECUTIVE LOSS {i+1}: {result}")
    
    # Check if emergency stop triggered
    final_params = learning_engine.get_adapted_parameters()
    logger.info(f"FINAL PARAMS: {final_params}")
    
    if final_params['consecutive_losses'] >= 3:
        logger.info("SUCCESS: Consecutive losses tracked correctly")
    
    # Test 3: Test learning monitor
    logger.info("TEST 3: Testing real-time learning monitor")
    
    # Check trading permission
    trading_allowed = learning_monitor.is_trading_allowed()
    logger.info(f"TRADING ALLOWED: {trading_allowed}")
    
    # Get current trading parameters
    trading_params = learning_monitor.get_current_trading_parameters()
    logger.info(f"TRADING PARAMS: {trading_params}")
    
    # Test 4: Simulate profitable trade to test recovery
    logger.info("TEST 4: Simulating profitable trade")
    profitable_trade = {
        'symbol': 'BTCUSDT',
        'side': 'Buy',
        'quantity': 0.0001,  # Very small position due to learning
        'profit_loss': 0.5,   # Small profit
        'timestamp': datetime.now().isoformat(),
        'execution_time': 0.2
    }
    
    profit_result = await learning_engine.learn_from_trade(profitable_trade)
    logger.info(f"PROFIT LEARNING: {profit_result}")
    
    # Test 5: Performance pattern analysis
    logger.info("TEST 5: Testing performance pattern analysis")
    analysis = await learning_engine.analyze_performance_patterns()
    logger.info(f"PERFORMANCE ANALYSIS: {analysis}")
    
    # Test 6: Market condition learning
    logger.info("TEST 6: Testing market condition learning")
    market_data = {
        'volatility': 0.08,  # High volatility
        'volume': 500000,    # Low volume
        'price_change_24h': -5.2  # Negative price change
    }
    
    market_learning = await learning_engine.learn_from_market_conditions(market_data)
    logger.info(f"MARKET LEARNING: {market_learning}")
    
    # Final verification
    logger.info("FINAL VERIFICATION:")
    final_state = learning_engine.get_adapted_parameters()
    
    # Check if system learned appropriately
    checks = {
        'position_size_reduced': final_state['position_size_multiplier'] < 0.1,
        'risk_tolerance_reduced': final_state['risk_tolerance'] < 0.01,
        'confidence_reduced': final_state['confidence_score'] < 0.5,
        'consecutive_losses_tracked': final_state['consecutive_losses'] > 0,
        'daily_pnl_negative': final_state['daily_pnl'] < 0
    }
    
    logger.info(f"LEARNING CHECKS: {checks}")
    
    # Count successful checks
    successful_checks = sum(checks.values())
    total_checks = len(checks)
    
    logger.info(f"LEARNING SYSTEM TEST COMPLETE: {successful_checks}/{total_checks} checks passed")
    
    if successful_checks >= 4:
        logger.info("SUCCESS: Learning system is functioning correctly")
        return True
    else:
        logger.error("FAILURE: Learning system needs improvement")
        return False

async def test_database_integration():
    """Test database integration for learning system"""
    
    logger.info("TESTING: Database integration")
    
    try:
        # Test database connection
        conn = sqlite3.connect("bybit_trading_bot.db")
        cursor = conn.cursor()
        
        # Check if learning tables exist
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name LIKE '%learning%' OR name LIKE '%adaptation%'
        """)
        
        tables = cursor.fetchall()
        logger.info(f"LEARNING TABLES: {[table[0] for table in tables]}")
        
        # Check recent learning data
        cursor.execute("""
            SELECT COUNT(*) FROM learning_metrics 
            WHERE timestamp > datetime('now', '-1 hour')
        """)
        
        recent_metrics = cursor.fetchone()[0]
        logger.info(f"RECENT LEARNING METRICS: {recent_metrics}")
        
        conn.close()
        
        if len(tables) >= 2:  # At least learning_metrics and adaptation_history
            logger.info("SUCCESS: Database integration working")
            return True
        else:
            logger.error("FAILURE: Learning tables not found")
            return False
            
    except Exception as e:
        logger.error(f"DATABASE TEST ERROR: {e}")
        return False

async def main():
    """Main test function"""
    
    logger.info("STARTING: Adaptive Learning System Tests")
    
    # Test 1: Learning system functionality
    learning_test = await test_learning_system()
    
    # Test 2: Database integration
    database_test = await test_database_integration()
    
    # Overall result
    if learning_test and database_test:
        logger.info("ALL TESTS PASSED: Learning system is ready for integration")
        return True
    else:
        logger.error("SOME TESTS FAILED: Learning system needs fixes")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        exit(0 if result else 1)
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
        exit(1)
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        exit(1)
