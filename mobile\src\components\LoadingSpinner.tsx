import React from 'react';
import { StyleSheet, View } from 'react-native';
import { ActivityIndicator, Text } from 'react-native-paper';
import { theme } from '../styles/theme';

interface LoadingSpinnerProps {
    message?: string;
    size?: 'small' | 'large';
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
    message = 'Loading...',
    size = 'large'
}) => {
    return (
        <View style={styles.container}>
            <ActivityIndicator
                size={size}
                color={theme.colors.primary}
                style={styles.spinner}
            />
            <Text variant="bodyMedium" style={styles.message}>
                {message}
            </Text>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: theme.colors.background,
        padding: theme.spacing.lg,
    },
    spinner: {
        marginBottom: theme.spacing.md,
    },
    message: {
        color: theme.colors.textSecondary,
        textAlign: 'center',
    },
});

export default LoadingSpinner;
