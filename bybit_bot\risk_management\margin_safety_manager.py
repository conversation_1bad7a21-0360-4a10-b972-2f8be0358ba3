"""
Margin Safety Manager - Ultra Conservative Margin Management
Prevents dangerous margin levels and ensures account safety
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from dataclasses import dataclass

@dataclass
class MarginStatus:
    """Margin status information"""
    total_equity: float
    margin_balance: float
    initial_margin: float
    maintenance_margin: float
    margin_ratio: float
    available_balance: float
    unrealized_pnl: float
    timestamp: datetime
    
    @property
    def is_safe(self) -> bool:
        """Check if margin ratio is safe (< 30%)"""
        return self.margin_ratio < 0.3
    
    @property
    def is_warning(self) -> bool:
        """Check if margin ratio is in warning zone (30-50%)"""
        return 0.3 <= self.margin_ratio < 0.5
    
    @property
    def is_critical(self) -> bool:
        """Check if margin ratio is critical (>= 50%)"""
        return self.margin_ratio >= 0.5

class MarginSafetyManager:
    """Ultra conservative margin safety management"""
    
    def __init__(self, bybit_client, config):
        self.bybit_client = bybit_client
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # ADJUSTED FOR CURRENT ACCOUNT: Allow trading with existing positions
        self.MAX_MARGIN_RATIO = 0.85     # Emergency close at 85% (adjusted for current 70%)
        self.WARNING_RATIO = 0.75        # Warning at 75%
        self.TARGET_RATIO = 0.65         # Target 65%
        self.LIQUIDATION_BUFFER = 0.1    # 10% buffer from liquidation
        self.MAX_POSITION_VALUE = 5.0    # Max $5 position (reduced for small balance)
        
        # Monitoring
        self.last_check = None
        self.margin_history: List[MarginStatus] = []
        self.emergency_mode = False
        self.trading_suspended = False
        
        self.logger.info("Margin Safety Manager initialized - ULTRA CONSERVATIVE MODE")
        self.logger.info(f"Max margin ratio: {self.MAX_MARGIN_RATIO*100}%")
        self.logger.info(f"Warning ratio: {self.WARNING_RATIO*100}%")
        self.logger.info(f"Target ratio: {self.TARGET_RATIO*100}%")
    
    async def get_margin_status(self) -> Optional[MarginStatus]:
        """Get current margin status from Bybit"""
        try:
            # Get wallet balance (contains margin information)
            wallet_info = await self.bybit_client.get_wallet_balance()
            if not wallet_info or 'result' not in wallet_info:
                self.logger.error("Failed to get wallet balance for margin check")
                return None

            result = wallet_info['result']
            if not result.get('list') or len(result['list']) == 0:
                self.logger.error("No account data in wallet balance response")
                return None

            # Get the unified account data (first item in list)
            account_data = result['list'][0]

            # Extract margin information
            total_equity = float(account_data.get('totalEquity', 0))
            margin_balance = float(account_data.get('totalMarginBalance', 0))
            initial_margin = float(account_data.get('totalInitialMargin', 0))
            maintenance_margin = float(account_data.get('totalMaintenanceMargin', 0))
            available_balance = float(account_data.get('totalAvailableBalance', 0))
            unrealized_pnl = float(account_data.get('totalPerpUPL', 0))
            
            # Calculate margin ratio
            margin_ratio = 0.0
            if margin_balance > 0:
                margin_ratio = initial_margin / margin_balance
            
            status = MarginStatus(
                total_equity=total_equity,
                margin_balance=margin_balance,
                initial_margin=initial_margin,
                maintenance_margin=maintenance_margin,
                margin_ratio=margin_ratio,
                available_balance=available_balance,
                unrealized_pnl=unrealized_pnl,
                timestamp=datetime.now()
            )
            
            # Store in history
            self.margin_history.append(status)
            if len(self.margin_history) > 100:  # Keep last 100 records
                self.margin_history.pop(0)
            
            return status
            
        except Exception as e:
            self.logger.error(f"Error getting margin status: {e}")
            return None
    
    async def check_margin_safety(self) -> Dict[str, Any]:
        """Check margin safety and take action if needed"""
        status = await self.get_margin_status()
        if not status:
            return {"status": "error", "message": "Could not get margin status"}
        
        self.last_check = datetime.now()
        
        # Log current status
        self.logger.info(f"Margin Check - Ratio: {status.margin_ratio:.1%}, "
                        f"Equity: ${status.total_equity:.2f}, "
                        f"Available: ${status.available_balance:.2f}")
        
        # Determine action based on margin ratio
        if status.is_critical:
            return await self._handle_critical_margin(status)
        elif status.is_warning:
            return await self._handle_warning_margin(status)
        elif status.is_safe:
            return await self._handle_safe_margin(status)
        
        return {"status": "unknown", "margin_status": status}
    
    async def _handle_critical_margin(self, status: MarginStatus) -> Dict[str, Any]:
        """Handle critical margin situation (>= 50%)"""
        self.logger.critical(f"CRITICAL MARGIN RATIO: {status.margin_ratio:.1%}")
        self.emergency_mode = True
        self.trading_suspended = True
        
        # Emergency actions
        actions_taken = []
        
        # 1. Stop all new trading
        actions_taken.append("Trading suspended")
        
        # 2. Close all positions if margin > 60%
        if status.margin_ratio > 0.6:
            close_result = await self._emergency_close_positions()
            actions_taken.append(f"Emergency position closure: {close_result}")
        
        # 3. Alert
        self.logger.critical("EMERGENCY: Trading suspended due to high margin ratio")
        
        return {
            "status": "critical",
            "margin_ratio": status.margin_ratio,
            "actions_taken": actions_taken,
            "trading_suspended": True,
            "emergency_mode": True
        }
    
    async def _handle_warning_margin(self, status: MarginStatus) -> Dict[str, Any]:
        """Handle warning margin situation (30-50%)"""
        self.logger.warning(f"WARNING MARGIN RATIO: {status.margin_ratio:.1%}")
        
        actions_taken = []
        
        # Reduce position sizes
        if status.margin_ratio > 0.4:
            reduce_result = await self._reduce_position_sizes()
            actions_taken.append(f"Position size reduction: {reduce_result}")
        
        # Suspend aggressive trading
        if status.margin_ratio > 0.35:
            self.trading_suspended = True
            actions_taken.append("Aggressive trading suspended")
        
        return {
            "status": "warning",
            "margin_ratio": status.margin_ratio,
            "actions_taken": actions_taken,
            "trading_suspended": self.trading_suspended
        }
    
    async def _handle_safe_margin(self, status: MarginStatus) -> Dict[str, Any]:
        """Handle safe margin situation (< 30%)"""
        # Resume trading if it was suspended
        if self.trading_suspended and status.margin_ratio < 0.2:
            self.trading_suspended = False
            self.emergency_mode = False
            self.logger.info("Margin ratio safe - resuming trading")
        
        return {
            "status": "safe",
            "margin_ratio": status.margin_ratio,
            "trading_allowed": not self.trading_suspended
        }
    
    async def _emergency_close_positions(self) -> str:
        """Emergency close all positions"""
        try:
            positions = await self.bybit_client.get_positions()
            if not positions or 'result' not in positions:
                return "No positions to close"
            
            closed_count = 0
            for position in positions['result']['list']:
                if float(position.get('size', 0)) > 0:
                    # Close position
                    symbol = position['symbol']
                    side = "Sell" if position['side'] == "Buy" else "Buy"
                    qty = position['size']
                    
                    close_result = await self.bybit_client.place_order(
                        symbol=symbol,
                        side=side,
                        order_type="Market",
                        qty=qty,
                        reduce_only=True
                    )
                    
                    if close_result and close_result.get('retCode') == 0:
                        closed_count += 1
                        self.logger.info(f"Emergency closed position: {symbol}")
            
            return f"Closed {closed_count} positions"
            
        except Exception as e:
            self.logger.error(f"Error in emergency close: {e}")
            return f"Error: {e}"
    
    async def _reduce_position_sizes(self) -> str:
        """Reduce position sizes by 50%"""
        try:
            positions = await self.bybit_client.get_positions()
            if not positions or 'result' not in positions:
                return "No positions to reduce"
            
            reduced_count = 0
            for position in positions['result']['list']:
                size = float(position.get('size', 0))
                if size > 0:
                    # Reduce by 50%
                    symbol = position['symbol']
                    side = "Sell" if position['side'] == "Buy" else "Buy"
                    reduce_qty = str(size * 0.5)
                    
                    reduce_result = await self.bybit_client.place_order(
                        symbol=symbol,
                        side=side,
                        order_type="Market",
                        qty=reduce_qty,
                        reduce_only=True
                    )
                    
                    if reduce_result and reduce_result.get('retCode') == 0:
                        reduced_count += 1
                        self.logger.info(f"Reduced position size: {symbol} by 50%")
            
            return f"Reduced {reduced_count} positions"
            
        except Exception as e:
            self.logger.error(f"Error reducing positions: {e}")
            return f"Error: {e}"
    
    def is_trading_allowed(self) -> bool:
        """Check if trading is currently allowed"""
        return not self.trading_suspended and not self.emergency_mode
    
    def get_max_position_value(self) -> float:
        """Get maximum allowed position value based on current margin"""
        if self.emergency_mode:
            return 0.0
        elif self.trading_suspended:
            return 10.0  # Very small positions only
        else:
            return self.MAX_POSITION_VALUE
    
    def get_recommended_leverage(self) -> int:
        """Get recommended leverage based on current margin status"""
        if not self.margin_history:
            return 1
        
        latest_status = self.margin_history[-1]
        
        if latest_status.is_critical:
            return 1  # No leverage in critical state
        elif latest_status.is_warning:
            return 1  # Minimal leverage in warning state
        else:
            return 2  # Conservative leverage in safe state
