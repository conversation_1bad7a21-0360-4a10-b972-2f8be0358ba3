import requests
import os

def download_file(url, folder):
    if not os.path.exists(folder):
        os.makedirs(folder)
    local_filename = os.path.join(folder, url.split('/')[-1])
    with requests.get(url, stream=True) as r:
        r.raise_for_status()
        with open(local_filename, 'wb') as f:
            for chunk in r.iter_content(chunk_size=8192): 
                f.write(chunk)
    return local_filename

if __name__ == "__main__":
    miniconda_url = "https://repo.anaconda.com/miniconda/Miniconda3-latest-Windows-x86_64.exe"
    download_folder = "E:\\"
    print(f"Downloading Miniconda from {miniconda_url} to {download_folder}")
    try:
        file_path = download_file(miniconda_url, download_folder)
        print(f"Successfully downloaded to {file_path}")
    except Exception as e:
        print(f"Error downloading Miniconda: {e}")
