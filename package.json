{"dependencies": {"@browsermcp/mcp": "^0.1.3", "@composio/mcp": "^1.0.7", "@currents/mcp": "^1.0.3", "@cyanheads/git-mcp-server": "^2.2.2", "@langchain/mcp-adapters": "^0.6.0", "@mastra/mcp": "^0.10.7", "@modelcontextprotocol/server-filesystem": "^2025.7.1", "@modelcontextprotocol/server-github": "^2025.4.8", "@notionhq/notion-mcp-server": "^1.8.1", "@playwright/mcp": "^0.0.32", "@supabase/mcp-utils": "^0.2.1", "@translated/lara-mcp": "^0.0.12", "@upstash/context7-mcp": "^1.0.14", "@vercel/mcp-adapter": "^1.0.0", "axios": "^1.4.0", "mcp-framework": "^0.2.15", "mcp-proxy": "^5.4.0", "mcp-server-code-runner": "^0.1.7", "n8n-mcp": "^2.7.21", "react-toastify": "^9.1.3", "tavily-mcp": "^0.2.9", "websearch-mcp": "^1.0.3"}}