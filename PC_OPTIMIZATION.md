# PC COMPATIBILITY - MSI CYBORG 15 A12VF

## System Specifications

- **Model**: MSI Cyborg 15 A12VF
- **Processor**: Intel Core i5-12450H (12th Gen, 2.00 GHz)
- **RAM**: 16GB (15.7GB Available)
- **Graphics**: 8GB Graphics Card
- **OS**: Windows 11 Home (Build 26100.4351)
- **Storage**: 1.44TB (453GB Used)

## Development Environment Optimization

### Node.js & React Development

```powershell
# Recommended Node.js version for your system
node --version  # Should be >= 18.0.0

# Optimized build settings for MSI hardware
npm config set target_arch x64
npm config set target_platform win32
npm config set msvs_version 2022
```

### Performance Settings

1. **Memory Allocation**
   - Node.js max memory: 8GB (half of available RAM)
   - Chrome DevTools memory: 4GB

2. **CPU Optimization**
   - Utilize all 12 cores for builds
   - Parallel processing enabled

3. **Storage Optimization**
   - Fast SSD utilization
   - Temp files on fastest drive

### VS Code Configuration

```json
{
    "typescript.preferences.maxTsServerMemory": 8192,
    "files.watcherExclude": {
        "**/node_modules/**": true,
        "**/dist/**": true,
        "**/build/**": true
    },
    "search.useIgnoreFiles": true,
    "search.exclude": {
        "**/node_modules": true,
        "**/dist": true
    }
}
```

### Development Scripts Optimization

```powershell
# Frontend Development (Optimized for MSI)
cd frontend
npm run dev -- --host 0.0.0.0 --port 3000

# Mobile Development
cd mobile
npx react-native start --port 8081

# Backend (Trading System)
conda activate bybit-trader
python main.py
```

### Network Configuration

- **Local Development**: <http://localhost:3000>
- **Network Access**: <http://*************:3000>
- **API Endpoint**: <http://*************:8000>

### Windows 11 Optimizations

1. **Developer Mode**: Enabled
2. **Windows Subsystem for Linux**: Optional (for advanced development)
3. **Hyper-V**: Disabled (for better Android emulator performance)
4. **Windows Defender**: Exclude development folders

### Firewall Configuration

```powershell
# Allow development ports
netsh advfirewall firewall add rule name="React Dev Server" dir=in action=allow protocol=TCP localport=3000
netsh advfirewall firewall add rule name="React Native Metro" dir=in action=allow protocol=TCP localport=8081
netsh advfirewall firewall add rule name="FastAPI Backend" dir=in action=allow protocol=TCP localport=8000
```

### Performance Monitoring

```powershell
# Monitor system resources during development
Get-Process -Name node | Select-Object Name, CPU, WorkingSet
Get-Process -Name chrome | Select-Object Name, CPU, WorkingSet
```

## Build Optimization Settings

### Frontend Build (Production)

```bash
# Optimized for MSI hardware
npm run build
# Uses all CPU cores for faster builds
# Utilizes 16GB RAM for large projects
```

### Mobile Build (Android)

```bash
# Optimized Gradle settings for MSI
cd mobile/android
./gradlew assembleRelease --parallel --max-workers=8
```

## Development Checklist

- [ ] Node.js 18+ installed
- [ ] Android Studio configured
- [ ] VS Code optimized
- [ ] Firewall rules configured
- [ ] Development ports accessible
- [ ] Network connectivity verified
- [ ] Performance monitoring enabled
