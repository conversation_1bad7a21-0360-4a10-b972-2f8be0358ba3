import {
    Psychology as AIIcon,
    Analytics as AnalyticsIcon,
    ChevronLeft as CollapseIcon,
    Dashboard as DashboardIcon,
    ChevronRight as ExpandIcon,
    AccountBalance as PortfolioIcon,
    PowerSettingsNew as PowerIcon,
    Settings as SettingsIcon,
    TrendingUp as TradingIcon,
    Palette as ThemeIcon,
    Help as HelpIcon,
    Keyboard as KeyboardIcon
} from '@mui/icons-material'
import {
    Box,
    Chip,
    Divider,
    Drawer,
    IconButton,
    List,
    ListItem,
    ListItemButton,
    ListItemIcon,
    ListItemText,
    Tooltip,
    Typography,
} from '@mui/material'
import { AnimatePresence, motion } from 'framer-motion'
import { useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { useResponsive } from '../../hooks/useResponsive'
import { useUserPreferences } from '../../hooks/useLocalStorage'
import ThemeCustomizer from '../common/ThemeCustomizer'

const Sidebar = ({ systemStatus, open = true, onClose, variant = 'persistent' }) => {
    const navigate = useNavigate()
    const location = useLocation()
    const { isMobile, getSidebarConfig } = useResponsive()
    const { preferences, updatePreference } = useUserPreferences()
    const [collapsed, setCollapsed] = useState(preferences.sidebarCollapsed || false)
    const [themeCustomizerOpen, setThemeCustomizerOpen] = useState(false)

    const sidebarConfig = getSidebarConfig()

    const menuItems = [
        {
            title: 'Dashboard',
            icon: <DashboardIcon />,
            path: '/',
            color: '#00ff88',
            shortcut: 'Ctrl+1'
        },
        {
            title: 'Trading',
            icon: <TradingIcon />,
            path: '/trading',
            color: '#ffa726',
            shortcut: 'Ctrl+2'
        },
        {
            title: 'Portfolio',
            icon: <PortfolioIcon />,
            path: '/portfolio',
            color: '#42a5f5',
            shortcut: 'Ctrl+3'
        },
        {
            title: 'Analytics',
            icon: <AnalyticsIcon />,
            path: '/analytics',
            color: '#ab47bc',
            shortcut: 'Ctrl+4'
        },
        {
            title: 'AI Status',
            icon: <AIIcon />,
            path: '/ai-status',
            color: '#ff7043',
            shortcut: 'Ctrl+5'
        },
        {
            title: 'Settings',
            icon: <SettingsIcon />,
            path: '/settings',
            color: '#78909c',
            shortcut: 'Ctrl+6'
        },
    ]

    const utilityItems = [
        {
            title: 'Theme Customizer',
            icon: <ThemeIcon />,
            action: () => setThemeCustomizerOpen(true),
            color: '#e91e63'
        },
        {
            title: 'Keyboard Shortcuts',
            icon: <KeyboardIcon />,
            action: () => console.log('Show shortcuts'),
            color: '#9c27b0'
        },
        {
            title: 'Help & Support',
            icon: <HelpIcon />,
            action: () => console.log('Show help'),
            color: '#607d8b'
        }
    ]

    const handleToggleCollapse = () => {
        const newCollapsed = !collapsed
        setCollapsed(newCollapsed)
        updatePreference('sidebarCollapsed', newCollapsed)
    }

    const getStatusColor = () => {
        if (!systemStatus) return '#666'
        if (systemStatus.status === 'running') return '#00ff88'
        return '#ffa726'
    }

    const getStatusText = () => {
        if (!systemStatus) return 'Unknown'
        return systemStatus.status === 'running' ? 'Active' : 'Initializing'
    }

    const sidebarContent = (
        <Box
            sx={{
                width: collapsed ? 60 : sidebarConfig.width,
                height: '100vh',
                background: 'linear-gradient(180deg, #0a0a0a 0%, #1a1a1a 100%)',
                borderRight: '1px solid rgba(255, 255, 255, 0.1)',
                display: 'flex',
                flexDirection: 'column',
                overflow: 'hidden',
                paddingTop: '64px' // Account for AppBar height
            }}
        >
            {/* System Status */}
            <Box
                sx={{
                    p: collapsed ? 1 : 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: collapsed ? 'center' : 'flex-start',
                    borderBottom: '1px solid rgba(255, 255, 255, 0.1)'
                }}
            >
                <Box
                    sx={{
                        width: 12,
                        height: 12,
                        borderRadius: '50%',
                        backgroundColor: getStatusColor(),
                        mr: collapsed ? 0 : 1,
                        boxShadow: `0 0 10px ${getStatusColor()}`,
                        animation: 'pulse 2s infinite',
                    }}
                />
                <AnimatePresence>
                    {!collapsed && (
                        <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            exit={{ opacity: 0 }}
                            transition={{ duration: 0.2 }}
                        >
                            <Typography variant="caption" sx={{ color: '#b3b3b3' }}>
                                System {getStatusText()}
                            </Typography>
                        </motion.div>
                    )}
                </AnimatePresence>
            </Box>

            {/* Navigation Menu */}
            <List sx={{ px: collapsed ? 0.5 : 1, py: 1, flex: 1 }}>
                {menuItems.map((item, index) => {
                    const isActive = location.pathname === item.path

                    return (
                        <motion.div
                            key={item.path}
                            initial={{ x: -50, opacity: 0 }}
                            animate={{ x: 0, opacity: 1 }}
                            transition={{ duration: 0.3, delay: index * 0.1 }}
                        >
                            <ListItem disablePadding sx={{ mb: 0.5 }}>
                                <Tooltip
                                    title={collapsed ? `${item.title} (${item.shortcut})` : ''}
                                    placement="right"
                                >
                                    <ListItemButton
                                        onClick={() => navigate(item.path)}
                                        sx={{
                                            borderRadius: 2,
                                            mx: 0.5,
                                            minHeight: 48,
                                            backgroundColor: isActive
                                                ? 'rgba(0, 255, 136, 0.1)'
                                                : 'transparent',
                                            border: isActive
                                                ? '1px solid rgba(0, 255, 136, 0.3)'
                                                : '1px solid transparent',
                                            '&:hover': {
                                                backgroundColor: 'rgba(255, 255, 255, 0.05)',
                                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                            },
                                            justifyContent: collapsed ? 'center' : 'flex-start',
                                        }}
                                    >
                                        <ListItemIcon
                                            sx={{
                                                color: isActive ? '#00ff88' : '#b3b3b3',
                                                minWidth: collapsed ? 'auto' : 40,
                                                justifyContent: 'center',
                                            }}
                                        >
                                            {item.icon}
                                        </ListItemIcon>
                                        <AnimatePresence>
                                            {!collapsed && (
                                                <motion.div
                                                    initial={{ opacity: 0, width: 0 }}
                                                    animate={{ opacity: 1, width: 'auto' }}
                                                    exit={{ opacity: 0, width: 0 }}
                                                    transition={{ duration: 0.2 }}
                                                    style={{ overflow: 'hidden', flex: 1 }}
                                                >
                                                    <ListItemText
                                                        primary={item.title}
                                                        secondary={item.shortcut}
                                                        sx={{
                                                            '& .MuiListItemText-primary': {
                                                                color: isActive ? '#00ff88' : '#ffffff',
                                                                fontWeight: isActive ? 600 : 400,
                                                                fontSize: '0.95rem',
                                                            },
                                                            '& .MuiListItemText-secondary': {
                                                                color: '#666',
                                                                fontSize: '0.7rem',
                                                            },
                                                        }}
                                                    />
                                                </motion.div>
                                            )}
                                        </AnimatePresence>
                                    </ListItemButton>
                                </Tooltip>
                            </ListItem>
                        </motion.div>
                    )
                })}

                <Divider sx={{ borderColor: 'rgba(255, 255, 255, 0.1)', my: 2 }} />

                {/* Utility Items */}
                {utilityItems.map((item, index) => (
                    <motion.div
                        key={item.title}
                        initial={{ x: -50, opacity: 0 }}
                        animate={{ x: 0, opacity: 1 }}
                        transition={{ duration: 0.3, delay: (menuItems.length + index) * 0.1 }}
                    >
                        <ListItem disablePadding sx={{ mb: 0.5 }}>
                            <Tooltip title={collapsed ? item.title : ''} placement="right">
                                <ListItemButton
                                    onClick={item.action}
                                    sx={{
                                        borderRadius: 2,
                                        mx: 0.5,
                                        minHeight: 48,
                                        '&:hover': {
                                            backgroundColor: 'rgba(255, 255, 255, 0.05)',
                                            border: '1px solid rgba(255, 255, 255, 0.1)',
                                        },
                                        justifyContent: collapsed ? 'center' : 'flex-start',
                                    }}
                                >
                                    <ListItemIcon
                                        sx={{
                                            color: item.color,
                                            minWidth: collapsed ? 'auto' : 40,
                                            justifyContent: 'center',
                                        }}
                                    >
                                        {item.icon}
                                    </ListItemIcon>
                                    <AnimatePresence>
                                        {!collapsed && (
                                            <motion.div
                                                initial={{ opacity: 0, width: 0 }}
                                                animate={{ opacity: 1, width: 'auto' }}
                                                exit={{ opacity: 0, width: 0 }}
                                                transition={{ duration: 0.2 }}
                                                style={{ overflow: 'hidden' }}
                                            >
                                                <ListItemText
                                                    primary={item.title}
                                                    sx={{
                                                        '& .MuiListItemText-primary': {
                                                            color: '#ffffff',
                                                            fontSize: '0.9rem',
                                                        },
                                                    }}
                                                />
                                            </motion.div>
                                        )}
                                    </AnimatePresence>
                                </ListItemButton>
                            </Tooltip>
                        </ListItem>
                    </motion.div>
                ))}
            </List>

            {/* Bottom Actions */}
            <Box sx={{ p: collapsed ? 1 : 2, mt: 'auto' }}>
                <AnimatePresence>
                    {!collapsed && systemStatus && (
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: 20 }}
                            transition={{ duration: 0.3 }}
                        >
                            <Box sx={{ mb: 2 }}>
                                <Typography variant="caption" sx={{ color: '#666', mb: 1, display: 'block' }}>
                                    Quick Stats
                                </Typography>
                                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                    <Chip
                                        label="AI Active"
                                        size="small"
                                        sx={{
                                            backgroundColor: 'rgba(0, 255, 136, 0.1)',
                                            color: '#00ff88',
                                            border: '1px solid rgba(0, 255, 136, 0.3)',
                                            fontSize: '0.75rem',
                                        }}
                                    />
                                    <Chip
                                        label="Live"
                                        size="small"
                                        sx={{
                                            backgroundColor: 'rgba(255, 167, 38, 0.1)',
                                            color: '#ffa726',
                                            border: '1px solid rgba(255, 167, 38, 0.3)',
                                            fontSize: '0.75rem',
                                        }}
                                    />
                                </Box>
                            </Box>
                        </motion.div>
                    )}
                </AnimatePresence>

                <Tooltip title={collapsed ? 'Emergency Stop' : ''} placement="right">
                    <IconButton
                        sx={{
                            width: '100%',
                            color: '#ff4757',
                            border: '1px solid rgba(255, 71, 87, 0.3)',
                            borderRadius: 2,
                            '&:hover': {
                                backgroundColor: 'rgba(255, 71, 87, 0.1)',
                                border: '1px solid rgba(255, 71, 87, 0.5)',
                            },
                        }}
                    >
                        <PowerIcon />
                        <AnimatePresence>
                            {!collapsed && (
                                <motion.span
                                    initial={{ opacity: 0, width: 0 }}
                                    animate={{ opacity: 1, width: 'auto' }}
                                    exit={{ opacity: 0, width: 0 }}
                                    transition={{ duration: 0.2 }}
                                    style={{
                                        marginLeft: 8,
                                        fontSize: '0.875rem',
                                        overflow: 'hidden',
                                        whiteSpace: 'nowrap'
                                    }}
                                >
                                    Emergency Stop
                                </motion.span>
                            )}
                        </AnimatePresence>
                    </IconButton>
                </Tooltip>

                {/* Collapse Toggle */}
                {!isMobile && (
                    <IconButton
                        onClick={handleToggleCollapse}
                        sx={{
                            width: '100%',
                            mt: 1,
                            color: '#b3b3b3',
                            border: '1px solid rgba(255, 255, 255, 0.1)',
                            borderRadius: 2,
                            '&:hover': {
                                color: '#00ff88',
                                backgroundColor: 'rgba(0, 255, 136, 0.1)',
                                border: '1px solid rgba(0, 255, 136, 0.3)',
                            },
                        }}
                    >
                        {collapsed ? <ExpandIcon /> : <CollapseIcon />}
                        <AnimatePresence>
                            {!collapsed && (
                                <motion.span
                                    initial={{ opacity: 0, width: 0 }}
                                    animate={{ opacity: 1, width: 'auto' }}
                                    exit={{ opacity: 0, width: 0 }}
                                    transition={{ duration: 0.2 }}
                                    style={{
                                        marginLeft: 8,
                                        fontSize: '0.875rem',
                                        overflow: 'hidden',
                                        whiteSpace: 'nowrap'
                                    }}
                                >
                                    Collapse
                                </motion.span>
                            )}
                        </AnimatePresence>
                    </IconButton>
                )}
            </Box>
        </Box>
    )

    if (variant === 'temporary') {
        return (
            <>
                <Drawer
                    variant="temporary"
                    open={open}
                    onClose={onClose}
                    ModalProps={{
                        keepMounted: true, // Better open performance on mobile
                    }}
                    PaperProps={{
                        sx: {
                            backgroundColor: 'transparent',
                            border: 'none',
                            boxShadow: 'none'
                        }
                    }}
                >
                    {sidebarContent}
                </Drawer>
                <ThemeCustomizer
                    open={themeCustomizerOpen}
                    onClose={() => setThemeCustomizerOpen(false)}
                />
            </>
        )
    }

    return (
        <>
            {sidebarContent}
            <ThemeCustomizer
                open={themeCustomizerOpen}
                onClose={() => setThemeCustomizerOpen(false)}
            />
        </>
    )
}

export default Sidebar
