#!/bin/bash

# Bybit Trading Bot Mobile App Setup Script
# This script initializes a React Native project for the trading bot

echo "🚀 Setting up Bybit Trading Bot Mobile App..."

# Check if React Native CLI is installed
if ! command -v react-native &> /dev/null; then
    echo "Installing React Native CLI..."
    npm install -g @react-native-community/cli
fi

# Initialize React Native project
echo "📱 Initializing React Native project..."
npx react-native init BybitTradingBotMobile --template react-native-template-typescript

# Navigate to project directory
cd BybitTradingBotMobile

# Install dependencies
echo "📦 Installing dependencies..."
npm install @react-native-async-storage/async-storage
npm install @react-native-community/netinfo
npm install @react-navigation/bottom-tabs
npm install @react-navigation/native
npm install @react-navigation/native-stack
npm install @tanstack/react-query
npm install react-native-biometrics
npm install react-native-chart-kit
npm install react-native-encrypted-storage
npm install react-native-fast-image
npm install react-native-flash-message
npm install react-native-gesture-handler
npm install react-native-keychain
npm install react-native-linear-gradient
npm install react-native-paper
npm install react-native-push-notification
npm install react-native-reanimated
npm install react-native-safe-area-context
npm install react-native-screens
npm install react-native-svg
npm install react-native-vector-icons
npm install react-native-webview

# Install React Native dependencies
echo "🔗 Installing React Native dependencies..."
npx react-native install

# iOS specific setup (if on macOS)
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🍎 Setting up iOS dependencies..."
    cd ios && pod install && cd ..
fi

# Android setup
echo "🤖 Setting up Android..."

# Create necessary directories
mkdir -p android/app/src/main/assets
mkdir -p src/components
mkdir -p src/screens
mkdir -p src/services
mkdir -p src/utils
mkdir -p src/styles
mkdir -p src/navigation
mkdir -p src/hooks
mkdir -p src/context

echo "✅ React Native project setup complete!"
echo ""
echo "Next steps:"
echo "1. Open Android Studio and sync the project"
echo "2. Start Metro bundler: npm start"
echo "3. Run on Android: npm run android"
echo "4. Run on iOS: npm run ios (macOS only)"
echo ""
echo "Don't forget to:"
echo "- Update the API endpoint in src/services/api.ts"
echo "- Configure push notifications"
echo "- Set up signing certificates for release builds"
