/**
 * Android Build Configuration for Motorola Moto G32
 * Optimized for XT2235-2 running Android 13
 */

// android/app/build.gradle additions
const motoG32Config = `
android {
    compileSdkVersion 34
    buildToolsVersion "34.0.0"

    defaultConfig {
        applicationId "com.bybitbot.mobile"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 1
        versionName "1.0"
        
        // Optimized for Moto G32 hardware
        ndk {
            abiFilters "arm64-v8a", "armeabi-v7a"
        }
        
        // Memory optimizations for mid-range device
        multiDexEnabled true
        vectorDrawables.useSupportLibrary = true
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro"
            
            // Performance optimizations
            shrinkResources true
            zipAlignEnabled true
            debuggable false
        }
        debug {
            debuggable true
            minifyEnabled false
        }
    }

    // Optimize for Moto G32 display
    splits {
        density {
            enable true
            exclude "ldpi", "mdpi", "xxxhdpi"
            include "hdpi", "xhdpi", "xxhdpi"
        }
    }

    // Memory management for 4GB RAM
    packagingOptions {
        pickFirst "lib/*/libc++_shared.so"
        pickFirst "lib/*/libjsc.so"
    }
}

dependencies {
    implementation "androidx.multidex:multidex:2.0.1"
    
    // Optimized for Android 13
    implementation "androidx.core:core:1.12.0"
    implementation "androidx.appcompat:appcompat:1.6.1"
}
`;

// Metro config optimizations
const metroConfig = `
const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');

const config = {
  resolver: {
    // Optimize bundle size for Moto G32
    assetExts: ['bin', 'txt', 'jpg', 'png', 'json', 'svg'],
  },
  transformer: {
    // Enable Hermes for better performance
    hermesCommand: 'hermesc',
    minifierConfig: {
      // Optimize for mobile performance
      keep_fnames: true,
      mangle: {
        keep_fnames: true,
      },
    },
  },
  serializer: {
    // Reduce bundle size
    createModuleIdFactory: function () {
      return function (path) {
        return path.substr(1).replace(/[^a-zA-Z0-9]/g, '');
      };
    },
  },
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
`;

// AndroidManifest.xml optimizations
const manifestPermissions = `
<!-- Network permissions for trading data -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

<!-- Background sync for trading updates -->
<uses-permission android:name="android.permission.WAKE_LOCK" />
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />

<!-- Biometric authentication (optional for Moto G32) -->
<uses-permission android:name="android.permission.USE_FINGERPRINT" />
<uses-permission android:name="android.permission.USE_BIOMETRIC" />

<!-- Notifications -->
<uses-permission android:name="android.permission.VIBRATE" />

<!-- Storage for offline data -->
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

<!-- Hardware acceleration -->
<uses-feature android:name="android.hardware.vulkan.level" android:required="false" />
<uses-feature android:name="android.hardware.vulkan.version" android:required="false" />

<!-- Declare hardware features -->
<uses-feature android:name="android.hardware.touchscreen" android:required="true" />
<uses-feature android:name="android.hardware.wifi" android:required="false" />
<uses-feature android:name="android.hardware.telephony" android:required="false" />

<!-- Application configuration -->
<application
    android:name=".MainApplication"
    android:allowBackup="false"
    android:icon="@mipmap/ic_launcher"
    android:label="@string/app_name"
    android:roundIcon="@mipmap/ic_launcher_round"
    android:theme="@style/AppTheme"
    android:usesCleartextTraffic="true"
    android:hardwareAccelerated="true"
    android:largeHeap="true">
`;

export { manifestPermissions, metroConfig, motoG32Config };

