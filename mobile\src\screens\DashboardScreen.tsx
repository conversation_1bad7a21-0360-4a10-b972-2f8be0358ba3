import { useQuery } from '@tanstack/react-query';
import React, { useState } from 'react';
import {
    Dimensions,
    RefreshControl,
    ScrollView,
    StyleSheet,
    TouchableOpacity,
    View,
} from 'react-native';
import { <PERSON><PERSON><PERSON>, PieChart } from 'react-native-chart-kit';
import LinearGradient from 'react-native-linear-gradient';
import {
    Button,
    Card,
    Chip,
    Text
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';

import ErrorMessage from '../components/ErrorMessage';
import LoadingSpinner from '../components/LoadingSpinner';
import { apiService } from '../services/api';
import { theme } from '../styles/theme';
import { formatCurrency, formatPercentage } from '../utils/formatting';

const { width: screenWidth } = Dimensions.get('window');

interface DashboardData {
    totalProfit: number;
    dailyProfit: number;
    totalTrades: number;
    winRate: number;
    activePositions: number;
    systemHealth: number;
    profitHistory: Array<{ date: string; profit: number }>;
    portfolioDistribution: Array<{ name: string; value: number; color: string }>;
    recentTrades: Array<{
        id: string;
        symbol: string;
        side: 'buy' | 'sell';
        amount: number;
        price: number;
        profit: number;
        time: string;
    }>;
}

const DashboardScreen: React.FC = () => {
    const [refreshing, setRefreshing] = useState(false);

    const {
        data: dashboardData,
        isLoading,
        error,
        refetch,
    } = useQuery<DashboardData>({
        queryKey: ['dashboard'],
        queryFn: apiService.getDashboardData,
        refetchInterval: 5000, // Refresh every 5 seconds
    });

    const onRefresh = async () => {
        setRefreshing(true);
        await refetch();
        setRefreshing(false);
    };

    if (isLoading && !dashboardData) {
        return <LoadingSpinner />;
    }

    if (error) {
        return <ErrorMessage error={error} onRetry={refetch} />;
    }

    const renderMetricCard = (
        title: string,
        value: string,
        subtitle: string,
        color: string,
        icon: string,
        trend?: 'up' | 'down' | 'neutral'
    ) => (
        <Card style={styles.metricCard}>
            <Card.Content style={styles.metricContent}>
                <View style={styles.metricHeader}>
                    <Icon name={icon} size={24} color={color} />
                    {trend && (
                        <Icon
                            name={trend === 'up' ? 'trending-up' : trend === 'down' ? 'trending-down' : 'trending-flat'}
                            size={16}
                            color={trend === 'up' ? theme.colors.success : trend === 'down' ? theme.colors.error : theme.colors.textSecondary}
                        />
                    )}
                </View>
                <Text variant="bodySmall" style={styles.metricTitle}>
                    {title}
                </Text>
                <Text variant="headlineSmall" style={[styles.metricValue, { color }]}>
                    {value}
                </Text>
                <Text variant="bodySmall" style={styles.metricSubtitle}>
                    {subtitle}
                </Text>
            </Card.Content>
        </Card>
    );

    const chartConfig = {
        backgroundColor: theme.colors.surface,
        backgroundGradientFrom: theme.colors.surface,
        backgroundGradientTo: theme.colors.surface,
        decimalPlaces: 2,
        color: (opacity = 1) => `rgba(0, 255, 136, ${opacity})`,
        labelColor: (opacity = 1) => `rgba(255, 255, 255, ${opacity})`,
        style: {
            borderRadius: theme.borderRadius.md,
        },
        propsForDots: {
            r: '4',
            strokeWidth: '2',
            stroke: theme.colors.primary,
        },
    };

    return (
        <ScrollView
            style={styles.container}
            showsVerticalScrollIndicator={false}
            refreshControl={
                <RefreshControl
                    refreshing={refreshing}
                    onRefresh={onRefresh}
                    colors={[theme.colors.primary]}
                    tintColor={theme.colors.primary}
                />
            }
        >
            {/* Header Section */}
            <LinearGradient
                colors={theme.colors.gradient.primary}
                style={styles.header}
            >
                <View style={styles.headerContent}>
                    <Text variant="headlineMedium" style={styles.headerTitle}>
                        Trading Dashboard
                    </Text>
                    <Text variant="bodyMedium" style={styles.headerSubtitle}>
                        Real-time system overview
                    </Text>
                </View>
                <TouchableOpacity style={styles.emergencyButton}>
                    <Icon name="stop" size={24} color={theme.colors.error} />
                </TouchableOpacity>
            </LinearGradient>

            {/* Metrics Grid */}
            <View style={styles.metricsGrid}>
                {renderMetricCard(
                    'Total Profit',
                    formatCurrency(dashboardData?.totalProfit || 0),
                    'All time earnings',
                    theme.colors.success,
                    'account-balance-wallet',
                    'up'
                )}
                {renderMetricCard(
                    'Daily P&L',
                    formatCurrency(dashboardData?.dailyProfit || 0),
                    'Today\'s performance',
                    dashboardData?.dailyProfit && dashboardData.dailyProfit >= 0 ? theme.colors.success : theme.colors.error,
                    'today',
                    dashboardData?.dailyProfit && dashboardData.dailyProfit >= 0 ? 'up' : 'down'
                )}
                {renderMetricCard(
                    'Win Rate',
                    formatPercentage(dashboardData?.winRate || 0),
                    `${dashboardData?.totalTrades || 0} total trades`,
                    theme.colors.info,
                    'trending-up'
                )}
                {renderMetricCard(
                    'Active Positions',
                    String(dashboardData?.activePositions || 0),
                    'Currently trading',
                    theme.colors.warning,
                    'swap-horiz'
                )}
            </View>

            {/* System Health */}
            <Card style={styles.card}>
                <Card.Title
                    title="System Health"
                    titleStyle={styles.cardTitle}
                    left={(props) => <Icon {...props} name="health-and-safety" color={theme.colors.success} />}
                />
                <Card.Content>
                    <View style={styles.healthContainer}>
                        <View style={styles.healthIndicator}>
                            <View
                                style={[
                                    styles.healthBar,
                                    {
                                        width: `${dashboardData?.systemHealth || 0}%`,
                                        backgroundColor: theme.colors.success,
                                    },
                                ]}
                            />
                        </View>
                        <Text variant="bodyLarge" style={styles.healthText}>
                            {dashboardData?.systemHealth || 0}% Operational
                        </Text>
                    </View>
                    <View style={styles.statusChips}>
                        <Chip icon="check" style={styles.statusChip}>API Connected</Chip>
                        <Chip icon="psychology" style={styles.statusChip}>AI Active</Chip>
                        <Chip icon="security" style={styles.statusChip}>Risk Managed</Chip>
                    </View>
                </Card.Content>
            </Card>

            {/* Profit Chart */}
            {dashboardData?.profitHistory && dashboardData.profitHistory.length > 0 && (
                <Card style={styles.card}>
                    <Card.Title
                        title="Profit Trend"
                        titleStyle={styles.cardTitle}
                        left={(props) => <Icon {...props} name="show-chart" color={theme.colors.primary} />}
                    />
                    <Card.Content>
                        <LineChart
                            data={{
                                labels: dashboardData.profitHistory.slice(-7).map((_, index) => `Day ${index + 1}`),
                                datasets: [
                                    {
                                        data: dashboardData.profitHistory.slice(-7).map(item => item.profit),
                                    },
                                ],
                            }}
                            width={screenWidth - 60}
                            height={200}
                            chartConfig={chartConfig}
                            bezier
                            style={styles.chart}
                        />
                    </Card.Content>
                </Card>
            )}

            {/* Portfolio Distribution */}
            {dashboardData?.portfolioDistribution && dashboardData.portfolioDistribution.length > 0 && (
                <Card style={styles.card}>
                    <Card.Title
                        title="Portfolio Distribution"
                        titleStyle={styles.cardTitle}
                        left={(props) => <Icon {...props} name="pie-chart" color={theme.colors.info} />}
                    />
                    <Card.Content>
                        <PieChart
                            data={dashboardData.portfolioDistribution}
                            width={screenWidth - 60}
                            height={200}
                            chartConfig={chartConfig}
                            accessor="value"
                            backgroundColor="transparent"
                            paddingLeft="15"
                            style={styles.chart}
                        />
                    </Card.Content>
                </Card>
            )}

            {/* Recent Trades */}
            <Card style={[styles.card, styles.lastCard]}>
                <Card.Title
                    title="Recent Trades"
                    titleStyle={styles.cardTitle}
                    left={(props) => <Icon {...props} name="list" color={theme.colors.secondary} />}
                    right={(props) => (
                        <Button mode="text" onPress={() => { }}>
                            View All
                        </Button>
                    )}
                />
                <Card.Content>
                    {dashboardData?.recentTrades?.map((trade, index) => (
                        <TouchableOpacity key={trade.id} style={styles.tradeItem}>
                            <View style={styles.tradeHeader}>
                                <Text variant="bodyLarge" style={styles.tradeSymbol}>
                                    {trade.symbol}
                                </Text>
                                <Chip
                                    style={[
                                        styles.sideChip,
                                        { backgroundColor: trade.side === 'buy' ? theme.colors.success : theme.colors.error },
                                    ]}
                                >
                                    {trade.side.toUpperCase()}
                                </Chip>
                            </View>
                            <View style={styles.tradeDetails}>
                                <Text variant="bodyMedium" style={styles.tradeText}>
                                    Amount: {trade.amount} @ {formatCurrency(trade.price)}
                                </Text>
                                <Text
                                    variant="bodyMedium"
                                    style={[
                                        styles.tradeProfit,
                                        { color: trade.profit >= 0 ? theme.colors.success : theme.colors.error },
                                    ]}
                                >
                                    {trade.profit >= 0 ? '+' : ''}{formatCurrency(trade.profit)}
                                </Text>
                            </View>
                            <Text variant="bodySmall" style={styles.tradeTime}>
                                {trade.time}
                            </Text>
                        </TouchableOpacity>
                    ))}
                </Card.Content>
            </Card>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: theme.colors.background,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: theme.spacing.lg,
        paddingTop: theme.spacing.xl,
    },
    headerContent: {
        flex: 1,
    },
    headerTitle: {
        color: theme.colors.background,
        fontWeight: theme.typography.weights.bold,
    },
    headerSubtitle: {
        color: theme.colors.background,
        opacity: 0.8,
        marginTop: theme.spacing.xs,
    },
    emergencyButton: {
        width: 48,
        height: 48,
        borderRadius: 24,
        backgroundColor: theme.colors.background,
        justifyContent: 'center',
        alignItems: 'center',
    },
    metricsGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        padding: theme.spacing.md,
        paddingTop: theme.spacing.sm,
    },
    metricCard: {
        width: '48%',
        margin: '1%',
        backgroundColor: theme.colors.surface,
    },
    metricContent: {
        padding: theme.spacing.md,
    },
    metricHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: theme.spacing.sm,
    },
    metricTitle: {
        color: theme.colors.textSecondary,
        marginBottom: theme.spacing.xs,
    },
    metricValue: {
        fontWeight: theme.typography.weights.bold,
        marginBottom: theme.spacing.xs,
    },
    metricSubtitle: {
        color: theme.colors.textTertiary,
    },
    card: {
        margin: theme.spacing.md,
        marginTop: 0,
        backgroundColor: theme.colors.surface,
    },
    lastCard: {
        marginBottom: theme.spacing.xxl,
    },
    cardTitle: {
        color: theme.colors.text,
        fontWeight: theme.typography.weights.semibold,
    },
    healthContainer: {
        marginBottom: theme.spacing.lg,
    },
    healthIndicator: {
        height: 8,
        backgroundColor: theme.colors.border,
        borderRadius: 4,
        overflow: 'hidden',
        marginBottom: theme.spacing.sm,
    },
    healthBar: {
        height: '100%',
        borderRadius: 4,
    },
    healthText: {
        color: theme.colors.text,
        textAlign: 'center',
    },
    statusChips: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        marginTop: theme.spacing.md,
    },
    statusChip: {
        backgroundColor: theme.colors.surfaceVariant,
    },
    chart: {
        borderRadius: theme.borderRadius.md,
    },
    tradeItem: {
        paddingVertical: theme.spacing.md,
        borderBottomWidth: 1,
        borderBottomColor: theme.colors.border,
    },
    tradeHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: theme.spacing.sm,
    },
    tradeSymbol: {
        color: theme.colors.text,
        fontWeight: theme.typography.weights.semibold,
    },
    sideChip: {
        height: 24,
    },
    tradeDetails: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: theme.spacing.xs,
    },
    tradeText: {
        color: theme.colors.textSecondary,
    },
    tradeProfit: {
        fontWeight: theme.typography.weights.semibold,
    },
    tradeTime: {
        color: theme.colors.textTertiary,
    },
});

export default DashboardScreen;
