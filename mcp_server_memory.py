#!/usr/bin/env python3
"""
Memory MCP Server for Trading Bot
Provides persistent memory and learning capabilities
"""

import asyncio
import json
import logging
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime
import sqlite3

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MemoryMCPServer:
    """Memory MCP Server for persistent storage and learning"""
    
    def __init__(self):
        self.db_path = project_root / "memory.db"
        self.server_name = "memory"
        self.init_database()
        
    def init_database(self):
        """Initialize memory database"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS memory_store (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key TEXT UNIQUE,
                    value TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS learning_patterns (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    pattern_type TEXT,
                    pattern_data TEXT,
                    confidence REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            logger.info("Memory database initialized")
    
    async def handle_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle MCP requests"""
        method = request.get("method", "")
        params = request.get("params", {})
        
        if method == "memory/store":
            return await self.store_memory(params)
        elif method == "memory/retrieve":
            return await self.retrieve_memory(params)
        elif method == "memory/pattern":
            return await self.store_pattern(params)
        elif method == "tools/list":
            return await self.list_tools()
        else:
            return {"error": f"Unknown method: {method}"}
    
    async def store_memory(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Store memory item"""
        key = params.get("key")
        value = params.get("value")
        
        if not key or not value:
            return {"error": "Key and value required"}
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO memory_store (key, value, updated_at)
                    VALUES (?, ?, CURRENT_TIMESTAMP)
                """, (key, json.dumps(value)))
                
            return {"success": True, "key": key}
        except Exception as e:
            return {"error": f"Failed to store memory: {e}"}
    
    async def retrieve_memory(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Retrieve memory item"""
        key = params.get("key")
        
        if not key:
            return {"error": "Key required"}
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "SELECT value FROM memory_store WHERE key = ?", (key,)
                )
                row = cursor.fetchone()
                
                if row:
                    return {"success": True, "value": json.loads(row[0])}
                else:
                    return {"error": "Key not found"}
        except Exception as e:
            return {"error": f"Failed to retrieve memory: {e}"}
    
    async def store_pattern(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Store learning pattern"""
        pattern_type = params.get("pattern_type")
        pattern_data = params.get("pattern_data")
        confidence = params.get("confidence", 0.5)
        
        if not pattern_type or not pattern_data:
            return {"error": "Pattern type and data required"}
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO learning_patterns (pattern_type, pattern_data, confidence)
                    VALUES (?, ?, ?)
                """, (pattern_type, json.dumps(pattern_data), confidence))
                
            return {"success": True, "pattern_type": pattern_type}
        except Exception as e:
            return {"error": f"Failed to store pattern: {e}"}
    
    async def list_tools(self) -> Dict[str, Any]:
        """List available tools"""
        return {
            "tools": [
                {
                    "name": "memory_store",
                    "description": "Store key-value pairs in persistent memory",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "key": {"type": "string"},
                            "value": {"type": "object"}
                        },
                        "required": ["key", "value"]
                    }
                },
                {
                    "name": "memory_retrieve",
                    "description": "Retrieve stored memory by key",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "key": {"type": "string"}
                        },
                        "required": ["key"]
                    }
                },
                {
                    "name": "pattern_store",
                    "description": "Store learning patterns for analysis",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "pattern_type": {"type": "string"},
                            "pattern_data": {"type": "object"},
                            "confidence": {"type": "number"}
                        },
                        "required": ["pattern_type", "pattern_data"]
                    }
                }
            ]
        }

async def main():
    """Main MCP server entry point"""
    server = MemoryMCPServer()
    logger.info("Memory MCP Server starting...")
    
    # Simple stdin/stdout MCP protocol handler
    while True:
        try:
            line = input()
            if not line:
                break
                
            request = json.loads(line)
            response = await server.handle_request(request)
            print(json.dumps(response))
            
        except EOFError:
            break
        except Exception as e:
            error_response = {"error": f"Server error: {e}"}
            print(json.dumps(error_response))
    
    logger.info("Memory MCP Server stopped")

if __name__ == "__main__":
    asyncio.run(main())
