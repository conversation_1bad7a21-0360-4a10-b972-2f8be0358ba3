#!/usr/bin/env python3
"""
AI SYSTEM STATUS CHECKER
Verifies if AI and ML systems are truly active with real data
"""

import sqlite3
import redis
import os
from datetime import datetime, timed<PERSON><PERSON>

def check_ai_database_activity():
    """Check AI-related database tables for real activity"""
    print("CHECKING AI DATABASE ACTIVITY")
    print("=" * 50)
    
    if not os.path.exists('bybit_trading_bot.db'):
        print("ERROR: Database does not exist!")
        return False
    
    conn = sqlite3.connect('bybit_trading_bot.db')
    cursor = conn.cursor()
    
    # Get all tables
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    print(f"Database Tables: {[t[0] for t in tables]}")
    
    ai_tables = [
        'ai_system_interactions',
        'trading_memories', 
        'cognitive_metrics',
        'model_training_history',
        'correlation_matrices'
    ]
    
    total_ai_records = 0
    recent_ai_activity = False
    
    for table in ai_tables:
        try:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"{table}: {count} records")
            total_ai_records += count
            
            # Check for recent activity (last hour)
            cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE timestamp > datetime('now', '-1 hour')")
            recent_count = cursor.fetchone()[0]
            if recent_count > 0:
                print(f"  -> {recent_count} records in last hour (ACTIVE)")
                recent_ai_activity = True
            else:
                print(f"  -> 0 records in last hour (INACTIVE)")
                
        except Exception as e:
            print(f"{table}: Table does not exist or error: {e}")
    
    conn.close()
    
    print(f"\nTOTAL AI RECORDS: {total_ai_records}")
    print(f"RECENT AI ACTIVITY: {'YES' if recent_ai_activity else 'NO'}")
    
    return total_ai_records > 0 and recent_ai_activity

def check_redis_ai_activity():
    """Check Redis for AI system activity"""
    print("\nCHECKING REDIS AI ACTIVITY")
    print("=" * 50)
    
    try:
        r = redis.Redis(host='localhost', port=6379, decode_responses=True)
        
        # Check AI-related keys
        ai_keys = []
        all_keys = r.keys('*')
        
        for key in all_keys:
            if any(ai_term in key.lower() for ai_term in ['ai', 'ml', 'model', 'prediction', 'cognitive', 'memory']):
                ai_keys.append(key)
        
        print(f"AI-related Redis keys: {len(ai_keys)}")
        for key in ai_keys[:10]:  # Show first 10
            value = r.get(key)
            print(f"  {key}: {value}")
        
        # Check for recent AI activity timestamps
        ai_activity_keys = [k for k in all_keys if 'ai_last_' in k or 'ml_last_' in k]
        print(f"\nAI Activity Timestamps: {len(ai_activity_keys)}")
        
        recent_ai_activity = False
        for key in ai_activity_keys:
            timestamp_str = r.get(key)
            if timestamp_str:
                try:
                    timestamp = datetime.fromisoformat(timestamp_str)
                    if timestamp > datetime.now() - timedelta(hours=1):
                        print(f"  {key}: {timestamp_str} (RECENT)")
                        recent_ai_activity = True
                    else:
                        print(f"  {key}: {timestamp_str} (OLD)")
                except:
                    print(f"  {key}: {timestamp_str} (INVALID FORMAT)")
        
        return len(ai_keys) > 0 and recent_ai_activity
        
    except Exception as e:
        print(f"ERROR connecting to Redis: {e}")
        return False

def check_ai_processes():
    """Check if AI processes are actually running"""
    print("\nCHECKING AI PROCESS STATUS")
    print("=" * 50)
    
    # Check if main.py is running with AI components
    try:
        import psutil
        ai_processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                if 'main.py' in cmdline or 'ai' in cmdline.lower() or 'ml' in cmdline.lower():
                    ai_processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cmdline': cmdline
                    })
            except:
                continue
        
        print(f"AI-related processes: {len(ai_processes)}")
        for proc in ai_processes:
            print(f"  PID {proc['pid']}: {proc['cmdline']}")
        
        return len(ai_processes) > 0
        
    except ImportError:
        print("psutil not available - cannot check processes")
        return None

def main():
    """Main AI status check"""
    print("AI AND ML SYSTEM STATUS VERIFICATION")
    print("CHECKING FOR REAL DATA ACTIVITY - NO FAKE DATA")
    print("=" * 60)
    
    # Check database activity
    db_active = check_ai_database_activity()
    
    # Check Redis activity  
    redis_active = check_redis_ai_activity()
    
    # Check processes
    processes_active = check_ai_processes()
    
    print("\n" + "=" * 60)
    print("AI SYSTEM STATUS SUMMARY")
    print("=" * 60)
    print(f"Database AI Activity: {'ACTIVE' if db_active else 'INACTIVE'}")
    print(f"Redis AI Activity: {'ACTIVE' if redis_active else 'INACTIVE'}")
    print(f"AI Processes Running: {'ACTIVE' if processes_active else 'INACTIVE' if processes_active is not None else 'UNKNOWN'}")
    
    overall_status = db_active and redis_active
    print(f"\nOVERALL AI STATUS: {'TRULY ACTIVE' if overall_status else 'NOT TRULY ACTIVE'}")
    
    if not overall_status:
        print("\nWARNING: AI systems appear to be inactive or using fake data!")
        print("Real AI activity requires:")
        print("1. Recent records in AI database tables")
        print("2. Active AI keys in Redis with recent timestamps")
        print("3. Running AI processes")
    
    return overall_status

if __name__ == "__main__":
    main()
