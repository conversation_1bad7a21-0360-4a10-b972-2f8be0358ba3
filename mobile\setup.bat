@echo off
echo 🚀 Setting up Bybit Trading Bot Mobile App...

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js first.
    pause
    exit /b 1
)

REM Check if React Native CLI is installed
call react-native --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing React Native CLI...
    call npm install -g @react-native-community/cli
)

REM Check if Android Studio is available
echo 📱 Please ensure Android Studio is installed and configured before continuing.
echo Press any key to continue...
pause >nul

REM Initialize React Native project
echo 📱 Initializing React Native project...
call npx react-native init BybitTradingBotMobile --template react-native-template-typescript

REM Navigate to project directory
cd BybitTradingBotMobile

REM Install dependencies
echo 📦 Installing dependencies...
call npm install @react-native-async-storage/async-storage @react-native-community/netinfo @react-navigation/bottom-tabs @react-navigation/native @react-navigation/native-stack @tanstack/react-query react-native-biometrics react-native-chart-kit react-native-encrypted-storage react-native-fast-image react-native-flash-message react-native-gesture-handler react-native-keychain react-native-linear-gradient react-native-paper react-native-push-notification react-native-reanimated react-native-safe-area-context react-native-screens react-native-svg react-native-vector-icons react-native-webview

REM Install React Native dependencies
echo 🔗 Linking React Native dependencies...
call npx react-native install

REM Create necessary directories
echo 📁 Creating project structure...
mkdir src\components 2>nul
mkdir src\screens 2>nul
mkdir src\services 2>nul
mkdir src\utils 2>nul
mkdir src\styles 2>nul
mkdir src\navigation 2>nul
mkdir src\hooks 2>nul
mkdir src\context 2>nul
mkdir android\app\src\main\assets 2>nul

echo ✅ React Native project setup complete!
echo.
echo Next steps:
echo 1. Open Android Studio and import the android folder
echo 2. Sync the project in Android Studio
echo 3. Connect your Android device or start an emulator
echo 4. Start Metro bundler: npm start
echo 5. Run on Android: npm run android
echo.
echo Don't forget to:
echo - API endpoint is configured for 91.179.83.180:8000
echo - Configure push notifications
echo - Set up signing certificates for release builds
echo - Test on your Motorola device
echo.
pause
