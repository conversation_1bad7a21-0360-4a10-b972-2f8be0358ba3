#!/usr/bin/env python3
"""
CONTINUOUS TRADING ENGINE
Generates continuous trading activity for profit
"""

import asyncio
import sqlite3
import redis
from datetime import datetime
from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
from bybit_bot.core.config import BotConfig

async def continuous_trading():
    """Execute continuous trading for maximum activity"""
    
    print("STARTING CONTINUOUS TRADING ENGINE")
    print("GENERATING MAXIMUM TRADING ACTIVITY")
    print("=" * 50)
    
    # Initialize client
    config = BotConfig()
    client = EnhancedBybitClient(config)
    await client.initialize()
    print("SUCCESS: Bybit client initialized")
    
    trade_count = 0
    
    while True:
        try:
            # Get current account status
            balance_info = await client.get_account_balance()
            if balance_info and 'list' in balance_info:
                available_balance = float(balance_info['list'][0]['totalAvailableBalance'])
                total_equity = float(balance_info['list'][0]['totalEquity'])
                
                print(f"\nTRADE #{trade_count + 1}")
                print(f"Available Balance: ${available_balance:.2f}")
                print(f"Total Equity: ${total_equity:.2f}")
                
                # Check current positions
                positions = await client.get_positions()
                current_sol_position = 0
                if positions:
                    for pos in positions:
                        if pos.get('symbol') == 'SOLUSDT':
                            current_sol_position = float(pos.get('size', 0))
                            print(f"Current SOL Position: {current_sol_position}")
                
                # Get current SOL price
                current_price = await client.get_current_price("SOLUSDT", category="linear")
                print(f"Current SOL Price: ${current_price}")
                
                # Determine trade action (use 0.1 SOL minimum for Bybit requirements)
                if current_sol_position > 0.25:
                    # Sell some position
                    trade_size = 0.1
                    side = "Sell"
                    print(f"EXECUTING SELL: {trade_size} SOL")
                else:
                    # Buy more position
                    trade_size = 0.1
                    side = "Buy"
                    print(f"EXECUTING BUY: {trade_size} SOL")
                
                # Execute trade
                order = await client.place_order(
                    symbol="SOLUSDT",
                    side=side,
                    order_type="Market",
                    qty=str(trade_size),
                    category="linear",
                    time_in_force="IOC"
                )
                
                if order and 'orderId' in order:
                    order_id = order['orderId']
                    print(f"SUCCESS: {side} order executed - Order ID: {order_id}")
                    
                    # Record in database
                    try:
                        conn = sqlite3.connect('bybit_trading_bot.db')
                        cursor = conn.cursor()
                        cursor.execute('''
                            INSERT INTO trades (timestamp, symbol, side, quantity, price, strategy, order_id, status)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (datetime.now(), "SOLUSDT", side, trade_size, current_price, "CONTINUOUS_TRADING", order_id, "executed"))
                        conn.commit()
                        conn.close()
                        print("SUCCESS: Trade recorded in database")
                    except Exception as e:
                        print(f"Error recording trade: {e}")
                    
                    # Update Redis
                    try:
                        r = redis.Redis(host='localhost', port=6379, decode_responses=True)
                        r.set(f"trade:{order_id}", f"SOLUSDT:{side}:{trade_size}:{current_price}")
                        r.incr("trades:count")
                        r.set("last_trade_time", datetime.now().isoformat())
                        r.set("trading:active", "TRUE")
                        print("SUCCESS: Trade data updated in Redis")
                    except Exception as e:
                        print(f"Error updating Redis: {e}")
                    
                    trade_count += 1
                    print(f"*** TRADE #{trade_count} COMPLETED ***")
                    print(f"Order ID: {order_id}")
                    print(f"Action: {side} {trade_size} SOL")
                    print(f"Price: ${current_price}")
                    print(f"*** CONTINUOUS TRADING ACTIVE ***")
                    
                else:
                    print(f"ERROR: {side} order failed")
                
            else:
                print("ERROR: Could not get account balance")
            
            # Wait before next trade (30 seconds for high activity)
            print(f"\nWaiting 30 seconds before next trade...")
            await asyncio.sleep(30)
            
        except Exception as e:
            print(f"ERROR in trading loop: {e}")
            await asyncio.sleep(10)

async def main():
    """Main execution"""
    print("STARTING CONTINUOUS TRADING ENGINE")
    print("This will generate continuous trading activity")
    print("Press Ctrl+C to stop")
    print()
    
    try:
        await continuous_trading()
    except KeyboardInterrupt:
        print("\nCONTINUOUS TRADING STOPPED")
    except Exception as e:
        print(f"ERROR: {e}")

if __name__ == "__main__":
    asyncio.run(main())
