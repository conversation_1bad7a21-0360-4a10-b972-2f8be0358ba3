import { useState, useEffect, useCallback } from 'react'

/**
 * Custom hook for managing localStorage with React state
 * Provides persistent storage for user preferences
 */
export const useLocalStorage = (key, initialValue) => {
    // Get value from localStorage or use initial value
    const [storedValue, setStoredValue] = useState(() => {
        try {
            if (typeof window === 'undefined') {
                return initialValue
            }
            
            const item = window.localStorage.getItem(key)
            return item ? JSON.parse(item) : initialValue
        } catch (error) {
            console.warn(`Error reading localStorage key "${key}":`, error)
            return initialValue
        }
    })
    
    // Update localStorage and state
    const setValue = useCallback((value) => {
        try {
            // Allow value to be a function so we have the same API as useState
            const valueToStore = value instanceof Function ? value(storedValue) : value
            
            setStoredValue(valueToStore)
            
            if (typeof window !== 'undefined') {
                window.localStorage.setItem(key, JSON.stringify(valueToStore))
            }
        } catch (error) {
            console.warn(`Error setting localStorage key "${key}":`, error)
        }
    }, [key, storedValue])
    
    // Remove item from localStorage
    const removeValue = useCallback(() => {
        try {
            setStoredValue(initialValue)
            if (typeof window !== 'undefined') {
                window.localStorage.removeItem(key)
            }
        } catch (error) {
            console.warn(`Error removing localStorage key "${key}":`, error)
        }
    }, [key, initialValue])
    
    return [storedValue, setValue, removeValue]
}

/**
 * Hook for managing user preferences with default values
 */
export const useUserPreferences = () => {
    const [preferences, setPreferences, removePreferences] = useLocalStorage('userPreferences', {
        theme: 'dark',
        sidebarCollapsed: false,
        dashboardLayout: 'default',
        chartTimeframe: '1h',
        notifications: {
            trades: true,
            alerts: true,
            system: true,
            sound: false
        },
        display: {
            currency: 'USD',
            precision: 2,
            compactNumbers: true,
            animations: true
        },
        trading: {
            confirmOrders: true,
            defaultOrderType: 'market',
            riskWarnings: true
        }
    })
    
    // Update specific preference
    const updatePreference = useCallback((path, value) => {
        setPreferences(prev => {
            const newPrefs = { ...prev }
            const keys = path.split('.')
            let current = newPrefs
            
            // Navigate to the parent of the target key
            for (let i = 0; i < keys.length - 1; i++) {
                if (!current[keys[i]]) {
                    current[keys[i]] = {}
                }
                current = current[keys[i]]
            }
            
            // Set the value
            current[keys[keys.length - 1]] = value
            return newPrefs
        })
    }, [setPreferences])
    
    // Get specific preference
    const getPreference = useCallback((path, defaultValue = null) => {
        const keys = path.split('.')
        let current = preferences
        
        for (const key of keys) {
            if (current && typeof current === 'object' && key in current) {
                current = current[key]
            } else {
                return defaultValue
            }
        }
        
        return current
    }, [preferences])
    
    // Reset preferences to default
    const resetPreferences = useCallback(() => {
        removePreferences()
    }, [removePreferences])
    
    return {
        preferences,
        setPreferences,
        updatePreference,
        getPreference,
        resetPreferences
    }
}

/**
 * Hook for managing dashboard layout preferences
 */
export const useDashboardLayout = () => {
    const [layout, setLayout] = useLocalStorage('dashboardLayout', [
        { i: 'balance', x: 0, y: 0, w: 3, h: 2 },
        { i: 'pnl', x: 3, y: 0, w: 3, h: 2 },
        { i: 'chart', x: 0, y: 2, w: 6, h: 4 },
        { i: 'trades', x: 6, y: 0, w: 6, h: 3 },
        { i: 'positions', x: 6, y: 3, w: 6, h: 3 }
    ])
    
    const [widgets, setWidgets] = useLocalStorage('dashboardWidgets', {
        balance: { enabled: true, title: 'Account Balance' },
        pnl: { enabled: true, title: 'P&L Summary' },
        chart: { enabled: true, title: 'Price Chart' },
        trades: { enabled: true, title: 'Recent Trades' },
        positions: { enabled: true, title: 'Open Positions' },
        market: { enabled: false, title: 'Market Overview' },
        news: { enabled: false, title: 'Market News' }
    })
    
    const updateLayout = useCallback((newLayout) => {
        setLayout(newLayout)
    }, [setLayout])
    
    const toggleWidget = useCallback((widgetId) => {
        setWidgets(prev => ({
            ...prev,
            [widgetId]: {
                ...prev[widgetId],
                enabled: !prev[widgetId]?.enabled
            }
        }))
    }, [setWidgets])
    
    const resetLayout = useCallback(() => {
        setLayout([
            { i: 'balance', x: 0, y: 0, w: 3, h: 2 },
            { i: 'pnl', x: 3, y: 0, w: 3, h: 2 },
            { i: 'chart', x: 0, y: 2, w: 6, h: 4 },
            { i: 'trades', x: 6, y: 0, w: 6, h: 3 },
            { i: 'positions', x: 6, y: 3, w: 6, h: 3 }
        ])
    }, [setLayout])
    
    return {
        layout,
        widgets,
        updateLayout,
        toggleWidget,
        resetLayout
    }
}

export default useLocalStorage
