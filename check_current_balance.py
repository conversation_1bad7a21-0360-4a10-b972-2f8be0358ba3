#!/usr/bin/env python3
"""
Check Current Balance - Direct account balance check
"""

import asyncio
import sys
import os
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def check_current_balance():
    with open("current_balance_check.txt", "w") as f:
        def log(msg):
            print(msg)
            f.write(msg + "\n")
            f.flush()
        
        log("=== CURRENT BALANCE CHECK ===")
        log(f"Time: {datetime.now()}")
        
        try:
            from bybit_bot.core.config import BotConfig
            from bybit_bot.exchange.bybit_client import BybitClient
            
            config = BotConfig()
            client = BybitClient(config)
            await client.initialize()
            
            log("Client initialized successfully")
            
            # Get current balance
            balance_data = await client.get_account_balance()
            if isinstance(balance_data, dict):
                total_equity = balance_data.get('total_equity', 0)
                available_balance = balance_data.get('available_balance', 0)
                used_margin = balance_data.get('used_margin', 0)
                unrealized_pnl = balance_data.get('unrealized_pnl', 0)
                
                log(f"CURRENT ACCOUNT STATUS:")
                log(f"  Total Equity: ${total_equity:.8f}")
                log(f"  Available Balance: ${available_balance:.8f}")
                log(f"  Used Margin: ${used_margin:.8f}")
                log(f"  Unrealized PnL: ${unrealized_pnl:.8f}")
                
                # Check individual coins with more precision
                coins = balance_data.get('coins', {})
                log(f"\nCOIN BALANCES:")
                for coin, data in coins.items():
                    total = data.get('total', 0)
                    available = data.get('available', 0)
                    unrealized_pnl = data.get('unrealized_pnl', 0)
                    log(f"  {coin}:")
                    log(f"    Total: {total}")
                    log(f"    Available: {available}")
                    log(f"    Unrealized PnL: {unrealized_pnl}")
            else:
                log(f"Error getting balance: {balance_data}")
            
            # Check for any recent orders or trades
            log(f"\nCHECKING FOR RECENT ACTIVITY...")
            try:
                # Check order history for today
                orders = await client._make_request("GET", "/v5/order/history", {
                    "category": "spot",
                    "limit": 50
                }, signed=True)
                
                if orders.get('retCode') == 0:
                    order_list = orders.get('result', {}).get('list', [])
                    log(f"Recent orders found: {len(order_list)}")
                    
                    # Show recent orders
                    for i, order in enumerate(order_list[:5]):
                        log(f"  Order {i+1}: {order.get('side')} {order.get('qty')} {order.get('symbol')} @ {order.get('price')} - Status: {order.get('orderStatus')}")
                        log(f"    Created: {order.get('createdTime')}")
                else:
                    log(f"Error checking orders: {orders}")
                    
            except Exception as e:
                log(f"Error checking recent activity: {e}")
            
            await client.close()
            log("\n=== BALANCE CHECK COMPLETE ===")
            
        except Exception as e:
            log(f"ERROR: {e}")
            import traceback
            log(traceback.format_exc())

if __name__ == "__main__":
    asyncio.run(check_current_balance())
