import {
    Account<PERSON>alance,
    Psychology,
    Refresh,
    Speed,
    TrendingUp
} from '@mui/icons-material'
import {
    Box,
    Chip,
    Grid,
    IconButton,
    LinearProgress,
    Tooltip,
    Typography
} from '@mui/material'
import { motion } from 'framer-motion'
import React, { useState, useCallback } from 'react'
import CountUp from 'react-countup'
import { useQuery } from 'react-query'
import toast from 'react-hot-toast'

// Components
import MarketOverview from './components/MarketOverview'
import MetricCard from './components/MetricCard'
import ProfitChart from './components/ProfitChart'
import RecentTrades from './components/RecentTrades'
import StrategyPerformance from './components/StrategyPerformance'
import SystemHealth from './components/SystemHealth'

// Services
import { portfolioService, systemStatusService } from '../../services/api'
import { useRealTimeData } from '../../hooks/useRealTimeData'

const Dashboard = () => {
    const [refreshKey, setRefreshKey] = useState(0)

    // Real-time data hook
    const {
        connectionStatus,
        balance,
        profitData,
        systemHealth: realtimeSystemHealth,
        trades,
        performanceMetrics,
        isConnected
    } = useRealTimeData()

    // Show connection status
    React.useEffect(() => {
        if (connectionStatus === 'connected') {
            toast.success('🚀 Connected to live trading system')
        } else if (connectionStatus === 'disconnected') {
            toast.error('⚠️ Connection lost - Attempting to reconnect...')
        }
    }, [connectionStatus])

    // Queries
    const { data: systemStatus, isLoading: systemLoading } = useQuery(
        ['systemStatus', refreshKey],
        () => systemStatusService.getStatus(),
        { refetchInterval: 5000 }
    )

    const { data: portfolioData, isLoading: portfolioLoading } = useQuery(
        ['portfolio', refreshKey],
        () => portfolioService.getBalance(),
        { refetchInterval: 10000 }
    )

    const { data: profitStatus, isLoading: profitLoading } = useQuery(
        ['profitStatus', refreshKey],
        () => systemStatusService.getProfitStatus(),
        { refetchInterval: 5000 }
    )

    const { data: aiStatus, isLoading: aiLoading } = useQuery(
        ['aiStatus', refreshKey],
        () => systemStatusService.getAIStatus(),
        { refetchInterval: 15000 }
    )

    // Refresh handler for real-time data
    const handleDataRefresh = useCallback(() => {
        setRefreshKey(prev => prev + 1)
        toast.info('🔄 Refreshing live data...')
    }, [])

    // Get current system health with real-time data fallback
    const systemHealth = realtimeSystemHealth || {
        overall: 'operational',
        api: isConnected ? 'connected' : 'disconnected',
        trading: 'active',
        monitoring: 'active'
    }

    // Use real-time balance or fallback
    const currentBalance = balance || { total: 0, available: 0, reserved: 0 }

    // Calculate metrics from real-time data
    const totalBalance = currentBalance.total || 0
    const todayPnL = performanceMetrics?.dailyPnL || 0
    const activeTrades = trades?.filter(trade => trade.status === 'active')?.length || 0
    const winRate = performanceMetrics?.winRate || 0

    // Refresh handler for queries (keeping for compatibility)
    const handleLegacyRefresh = useCallback(() => {
        setRefreshKey(prev => prev + 1)
    }, [])

    const handleRefresh = () => {
        handleDataRefresh()
    }

    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1
            }
        }
    }

    const itemVariants = {
        hidden: { y: 20, opacity: 0 },
        visible: {
            y: 0,
            opacity: 1,
            transition: { duration: 0.5 }
        }
    }

    return (
        <Box
            sx={{
                minHeight: '100vh',
                background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%)',
                p: 3,
            }}
        >
            {/* Header */}
            <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
            >
                <Box
                    sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        mb: 4,
                    }}
                >
                    <Box>
                        <Typography
                            variant="h3"
                            sx={{
                                fontWeight: 700,
                                background: 'linear-gradient(45deg, #00ff88, #ffffff)',
                                backgroundClip: 'text',
                                WebkitBackgroundClip: 'text',
                                WebkitTextFillColor: 'transparent',
                                mb: 1,
                            }}
                        >
                            Trading Dashboard
                        </Typography>
                        <Typography variant="h6" sx={{ color: '#b3b3b3' }}>
                            Real-time autonomous trading system overview
                        </Typography>
                    </Box>

                    <Box sx={{ display: 'flex', gap: 1 }}>
                        <Tooltip title="Refresh Data">
                            <IconButton
                                onClick={handleRefresh}
                                sx={{
                                    background: 'rgba(255, 255, 255, 0.05)',
                                    border: '1px solid rgba(255, 255, 255, 0.1)',
                                    color: '#00ff88',
                                    '&:hover': {
                                        background: 'rgba(0, 255, 136, 0.1)',
                                        border: '1px solid rgba(0, 255, 136, 0.3)',
                                    },
                                }}
                            >
                                <Refresh />
                            </IconButton>
                        </Tooltip>

                        <Chip
                            label={systemStatus?.status === 'running' ? 'LIVE' : 'INITIALIZING'}
                            sx={{
                                backgroundColor: systemStatus?.status === 'running'
                                    ? 'rgba(0, 255, 136, 0.1)'
                                    : 'rgba(255, 167, 38, 0.1)',
                                color: systemStatus?.status === 'running' ? '#00ff88' : '#ffa726',
                                border: systemStatus?.status === 'running'
                                    ? '1px solid rgba(0, 255, 136, 0.3)'
                                    : '1px solid rgba(255, 167, 38, 0.3)',
                                fontWeight: 600,
                            }}
                        />
                    </Box>
                </Box>
            </motion.div>

            {/* Main Metrics Grid */}
            <motion.div
                variants={containerVariants}
                initial="hidden"
                animate="visible"
            >
                <Grid container spacing={3} sx={{ mb: 4 }}>
                    {/* Total Balance */}
                    <Grid item xs={12} sm={6} md={3}>
                        <motion.div variants={itemVariants}>
                            <MetricCard
                                title="Total Balance"
                                value={
                                    <CountUp
                                        start={0}
                                        end={totalBalance}
                                        duration={2}
                                        separator=","
                                        decimals={2}
                                        prefix="$"
                                    />
                                }
                                icon={<AccountBalance />}
                                color="#00ff88"
                                loading={portfolioLoading && !isConnected}
                            />
                        </motion.div>
                    </Grid>

                    {/* Daily P&L */}
                    <Grid item xs={12} sm={6} md={3}>
                        <motion.div variants={itemVariants}>
                            <MetricCard
                                title="Daily P&L"
                                value={
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                        <CountUp
                                            start={0}
                                            end={todayPnL}
                                            duration={2}
                                            separator=","
                                            decimals={2}
                                            prefix={todayPnL >= 0 ? "+$" : "-$"}
                                        />
                                        <Chip
                                            label={`${todayPnL >= 0 ? '+' : ''}${((todayPnL / totalBalance) * 100).toFixed(2)}%`}
                                            size="small"
                                            sx={{
                                                backgroundColor: todayPnL >= 0 ? 'rgba(0, 255, 136, 0.1)' : 'rgba(255, 82, 82, 0.1)',
                                                color: todayPnL >= 0 ? '#00ff88' : '#ff5252',
                                                fontSize: '0.75rem',
                                            }}
                                        />
                                    </Box>
                                }
                                icon={<TrendingUp />}
                                color={todayPnL >= 0 ? "#00ff88" : "#ff5252"}
                                loading={profitLoading && !isConnected}
                            />
                        </motion.div>
                    </Grid>

                    {/* Win Rate */}
                    <Grid item xs={12} sm={6} md={3}>
                        <motion.div variants={itemVariants}>
                            <MetricCard
                                title="Win Rate"
                                value={
                                    <Box>
                                        <CountUp
                                            start={0}
                                            end={winRate}
                                            duration={2}
                                            decimals={1}
                                            suffix="%"
                                        />
                                        <LinearProgress
                                            variant="determinate"
                                            value={winRate}
                                            sx={{
                                                mt: 1,
                                                height: 6,
                                                borderRadius: 3,
                                                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                                                '& .MuiLinearProgress-bar': {
                                                    background: 'linear-gradient(90deg, #00ff88, #00cc6a)',
                                                    borderRadius: 3,
                                                },
                                            }}
                                        />
                                    </Box>
                                }
                                icon={<Speed />}
                                color="#42a5f5"
                                loading={!isConnected}
                            />
                        </motion.div>
                    </Grid>

                    {/* AI Confidence */}
                    <Grid item xs={12} sm={6} md={3}>
                        <motion.div variants={itemVariants}>
                            <MetricCard
                                title="AI Confidence"
                                value={
                                    <Box>
                                        <CountUp
                                            start={0}
                                            end={performanceMetrics?.aiConfidence || 0}
                                            duration={2}
                                            decimals={1}
                                            suffix="%"
                                        />
                                        <LinearProgress
                                            variant="determinate"
                                            value={performanceMetrics?.aiConfidence || 0}
                                            sx={{
                                                mt: 1,
                                                height: 6,
                                                borderRadius: 3,
                                                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                                                '& .MuiLinearProgress-bar': {
                                                    background: 'linear-gradient(90deg, #ff7043, #f4511e)',
                                                    borderRadius: 3,
                                                },
                                            }}
                                        />
                                    </Box>
                                }
                                icon={<Psychology />}
                                color="#ff7043"
                                loading={aiLoading && !isConnected}
                            />
                        </motion.div>
                    </Grid>
                </Grid>

                {/* Charts and Analytics Grid */}
                <Grid container spacing={3} sx={{ mb: 4 }}>
                    {/* Profit Chart */}
                    <Grid item xs={12} lg={8}>
                        <motion.div variants={itemVariants}>
                            <ProfitChart profitData={profitData} />
                        </motion.div>
                    </Grid>

                    {/* System Health */}
                    <Grid item xs={12} lg={4}>
                        <motion.div variants={itemVariants}>
                            <SystemHealth 
                                systemStatus={systemStatus} 
                                aiStatus={aiStatus}
                                realtimeHealth={systemHealth}
                                isConnected={isConnected}
                            />
                        </motion.div>
                    </Grid>
                </Grid>

                {/* Bottom Grid */}
                <Grid container spacing={3}>
                    {/* Strategy Performance */}
                    <Grid item xs={12} md={6}>
                        <motion.div variants={itemVariants}>
                            <StrategyPerformance performanceMetrics={performanceMetrics} />
                        </motion.div>
                    </Grid>

                    {/* Recent Trades */}
                    <Grid item xs={12} md={6}>
                        <motion.div variants={itemVariants}>
                            <RecentTrades trades={trades} />
                        </motion.div>
                    </Grid>

                    {/* Market Overview */}
                    <Grid item xs={12}>
                        <motion.div variants={itemVariants}>
                            <MarketOverview />
                        </motion.div>
                    </Grid>
                </Grid>
            </motion.div>
        </Box>
    )
}

export default Dashboard
