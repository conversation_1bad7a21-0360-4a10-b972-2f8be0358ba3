#!/usr/bin/env python3
"""
Check if six is available and install if needed
"""
import sys
import subprocess

def check_and_install_six():
    try:
        import six
        print("SUCCESS: six module is already available")
        print(f"six version: {six.__version__}")
        print(f"six location: {six.__file__}")
        return True
    except ImportError:
        print("six module not found, attempting to install...")
        try:
            # Try installing six
            result = subprocess.run([sys.executable, "-m", "pip", "install", "six", "--user"], 
                                  capture_output=True, text=True, timeout=30)
            print(f"Install result: {result.returncode}")
            print(f"stdout: {result.stdout}")
            if result.stderr:
                print(f"stderr: {result.stderr}")
            
            # Try importing again
            import six
            print("SUCCESS: six installed and imported successfully")
            return True
        except Exception as e:
            print(f"ERROR: Failed to install six: {e}")
            return False

if __name__ == "__main__":
    success = check_and_install_six()
    print(f"Final result: {'SUCCESS' if success else 'FAILED'}")
