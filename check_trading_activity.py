#!/usr/bin/env python3
"""
Check Trading Activity - Look for signs of actual trading activity
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def check_trading_activity():
    with open("trading_activity_output.txt", "w") as f:
        def log(msg):
            print(msg)
            f.write(msg + "\n")
            f.flush()
        
        log("=== TRADING ACTIVITY CHECK ===")
        log(f"Time: {datetime.now()}")
        
        try:
            from bybit_bot.core.config import BotConfig
            from bybit_bot.exchange.bybit_client import BybitClient
            
            config = BotConfig()
            client = BybitClient(config)
            await client.initialize()
            
            log("Client initialized successfully")
            
            # Check account balance
            log("\n1. Current Account Status:")
            balance_data = await client.get_account_balance()
            if isinstance(balance_data, dict):
                total_equity = balance_data.get('total_equity', 0)
                available_balance = balance_data.get('available_balance', 0)
                unrealized_pnl = balance_data.get('unrealized_pnl', 0)
                used_margin = balance_data.get('used_margin', 0)
                
                log(f"   Total Equity: ${total_equity:.2f}")
                log(f"   Available Balance: ${available_balance:.2f}")
                log(f"   Used Margin: ${used_margin:.2f}")
                log(f"   Unrealized PnL: ${unrealized_pnl:.2f}")
                
                # Check individual coins
                coins = balance_data.get('coins', {})
                log(f"   Active Coins: {len(coins)}")
                for coin, data in coins.items():
                    total = data.get('total', 0)
                    available = data.get('available', 0)
                    if total > 0:
                        log(f"     {coin}: Total={total}, Available={available}")
            
            # Check for recent orders
            log("\n2. Recent Orders Check:")
            try:
                # Check BTCUSDT orders
                orders_btc = await client._make_request("GET", "/v5/order/history", {
                    "category": "spot",
                    "symbol": "BTCUSDT",
                    "limit": 10
                }, signed=True)
                
                if orders_btc.get('retCode') == 0:
                    orders_list = orders_btc.get('result', {}).get('list', [])
                    log(f"   BTCUSDT Orders: {len(orders_list)} found")
                    for order in orders_list[:3]:  # Show first 3
                        log(f"     Order: {order.get('side')} {order.get('qty')} @ {order.get('price')} - Status: {order.get('orderStatus')}")
                else:
                    log(f"   BTCUSDT Orders: Error - {orders_btc}")
                
                # Check ETHUSDT orders
                orders_eth = await client._make_request("GET", "/v5/order/history", {
                    "category": "spot",
                    "symbol": "ETHUSDT", 
                    "limit": 10
                }, signed=True)
                
                if orders_eth.get('retCode') == 0:
                    orders_list = orders_eth.get('result', {}).get('list', [])
                    log(f"   ETHUSDT Orders: {len(orders_list)} found")
                    for order in orders_list[:3]:  # Show first 3
                        log(f"     Order: {order.get('side')} {order.get('qty')} @ {order.get('price')} - Status: {order.get('orderStatus')}")
                else:
                    log(f"   ETHUSDT Orders: Error - {orders_eth}")
                    
            except Exception as e:
                log(f"   Error checking orders: {e}")
            
            # Check for active positions
            log("\n3. Active Positions Check:")
            try:
                positions = await client._make_request("GET", "/v5/position/list", {
                    "category": "spot"
                }, signed=True)
                
                if positions.get('retCode') == 0:
                    pos_list = positions.get('result', {}).get('list', [])
                    log(f"   Active Positions: {len(pos_list)} found")
                    for pos in pos_list:
                        if float(pos.get('size', 0)) > 0:
                            log(f"     Position: {pos.get('symbol')} - Size: {pos.get('size')} - PnL: {pos.get('unrealisedPnl', 0)}")
                else:
                    log(f"   Positions Error: {positions}")
                    
            except Exception as e:
                log(f"   Error checking positions: {e}")
            
            # Check current market prices
            log("\n4. Current Market Prices:")
            try:
                btc_klines = await client.get_klines("spot", "BTCUSDT", "1", 1)
                if btc_klines.get('retCode') == 0:
                    btc_price = btc_klines['result']['list'][0][4]
                    log(f"   BTCUSDT: ${btc_price}")
                
                eth_klines = await client.get_klines("spot", "ETHUSDT", "1", 1)
                if eth_klines.get('retCode') == 0:
                    eth_price = eth_klines['result']['list'][0][4]
                    log(f"   ETHUSDT: ${eth_price}")
                    
            except Exception as e:
                log(f"   Error getting prices: {e}")
            
            await client.close()
            log("\n=== TRADING ACTIVITY CHECK COMPLETE ===")
            
        except Exception as e:
            log(f"ERROR: {e}")
            import traceback
            log(traceback.format_exc())

if __name__ == "__main__":
    asyncio.run(check_trading_activity())
