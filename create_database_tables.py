#!/usr/bin/env python3
"""
Create missing database tables for the trading bot system
"""

import sqlite3
import os
from datetime import datetime

def create_all_tables():
    """Create all required database tables"""
    
    # Connect to database
    db_path = 'bybit_trading_bot.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    print(f"Creating database tables in {db_path}")
    
    # Create trades table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS trades (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            symbol TEXT NOT NULL,
            side TEXT NOT NULL,
            quantity REAL NOT NULL,
            price REAL NOT NULL,
            pnl REAL DEFAULT 0,
            strategy TEXT,
            order_id TEXT,
            status TEXT DEFAULT 'completed'
        )
    ''')
    
    # Create trading_memories table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS trading_memories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            memory_type TEXT NOT NULL,
            content TEXT NOT NULL,
            importance REAL DEFAULT 1.0,
            strategy TEXT,
            symbol TEXT
        )
    ''')
    
    # Create strategy_memories table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS strategy_memories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            strategy_name TEXT NOT NULL,
            memory_content TEXT NOT NULL,
            performance_score REAL DEFAULT 0.0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Create cognitive_metrics table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS cognitive_metrics (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            metric_type TEXT NOT NULL,
            metric_value REAL NOT NULL,
            context TEXT,
            confidence REAL DEFAULT 1.0
        )
    ''')
    
    # Create time_aware_memories table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS time_aware_memories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            memory_type TEXT NOT NULL,
            content TEXT NOT NULL,
            time_context TEXT,
            market_session TEXT,
            importance REAL DEFAULT 1.0
        )
    ''')
    
    # Create market_correlations table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS market_correlations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol_pair TEXT NOT NULL,
            correlation_value REAL NOT NULL,
            timeframe TEXT NOT NULL,
            last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
            confidence REAL DEFAULT 1.0
        )
    ''')
    
    # Create strategy_performance_memories table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS strategy_performance_memories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            strategy_name TEXT NOT NULL,
            performance_data TEXT NOT NULL,
            win_rate REAL DEFAULT 0.0,
            profit_factor REAL DEFAULT 0.0,
            last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Create system_state table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS system_state (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            component TEXT NOT NULL UNIQUE,
            last_run_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            status TEXT DEFAULT 'active',
            metadata TEXT
        )
    ''')
    
    # Create ai_memories table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS ai_memories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            memory_type TEXT NOT NULL,
            content TEXT NOT NULL,
            ai_component TEXT,
            importance REAL DEFAULT 1.0
        )
    ''')
    
    # Create positions table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS positions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            symbol TEXT NOT NULL,
            side TEXT NOT NULL,
            size REAL NOT NULL,
            entry_price REAL NOT NULL,
            current_price REAL,
            pnl REAL DEFAULT 0,
            status TEXT DEFAULT 'open'
        )
    ''')
    
    # Commit changes
    conn.commit()
    
    # Verify tables were created
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    print(f"Created {len(tables)} tables:")
    for table in tables:
        print(f"  - {table[0]}")
    
    # Insert initial system state
    cursor.execute('''
        INSERT OR REPLACE INTO system_state (component, last_run_timestamp, status)
        VALUES ('memory_manager', ?, 'active')
    ''', (datetime.now(),))
    
    cursor.execute('''
        INSERT OR REPLACE INTO system_state (component, last_run_timestamp, status)
        VALUES ('trading_system', ?, 'active')
    ''', (datetime.now(),))
    
    conn.commit()
    conn.close()
    
    print("SUCCESS: All database tables created successfully!")
    print("Database is ready for live trading operations")

if __name__ == "__main__":
    create_all_tables()
