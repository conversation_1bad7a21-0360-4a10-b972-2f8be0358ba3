# PowerShell Build Script for Motorola Moto G32
# Optimized for your specific device and development environment

Write-Host "🔧 Building Autonomous Trading App for Motorola Moto G32..." -ForegroundColor Green
Write-Host "📱 Device: Motorola Moto G32 (XT2235-2)" -ForegroundColor Cyan
Write-Host "🖥️  PC: MSI Cyborg 15 A12VF" -ForegroundColor Cyan

# Check if we're in the right directory
if (!(Test-Path "package.json")) {
    Write-Host "❌ Error: package.json not found. Please run from mobile directory." -ForegroundColor Red
    exit 1
}

# Check if Android SDK is available
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
if (!(Test-Path $env:ANDROID_HOME)) {
    Write-Host "⚠️  Android SDK not found at $env:ANDROID_HOME" -ForegroundColor Yellow
    Write-Host "Please install Android Studio first or set ANDROID_HOME manually" -ForegroundColor Yellow
}

Write-Host "🧹 Cleaning previous builds..." -ForegroundColor Yellow
if (Test-Path "android\app\build") {
    Remove-Item -Recurse -Force "android\app\build"
}

Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow
npm install

Write-Host "🔧 Setting up Metro bundler..." -ForegroundColor Yellow
Start-Process -NoNewWindow -FilePath "npx" -ArgumentList "react-native", "start", "--reset-cache" -PassThru

# Wait a moment for Metro to start
Start-Sleep -Seconds 3

Write-Host "🏗️  Building APK for Motorola Moto G32..." -ForegroundColor Green

# Change to Android directory
Set-Location "android"

# Clean and build
Write-Host "🧹 Cleaning Android project..." -ForegroundColor Yellow
.\gradlew clean

Write-Host "🔨 Building release APK..." -ForegroundColor Yellow
.\gradlew assembleRelease

# Go back to mobile directory
Set-Location ".."

# Check if APK was built successfully
$apkPath = "android\app\build\outputs\apk\release\app-release.apk"
if (Test-Path $apkPath) {
    $apkSize = (Get-Item $apkPath).Length / 1MB
    Write-Host "✅ APK built successfully!" -ForegroundColor Green
    Write-Host "📱 APK Location: $apkPath" -ForegroundColor Cyan
    Write-Host "📏 APK Size: $([math]::Round($apkSize, 2)) MB" -ForegroundColor Cyan
    
    # Copy APK to easy access location
    $outputDir = "..\moto-g32-builds"
    if (!(Test-Path $outputDir)) {
        New-Item -ItemType Directory -Path $outputDir
    }
    
    $timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"
    $outputApk = "$outputDir\AutonomousTrader_MotoG32_$timestamp.apk"
    Copy-Item $apkPath $outputApk
    
    Write-Host "📋 APK copied to: $outputApk" -ForegroundColor Green
    Write-Host ""
    Write-Host "🚀 NEXT STEPS FOR YOUR MOTOROLA MOTO G32:" -ForegroundColor Yellow
    Write-Host "1. Enable 'Developer Options' on your Moto G32:" -ForegroundColor White
    Write-Host "   - Go to Settings > About phone" -ForegroundColor Gray
    Write-Host "   - Tap 'Build number' 7 times" -ForegroundColor Gray
    Write-Host "2. Enable 'USB Debugging' in Developer Options" -ForegroundColor White
    Write-Host "3. Enable 'Install unknown apps' for your file manager" -ForegroundColor White
    Write-Host "4. Transfer APK to phone and install" -ForegroundColor White
    Write-Host ""
    Write-Host "🔗 OR install directly via USB:" -ForegroundColor Yellow
    Write-Host "   adb install `"$outputApk`"" -ForegroundColor Gray
    
} else {
    Write-Host "❌ APK build failed!" -ForegroundColor Red
    Write-Host "Check the build logs above for errors." -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🎉 Build process completed!" -ForegroundColor Green
Write-Host "📱 Your Motorola Moto G32 trading app is ready!" -ForegroundColor Green
