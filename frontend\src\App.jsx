import { CssBaseline } from '@mui/material'
import { ThemeProvider, createTheme } from '@mui/material/styles'
import { useEffect, useState } from 'react'
import { Toaster } from 'react-hot-toast'
import { QueryClient, QueryClientProvider } from 'react-query'
import { Route, BrowserRouter as Router, Routes } from 'react-router-dom'

// Components
import LoadingScreen from './components/LoadingScreen/LoadingScreen'
import ResponsiveLayout from './components/layout/ResponsiveLayout'
import ErrorBoundary from './components/common/ErrorBoundary'
import AIStatus from './pages/AIStatus/AIStatus'
import Analytics from './pages/Analytics/Analytics'
import Dashboard from './pages/Dashboard/Dashboard'
import Portfolio from './pages/Portfolio/Portfolio'
import Settings from './pages/Settings/Settings'
import Trading from './pages/Trading/Trading'

// Services
import { systemStatusService } from './services/api'
import websocketService from './services/websocket'

// Create React Query client
const queryClient = new QueryClient({
    defaultOptions: {
        queries: {
            refetchOnWindowFocus: false,
            retry: 1,
            staleTime: 30000,
        },
    },
})

// Create dark theme
const darkTheme = createTheme({
    palette: {
        mode: 'dark',
        primary: {
            main: '#00ff88',
            dark: '#00cc6a',
            light: '#33ff9a',
        },
        secondary: {
            main: '#ff4757',
        },
        background: {
            default: '#0a0a0a',
            paper: 'rgba(255, 255, 255, 0.05)',
        },
        text: {
            primary: '#ffffff',
            secondary: '#b3b3b3',
        },
    },
    typography: {
        fontFamily: "'Inter', sans-serif",
        h1: {
            fontWeight: 700,
        },
        h2: {
            fontWeight: 600,
        },
        h3: {
            fontWeight: 600,
        },
        h4: {
            fontWeight: 600,
        },
        h5: {
            fontWeight: 500,
        },
        h6: {
            fontWeight: 500,
        },
    },
    components: {
        MuiButton: {
            styleOverrides: {
                root: {
                    textTransform: 'none',
                    borderRadius: 8,
                },
            },
        },
        MuiCard: {
            styleOverrides: {
                root: {
                    background: 'rgba(255, 255, 255, 0.05)',
                    backdropFilter: 'blur(10px)',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    borderRadius: 12,
                },
            },
        },
    },
})

function App() {
    const [isLoading, setIsLoading] = useState(true)
    const [systemStatus, setSystemStatus] = useState(null)

    useEffect(() => {
        const initializeApp = async () => {
            try {
                // Check system status
                const status = await systemStatusService.getStatus()
                setSystemStatus(status)

                // Simulate loading time for smooth UX
                setTimeout(() => {
                    setIsLoading(false)
                }, 2000)
            } catch (error) {
                console.error('Failed to initialize app:', error)
                // Still show the app even if API is down
                setTimeout(() => {
                    setIsLoading(false)
                }, 3000)
            }
        }

        initializeApp()
    }, [])

    useEffect(() => {
        // WebSocket service is already initialized and connected
        // when imported (see websocket.js)
        return () => {
            // Cleanup WebSocket connection
            websocketService.disconnect()
        }
    }, [])

    if (isLoading) {
        return <LoadingScreen />
    }

    return (
        <QueryClientProvider client={queryClient}>
            <ThemeProvider theme={darkTheme}>
                <CssBaseline />
                <Router>
                    <ErrorBoundary>
                        <ResponsiveLayout systemStatus={systemStatus}>
                            <Routes>
                                <Route path="/" element={<Dashboard />} />
                                <Route path="/trading" element={<Trading />} />
                                <Route path="/portfolio" element={<Portfolio />} />
                                <Route path="/analytics" element={<Analytics />} />
                                <Route path="/ai-status" element={<AIStatus />} />
                                <Route path="/settings" element={<Settings />} />
                            </Routes>
                        </ResponsiveLayout>
                    </ErrorBoundary>
                    <Toaster
                        position="top-right"
                        toastOptions={{
                            duration: 4000,
                            style: {
                                background: 'rgba(0, 0, 0, 0.8)',
                                color: '#fff',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                backdropFilter: 'blur(10px)',
                            },
                        }}
                    />
                </Router>
            </ThemeProvider>
        </QueryClientProvider>
    )
}

export default App
