import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import sqlite3
import json
from .adaptive_learning_engine import AdaptiveLearningEngine

class RealTimeLearningMonitor:
    """Real-time learning monitor that continuously adapts trading parameters"""
    
    def __init__(self, db_path: str = "bybit_trading_bot.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self.learning_engine = AdaptiveLearningEngine(db_path)
        self.monitoring_active = False
        self.adaptation_interval = 30  # Check every 30 seconds
        self.last_adaptation_time = datetime.now()
        
        # Critical safety parameters
        self.emergency_stop_active = False
        self.max_consecutive_losses = 3
        self.max_daily_loss_eur = 5.0
        self.min_account_balance = 50.0  # Minimum account balance in EUR

        # PROFIT MAXIMIZATION PARAMETERS
        self.daily_profit_target = 15.0  # Target 15 EUR daily profit
        self.profit_acceleration_threshold = 5.0  # Accelerate after 5 EUR profit
        self.hyper_profit_threshold = 10.0  # Enter hyper mode after 10 EUR profit
        self.profit_mode_active = False
        
    async def start_monitoring(self):
        """Start real-time learning and adaptation monitoring"""
        self.monitoring_active = True
        self.logger.info("LEARNING MONITOR: Real-time learning monitor started")
        
        while self.monitoring_active:
            try:
                # Check for new trades to learn from
                await self._check_new_trades()
                
                # Analyze current performance
                await self._analyze_current_performance()
                
                # Check emergency conditions
                await self._check_emergency_conditions()
                
                # Adapt parameters if needed
                await self._adapt_parameters()
                
                # Wait before next check
                await asyncio.sleep(self.adaptation_interval)
                
            except Exception as e:
                self.logger.error(f"Error in learning monitor: {e}")
                await asyncio.sleep(5)  # Short wait on error
    
    async def stop_monitoring(self):
        """Stop the learning monitor"""
        self.monitoring_active = False
        self.logger.info("LEARNING MONITOR: Real-time learning monitor stopped")
    
    async def _check_new_trades(self):
        """Check for new trades and learn from them"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get trades from last 5 minutes
            cursor.execute('''
                SELECT symbol, side, quantity, profit_loss, timestamp, execution_time
                FROM trades 
                WHERE timestamp > datetime('now', '-5 minutes')
                AND profit_loss IS NOT NULL
                ORDER BY timestamp DESC
            ''')
            
            recent_trades = cursor.fetchall()
            conn.close()
            
            # Learn from each new trade
            for trade in recent_trades:
                trade_data = {
                    'symbol': trade[0],
                    'side': trade[1],
                    'quantity': trade[2],
                    'profit_loss': trade[3],
                    'timestamp': trade[4],
                    'execution_time': trade[5] or 0
                }
                
                # Learn from this trade
                learning_result = await self.learning_engine.learn_from_trade(trade_data)
                
                if learning_result.get('learned'):
                    adaptations = learning_result.get('adaptations', {})
                    if adaptations:
                        self.logger.info(f"LEARNING: Adapted parameters after trade - {adaptations}")
                
                # Check for emergency stop
                if learning_result.get('emergency_stop_triggered'):
                    self.emergency_stop_active = True
                    self.logger.critical("EMERGENCY STOP: Daily loss limit exceeded - halting all trading")

                # PROFIT MAXIMIZATION: Check for profit acceleration opportunities
                daily_pnl = learning_result.get('daily_pnl', 0)
                if daily_pnl > self.profit_acceleration_threshold and not self.profit_mode_active:
                    self.profit_mode_active = True
                    self.logger.info(f"PROFIT ACCELERATION: Daily profit {daily_pnl} EUR - entering profit mode")

                    # Force more aggressive parameters
                    self.learning_engine.position_size_multiplier = min(2.0, self.learning_engine.position_size_multiplier * 1.5)
                    self.learning_engine.risk_tolerance = min(0.03, self.learning_engine.risk_tolerance * 1.3)

                # HYPER PROFIT MODE: Maximum aggression when highly profitable
                if daily_pnl > self.hyper_profit_threshold:
                    self.logger.info(f"HYPER PROFIT MODE: Daily profit {daily_pnl} EUR - maximum aggression")

                    # Maximum profit parameters
                    self.learning_engine.position_size_multiplier = min(5.0, self.learning_engine.position_size_multiplier * 2.0)
                    self.learning_engine.risk_tolerance = min(0.05, self.learning_engine.risk_tolerance * 1.5)
                    self.learning_engine.max_position_size = min(1.0, self.learning_engine.max_position_size * 2.0)
                    
        except Exception as e:
            self.logger.error(f"Error checking new trades: {e}")
    
    async def _analyze_current_performance(self):
        """Analyze current performance and adapt if needed"""
        try:
            # Run performance pattern analysis every 10 minutes
            if (datetime.now() - self.last_adaptation_time).total_seconds() > 600:
                analysis = await self.learning_engine.analyze_performance_patterns()
                
                if 'adaptations' in analysis and analysis['adaptations']:
                    self.logger.info(f"PERFORMANCE ANALYSIS: {analysis}")
                    self.last_adaptation_time = datetime.now()
                    
        except Exception as e:
            self.logger.error(f"Error analyzing performance: {e}")
    
    async def _check_emergency_conditions(self):
        """Check for emergency conditions that require immediate action"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check recent losses
            cursor.execute('''
                SELECT COUNT(*) FROM trades 
                WHERE timestamp > datetime('now', '-1 hour')
                AND profit_loss < 0
            ''')
            recent_losses = cursor.fetchone()[0]
            
            # Check total daily PnL
            cursor.execute('''
                SELECT SUM(profit_loss) FROM trades 
                WHERE date(timestamp) = date('now')
                AND profit_loss IS NOT NULL
            ''')
            daily_pnl = cursor.fetchone()[0] or 0
            
            conn.close()
            
            # Emergency conditions
            emergency_triggered = False
            
            if recent_losses >= self.max_consecutive_losses:
                self.emergency_stop_active = True
                emergency_triggered = True
                self.logger.critical(f"EMERGENCY: {recent_losses} consecutive losses in last hour")
            
            if daily_pnl < -self.max_daily_loss_eur:
                self.emergency_stop_active = True
                emergency_triggered = True
                self.logger.critical(f"EMERGENCY: Daily loss {daily_pnl} EUR exceeds limit")
            
            if emergency_triggered:
                # Force ultra-conservative parameters
                self.learning_engine.position_size_multiplier = 0.001
                self.learning_engine.risk_tolerance = 0.001
                self.learning_engine.confidence_score = 0.01
                
                await self.learning_engine._store_adaptation(
                    "emergency_parameters", 1.0, 0.001, 
                    f"Emergency stop triggered - losses: {recent_losses}, daily PnL: {daily_pnl}"
                )
                
        except Exception as e:
            self.logger.error(f"Error checking emergency conditions: {e}")
    
    async def _adapt_parameters(self):
        """Adapt trading parameters based on learning and profit targets"""
        try:
            # Get current adapted parameters
            params = self.learning_engine.get_adapted_parameters()
            daily_pnl = params['daily_pnl']

            # Log current state with profit target progress
            profit_progress = (daily_pnl / self.daily_profit_target) * 100 if self.daily_profit_target > 0 else 0

            self.logger.info(f"PROFIT TARGET PROGRESS: {profit_progress:.1f}% ({daily_pnl:.2f}/{self.daily_profit_target:.2f} EUR)")
            self.logger.info(f"CURRENT PARAMETERS: "
                           f"Position Size: {params['position_size_multiplier']:.4f}, "
                           f"Risk: {params['risk_tolerance']:.4f}, "
                           f"Confidence: {params['confidence_score']:.4f}")

            # PROFIT MAXIMIZATION LOGIC
            if daily_pnl > 0:
                # Approaching daily target - increase aggression
                if daily_pnl >= self.daily_profit_target * 0.5:  # 50% of target reached
                    self.logger.info("PROFIT ACCELERATION: 50% of daily target reached - increasing aggression")

                    # Increase position size to reach target faster
                    old_multiplier = self.learning_engine.position_size_multiplier
                    self.learning_engine.position_size_multiplier = min(3.0, old_multiplier * 1.2)

                    await self.learning_engine._store_adaptation(
                        "profit_target_acceleration", old_multiplier, self.learning_engine.position_size_multiplier,
                        f"Accelerating to reach daily target: {daily_pnl:.2f}/{self.daily_profit_target:.2f} EUR"
                    )

                # Target reached - maintain aggressive stance
                if daily_pnl >= self.daily_profit_target:
                    self.logger.info(f"DAILY TARGET ACHIEVED: {daily_pnl:.2f} EUR - maintaining aggressive parameters")

                    # Keep aggressive parameters to maximize beyond target
                    self.learning_engine.position_size_multiplier = max(2.0, self.learning_engine.position_size_multiplier)
                    self.learning_engine.risk_tolerance = max(0.02, self.learning_engine.risk_tolerance)

            # Check if parameters are too conservative given recent performance
            if params['consecutive_losses'] >= 2:
                # Force more conservative parameters only if losing
                if self.learning_engine.position_size_multiplier > 0.01:
                    old_size = self.learning_engine.position_size_multiplier
                    self.learning_engine.position_size_multiplier = 0.01

                    await self.learning_engine._store_adaptation(
                        "forced_conservative", old_size, 0.01,
                        f"Forced conservative after {params['consecutive_losses']} losses"
                    )

            # Reset profit mode if daily PnL goes negative
            if daily_pnl < 0 and self.profit_mode_active:
                self.profit_mode_active = False
                self.logger.warning("PROFIT MODE DISABLED: Daily PnL turned negative")

        except Exception as e:
            self.logger.error(f"Error adapting parameters: {e}")
    
    def get_current_trading_parameters(self) -> Dict[str, Any]:
        """Get current trading parameters optimized for maximum profit"""
        params = self.learning_engine.get_adapted_parameters()
        daily_pnl = params['daily_pnl']

        # PROFIT MAXIMIZATION: Adjust max position size based on profitability
        if daily_pnl > self.hyper_profit_threshold:
            # Hyper profit mode - maximum position sizes
            max_position_size = min(1.0, params['max_position_size'])
        elif daily_pnl > self.profit_acceleration_threshold:
            # Profit acceleration mode - increased position sizes
            max_position_size = min(0.1, params['max_position_size'])
        elif daily_pnl > 0:
            # Profitable but conservative
            max_position_size = min(0.01, params['max_position_size'])
        else:
            # Losing - ultra conservative
            max_position_size = min(0.001, params['max_position_size'])

        return {
            "position_size_multiplier": params['position_size_multiplier'],
            "risk_tolerance": params['risk_tolerance'],
            "confidence_score": params['confidence_score'],
            "max_position_size": max_position_size,
            "emergency_stop_active": self.emergency_stop_active,
            "market_regime": params['market_regime'],
            "daily_pnl": daily_pnl,
            "consecutive_losses": params['consecutive_losses'],
            "trading_allowed": not self.emergency_stop_active and daily_pnl > -self.max_daily_loss_eur,
            "profit_mode_active": self.profit_mode_active,
            "daily_profit_target": self.daily_profit_target,
            "profit_target_progress": (daily_pnl / self.daily_profit_target) * 100 if self.daily_profit_target > 0 else 0
        }
    
    async def force_emergency_stop(self, reason: str):
        """Force emergency stop with reason"""
        self.emergency_stop_active = True
        self.learning_engine.position_size_multiplier = 0.0001
        self.learning_engine.risk_tolerance = 0.0001
        
        await self.learning_engine._store_adaptation(
            "forced_emergency_stop", 1.0, 0.0001, f"Manual emergency stop: {reason}"
        )
        
        self.logger.critical(f"FORCED EMERGENCY STOP: {reason}")
    
    async def reset_emergency_stop(self):
        """Reset emergency stop (use with caution)"""
        self.emergency_stop_active = False
        self.learning_engine.position_size_multiplier = 0.01  # Still very conservative
        self.learning_engine.risk_tolerance = 0.005
        
        self.logger.warning("Emergency stop reset - trading resumed with conservative parameters")
    
    def is_trading_allowed(self) -> bool:
        """Check if trading is currently allowed"""
        params = self.learning_engine.get_adapted_parameters()
        return (not self.emergency_stop_active and 
                params['daily_pnl'] > -self.max_daily_loss_eur and
                params['consecutive_losses'] < self.max_consecutive_losses)
