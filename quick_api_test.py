#!/usr/bin/env python3
"""
Quick API Test - Verify Bybit API connectivity and signature
"""
import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from bybit_bot.core.config import BotConfig
from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient

async def test_api_connection():
    """Test API connection and authentication"""
    
    print("BYBIT API CONNECTION TEST")
    print("=" * 30)
    
    try:
        # Load configuration
        config = BotConfig()
        print(f"✓ Configuration loaded")
        
        # Check API keys
        api_key = getattr(config, 'bybit_api_key', '') or config.api_keys.bybit.get('api_key', '')
        api_secret = getattr(config, 'bybit_api_secret', '') or config.api_keys.bybit.get('api_secret', '')
        
        if not api_key or not api_secret:
            print("❌ API keys not found in configuration")
            return False
        
        print(f"✓ API Key found: {api_key[:8]}...")
        print(f"✓ API Secret found: {api_secret[:8]}...")
        
        # Initialize client
        client = EnhancedBybitClient(config)
        await client.initialize()
        print("✓ Client initialized")
        
        # Test public endpoint (no authentication)
        print("\n--- Testing Public Endpoint ---")
        market_data = await client.get_current_price("BTCUSDT")
        if market_data > 0:
            print(f"✓ BTC/USDT Price: ${market_data:,.2f}")
        else:
            print("❌ Failed to get market data")
            return False
        
        # Test private endpoint (requires authentication)
        print("\n--- Testing Private Endpoint ---")
        try:
            balance = await client.get_account_balance()
            if balance:
                print("✓ Account balance retrieved successfully")
                print(f"Balance data keys: {list(balance.keys())}")
            else:
                print("⚠️  Empty balance response (might indicate API permission issues)")
        except Exception as e:
            print(f"❌ Authentication failed: {e}")
            if "10004" in str(e):
                print("   This is a signature error - check API keys and signature generation")
            return False
        
        # Test market data with different parameters
        print("\n--- Testing Enhanced Market Data ---")
        try:
            enhanced_data = await client.get_market_data("BTCUSDT", timeframe="5", limit=10)
            if enhanced_data:
                print(f"✓ Enhanced market data retrieved: {len(enhanced_data)} candles")
            else:
                print("⚠️  No enhanced market data received")
        except Exception as e:
            print(f"❌ Enhanced market data failed: {e}")
            return False
        
        await client.close()
        print("\n🎉 ALL API TESTS PASSED!")
        return True
        
    except Exception as e:
        print(f"\n❌ API TEST FAILED: {e}")
        return False

if __name__ == "__main__":
    print("Quick API Connection Test for Bybit Bot")
    success = asyncio.run(test_api_connection())
    
    if success:
        print("\n✅ API connection is working properly!")
        print("You can now run the main bot with confidence.")
    else:
        print("\n⚠️  API connection has issues that need to be resolved.")
        print("Check your API keys in config.yaml")
    
    print("\nTo run the main bot:")
    print("python main.py")
