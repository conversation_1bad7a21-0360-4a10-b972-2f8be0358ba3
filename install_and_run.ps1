# INSTALL DEPENDENCIES AND START REAL TRADING
Write-Host "INSTALLING REQUIRED DEPENDENCIES FOR REAL TRADING" -ForegroundColor Green

# Set working directory
Set-Location "E:\The_real_deal_copy\Bybit_Bot\BOT"

# Install critical dependencies
Write-Host "Installing python-dotenv..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" -m pip install python-dotenv

Write-Host "Installing pybit..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" -m pip install pybit

Write-Host "Installing aiohttp..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" -m pip install aiohttp

Write-Host "Installing asyncio..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" -m pip install asyncio

Write-Host "Installing loguru..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" -m pip install loguru

Write-Host "Installing pydantic..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" -m pip install pydantic

Write-Host "Installing PyYAML..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" -m pip install PyYAML

Write-Host "Installing redis..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" -m pip install redis

Write-Host "Installing sqlalchemy..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" -m pip install sqlalchemy

Write-Host "Installing pandas..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" -m pip install pandas

Write-Host "Installing numpy..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" -m pip install numpy

Write-Host "DEPENDENCIES INSTALLED - STARTING REAL TRADING SYSTEM" -ForegroundColor Green

# Set Python path
$env:PYTHONPATH = "E:\The_real_deal_copy\Bybit_Bot\BOT"

# Start the real trading system
Write-Host "STARTING REAL TRADING SYSTEM - NO FAKE DATA!" -ForegroundColor Red
& "E:\conda\Miniconda3\python.exe" main.py

Write-Host "Trading system stopped." -ForegroundColor Yellow
Read-Host "Press Enter to exit"
