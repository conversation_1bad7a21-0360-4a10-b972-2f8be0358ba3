#!/usr/bin/env python3
"""
Quick Status Check for Bybit Trading Bot
Checks if the 'bids' KeyError issue has been resolved
"""

import sys
from pathlib import Path

def quick_status_check():
    """Quick check of system status"""
    print("QUICK STATUS CHECK - BYBIT V5 API INTEGRATION")
    print("=" * 50)
    
    try:
        # Check log file
        log_file = Path("logs/bybit_trading_bot.log")
        if not log_file.exists():
            print("ERROR: Log file not found")
            return False
        
        # Read log file
        with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        lines = content.split('\n')
        print(f"Log file has {len(lines)} total lines")
        
        # Check for 'bids' KeyError exceptions
        bids_errors = [line for line in lines if "'bids'" in line and "Error getting ultra-fast price data" in line]
        print(f"Total 'bids' KeyError count: {len(bids_errors)}")
        
        # Check recent activity (last 100 lines)
        recent_lines = lines[-100:] if len(lines) >= 100 else lines
        recent_bids_errors = [line for line in recent_lines if "'bids'" in line and "Error getting ultra-fast price data" in line]
        print(f"Recent 'bids' KeyError count (last 100 lines): {len(recent_bids_errors)}")
        
        # Check for rate limiting
        rate_limit_errors = [line for line in recent_lines if "Access too frequent" in line]
        print(f"Recent rate limit errors: {len(rate_limit_errors)}")
        
        # Show last few log entries
        print(f"\nLast 5 log entries:")
        for line in recent_lines[-5:]:
            if line.strip():
                print(f"  {line.strip()}")
        
        # Check rate limiter status
        try:
            sys.path.insert(0, str(Path(__file__).parent))
            from bybit_bot.utils.global_rate_limiter import rate_limiter
            print(f"\nRate Limiter Status:")
            print(f"  - Requests per second: {rate_limiter.config.requests_per_second}")
            print(f"  - Emergency mode: {rate_limiter.emergency_mode}")
            print(f"  - Consecutive errors: {rate_limiter.consecutive_errors}")
        except Exception as e:
            print(f"  - Could not check rate limiter: {e}")
        
        # Determine status
        print(f"\n" + "=" * 50)
        if len(recent_bids_errors) == 0:
            print(f"SUCCESS: No recent 'bids' KeyError exceptions!")
            print(f"BYBIT V5 API INTEGRATION FIX IS WORKING!")
            
            if len(rate_limit_errors) > 0:
                print(f"INFO: Rate limiting detected - this is normal")
                print(f"System is using backup data sources when needed")
            
            print(f"SYSTEM STATUS: OPERATIONAL")
            return True
        else:
            print(f"WARNING: Still finding recent 'bids' KeyError exceptions")
            print(f"SYSTEM STATUS: NEEDS ADDITIONAL FIXES")
            
            # Show recent errors
            print(f"\nRecent 'bids' errors:")
            for error in recent_bids_errors[-3:]:
                print(f"  {error.strip()}")
            
            return False
            
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = quick_status_check()
    sys.exit(0 if success else 1)
