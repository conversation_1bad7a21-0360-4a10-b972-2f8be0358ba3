import {
    CheckCircle,
    Cloud,
    Error,
    Psychology,
    Security,
    Storage,
    Warning,
} from '@mui/icons-material'
import {
    Box,
    Card,
    CardContent,
    Chip,
    LinearProgress,
    List,
    ListItem,
    ListItemIcon,
    ListItemText,
    Typography,
} from '@mui/material'
import { motion } from 'framer-motion'

const SystemHealth = ({ systemStatus, aiStatus, realtimeHealth, isConnected }) => {
    // Use real-time health data if available, otherwise fall back to static metrics
    const baseHealthMetrics = [
        {
            name: 'Trading Engine',
            status: realtimeHealth?.trading === 'active' ? 'healthy' : 'warning',
            value: realtimeHealth?.trading === 'active' ? 98.5 : 75.0,
            icon: <CheckCircle />,
            color: realtimeHealth?.trading === 'active' ? '#00ff88' : '#ffa726',
        },
        {
            name: 'AI Systems',
            status: aiStatus?.status === 'running' ? 'healthy' : 'warning',
            value: aiStatus?.confidence || 94.2,
            icon: <Psychology />,
            color: aiStatus?.status === 'running' ? '#00ff88' : '#ffa726',
        },
        {
            name: 'Database',
            status: 'healthy',
            value: 99.1,
            icon: <Storage />,
            color: '#00ff88',
        },
        {
            name: 'API Connectivity',
            status: isConnected ? 'healthy' : 'error',
            value: isConnected ? 95.3 : 0,
            icon: <Cloud />,
            color: isConnected ? '#00ff88' : '#ff4757',
        },
        {
            name: 'Security',
            status: 'healthy',
            value: 100,
            icon: <Security />,
            color: '#00ff88',
        },
    ]

    const healthMetrics = baseHealthMetrics

    const getStatusIcon = (status) => {
        switch (status) {
            case 'healthy':
                return <CheckCircle sx={{ color: '#00ff88' }} />
            case 'warning':
                return <Warning sx={{ color: '#ffa726' }} />
            case 'error':
                return <Error sx={{ color: '#ff4757' }} />
            default:
                return <CheckCircle sx={{ color: '#666' }} />
        }
    }

    const getStatusColor = (status) => {
        switch (status) {
            case 'healthy':
                return '#00ff88'
            case 'warning':
                return '#ffa726'
            case 'error':
                return '#ff4757'
            default:
                return '#666'
        }
    }

    const overallHealth = healthMetrics.reduce((acc, metric) => acc + metric.value, 0) / healthMetrics.length

    return (
        <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
        >
            <Card
                sx={{
                    background: 'rgba(255, 255, 255, 0.03)',
                    backdropFilter: 'blur(20px)',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    borderRadius: 3,
                    height: '400px',
                }}
            >
                <CardContent sx={{ height: '100%', p: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                        <Typography
                            variant="h6"
                            sx={{
                                fontWeight: 600,
                                color: '#ffffff',
                            }}
                        >
                            System Health
                        </Typography>

                        <Chip
                            label={`${overallHealth.toFixed(1)}% Healthy`}
                            sx={{
                                backgroundColor: 'rgba(0, 255, 136, 0.1)',
                                color: '#00ff88',
                                border: '1px solid rgba(0, 255, 136, 0.3)',
                                fontSize: '0.875rem',
                            }}
                        />
                    </Box>

                    {/* Overall Health Progress */}
                    <Box sx={{ mb: 3 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                            <Typography variant="body2" sx={{ color: '#b3b3b3' }}>
                                Overall System Health
                            </Typography>
                            <Typography variant="body2" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                {overallHealth.toFixed(1)}%
                            </Typography>
                        </Box>
                        <LinearProgress
                            variant="determinate"
                            value={overallHealth}
                            sx={{
                                height: 8,
                                borderRadius: 4,
                                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                                '& .MuiLinearProgress-bar': {
                                    background: `linear-gradient(90deg, ${overallHealth > 90 ? '#00ff88' : overallHealth > 70 ? '#ffa726' : '#ff4757'
                                        }, ${overallHealth > 90 ? '#00cc6a' : overallHealth > 70 ? '#ff9800' : '#e53e3e'
                                        })`,
                                    borderRadius: 4,
                                },
                            }}
                        />
                    </Box>

                    {/* Health Metrics List */}
                    <List sx={{ py: 0, overflow: 'auto', maxHeight: 'calc(100% - 120px)' }}>
                        {healthMetrics.map((metric, index) => (
                            <motion.div
                                key={metric.name}
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.4, delay: index * 0.1 }}
                            >
                                <ListItem
                                    sx={{
                                        px: 0,
                                        py: 1,
                                        borderBottom: index < healthMetrics.length - 1 ? '1px solid rgba(255, 255, 255, 0.05)' : 'none',
                                    }}
                                >
                                    <ListItemIcon sx={{ minWidth: 40 }}>
                                        {getStatusIcon(metric.status)}
                                    </ListItemIcon>
                                    <ListItemText
                                        primary={
                                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                <Typography variant="body2" sx={{ color: '#ffffff', fontWeight: 500 }}>
                                                    {metric.name}
                                                </Typography>
                                                <Typography
                                                    variant="caption"
                                                    sx={{
                                                        color: getStatusColor(metric.status),
                                                        fontWeight: 600,
                                                    }}
                                                >
                                                    {metric.value}%
                                                </Typography>
                                            </Box>
                                        }
                                        secondary={
                                            <LinearProgress
                                                variant="determinate"
                                                value={metric.value}
                                                sx={{
                                                    mt: 0.5,
                                                    height: 4,
                                                    borderRadius: 2,
                                                    backgroundColor: 'rgba(255, 255, 255, 0.05)',
                                                    '& .MuiLinearProgress-bar': {
                                                        backgroundColor: metric.color,
                                                        borderRadius: 2,
                                                    },
                                                }}
                                            />
                                        }
                                    />
                                </ListItem>
                            </motion.div>
                        ))}
                    </List>

                    {/* System Status Summary */}
                    <Box
                        sx={{
                            mt: 2,
                            p: 2,
                            background: 'rgba(0, 255, 136, 0.05)',
                            border: '1px solid rgba(0, 255, 136, 0.1)',
                            borderRadius: 2,
                        }}
                    >
                        <Typography variant="caption" sx={{ color: '#00ff88', fontWeight: 600 }}>
                            System Status: {isConnected && systemStatus?.status === 'running' ? 'Fully Operational' : 'Offline/Initializing'}
                        </Typography>
                        <Typography variant="caption" sx={{ color: '#b3b3b3', display: 'block', mt: 0.5 }}>
                            {isConnected ? 'All core systems are functioning within normal parameters' : 'System connectivity issues detected'}
                        </Typography>
                    </Box>
                </CardContent>
            </Card>
        </motion.div>
    )
}

export default SystemHealth
