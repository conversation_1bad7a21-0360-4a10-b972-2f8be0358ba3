import {
    AccountBalance,
    Assessment,
    Download,
    <PERSON><PERSON>hart,
    Refresh,
    SwapH<PERSON>z,
    TrendingDown,
    TrendingUp
} from '@mui/icons-material'
import {
    Avatar,
    Box,
    Button,
    Card,
    CardContent,
    Chip,
    Container,
    Grid,
    IconButton,
    LinearProgress,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Typography
} from '@mui/material'
import { motion } from 'framer-motion'
import { useState } from 'react'
import { Area, AreaChart, CartesianGrid, Cell, PieChart as Recharts<PERSON>ie<PERSON>hart, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts'
import { useRealTimeData } from '../../hooks/useRealTimeData'
import toast from 'react-hot-toast'

const Portfolio = () => {
    const [timeframe, setTimeframe] = useState('24h')
    const [anchorEl, setAnchorEl] = useState(null)

    // Real-time data hook
    const {
        balance,
        profitData,
        performanceMetrics,
        isConnected
    } = useRealTimeData()

    // Use real-time data or fallback to mock data
    const portfolioData = {
        totalBalance: balance?.total || 125420.50,
        totalValue: balance?.total || 128734.67,
        dailyPnL: performanceMetrics?.dailyPnL || 2847.32,
        dailyPnLPercent: performanceMetrics?.dailyPnLPercent || 2.34,
        availableBalance: balance?.available || 45678.90,
        marginUsed: balance?.reserved || 79755.77,
        marginLevel: 162.3,
    }

    const assets = [
        {
            symbol: 'USDT',
            name: 'Tether',
            balance: 45678.90,
            value: 45678.90,
            percentage: 35.4,
            change24h: 0.02,
            color: '#26a69a',
        },
        {
            symbol: 'BTC',
            name: 'Bitcoin',
            balance: 1.2456,
            value: 52748.32,
            percentage: 40.9,
            change24h: 3.45,
            color: '#f7931a',
        },
        {
            symbol: 'ETH',
            name: 'Ethereum',
            balance: 8.4523,
            value: 21478.65,
            percentage: 16.7,
            change24h: -1.23,
            color: '#627eea',
        },
        {
            symbol: 'BNB',
            name: 'Binance Coin',
            balance: 28.567,
            value: 9023.45,
            percentage: 7.0,
            change24h: 2.87,
            color: '#f3ba2f',
        },
    ]

    const portfolioHistory = [
        { time: '00:00', value: 122450 },
        { time: '04:00', value: 123120 },
        { time: '08:00', value: 124890 },
        { time: '12:00', value: 126780 },
        { time: '16:00', value: 125430 },
        { time: '20:00', value: 128734 },
    ]

    const recentTransactions = [
        {
            type: 'trade',
            symbol: 'BTCUSDT',
            side: 'buy',
            amount: 0.0145,
            price: 42350.50,
            total: 614.08,
            timestamp: '2024-01-15 14:30:25',
            status: 'completed',
        },
        {
            type: 'trade',
            symbol: 'ETHUSDT',
            side: 'sell',
            amount: 0.823,
            price: 2543.20,
            total: 2093.05,
            timestamp: '2024-01-15 13:45:12',
            status: 'completed',
        },
        {
            type: 'deposit',
            symbol: 'USDT',
            amount: 5000,
            timestamp: '2024-01-15 10:20:08',
            status: 'completed',
        },
        {
            type: 'trade',
            symbol: 'ADAUSDT',
            side: 'buy',
            amount: 2847.5,
            price: 0.4523,
            total: 1288.31,
            timestamp: '2024-01-15 09:15:33',
            status: 'completed',
        },
    ]

    const COLORS = ['#00ff88', '#42a5f5', '#ffa726', '#8e24aa', '#ff4757']

    const pieData = assets.map((asset, index) => ({
        name: asset.symbol,
        value: asset.percentage,
        color: COLORS[index % COLORS.length],
    }))

    const getTransactionColor = (type, side) => {
        if (type === 'deposit') return '#00ff88'
        if (type === 'withdrawal') return '#ff4757'
        if (side === 'buy') return '#42a5f5'
        if (side === 'sell') return '#ffa726'
        return '#666'
    }

    const getTransactionIcon = (type, side) => {
        if (type === 'deposit') return '💰'
        if (type === 'withdrawal') return '💸'
        if (side === 'buy') return '📈'
        if (side === 'sell') return '📉'
        return '🔄'
    }

    return (
        <Container maxWidth="xl" sx={{ py: 3 }}>
            <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
            >
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
                    <Typography
                        variant="h4"
                        sx={{
                            fontWeight: 700,
                            color: '#ffffff',
                            background: 'linear-gradient(45deg, #00ff88, #00ccff)',
                            WebkitBackgroundClip: 'text',
                            WebkitTextFillColor: 'transparent',
                        }}
                    >
                        Portfolio Overview
                    </Typography>

                    <Box sx={{ display: 'flex', gap: 2 }}>
                        <Button
                            variant="outlined"
                            startIcon={<Download />}
                            sx={{
                                borderColor: '#42a5f5',
                                color: '#42a5f5',
                                '&:hover': {
                                    borderColor: '#42a5f5',
                                    backgroundColor: 'rgba(66, 165, 245, 0.1)',
                                },
                            }}
                        >
                            Export Report
                        </Button>

                        <IconButton
                            sx={{
                                background: 'rgba(255, 255, 255, 0.05)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                color: '#00ff88',
                                '&:hover': {
                                    background: 'rgba(0, 255, 136, 0.1)',
                                    border: '1px solid rgba(0, 255, 136, 0.3)',
                                },
                            }}
                        >
                            <Refresh />
                        </IconButton>
                    </Box>
                </Box>
            </motion.div>

            <Grid container spacing={3}>
                {/* Portfolio Summary Cards */}
                <Grid item xs={12} md={3}>
                    <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.6, delay: 0.1 }}
                    >
                        <Card
                            sx={{
                                background: 'rgba(255, 255, 255, 0.03)',
                                backdropFilter: 'blur(20px)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: 3,
                            }}
                        >
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                                    <AccountBalance sx={{ color: '#00ff88' }} />
                                    <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                        Total Value
                                    </Typography>
                                </Box>
                                <Typography variant="h3" sx={{ color: '#00ff88', fontWeight: 700, mb: 1 }}>
                                    ${portfolioData.totalValue.toLocaleString()}
                                </Typography>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                    <TrendingUp sx={{ color: '#00ff88', fontSize: 16 }} />
                                    <Typography variant="body2" sx={{ color: '#00ff88' }}>
                                        +${portfolioData.dailyPnL.toFixed(2)} ({portfolioData.dailyPnLPercent}%)
                                    </Typography>
                                </Box>
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>

                <Grid item xs={12} md={3}>
                    <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.6, delay: 0.2 }}
                    >
                        <Card
                            sx={{
                                background: 'rgba(255, 255, 255, 0.03)',
                                backdropFilter: 'blur(20px)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: 3,
                            }}
                        >
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                                    <SwapHoriz sx={{ color: '#42a5f5' }} />
                                    <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                        Available
                                    </Typography>
                                </Box>
                                <Typography variant="h3" sx={{ color: '#42a5f5', fontWeight: 700, mb: 1 }}>
                                    ${portfolioData.availableBalance.toLocaleString()}
                                </Typography>
                                <Typography variant="body2" sx={{ color: '#b3b3b3' }}>
                                    Ready for trading
                                </Typography>
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>

                <Grid item xs={12} md={3}>
                    <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.6, delay: 0.3 }}
                    >
                        <Card
                            sx={{
                                background: 'rgba(255, 255, 255, 0.03)',
                                backdropFilter: 'blur(20px)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: 3,
                            }}
                        >
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                                    <Assessment sx={{ color: '#ffa726' }} />
                                    <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                        Margin Used
                                    </Typography>
                                </Box>
                                <Typography variant="h3" sx={{ color: '#ffa726', fontWeight: 700, mb: 1 }}>
                                    ${portfolioData.marginUsed.toLocaleString()}
                                </Typography>
                                <Typography variant="body2" sx={{ color: '#b3b3b3' }}>
                                    In active positions
                                </Typography>
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>

                <Grid item xs={12} md={3}>
                    <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.6, delay: 0.4 }}
                    >
                        <Card
                            sx={{
                                background: 'rgba(255, 255, 255, 0.03)',
                                backdropFilter: 'blur(20px)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: 3,
                            }}
                        >
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                                    <PieChart sx={{ color: '#8e24aa' }} />
                                    <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                        Margin Level
                                    </Typography>
                                </Box>
                                <Typography variant="h3" sx={{ color: '#8e24aa', fontWeight: 700, mb: 1 }}>
                                    {portfolioData.marginLevel}%
                                </Typography>
                                <Typography variant="body2" sx={{ color: '#b3b3b3' }}>
                                    Safety ratio
                                </Typography>
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>

                {/* Portfolio Chart */}
                <Grid item xs={12} lg={8}>
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.5 }}
                    >
                        <Card
                            sx={{
                                background: 'rgba(255, 255, 255, 0.03)',
                                backdropFilter: 'blur(20px)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: 3,
                                height: '400px',
                            }}
                        >
                            <CardContent sx={{ height: '100%' }}>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                                    <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                        Portfolio Performance
                                    </Typography>
                                    <Box sx={{ display: 'flex', gap: 1 }}>
                                        {['24h', '7d', '30d'].map((period) => (
                                            <Button
                                                key={period}
                                                size="small"
                                                variant={timeframe === period ? 'contained' : 'outlined'}
                                                onClick={() => setTimeframe(period)}
                                                sx={{
                                                    minWidth: '50px',
                                                    backgroundColor: timeframe === period ? '#00ff88' : 'transparent',
                                                    borderColor: '#00ff88',
                                                    color: timeframe === period ? '#000000' : '#00ff88',
                                                    '&:hover': {
                                                        backgroundColor: timeframe === period ? '#00ff88' : 'rgba(0, 255, 136, 0.1)',
                                                        borderColor: '#00ff88',
                                                    },
                                                }}
                                            >
                                                {period}
                                            </Button>
                                        ))}
                                    </Box>
                                </Box>

                                <ResponsiveContainer width="100%" height="85%">
                                    <AreaChart data={portfolioHistory}>
                                        <defs>
                                            <linearGradient id="colorValue" x1="0" y1="0" x2="0" y2="1">
                                                <stop offset="5%" stopColor="#00ff88" stopOpacity={0.3} />
                                                <stop offset="95%" stopColor="#00ff88" stopOpacity={0} />
                                            </linearGradient>
                                        </defs>
                                        <CartesianGrid strokeDasharray="3 3" stroke="rgba(255, 255, 255, 0.1)" />
                                        <XAxis dataKey="time" stroke="#b3b3b3" />
                                        <YAxis stroke="#b3b3b3" />
                                        <Tooltip
                                            contentStyle={{
                                                backgroundColor: 'rgba(20, 20, 20, 0.9)',
                                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                                borderRadius: '8px',
                                                color: '#ffffff',
                                            }}
                                        />
                                        <Area
                                            type="monotone"
                                            dataKey="value"
                                            stroke="#00ff88"
                                            strokeWidth={2}
                                            fillOpacity={1}
                                            fill="url(#colorValue)"
                                        />
                                    </AreaChart>
                                </ResponsiveContainer>
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>

                {/* Asset Allocation */}
                <Grid item xs={12} lg={4}>
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.6 }}
                    >
                        <Card
                            sx={{
                                background: 'rgba(255, 255, 255, 0.03)',
                                backdropFilter: 'blur(20px)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: 3,
                                height: '400px',
                            }}
                        >
                            <CardContent sx={{ height: '100%' }}>
                                <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600, mb: 3 }}>
                                    Asset Allocation
                                </Typography>

                                <Box sx={{ height: '200px', mb: 3 }}>
                                    <ResponsiveContainer width="100%" height="100%">
                                        <RechartsPieChart>
                                            <pie
                                                data={pieData}
                                                cx="50%"
                                                cy="50%"
                                                innerRadius={40}
                                                outerRadius={80}
                                                paddingAngle={5}
                                                dataKey="value"
                                            >
                                                {pieData.map((entry, index) => (
                                                    <Cell key={`cell-${index}`} fill={entry.color} />
                                                ))}
                                            </pie>
                                            <Tooltip
                                                contentStyle={{
                                                    backgroundColor: 'rgba(20, 20, 20, 0.9)',
                                                    border: '1px solid rgba(255, 255, 255, 0.1)',
                                                    borderRadius: '8px',
                                                    color: '#ffffff',
                                                }}
                                            />
                                        </RechartsPieChart>
                                    </ResponsiveContainer>
                                </Box>

                                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                                    {assets.map((asset, index) => (
                                        <Box
                                            key={asset.symbol}
                                            sx={{
                                                display: 'flex',
                                                justifyContent: 'space-between',
                                                alignItems: 'center',
                                                p: 1,
                                                borderRadius: 1,
                                                background: 'rgba(255, 255, 255, 0.02)',
                                            }}
                                        >
                                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                <Box
                                                    sx={{
                                                        width: 8,
                                                        height: 8,
                                                        borderRadius: '50%',
                                                        backgroundColor: COLORS[index % COLORS.length],
                                                    }}
                                                />
                                                <Typography variant="body2" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                                    {asset.symbol}
                                                </Typography>
                                            </Box>
                                            <Typography variant="body2" sx={{ color: '#b3b3b3' }}>
                                                {asset.percentage}%
                                            </Typography>
                                        </Box>
                                    ))}
                                </Box>
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>

                {/* Asset Holdings */}
                <Grid item xs={12} lg={8}>
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.7 }}
                    >
                        <Card
                            sx={{
                                background: 'rgba(255, 255, 255, 0.03)',
                                backdropFilter: 'blur(20px)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: 3,
                            }}
                        >
                            <CardContent>
                                <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600, mb: 3 }}>
                                    Asset Holdings
                                </Typography>

                                <TableContainer>
                                    <Table>
                                        <TableHead>
                                            <TableRow>
                                                <TableCell sx={{ color: '#b3b3b3', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
                                                    Asset
                                                </TableCell>
                                                <TableCell sx={{ color: '#b3b3b3', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
                                                    Balance
                                                </TableCell>
                                                <TableCell sx={{ color: '#b3b3b3', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
                                                    Value (USD)
                                                </TableCell>
                                                <TableCell sx={{ color: '#b3b3b3', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
                                                    Allocation
                                                </TableCell>
                                                <TableCell sx={{ color: '#b3b3b3', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
                                                    24h Change
                                                </TableCell>
                                            </TableRow>
                                        </TableHead>
                                        <TableBody>
                                            {assets.map((asset, index) => (
                                                <TableRow key={asset.symbol}>
                                                    <TableCell sx={{ borderBottom: '1px solid rgba(255, 255, 255, 0.05)' }}>
                                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                                            <Avatar
                                                                sx={{
                                                                    width: 32,
                                                                    height: 32,
                                                                    backgroundColor: `${COLORS[index % COLORS.length]}20`,
                                                                    color: COLORS[index % COLORS.length],
                                                                    fontSize: '0.8rem',
                                                                    fontWeight: 600,
                                                                }}
                                                            >
                                                                {asset.symbol.slice(0, 2)}
                                                            </Avatar>
                                                            <Box>
                                                                <Typography variant="body2" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                                                    {asset.symbol}
                                                                </Typography>
                                                                <Typography variant="caption" sx={{ color: '#b3b3b3' }}>
                                                                    {asset.name}
                                                                </Typography>
                                                            </Box>
                                                        </Box>
                                                    </TableCell>
                                                    <TableCell sx={{ color: '#ffffff', borderBottom: '1px solid rgba(255, 255, 255, 0.05)' }}>
                                                        {asset.balance.toLocaleString()}
                                                    </TableCell>
                                                    <TableCell sx={{ color: '#ffffff', borderBottom: '1px solid rgba(255, 255, 255, 0.05)' }}>
                                                        ${asset.value.toLocaleString()}
                                                    </TableCell>
                                                    <TableCell sx={{ borderBottom: '1px solid rgba(255, 255, 255, 0.05)' }}>
                                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                            <LinearProgress
                                                                variant="determinate"
                                                                value={asset.percentage}
                                                                sx={{
                                                                    width: 60,
                                                                    height: 6,
                                                                    borderRadius: 3,
                                                                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                                                                    '& .MuiLinearProgress-bar': {
                                                                        backgroundColor: COLORS[index % COLORS.length],
                                                                        borderRadius: 3,
                                                                    },
                                                                }}
                                                            />
                                                            <Typography variant="body2" sx={{ color: '#ffffff' }}>
                                                                {asset.percentage}%
                                                            </Typography>
                                                        </Box>
                                                    </TableCell>
                                                    <TableCell sx={{ borderBottom: '1px solid rgba(255, 255, 255, 0.05)' }}>
                                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                                            {asset.change24h > 0 ? (
                                                                <TrendingUp sx={{ color: '#00ff88', fontSize: 16 }} />
                                                            ) : (
                                                                <TrendingDown sx={{ color: '#ff4757', fontSize: 16 }} />
                                                            )}
                                                            <Typography
                                                                variant="body2"
                                                                sx={{
                                                                    color: asset.change24h > 0 ? '#00ff88' : '#ff4757',
                                                                    fontWeight: 600,
                                                                }}
                                                            >
                                                                {asset.change24h > 0 ? '+' : ''}{asset.change24h}%
                                                            </Typography>
                                                        </Box>
                                                    </TableCell>
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                    </Table>
                                </TableContainer>
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>

                {/* Recent Transactions */}
                <Grid item xs={12} lg={4}>
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.8 }}
                    >
                        <Card
                            sx={{
                                background: 'rgba(255, 255, 255, 0.03)',
                                backdropFilter: 'blur(20px)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: 3,
                                height: '400px',
                            }}
                        >
                            <CardContent sx={{ height: '100%' }}>
                                <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600, mb: 3 }}>
                                    Recent Transactions
                                </Typography>

                                <Box sx={{ overflow: 'auto', maxHeight: 'calc(100% - 60px)' }}>
                                    {recentTransactions.map((transaction, index) => (
                                        <Box
                                            key={index}
                                            sx={{
                                                p: 2,
                                                mb: 2,
                                                background: 'rgba(255, 255, 255, 0.02)',
                                                borderRadius: 2,
                                                border: '1px solid rgba(255, 255, 255, 0.05)',
                                            }}
                                        >
                                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                    <Typography sx={{ fontSize: '1.2rem' }}>
                                                        {getTransactionIcon(transaction.type, transaction.side)}
                                                    </Typography>
                                                    <Typography variant="body2" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                                        {transaction.symbol}
                                                    </Typography>
                                                    {transaction.side && (
                                                        <Chip
                                                            label={transaction.side.toUpperCase()}
                                                            size="small"
                                                            sx={{
                                                                backgroundColor: `${getTransactionColor(transaction.type, transaction.side)}20`,
                                                                color: getTransactionColor(transaction.type, transaction.side),
                                                                fontSize: '0.7rem',
                                                                height: 18,
                                                            }}
                                                        />
                                                    )}
                                                </Box>
                                                <Chip
                                                    label={transaction.status}
                                                    size="small"
                                                    sx={{
                                                        backgroundColor: '#00ff8820',
                                                        color: '#00ff88',
                                                        fontSize: '0.65rem',
                                                        height: 16,
                                                    }}
                                                />
                                            </Box>

                                            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                                <Typography variant="caption" sx={{ color: '#b3b3b3' }}>
                                                    Amount: {transaction.amount}
                                                </Typography>
                                                {transaction.price && (
                                                    <Typography variant="caption" sx={{ color: '#b3b3b3' }}>
                                                        Price: ${transaction.price.toLocaleString()}
                                                    </Typography>
                                                )}
                                            </Box>

                                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                <Typography variant="caption" sx={{ color: '#666' }}>
                                                    {new Date(transaction.timestamp).toLocaleTimeString()}
                                                </Typography>
                                                {transaction.total && (
                                                    <Typography variant="body2" sx={{ color: '#00ff88', fontWeight: 600 }}>
                                                        ${transaction.total.toFixed(2)}
                                                    </Typography>
                                                )}
                                            </Box>
                                        </Box>
                                    ))}
                                </Box>
                            </CardContent>
                        </Card>
                    </motion.div>
                </Grid>
            </Grid>
        </Container>
    )
}

export default Portfolio
