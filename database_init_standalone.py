"""
Standalone Database Setup Script for Super-GPT Trading Bot
Creates all required tables using SQLite (compatible with the main system)
"""
import sys
import logging
import sqlite3
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("DatabaseInit")

def create_database_schema():
    """Create the basic database schema using SQLite"""
    try:
        # Use SQLite database path consistent with main system
        db_path = Path("data/bybit_trading_bot.db")
        db_path.parent.mkdir(parents=True, exist_ok=True)

        # Connect to SQLite database
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # Create basic tables
        tables_sql = """
        -- Trading data tables (SQLite Compatible)
        CREATE TABLE IF NOT EXISTS trades (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol VARCHAR(20) NOT NULL,
            side VARCHAR(10) NOT NULL,
            size REAL NOT NULL,
            price REAL NOT NULL,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            strategy VARCHAR(50),
            profit_loss REAL,
            fees REAL
        );
        
        -- System health and errors
        CREATE TABLE IF NOT EXISTS system_health (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            cpu_usage REAL,
            memory_usage REAL,
            disk_usage REAL,
            error_rate REAL,
            component_status TEXT
        );
        
        CREATE TABLE IF NOT EXISTS error_events (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            event_id VARCHAR(100) UNIQUE NOT NULL,
            component VARCHAR(50) NOT NULL,
            error_type VARCHAR(100) NOT NULL,
            error_message TEXT,
            severity VARCHAR(20) NOT NULL,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            stack_trace TEXT,
            system_state TEXT,
            recovery_attempts INTEGER DEFAULT 0,
            resolved BOOLEAN DEFAULT FALSE
        );
        
        -- Configuration and settings
        CREATE TABLE IF NOT EXISTS bot_config (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            config_key VARCHAR(100) UNIQUE NOT NULL,
            config_value TEXT,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Performance metrics
        CREATE TABLE IF NOT EXISTS performance_metrics (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            total_trades INTEGER,
            profit_loss REAL,
            win_rate REAL,
            sharpe_ratio REAL,
            max_drawdown REAL,
            strategy_performance TEXT
        );
        """
        
        # Execute table creation (SQLite compatible - one statement at a time)
        # Split the SQL into individual statements and execute them separately
        statements = [stmt.strip() for stmt in tables_sql.split(';') if stmt.strip()]
        for statement in statements:
            try:
                cursor.execute(statement)
                logger.info(f"Executed: {statement[:50]}...")
            except Exception as e:
                logger.error(f"Failed to execute statement: {e}")
                continue

        # Create indexes (one at a time for SQLite compatibility)
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_trades_symbol ON trades(symbol)",
            "CREATE INDEX IF NOT EXISTS idx_trades_timestamp ON trades(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_error_events_timestamp ON error_events(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_error_events_component ON error_events(component)",
            "CREATE INDEX IF NOT EXISTS idx_system_health_timestamp ON system_health(timestamp)"
        ]
        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
            except Exception as e:
                logger.warning(f"Failed to create index: {e}")
        
        # Insert initial configuration (SQLite compatible)
        initial_configs = [
            ("system_initialized", '{"value": true, "timestamp": "2024-01-01T00:00:00"}'),
            ("auto_healing_enabled", '{"value": true}'),
            ("risk_limits", '{"max_position_size": 1000, "max_daily_loss": 500}')
        ]

        for config_key, config_value in initial_configs:
            try:
                cursor.execute("INSERT OR IGNORE INTO bot_config (config_key, config_value) VALUES (?, ?)",
                             (config_key, config_value))
                logger.info(f"Inserted config: {config_key}")
            except Exception as e:
                logger.error(f"Failed to insert config {config_key}: {e}")

        # Commit changes and close connection
        conn.commit()
        cursor.close()
        conn.close()

        logger.info("SUCCESS: Database schema created successfully!")
        return True

    except Exception as e:
        logger.error(f"ERROR: Database initialization failed: {e}")
        return False

if __name__ == "__main__":
    logger.info("STARTING database initialization...")
    success = create_database_schema()
    if success:
        logger.info("SUCCESS: Database initialization completed successfully!")
        sys.exit(0)
    else:
        logger.error("ERROR: Database initialization failed!")
        sys.exit(1)
