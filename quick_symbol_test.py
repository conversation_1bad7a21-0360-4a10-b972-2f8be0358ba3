#!/usr/bin/env python3
"""
Quick Symbol Test - Test the fixed symbol handling
"""

import asyncio
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from bybit_bot.exchange.bybit_client import BybitClient

async def test_symbols():
    print("=== QUICK SYMBOL TEST ===")
    
    # Create a simple client
    client = BybitClient(
        api_key="WbQDRvmESPfUGgXQEj",
        api_secret="vdvi3Q34C7m65rHuzFw3I9kbGeyGr4oMFUga",
        testnet=False
    )
    
    try:
        print("Testing BTCUSDT spot market data...")
        market_data = await client.get_market_data("BTCUSDT", "1", 5)
        if market_data:
            print(f"SUCCESS: Got {len(market_data)} candles for BTCUSDT")
            print(f"Latest close: {market_data[0]['close']}")
        else:
            print("FAILED: No market data")
            
        print("\nTesting ETHUSDT spot market data...")
        market_data = await client.get_market_data("ETHUSDT", "1", 5)
        if market_data:
            print(f"SUCCESS: Got {len(market_data)} candles for ETHUSDT")
            print(f"Latest close: {market_data[0]['close']}")
        else:
            print("FAILED: No market data")
            
        print("\nTesting klines directly...")
        klines = await client.get_klines("spot", "BTCUSDT", "1", 5)
        if klines.get('retCode') == 0:
            print("SUCCESS: Direct klines call working")
            if klines['result']['list']:
                print(f"Latest price: {klines['result']['list'][0][4]}")
        else:
            print(f"FAILED: {klines}")
            
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await client.close()

if __name__ == "__main__":
    asyncio.run(test_symbols())
