# IDE and Editor Files
.idea/
.vscode/settings.json
.vscode/launch.json
*.swp
*.swo
*~

# Environment and Configuration Files
**/.env
**/.env.*
.env
.env.*
.env.local
.env.development
.env.test
.env.production
.env.backup
config.yaml
config_trading.yaml
config_template.yaml
*.yaml
*.yml
!docker-compose*.yml
!docker-compose*.yaml
!.github/workflows/*.yml

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
*.pyc
*.pyo
*.pyd
.env
pip-log.txt
pip-delete-this-directory.txt
.tox/
.nox/
.coverage
.coverage.*
.cache
.pytest_cache/
cover/
*.cover
*.py,cover
.hypothesis/

# Virtual Environments
**/.venv/
.venv/
venv/
ENV/
env/
.env/
**agent_dictvenv

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
gui/node_modules/
gui/.next/

# Database Files
*.db
*.sqlite
*.sqlite3
database/
data/
backups/
*.sql.backup

# Logs and Debug Files
*.log
logs/
debug.log
error.log
access.log
**/*.log

# OS Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary Files
*.tmp
*.temp
workspace/output/
workspace/input/
workspace/logs/

# Runtime Files
celerybeat-schedule
celerybeat.pid
*.pid

# Security and Secrets
*.key
*.pem
*.p12
*.pfx
secrets/
keys/
certs/
certificates/

# Tool and Build Files
../bfg-report*
superagi/tools/marketplace_tools/
superagi/tools/external_tools/
tests/unit_tests/resource_manager/test_path
/tools.json
.coverage
htmlcov/

# Deployment Files
deployment/
docker-data/

# AI and ML Model Files
models/
*.model
*.pkl
*.joblib
*.h5

# Large Data Files
*.csv
*.json
!package.json
!requirements.txt
*.zip
*.tar.gz
*.7z

# API Keys and Configuration Templates
*api_key*
*secret*
*token*
*password*