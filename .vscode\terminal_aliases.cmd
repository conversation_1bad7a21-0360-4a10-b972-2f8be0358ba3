@echo off
REM Terminal aliases for Windows CMD - Optimized for Bybit Bot
REM Using full paths to avoid conflicts and ensure proper execution

REM Python aliases with full paths
doskey py=E:\conda\miniconda3\envs\bybit-trader\python.exe $*
doskey python3=E:\conda\miniconda3\envs\bybit-trader\python.exe $*
doskey pip3=E:\conda\miniconda3\envs\bybit-trader\Scripts\pip.exe $*

REM Conda shortcuts with full paths
doskey activate=E:\conda\miniconda3\Scripts\conda.exe activate $*
doskey deactivate=E:\conda\miniconda3\Scripts\conda.exe deactivate
doskey condalist=E:\conda\miniconda3\Scripts\conda.exe list
doskey condainstall=E:\conda\miniconda3\Scripts\conda.exe install $*
doskey pipinstall=E:\conda\miniconda3\envs\bybit-trader\Scripts\pip.exe install $*
doskey pipreqs=E:\conda\miniconda3\envs\bybit-trader\Scripts\pip.exe install -r requirements.txt

REM Navigation shortcuts
doskey bot=cd /d E:\The_real_deal_copy\Bybit_Bot\BOT
doskey root=cd /d E:\The_real_deal_copy\Bybit_Bot\BOT
doskey scripts=cd /d E:\The_real_deal_copy\Bybit_Bot\BOT\scripts
doskey tests=cd /d E:\The_real_deal_copy\Bybit_Bot\BOT\tests

REM Python execution shortcuts with full paths
doskey main=E:\conda\miniconda3\envs\bybit-trader\python.exe main.py $*
doskey test=E:\conda\miniconda3\envs\bybit-trader\Scripts\pytest.exe -v $*
doskey testall=E:\conda\miniconda3\envs\bybit-trader\Scripts\pytest.exe -v
doskey lint=E:\conda\miniconda3\envs\bybit-trader\Scripts\flake8.exe . && E:\conda\miniconda3\envs\bybit-trader\Scripts\pylint.exe bybit_bot && E:\conda\miniconda3\envs\bybit-trader\Scripts\mypy.exe bybit_bot
doskey format=E:\conda\miniconda3\envs\bybit-trader\Scripts\black.exe . && E:\conda\miniconda3\envs\bybit-trader\Scripts\isort.exe .
doskey clean=for /r %%i in (*.pyc) do del "%%i" && for /d /r %%i in (__pycache__) do rmdir /s /q "%%i"

REM Node.js shortcuts (using working installation)
doskey node=E:\The_real_deal_copy\Bybit_Bot\BOT\temp_node_extract\node-v20.11.0-win-x64\node.exe $*
doskey npm=E:\The_real_deal_copy\Bybit_Bot\BOT\temp_node_extract\node-v20.11.0-win-x64\npm.cmd $*
doskey npx=E:\The_real_deal_copy\Bybit_Bot\BOT\temp_node_extract\node-v20.11.0-win-x64\npx.cmd $*

REM Git shortcuts
doskey gs=git status
doskey ga=git add $*
doskey gc=git commit -m $*
doskey gp=git push
doskey gl=git log --oneline -10
doskey gd=git diff $*

REM Combined commands with correct environment
doskey runbot=E:\conda\miniconda3\Scripts\conda.exe activate bybit-trader && E:\conda\miniconda3\envs\bybit-trader\python.exe main.py
doskey testbot=E:\conda\miniconda3\Scripts\conda.exe activate bybit-trader && E:\conda\miniconda3\envs\bybit-trader\Scripts\pytest.exe -v
doskey fixcode=E:\conda\miniconda3\Scripts\conda.exe activate bybit-trader && E:\conda\miniconda3\envs\bybit-trader\Scripts\black.exe . && E:\conda\miniconda3\envs\bybit-trader\Scripts\isort.exe . && E:\conda\miniconda3\envs\bybit-trader\Scripts\flake8.exe .
doskey checkcode=E:\conda\miniconda3\Scripts\conda.exe activate bybit-trader && E:\conda\miniconda3\envs\bybit-trader\Scripts\pylint.exe bybit_bot && E:\conda\miniconda3\envs\bybit-trader\Scripts\mypy.exe bybit_bot
doskey setupenv=E:\conda\miniconda3\Scripts\conda.exe activate bybit-trader && E:\conda\miniconda3\envs\bybit-trader\Scripts\pip.exe install -r requirements.txt

REM Quick environment activation
doskey botenv=E:\conda\miniconda3\Scripts\conda.exe activate bybit-trader

REM Help command
doskey help=echo Available aliases: py, python3, pip3, activate, deactivate, condalist, condainstall, pipinstall, pipreqs, bot, root, scripts, tests, main, test, testall, lint, format, clean, node, npm, npx, gs, ga, gc, gp, gl, gd, runbot, testbot, fixcode, checkcode, setupenv, botenv

echo Terminal aliases loaded successfully. Type 'help' for available commands.
echo Environment: bybit-trader ^| Python: E:\conda\miniconda3\envs\bybit-trader\python.exe
echo Node.js: E:\Program Files\nodejs\node.exe
