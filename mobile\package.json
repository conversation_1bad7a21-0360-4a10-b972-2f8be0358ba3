{"name": "bybit-trading-bot-mobile", "version": "1.0.0", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint .", "build:android": "cd android && ./gradlew assembleRelease", "build:android-bundle": "cd android && ./gradlew bundleRelease"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.21.0", "@react-native-community/netinfo": "^11.2.1", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@tanstack/react-query": "^5.17.1", "react": "18.2.0", "react-native": "0.73.2", "react-native-biometrics": "^3.0.1", "react-native-chart-kit": "^6.12.0", "react-native-encrypted-storage": "^4.0.3", "react-native-fast-image": "^8.6.3", "react-native-flash-message": "^0.4.2", "react-native-gesture-handler": "^2.14.1", "react-native-keychain": "^8.1.3", "react-native-linear-gradient": "^2.8.3", "react-native-paper": "^5.12.3", "react-native-push-notification": "^8.1.1", "react-native-reanimated": "^3.6.2", "react-native-safe-area-context": "^4.8.2", "react-native-screens": "^3.29.0", "react-native-svg": "^14.1.0", "react-native-vector-icons": "^10.0.3", "react-native-webview": "^13.6.4"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "^0.73.19", "@react-native/eslint-config": "^0.73.2", "@react-native/metro-config": "^0.73.3", "@react-native/typescript-config": "^0.73.1", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "metro-react-native-babel-preset": "0.77.0", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}