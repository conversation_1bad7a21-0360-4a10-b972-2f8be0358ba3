import { useState, useCallback, useEffect } from 'react'
import toast from 'react-hot-toast'
import { useUserPreferences } from './useLocalStorage'

/**
 * Enhanced notification system with categorization, persistence, and user preferences
 */
export const useNotifications = () => {
    const { getPreference } = useUserPreferences()
    const [notifications, setNotifications] = useState([])
    const [unreadCount, setUnreadCount] = useState(0)
    
    // Notification preferences
    const notificationPrefs = getPreference('notifications', {
        trades: true,
        alerts: true,
        system: true,
        sound: false
    })
    
    // Add notification
    const addNotification = useCallback((notification) => {
        const id = Date.now() + Math.random()
        const newNotification = {
            id,
            timestamp: new Date(),
            read: false,
            ...notification
        }
        
        setNotifications(prev => [newNotification, ...prev])
        setUnreadCount(prev => prev + 1)
        
        // Show toast based on preferences and type
        const shouldShow = notificationPrefs[notification.type] !== false
        if (shouldShow) {
            showToast(newNotification)
        }
        
        // Play sound if enabled
        if (notificationPrefs.sound && notification.priority === 'high') {
            playNotificationSound(notification.type)
        }
        
        return id
    }, [notificationPrefs])
    
    // Show toast notification
    const showToast = useCallback((notification) => {
        const toastOptions = {
            duration: getToastDuration(notification.priority),
            style: getToastStyle(notification.type),
            icon: getToastIcon(notification.type)
        }
        
        switch (notification.type) {
            case 'trade':
                toast.success(notification.message, toastOptions)
                break
            case 'alert':
                toast.error(notification.message, toastOptions)
                break
            case 'system':
                toast(notification.message, toastOptions)
                break
            case 'profit':
                toast.success(notification.message, {
                    ...toastOptions,
                    icon: '💰'
                })
                break
            case 'loss':
                toast.error(notification.message, {
                    ...toastOptions,
                    icon: '📉'
                })
                break
            default:
                toast(notification.message, toastOptions)
        }
    }, [])
    
    // Get toast duration based on priority
    const getToastDuration = (priority) => {
        switch (priority) {
            case 'high': return 8000
            case 'medium': return 5000
            case 'low': return 3000
            default: return 4000
        }
    }
    
    // Get toast styling based on type
    const getToastStyle = (type) => {
        const baseStyle = {
            background: 'rgba(0, 0, 0, 0.9)',
            color: '#fff',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            borderRadius: '8px',
            fontSize: '14px'
        }
        
        switch (type) {
            case 'trade':
                return { ...baseStyle, borderColor: 'rgba(0, 255, 136, 0.3)' }
            case 'alert':
                return { ...baseStyle, borderColor: 'rgba(255, 82, 82, 0.3)' }
            case 'profit':
                return { ...baseStyle, borderColor: 'rgba(0, 255, 136, 0.5)' }
            case 'loss':
                return { ...baseStyle, borderColor: 'rgba(255, 82, 82, 0.5)' }
            default:
                return baseStyle
        }
    }
    
    // Get toast icon based on type
    const getToastIcon = (type) => {
        switch (type) {
            case 'trade': return '📊'
            case 'alert': return '⚠️'
            case 'system': return '🔧'
            case 'profit': return '💰'
            case 'loss': return '📉'
            default: return '📢'
        }
    }
    
    // Play notification sound
    const playNotificationSound = useCallback((type) => {
        try {
            const audio = new Audio()
            switch (type) {
                case 'trade':
                    audio.src = '/sounds/trade.mp3'
                    break
                case 'alert':
                    audio.src = '/sounds/alert.mp3'
                    break
                case 'profit':
                    audio.src = '/sounds/profit.mp3'
                    break
                case 'loss':
                    audio.src = '/sounds/loss.mp3'
                    break
                default:
                    audio.src = '/sounds/notification.mp3'
            }
            audio.volume = 0.3
            audio.play().catch(() => {
                // Ignore audio play errors (user interaction required)
            })
        } catch (error) {
            console.warn('Could not play notification sound:', error)
        }
    }, [])
    
    // Mark notification as read
    const markAsRead = useCallback((id) => {
        setNotifications(prev => 
            prev.map(notification => 
                notification.id === id 
                    ? { ...notification, read: true }
                    : notification
            )
        )
        setUnreadCount(prev => Math.max(0, prev - 1))
    }, [])
    
    // Mark all as read
    const markAllAsRead = useCallback(() => {
        setNotifications(prev => 
            prev.map(notification => ({ ...notification, read: true }))
        )
        setUnreadCount(0)
    }, [])
    
    // Remove notification
    const removeNotification = useCallback((id) => {
        setNotifications(prev => {
            const notification = prev.find(n => n.id === id)
            if (notification && !notification.read) {
                setUnreadCount(count => Math.max(0, count - 1))
            }
            return prev.filter(n => n.id !== id)
        })
    }, [])
    
    // Clear all notifications
    const clearAll = useCallback(() => {
        setNotifications([])
        setUnreadCount(0)
    }, [])
    
    // Predefined notification creators
    const notifyTrade = useCallback((trade) => {
        return addNotification({
            type: 'trade',
            title: 'Trade Executed',
            message: `${trade.side.toUpperCase()} ${trade.quantity} ${trade.symbol} at $${trade.price}`,
            priority: 'medium',
            data: trade
        })
    }, [addNotification])
    
    const notifyProfit = useCallback((amount, percentage) => {
        return addNotification({
            type: 'profit',
            title: 'Profit Realized',
            message: `+$${amount.toFixed(2)} (${percentage.toFixed(2)}%)`,
            priority: 'high',
            data: { amount, percentage }
        })
    }, [addNotification])
    
    const notifyLoss = useCallback((amount, percentage) => {
        return addNotification({
            type: 'loss',
            title: 'Loss Realized',
            message: `-$${Math.abs(amount).toFixed(2)} (${percentage.toFixed(2)}%)`,
            priority: 'high',
            data: { amount, percentage }
        })
    }, [addNotification])
    
    const notifyAlert = useCallback((message, data = {}) => {
        return addNotification({
            type: 'alert',
            title: 'Alert',
            message,
            priority: 'high',
            data
        })
    }, [addNotification])
    
    const notifySystem = useCallback((message, priority = 'medium') => {
        return addNotification({
            type: 'system',
            title: 'System',
            message,
            priority,
            data: {}
        })
    }, [addNotification])
    
    // Auto-cleanup old notifications
    useEffect(() => {
        const cleanup = setInterval(() => {
            const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
            setNotifications(prev => 
                prev.filter(notification => 
                    new Date(notification.timestamp) > oneDayAgo
                )
            )
        }, 60 * 60 * 1000) // Check every hour
        
        return () => clearInterval(cleanup)
    }, [])
    
    return {
        notifications,
        unreadCount,
        addNotification,
        markAsRead,
        markAllAsRead,
        removeNotification,
        clearAll,
        
        // Predefined notification types
        notifyTrade,
        notifyProfit,
        notifyLoss,
        notifyAlert,
        notifySystem
    }
}

export default useNotifications
