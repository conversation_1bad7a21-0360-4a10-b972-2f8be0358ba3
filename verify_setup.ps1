# Comprehensive setup verification script for Bybit Bot
Write-Host "=== Bybit Bot Environment Verification ===" -ForegroundColor Cyan

# Check if we're in the correct directory
$expectedPath = "E:\The_real_deal_copy\Bybit_Bot\BOT"
$currentPath = (Get-Location).Path

Write-Host "`n1. Directory Check:" -ForegroundColor Yellow
if ($currentPath -eq $expectedPath) {
    Write-Host "   OK Correct directory: $currentPath" -ForegroundColor Green
} else {
    Write-Host "   ERROR Wrong directory. Expected: $expectedPath" -ForegroundColor Red
    Write-Host "   Current: $currentPath" -ForegroundColor Red
    Set-Location $expectedPath
    Write-Host "   CHANGED to correct directory" -ForegroundColor Yellow
}

# Check conda installation
Write-Host "`n2. Conda Installation Check:" -ForegroundColor Yellow
try {
    & "E:\conda\miniconda3\shell\condabin\conda-hook.ps1"
    $condaVersion = conda --version
    Write-Host "   OK Conda available: $condaVersion" -ForegroundColor Green
} catch {
    Write-Host "   ERROR Conda not available: $_" -ForegroundColor Red
    exit 1
}

# Check environment activation
Write-Host "`n3. Environment Activation Check:" -ForegroundColor Yellow
try {
    conda activate bybit-trader
    $envInfo = conda info --envs | Select-String "bybit-trader.*\*"
    if ($envInfo) {
        Write-Host "   OK bybit-trader environment activated" -ForegroundColor Green
    } else {
        Write-Host "   ERROR bybit-trader environment not active" -ForegroundColor Red
    }
} catch {
    Write-Host "   ERROR Failed to activate environment: $_" -ForegroundColor Red
}

# Check Python
Write-Host "`n4. Python Check:" -ForegroundColor Yellow
try {
    $pythonVersion = python --version
    $pythonPath = python -c "import sys; print(sys.executable)"
    Write-Host "   OK Python available: $pythonVersion" -ForegroundColor Green
    Write-Host "   OK Python path: $pythonPath" -ForegroundColor Green

    if ($pythonPath -like "*bybit-trader*") {
        Write-Host "   OK Using correct conda environment" -ForegroundColor Green
    } else {
        Write-Host "   ERROR Not using bybit-trader environment" -ForegroundColor Red
    }
} catch {
    Write-Host "   ERROR Python not available: $_" -ForegroundColor Red
}

# Check key packages
Write-Host "`n5. Package Check:" -ForegroundColor Yellow
$packages = @("pandas", "numpy", "websockets", "aiohttp")
foreach ($package in $packages) {
    try {
        $version = python -c "import $package; print($package.__version__)" 2>$null
        if ($version) {
            Write-Host "   OK $package : $version" -ForegroundColor Green
        } else {
            Write-Host "   ERROR $package : Not available" -ForegroundColor Red
        }
    } catch {
        Write-Host "   ERROR $package : Error checking" -ForegroundColor Red
    }
}

# Check main.py
Write-Host "`n6. Main Script Check:" -ForegroundColor Yellow
if (Test-Path "main.py") {
    Write-Host "   OK main.py exists" -ForegroundColor Green
} else {
    Write-Host "   ERROR main.py not found" -ForegroundColor Red
}

Write-Host "`n=== Verification Complete ===" -ForegroundColor Cyan
Write-Host "If all checks passed, you can run: python main.py" -ForegroundColor Green
