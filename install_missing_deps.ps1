# INSTALL ALL MISSING DEPENDENCIES FOR REAL TRADING
Write-Host "INSTALLING MISSING DEPENDENCIES FOR REAL TRADING SYSTEM" -ForegroundColor Green

# Install missing critical dependencies
Write-Host "Installing aiosqlite..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" -m pip install aiosqlite

Write-Host "Installing holidays..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" -m pip install holidays

Write-Host "Installing scipy..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" -m pip install scipy

Write-Host "Installing ccxt..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" -m pip install ccxt

Write-Host "Installing scikit-learn..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" -m pip install scikit-learn

Write-Host "Installing ta..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" -m pip install ta

Write-Host "Installing asyncpg..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" -m pip install asyncpg

Write-Host "Installing websockets..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" -m pip install websockets

Write-Host "Installing fastapi..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" -m pip install fastapi

Write-Host "Installing uvicorn..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" -m pip install uvicorn

Write-Host "ALL DEPENDENCIES INSTALLED - STARTING REAL TRADING SYSTEM" -ForegroundColor Green

# Set Python path
$env:PYTHONPATH = "E:\The_real_deal_copy\Bybit_Bot\BOT"

# Start the real trading system
Write-Host "STARTING REAL TRADING SYSTEM - ALL DEPENDENCIES RESOLVED!" -ForegroundColor Red
& "E:\conda\Miniconda3\python.exe" main.py

Write-Host "Trading system stopped." -ForegroundColor Yellow
Read-Host "Press Enter to exit"
