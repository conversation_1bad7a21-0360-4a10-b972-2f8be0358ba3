# EXEC SHELL FOR AUGMENT - INSTALLATION & USAGE GUIDE

## 🎯 **What is Exec Shell?**

Exec Shell is a Model Context Protocol (MCP) server that provides **secure shell command execution** capabilities for Augment. It allows Augment to run terminal commands in a controlled, safe environment.

## 🔧 **Installation**

### Method 1: Automatic Installation
```bash
# Run the installation script
./install_exec_shell.bat
```

### Method 2: Manual Installation
```bash
# Install the MCP server
npx -y shell-command-mcp

# Or install globally
npm install -g shell-command-mcp
```

## ⚙️ **Configuration**

The configuration has been **automatically added** to your `.vscode/settings.json`:

```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "shell-command",
        "command": "npx",
        "args": ["-y", "shell-command-mcp"],
        "env": {
          "ALLOWED_COMMANDS": "python,pip,conda,npm,node,git,ls,cat,echo,cd,pwd,mkdir,touch,rm,cp,mv,grep,find,which,where,dir,type,curl,wget,ping,netstat,ps,tasklist,kill,taskkill,systemctl,service,chmod,chown,whoami,date,uptime,df,du,free,top,htop,history,clear,cls,exit,logout,uname,hostname,ipconfig,ifconfig,env,set,export,alias,unalias,source,bash,sh,cmd,powershell,wsl"
        }
      }
    ]
  }
}
```

## 🛡️ **Security Features**

- **Allowlist System**: Only pre-approved commands can be executed
- **Environment Isolation**: Commands run in controlled environment
- **YAML Output**: Structured, safe output format
- **No Arbitrary Code**: Cannot execute arbitrary scripts or dangerous commands

## 📋 **Allowed Commands**

### **Development & Package Management**
- `python`, `pip`, `conda`
- `npm`, `node`, `git`

### **File System Operations**
- `ls`, `cat`, `echo`, `cd`, `pwd`
- `mkdir`, `touch`, `rm`, `cp`, `mv`
- `grep`, `find`, `which`, `where`

### **System Information**
- `whoami`, `date`, `uptime`
- `ps`, `tasklist`, `top`, `htop`
- `df`, `du`, `free`

### **Network & Connectivity**
- `curl`, `wget`, `ping`
- `netstat`, `ipconfig`, `ifconfig`

### **Terminal Control**
- `clear`, `cls`, `history`
- `bash`, `sh`, `cmd`, `powershell`

## 🚀 **How to Use with Augment**

1. **Restart VS Code/Augment** after installation
2. **Ask Augment to run commands**:
   ```
   "Can you check the Python version using the terminal?"
   "Please list the files in the current directory"
   "Run 'pip list' to show installed packages"
   "Check the Git status of this repository"
   ```

3. **Augment will automatically use the Exec Shell** to execute commands safely

## 💡 **Example Usage**

### Check System Status
```
@Augment: "Check the current working directory and list Python packages"
```

### Development Tasks
```
@Augment: "Run the bot's main.py file and show any errors"
@Augment: "Check Git status and show recent commits"
```

### System Monitoring
```
@Augment: "Show running processes related to Python"
@Augment: "Check network connectivity to api.bybit.com"
```

## 🔄 **Troubleshooting**

### MCP Server Not Found
1. Restart VS Code completely
2. Check that Node.js is installed: `node --version`
3. Reinstall: `npx -y shell-command-mcp`

### Command Not Allowed
- Check the `ALLOWED_COMMANDS` list in settings.json
- Add new commands to the allowlist if needed

### Permission Errors
- Ensure the terminal has proper permissions
- Run VS Code as administrator if needed (Windows)

## ⚠️ **Important Notes**

1. **Restart Required**: You must restart VS Code/Augment after installation
2. **Command Restrictions**: Only allowlisted commands will execute
3. **Working Directory**: Commands run from the current workspace directory
4. **Output Format**: Results are returned in YAML format for safety

## 🎁 **Benefits for Trading Bot Development**

- **Real-time System Monitoring**: Check bot status, processes, network
- **Package Management**: Install/update dependencies safely
- **Log Analysis**: Read log files, search for errors
- **Git Operations**: Commit changes, check repository status
- **Performance Monitoring**: Check CPU, memory, disk usage
- **Network Diagnostics**: Test API connectivity, ping endpoints

---

**🚨 After installation, RESTART VS CODE to activate the Exec Shell!**
