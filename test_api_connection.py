#!/usr/bin/env python3
"""
Test API Connection and Account Info Retrieval
"""

import asyncio
import os
import sys
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from bybit_bot.exchange.bybit_client import BybitClient
from bybit_bot.core.config import BotConfig

async def test_api_connection():
    """Test basic API connection and account info retrieval"""

    # Load environment variables
    load_dotenv()

    api_key = os.getenv('BYBIT_API_KEY')
    api_secret = os.getenv('BYBIT_API_SECRET')

    if not api_key or not api_secret:
        print("ERROR: API keys not found in environment variables")
        return False

    print(f"API Key: {api_key[:8]}...")
    print(f"API Secret: {'*' * len(api_secret)}")

    # Create config object
    config = BotConfig()

    # Initialize client
    client = BybitClient(config)
    
    try:
        # Initialize the client
        await client.initialize()
        print("SUCCESS: Client initialized")
        
        # Test account info
        print("\nTesting account info...")
        account_info = await client.get_account_info()
        print(f"Account info response: {account_info}")
        
        if account_info and 'result' in account_info:
            result = account_info['result']
            print(f"Total Equity: {result.get('totalEquity', 'N/A')}")
            print(f"Available Balance: {result.get('totalAvailableBalance', 'N/A')}")
            print(f"Initial Margin: {result.get('totalInitialMargin', 'N/A')}")
            print("SUCCESS: Account info retrieved successfully")
        else:
            print("ERROR: Invalid account info response")
            return False
        
        # Test wallet balance
        print("\nTesting wallet balance...")
        wallet_balance = await client.get_wallet_balance()
        print(f"Wallet balance response: {wallet_balance}")
        
        # Test positions
        print("\nTesting positions...")
        positions = await client.get_positions()
        print(f"Positions count: {len(positions) if positions else 0}")
        
        return True
        
    except Exception as e:
        print(f"ERROR: {e}")
        return False
    
    finally:
        await client.close()

if __name__ == "__main__":
    success = asyncio.run(test_api_connection())
    if success:
        print("\nALL TESTS PASSED - API CONNECTION WORKING")
    else:
        print("\nTESTS FAILED - API CONNECTION ISSUES")
    sys.exit(0 if success else 1)
