import React, { useMemo, useState } from 'react'
import {
    useReactTable,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    flexRender
} from '@tanstack/react-table'
import {
    Box,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    TablePagination,
    TextField,
    IconButton,
    Chip,
    Typography,
    Paper,
    Skeleton,
    Tooltip
} from '@mui/material'
import {
    ArrowUpward,
    ArrowDownward,
    FilterList,
    Search,
    Refresh
} from '@mui/icons-material'
import { motion, AnimatePresence } from 'framer-motion'
import { FixedSizeList as List } from 'react-window'
import { useResponsive } from '../../hooks/useResponsive'

/**
 * Advanced data table with sorting, filtering, pagination, and virtualization
 * Optimized for trading data with responsive design
 */
const DataTable = ({
    data = [],
    columns = [],
    loading = false,
    error = null,
    onRefresh,
    enableSorting = true,
    enableFiltering = true,
    enablePagination = true,
    enableVirtualization = false,
    pageSize: initialPageSize,
    searchPlaceholder = 'Search...',
    emptyMessage = 'No data available',
    height = 400,
    stickyHeader = true,
    onRowClick,
    rowClassName,
    ...props
}) => {
    const { isMobile, getTableConfig } = useResponsive()
    const tableConfig = getTableConfig()
    
    const [sorting, setSorting] = useState([])
    const [globalFilter, setGlobalFilter] = useState('')
    const [pagination, setPagination] = useState({
        pageIndex: 0,
        pageSize: initialPageSize || tableConfig.pageSize
    })
    
    // Memoize table data and columns
    const tableData = useMemo(() => data || [], [data])
    const tableColumns = useMemo(() => columns, [columns])
    
    // Create table instance
    const table = useReactTable({
        data: tableData,
        columns: tableColumns,
        state: {
            sorting,
            globalFilter,
            pagination: enablePagination ? pagination : undefined
        },
        onSortingChange: setSorting,
        onGlobalFilterChange: setGlobalFilter,
        onPaginationChange: setPagination,
        getCoreRowModel: getCoreRowModel(),
        getFilteredRowModel: enableFiltering ? getFilteredRowModel() : undefined,
        getSortedRowModel: enableSorting ? getSortedRowModel() : undefined,
        getPaginationRowModel: enablePagination ? getPaginationRowModel() : undefined,
        enableSorting,
        enableFiltering,
        manualPagination: false,
        debugTable: process.env.NODE_ENV === 'development'
    })
    
    // Loading skeleton
    const LoadingSkeleton = () => (
        <TableBody>
            {Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={index}>
                    {tableColumns.map((_, colIndex) => (
                        <TableCell key={colIndex}>
                            <Skeleton variant="text" />
                        </TableCell>
                    ))}
                </TableRow>
            ))}
        </TableBody>
    )
    
    // Error state
    if (error) {
        return (
            <Box
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    height: 200,
                    color: '#ff5252'
                }}
            >
                <Typography variant="h6" sx={{ mb: 1 }}>
                    Error loading data
                </Typography>
                <Typography variant="body2" sx={{ mb: 2, color: '#b3b3b3' }}>
                    {error.message || 'An unexpected error occurred'}
                </Typography>
                {onRefresh && (
                    <IconButton onClick={onRefresh} sx={{ color: '#00ff88' }}>
                        <Refresh />
                    </IconButton>
                )}
            </Box>
        )
    }
    
    // Virtualized row renderer
    const VirtualizedRow = ({ index, style }) => {
        const row = table.getRowModel().rows[index]
        if (!row) return null
        
        return (
            <div style={style}>
                <TableRow
                    hover
                    onClick={() => onRowClick?.(row.original)}
                    sx={{
                        cursor: onRowClick ? 'pointer' : 'default',
                        '&:hover': {
                            backgroundColor: 'rgba(255, 255, 255, 0.05)'
                        }
                    }}
                    className={rowClassName?.(row.original)}
                >
                    {row.getVisibleCells().map(cell => (
                        <TableCell
                            key={cell.id}
                            sx={{
                                borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                                color: '#fff'
                            }}
                        >
                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </TableCell>
                    ))}
                </TableRow>
            </div>
        )
    }
    
    return (
        <Paper
            sx={{
                background: 'rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                borderRadius: 2,
                overflow: 'hidden'
            }}
            {...props}
        >
            {/* Table Header with Search and Actions */}
            {(enableFiltering || onRefresh) && (
                <Box
                    sx={{
                        p: 2,
                        borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                        display: 'flex',
                        alignItems: 'center',
                        gap: 2,
                        flexWrap: 'wrap'
                    }}
                >
                    {enableFiltering && (
                        <TextField
                            size="small"
                            placeholder={searchPlaceholder}
                            value={globalFilter}
                            onChange={(e) => setGlobalFilter(e.target.value)}
                            InputProps={{
                                startAdornment: <Search sx={{ mr: 1, color: '#b3b3b3' }} />
                            }}
                            sx={{
                                minWidth: 200,
                                '& .MuiOutlinedInput-root': {
                                    backgroundColor: 'rgba(255, 255, 255, 0.05)',
                                    '& fieldset': {
                                        borderColor: 'rgba(255, 255, 255, 0.2)'
                                    },
                                    '&:hover fieldset': {
                                        borderColor: 'rgba(255, 255, 255, 0.3)'
                                    }
                                },
                                '& .MuiInputBase-input': {
                                    color: '#fff'
                                }
                            }}
                        />
                    )}
                    
                    <Box sx={{ flex: 1 }} />
                    
                    {/* Results count */}
                    <Chip
                        label={`${table.getFilteredRowModel().rows.length} results`}
                        size="small"
                        sx={{
                            backgroundColor: 'rgba(0, 255, 136, 0.1)',
                            color: '#00ff88',
                            border: '1px solid rgba(0, 255, 136, 0.3)'
                        }}
                    />
                    
                    {onRefresh && (
                        <Tooltip title="Refresh Data">
                            <IconButton
                                onClick={onRefresh}
                                sx={{ color: '#00ff88' }}
                            >
                                <Refresh />
                            </IconButton>
                        </Tooltip>
                    )}
                </Box>
            )}
            
            {/* Table Container */}
            <TableContainer sx={{ height: enableVirtualization ? height : 'auto' }}>
                <Table stickyHeader={stickyHeader} size={isMobile ? 'small' : 'medium'}>
                    {/* Table Head */}
                    <TableHead>
                        {table.getHeaderGroups().map(headerGroup => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map(header => (
                                    <TableCell
                                        key={header.id}
                                        sx={{
                                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                            borderBottom: '2px solid rgba(0, 255, 136, 0.3)',
                                            color: '#fff',
                                            fontWeight: 600,
                                            cursor: header.column.getCanSort() ? 'pointer' : 'default'
                                        }}
                                        onClick={header.column.getToggleSortingHandler()}
                                    >
                                        <Box
                                            sx={{
                                                display: 'flex',
                                                alignItems: 'center',
                                                gap: 1
                                            }}
                                        >
                                            {flexRender(
                                                header.column.columnDef.header,
                                                header.getContext()
                                            )}
                                            
                                            {header.column.getCanSort() && (
                                                <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                                                    {header.column.getIsSorted() === 'asc' && (
                                                        <ArrowUpward sx={{ fontSize: 16, color: '#00ff88' }} />
                                                    )}
                                                    {header.column.getIsSorted() === 'desc' && (
                                                        <ArrowDownward sx={{ fontSize: 16, color: '#00ff88' }} />
                                                    )}
                                                    {!header.column.getIsSorted() && (
                                                        <FilterList sx={{ fontSize: 16, color: '#666' }} />
                                                    )}
                                                </Box>
                                            )}
                                        </Box>
                                    </TableCell>
                                ))}
                            </TableRow>
                        ))}
                    </TableHead>
                    
                    {/* Table Body */}
                    {loading ? (
                        <LoadingSkeleton />
                    ) : enableVirtualization ? (
                        <TableBody>
                            <TableRow>
                                <TableCell
                                    colSpan={tableColumns.length}
                                    sx={{ p: 0, border: 'none' }}
                                >
                                    <List
                                        height={height - 100}
                                        itemCount={table.getRowModel().rows.length}
                                        itemSize={50}
                                        itemData={table.getRowModel().rows}
                                    >
                                        {VirtualizedRow}
                                    </List>
                                </TableCell>
                            </TableRow>
                        </TableBody>
                    ) : (
                        <TableBody>
                            <AnimatePresence>
                                {table.getRowModel().rows.map((row, index) => (
                                    <TableRow
                                        key={row.id}
                                        component={motion.tr}
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        exit={{ opacity: 0, y: -20 }}
                                        transition={{ duration: 0.2, delay: index * 0.05 }}
                                        hover
                                        onClick={() => onRowClick?.(row.original)}
                                        sx={{
                                            cursor: onRowClick ? 'pointer' : 'default',
                                            '&:hover': {
                                                backgroundColor: 'rgba(255, 255, 255, 0.05)'
                                            }
                                        }}
                                        className={rowClassName?.(row.original)}
                                    >
                                        {row.getVisibleCells().map(cell => (
                                            <TableCell
                                                key={cell.id}
                                                sx={{
                                                    borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                                                    color: '#fff'
                                                }}
                                            >
                                                {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                ))}
                            </AnimatePresence>
                            
                            {/* Empty state */}
                            {table.getRowModel().rows.length === 0 && (
                                <TableRow>
                                    <TableCell
                                        colSpan={tableColumns.length}
                                        sx={{
                                            textAlign: 'center',
                                            py: 4,
                                            color: '#b3b3b3'
                                        }}
                                    >
                                        <Typography variant="body1">
                                            {emptyMessage}
                                        </Typography>
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    )}
                </Table>
            </TableContainer>
            
            {/* Pagination */}
            {enablePagination && !enableVirtualization && (
                <TablePagination
                    component="div"
                    count={table.getFilteredRowModel().rows.length}
                    page={pagination.pageIndex}
                    onPageChange={(_, page) => setPagination(prev => ({ ...prev, pageIndex: page }))}
                    rowsPerPage={pagination.pageSize}
                    onRowsPerPageChange={(e) => setPagination(prev => ({ 
                        ...prev, 
                        pageSize: parseInt(e.target.value, 10),
                        pageIndex: 0
                    }))}
                    rowsPerPageOptions={[5, 10, 25, 50, 100]}
                    sx={{
                        borderTop: '1px solid rgba(255, 255, 255, 0.1)',
                        color: '#fff',
                        '& .MuiTablePagination-selectIcon': {
                            color: '#fff'
                        },
                        '& .MuiTablePagination-select': {
                            color: '#fff'
                        }
                    }}
                />
            )}
        </Paper>
    )
}

export default DataTable
