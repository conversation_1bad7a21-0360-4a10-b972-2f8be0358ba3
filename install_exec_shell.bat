@echo off
echo ================================================================
echo INSTALLING EXEC SHELL (Shell Command MCP Server) FOR AUGMENT
echo ================================================================

echo [STEP 1] Testing Node.js availability...
node --version
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found. Please install Node.js first.
    echo Download from: https://nodejs.org/
    pause
    exit /b 1
)

echo [STEP 2] Testing NPM availability...
npm --version
if %errorlevel% neq 0 (
    echo ERROR: NPM not found. Please install Node.js with NPM.
    pause
    exit /b 1
)

echo [STEP 3] Installing Shell Command MCP Server...
npx -y shell-command-mcp --version
if %errorlevel% neq 0 (
    echo Installing shell-command-mcp package...
    npm install -g shell-command-mcp
)

echo [STEP 4] Testing installation...
npx shell-command-mcp --version

echo ================================================================
echo INSTALLATION COMPLETE!
echo ================================================================
echo.
echo CONFIGURATION ALREADY ADDED TO:
echo   .vscode\settings.json
echo.
echo ALLOWED COMMANDS INCLUDE:
echo   - python, pip, conda
echo   - git, npm, node  
echo   - ls, cat, echo, cd, pwd
echo   - mkdir, touch, rm, cp, mv
echo   - grep, find, curl, wget
echo   - And many more system commands
echo.
echo RESTART VS CODE/AUGMENT TO ACTIVATE THE MCP SERVER
echo ================================================================
pause
