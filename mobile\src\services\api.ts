class ApiService {
    private baseURL: string;

    constructor() {
        // FastAPI backend URL with your actual IP
        this.baseURL = 'http://*************:8000'; // Your trading system IP
    }

    private async fetchWithTimeout(url: string, options: RequestInit = {}, timeout: number = 15000): Promise<Response> {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        try {
            const response = await fetch(url, {
                ...options,
                signal: controller.signal,
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'BybitTradingBot-Mobile/1.0.0 (Android 13; Motorola Moto G32)',
                    'X-Device-Model': 'XT2235-2',
                    'X-Platform': 'android',
                    ...options.headers,
                },
            });
            clearTimeout(timeoutId);
            return response;
        } catch (error) {
            clearTimeout(timeoutId);
            // Enhanced error handling for mobile networks
            if (error instanceof Error) {
                if (error.name === 'AbortError') {
                    throw new Error('Network timeout - please check your connection');
                }
                if (error.message.includes('Network request failed')) {
                    throw new Error('Connection failed - switching to mobile data may help');
                }
            }
            throw error;
        }
    }

    private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
        const url = `${this.baseURL}${endpoint}`;

        try {
            const response = await this.fetchWithTimeout(url, options);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            return data as T;
        } catch (error) {
            console.error(`API request failed: ${endpoint}`, error);
            throw error;
        }
    }

    // System Status
    async getSystemStatus() {
        return this.request('/system-status');
    }

    // Dashboard Data
    async getDashboardData() {
        const [systemStatus, profitStatus] = await Promise.all([
            this.getSystemStatus(),
            this.getProfitStatus(),
        ]);

        // Mock data for now - replace with actual API calls
        return {
            totalProfit: 15420.50,
            dailyProfit: 892.30,
            totalTrades: 156,
            winRate: 0.72,
            activePositions: 8,
            systemHealth: 98,
            profitHistory: [
                { date: '2025-01-01', profit: 1200 },
                { date: '2025-01-02', profit: 1450 },
                { date: '2025-01-03', profit: 1100 },
                { date: '2025-01-04', profit: 1680 },
                { date: '2025-01-05', profit: 1520 },
                { date: '2025-01-06', profit: 1750 },
                { date: '2025-01-07', profit: 1890 },
            ],
            portfolioDistribution: [
                { name: 'BTC', value: 35, color: '#f7931a' },
                { name: 'ETH', value: 25, color: '#627eea' },
                { name: 'ADA', value: 15, color: '#0033ad' },
                { name: 'SOL', value: 12, color: '#9945ff' },
                { name: 'Others', value: 13, color: '#00ff88' },
            ],
            recentTrades: [
                {
                    id: '1',
                    symbol: 'BTCUSDT',
                    side: 'buy' as const,
                    amount: 0.1,
                    price: 45230.50,
                    profit: 125.80,
                    time: '2 min ago',
                },
                {
                    id: '2',
                    symbol: 'ETHUSDT',
                    side: 'sell' as const,
                    amount: 2.5,
                    price: 3456.20,
                    profit: -45.30,
                    time: '8 min ago',
                },
                {
                    id: '3',
                    symbol: 'ADAUSDT',
                    side: 'buy' as const,
                    amount: 1000,
                    price: 0.52,
                    profit: 78.90,
                    time: '15 min ago',
                },
            ],
        };
    }

    // AI Status
    async getAIStatus() {
        return this.request('/ai-status');
    }

    // Profit Status
    async getProfitStatus() {
        return this.request('/profit-status');
    }

    // Trading Operations
    async getActivePositions() {
        return this.request('/positions');
    }

    async getActiveStrategies() {
        // Mock data for now
        return [
            {
                id: '1',
                name: 'Grid Trading BTC',
                status: 'active',
                profit: 1250.30,
                trades: 45,
                winRate: 0.78,
                risk: 'medium',
            },
            {
                id: '2',
                name: 'DCA ETH Strategy',
                status: 'active',
                profit: 890.50,
                trades: 32,
                winRate: 0.69,
                risk: 'low',
            },
            {
                id: '3',
                name: 'Arbitrage Scanner',
                status: 'paused',
                profit: 145.20,
                trades: 12,
                winRate: 0.83,
                risk: 'high',
            },
        ];
    }

    async emergencyStop() {
        return this.request('/emergency-stop', {
            method: 'POST',
        });
    }

    async pauseStrategy(strategyId: string) {
        return this.request(`/strategies/${strategyId}/pause`, {
            method: 'POST',
        });
    }

    async resumeStrategy(strategyId: string) {
        return this.request(`/strategies/${strategyId}/resume`, {
            method: 'POST',
        });
    }

    // Portfolio
    async getPortfolioData() {
        // Mock data for now
        return {
            totalValue: 25420.50,
            totalProfit: 3420.30,
            totalProfitPercentage: 0.156,
            dailyChange: 892.30,
            dailyChangePercentage: 0.036,
            assets: [
                {
                    symbol: 'BTC',
                    name: 'Bitcoin',
                    amount: 0.5621,
                    value: 8900.50,
                    profit: 1250.30,
                    profitPercentage: 0.123,
                    allocation: 35,
                },
                {
                    symbol: 'ETH',
                    name: 'Ethereum',
                    amount: 2.1456,
                    value: 6355.20,
                    profit: 890.50,
                    profitPercentage: 0.089,
                    allocation: 25,
                },
                {
                    symbol: 'ADA',
                    name: 'Cardano',
                    amount: 7500,
                    value: 3815.75,
                    profit: 456.80,
                    profitPercentage: 0.135,
                    allocation: 15,
                },
            ],
            transactions: [
                {
                    id: '1',
                    type: 'buy',
                    symbol: 'BTCUSDT',
                    amount: 0.1,
                    price: 45230.50,
                    total: 4523.05,
                    time: '2025-01-09 14:30:25',
                },
                {
                    id: '2',
                    type: 'sell',
                    symbol: 'ETHUSDT',
                    amount: 0.5,
                    price: 3456.20,
                    total: 1728.10,
                    time: '2025-01-09 13:45:12',
                },
            ],
        };
    }

    // Settings
    async getSettings() {
        // Mock data for now
        return {
            trading: {
                autoTrading: true,
                maxPositionSize: 25,
                riskLevel: 'medium',
                stopLoss: 5,
                takeProfit: 15,
            },
            notifications: {
                pushEnabled: true,
                tradeAlerts: true,
                profitAlerts: true,
                systemAlerts: true,
            },
            security: {
                biometricEnabled: true,
                autoLockEnabled: true,
                autoLockTimeout: 300,
            },
        };
    }

    async updateSettings(settings: any) {
        return this.request('/settings', {
            method: 'PUT',
            body: JSON.stringify(settings),
        });
    }

    // Trading Data
    async getTradingData() {
        // Mock data for now - replace with actual API call
        return {
            isActive: true,
            totalProfit: 1250.75,
            dailyProfit: 125.50,
            positions: [
                {
                    symbol: 'BTCUSDT',
                    side: 'Buy',
                    size: 0.1,
                    pnl: 125.50,
                },
                {
                    symbol: 'ETHUSDT',
                    side: 'Sell',
                    size: 0.5,
                    pnl: -45.20,
                },
            ],
            orders: [
                {
                    symbol: 'ADAUSDT',
                    type: 'Limit',
                    price: 0.45,
                    quantity: 1000,
                },
            ],
            strategies: [
                {
                    name: 'AI Momentum Strategy',
                    status: 'Active',
                    description: 'Advanced momentum trading with AI',
                    profit: 850.25,
                    winRate: 68.5,
                },
            ],
        };
    }

    // AI Status
    async getAIStatus() {
        // Mock data for now - replace with actual API call
        return {
            systemHealth: 92,
            components: {
                meta_cognition: {
                    name: 'Meta-Cognition Engine',
                    status: 'Active',
                    description: 'Self-aware AI system monitoring',
                    lastUpdate: '2025-01-09 14:30:25',
                },
                supergpt_integration: {
                    name: 'SuperGPT Integration',
                    status: 'Active',
                    description: 'Advanced AI trading decisions',
                    lastUpdate: '2025-01-09 14:29:15',
                },
                code_evolution: {
                    name: 'Code Evolution',
                    status: 'Active',
                    description: 'Self-improving code optimization',
                    lastUpdate: '2025-01-09 14:28:45',
                },
            },
            models: [
                {
                    name: 'Price Prediction Model',
                    status: 'Active',
                    description: 'Predicts price movements using LSTM',
                    accuracy: 85.2,
                    confidence: 92.1,
                    lastPrediction: 'Bullish',
                },
                {
                    name: 'Risk Assessment Model',
                    status: 'Active',
                    description: 'Evaluates trade risk factors',
                    accuracy: 78.9,
                    confidence: 88.5,
                    lastPrediction: 'Low Risk',
                },
            ],
            agents: [
                {
                    name: 'Trading Agent',
                    type: 'trading',
                    status: 'Active',
                    description: 'Executes trading strategies',
                    tasksCompleted: 156,
                },
                {
                    name: 'Research Agent',
                    type: 'research',
                    status: 'Active',
                    description: 'Analyzes market data and news',
                    tasksCompleted: 89,
                },
                {
                    name: 'Risk Agent',
                    type: 'risk',
                    status: 'Active',
                    description: 'Monitors and manages risk',
                    tasksCompleted: 234,
                },
            ],
        };
    }
}

export const apiService = new ApiService();
