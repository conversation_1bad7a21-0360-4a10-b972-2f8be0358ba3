#!/usr/bin/env python3
"""
Bybit MCP Server - Entry point for Continue.dev integration
Integrates with main.py unified system for real trading operations
"""

import asyncio
import sys
import os
import logging
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging for MCP server
os.makedirs(project_root / 'logs', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(project_root / 'logs' / 'mcp_server.log'),
        logging.StreamHandler(sys.stderr)
    ]
)

logger = logging.getLogger(__name__)

# MCP Protocol imports - Updated for proper MCP integration
try:
    # Try to import the official MCP Python SDK
    from mcp.server import Server
    from mcp.server.stdio import stdio_server
    from mcp.types import Tool, Resource
    MCP_AVAILABLE = True
    logger.info("MCP Python SDK successfully imported")
except ImportError:
    try:
        # Fallback: Try alternative MCP import
        import mcp
        MCP_AVAILABLE = True
        logger.info("Alternative MCP library imported")

        # Create mock classes for compatibility
        class Server:
            def __init__(self, name: str):
                self.name = name
                self.tools = {}
                self.resources = {}

        class Tool:
            def __init__(self, name: str, description: str, input_schema: dict):
                self.name = name
                self.description = description
                self.input_schema = input_schema

        class Resource:
            def __init__(self, uri: str, name: str, description: str):
                self.uri = uri
                self.name = name
                self.description = description

        def stdio_server():
            pass

    except ImportError:
        logger.warning("MCP Python library not available - running in compatibility mode")
        MCP_AVAILABLE = False

        # NO MOCK CLASSES - DISABLE MCP IF NOT AVAILABLE
        logger.error("MCP Python library not available - MCP SERVER DISABLED")

        class Server:
            def __init__(self, name: str):
                raise ImportError("MCP not available - no mock classes allowed")

        class Tool:
            def __init__(self, name: str, description: str, input_schema: dict):
                raise ImportError("MCP not available - no mock classes allowed")

        class Resource:
            def __init__(self, uri: str, name: str, description: str):
                raise ImportError("MCP not available - no mock classes allowed")

        def stdio_server():
            raise ImportError("MCP not available - no mock server allowed")

# Import the unified system - Updated for main.py integration
try:
    # Try to import from main.py (the actual entry point)
    from main import BybitTradingBotSystem
    UNIFIED_SYSTEM_AVAILABLE = True
    logger.info("Successfully imported BybitTradingBotSystem from main.py")
except ImportError:
    try:
        # Fallback: Try main_unified_system if it exists
        from main_unified_system import UnifiedTradingSystem, app
        UNIFIED_SYSTEM_AVAILABLE = True
        logger.info("Successfully imported unified trading system")
    except ImportError as e:
        logger.warning(f"Unified system not available: {e}")
        UNIFIED_SYSTEM_AVAILABLE = False

        # NO MOCK SYSTEM - IMPORT REAL SYSTEM OR FAIL
        try:
            from main import BybitTradingBotSystem
        except ImportError:
            logger.error("Cannot import real BybitTradingBotSystem - MCP SERVER DISABLED")
            class BybitTradingBotSystem:
                def __init__(self):
                    raise ImportError("Real trading system not available - no mock system allowed")

class BybitMCPServer:
    """MCP server that integrates with the main trading system"""

    def __init__(self):
        self.server = Server("bybit-trading-bot")
        self.trading_system = None
        self.system_initialized = False
        self._setup_tools()
        self._setup_resources()

    async def _ensure_system_initialized(self):
        """Ensure the trading system is initialized"""
        if not self.system_initialized:
            try:
                if UNIFIED_SYSTEM_AVAILABLE:
                    # Try to get the trading system instance
                    try:
                        from main import BybitTradingBotSystem
                        self.trading_system = BybitTradingBotSystem()
                        logger.info("BybitTradingBotSystem initialized for MCP server")
                    except ImportError:
                        from main_unified_system import unified_system
                        self.trading_system = unified_system
                        logger.info("Unified system initialized for MCP server")

                    # Initialize if not already done
                    if hasattr(self.trading_system, 'system_initialized') and not self.trading_system.system_initialized:
                        if hasattr(self.trading_system, 'initialize_complete_system'):
                            await self.trading_system.initialize_complete_system()
                else:
                    # Create mock system
                    self.trading_system = BybitTradingBotSystem()
                    logger.info("Mock trading system initialized for MCP server")

                self.system_initialized = True
            except Exception as e:
                logger.error(f"Failed to initialize trading system: {e}")
                # Don't raise - continue with mock system
                self.trading_system = BybitTradingBotSystem()
                self.system_initialized = True

    def _setup_tools(self):
        """Setup MCP tools that integrate with the unified system"""

        @self.server.tool
        async def get_system_status() -> Dict[str, Any]:
            """Get comprehensive system status from the unified trading system"""
            try:
                await self._ensure_system_initialized()

                status = {
                    "timestamp": datetime.now().isoformat(),
                    "system_initialized": self.unified_system.system_initialized,
                    "completed_phases": len(self.unified_system.completed_phases),
                    "total_phases": len(self.unified_system.initialization_phases),
                    "components": {}
                }

                # Check component status
                if self.unified_system.bybit_client:
                    status["components"]["bybit_client"] = "initialized"
                if self.unified_system.bot_manager:
                    status["components"]["bot_manager"] = "initialized"
                if self.unified_system.risk_manager:
                    status["components"]["risk_manager"] = "initialized"
                if self.unified_system.performance_analyzer:
                    status["components"]["performance_analyzer"] = "initialized"

                return status
            except Exception as e:
                return {"error": str(e), "timestamp": datetime.now().isoformat()}

        @self.server.tool
        async def get_market_data(symbol: str = "BTCUSDT") -> Dict[str, Any]:
            """Get real-time market data from the unified system"""
            try:
                await self._ensure_system_initialized()

                if self.unified_system.bybit_client:
                    # Use the real Bybit client from the unified system
                    data = await self.unified_system.bybit_client.get_realtime_data(symbol)
                    return data
                else:
                    return {"error": "Bybit client not initialized", "symbol": symbol}

            except Exception as e:
                return {"error": str(e), "symbol": symbol, "timestamp": datetime.now().isoformat()}

        @self.server.tool
        async def execute_trade(
            symbol: str,
            side: str,
            order_type: str,
            qty: float,
            price: Optional[float] = None
        ) -> Dict[str, Any]:
            """Execute a trade through the unified system"""
            try:
                await self._ensure_system_initialized()

                if self.unified_system.bybit_client:
                    result = await self.unified_system.bybit_client.execute_order(
                        symbol=symbol,
                        side=side,
                        order_type=order_type,
                        qty=qty,
                        price=price
                    )
                    return result
                else:
                    return {"error": "Trading client not initialized"}

            except Exception as e:
                return {"error": str(e), "timestamp": datetime.now().isoformat()}

        @self.server.tool
        async def get_positions() -> Dict[str, Any]:
            """Get current positions from the unified system"""
            try:
                await self._ensure_system_initialized()

                if self.unified_system.bybit_client:
                    positions = await self.unified_system.bybit_client.get_positions()
                    return {"positions": positions, "count": len(positions)}
                else:
                    return {"error": "Trading client not initialized"}

            except Exception as e:
                return {"error": str(e), "timestamp": datetime.now().isoformat()}

        @self.server.tool
        async def get_account_info() -> Dict[str, Any]:
            """Get account information from the unified system"""
            try:
                await self._ensure_system_initialized()

                if self.unified_system.bybit_client:
                    account_info = await self.unified_system.bybit_client.get_account_info()
                    return account_info
                else:
                    return {"error": "Trading client not initialized"}

            except Exception as e:
                return {"error": str(e), "timestamp": datetime.now().isoformat()}

        @self.server.tool
        async def assess_risk(symbol: str, position_size: float) -> Dict[str, Any]:
            """Assess risk for a potential position"""
            try:
                await self._ensure_system_initialized()

                if self.unified_system.risk_manager:
                    risk_assessment = await self.unified_system.risk_manager.assess_position_risk(symbol, position_size)
                    return risk_assessment
                else:
                    return {"error": "Risk manager not initialized"}

            except Exception as e:
                return {"error": str(e), "timestamp": datetime.now().isoformat()}

        @self.server.tool
        async def get_performance_metrics() -> Dict[str, Any]:
            """Get performance metrics from the unified system"""
            try:
                await self._ensure_system_initialized()

                if self.unified_system.performance_analyzer:
                    metrics = await self.unified_system.performance_analyzer.get_snapshot()
                    return metrics
                else:
                    return {"error": "Performance analyzer not initialized"}

            except Exception as e:
                return {"error": str(e), "timestamp": datetime.now().isoformat()}

    def _setup_resources(self):
        """Setup MCP resources"""

        @self.server.resource("trading_status")
        async def trading_status() -> str:
            """Real-time trading system status"""
            try:
                await self._ensure_system_initialized()

                status = {
                    "timestamp": datetime.now().isoformat(),
                    "system_operational": self.unified_system.system_initialized,
                    "active_components": [],
                    "system_health": "operational" if self.unified_system.system_initialized else "initializing"
                }

                # Check which components are active
                if self.unified_system.bybit_client:
                    status["active_components"].append("bybit_client")
                if self.unified_system.bot_manager:
                    status["active_components"].append("bot_manager")
                if self.unified_system.risk_manager:
                    status["active_components"].append("risk_manager")
                if self.unified_system.performance_analyzer:
                    status["active_components"].append("performance_analyzer")

                return json.dumps(status, indent=2)
            except Exception as e:
                return json.dumps({"error": str(e)}, indent=2)

        @self.server.resource("system_overview")
        async def system_overview() -> str:
            """Comprehensive system overview"""
            try:
                await self._ensure_system_initialized()

                overview = {
                    "system_name": "Unified Autonomous Trading System",
                    "initialization_status": {
                        "completed_phases": self.unified_system.completed_phases,
                        "total_phases": self.unified_system.initialization_phases,
                        "progress_percentage": (len(self.unified_system.completed_phases) / len(self.unified_system.initialization_phases)) * 100
                    },
                    "timestamp": datetime.now().isoformat()
                }

                return json.dumps(overview, indent=2)
            except Exception as e:
                return json.dumps({"error": str(e)}, indent=2)

    async def run(self):
        """Run the MCP server"""
        logger.info("Starting Bybit MCP Server...")
        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                self.server.create_initialization_options()
            )

async def main():
    """Main entry point for MCP server"""
    try:
        server = BybitMCPServer()
        await server.run()
    except KeyboardInterrupt:
        logger.info("Server shutdown requested")
    except Exception as e:
        logger.error(f"Server error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
