import {
    Add,
    Notifications,
    Restore,
    Save,
    Security,
    Speed,
    TrendingUp,
    VpnKey
} from '@mui/icons-material'
import {
    <PERSON><PERSON>,
    <PERSON>,
    Button,
    Card,
    CardContent,
    Chip,
    Container,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    FormControlLabel,
    Grid,
    MenuItem,
    Slider,
    Switch,
    Tab,
    Tabs,
    TextField,
    Typography
} from '@mui/material'
import { motion } from 'framer-motion'
import { useState } from 'react'

const Settings = () => {
    const [activeTab, setActiveTab] = useState(0)
    const [saveDialog, setSaveDialog] = useState(false)
    const [resetDialog, setResetDialog] = useState(false)

    // Trading Settings State
    const [tradingSettings, setTradingSettings] = useState({
        autoTrading: true,
        maxPositionSize: 25,
        riskLevel: 'medium',
        stopLoss: 5,
        takeProfit: 15,
        maxDailyLoss: 10,
        tradingPairs: ['BTCUSDT', 'ETHUSDT', 'ADAUSDT'],
        leverageMultiplier: 2,
        slippageTolerance: 0.5,
    })

    // Risk Management Settings
    const [riskSettings, setRiskSettings] = useState({
        maxPortfolioRisk: 20,
        correlationLimit: 0.7,
        diversificationMin: 5,
        volatilityThreshold: 15,
        drawdownLimit: 15,
        positionSizing: 'kelly',
        riskBudget: 50000,
        emergencyStopLoss: 25,
    })

    // AI Settings
    const [aiSettings, setAiSettings] = useState({
        aiTradingEnabled: true,
        mlPredictions: true,
        sentimentAnalysis: true,
        newsAnalysis: true,
        technicalAnalysis: true,
        confidenceThreshold: 75,
        retrainFrequency: 'daily',
        learningRate: 0.001,
    })

    // Notification Settings
    const [notificationSettings, setNotificationSettings] = useState({
        emailAlerts: true,
        pushNotifications: true,
        telegramBot: false,
        tradeAlerts: true,
        profitLossAlerts: true,
        systemAlerts: true,
        aiInsights: true,
        dailyReports: true,
    })

    // API Settings
    const [apiSettings, setApiSettings] = useState({
        apiKey: '••••••••••••••••',
        secretKey: '••••••••••••••••',
        testMode: false,
        apiEndpoint: 'https://api.bybit.com',
        rateLimit: 600,
        timeout: 5000,
    })

    const TabPanel = ({ children, value, index }) => (
        <div role="tabpanel" hidden={value !== index}>
            {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
        </div>
    )

    const handleSaveSettings = () => {
        // Here you would save settings to backend
        setSaveDialog(false)
        // Show success message
    }

    const handleResetSettings = () => {
        // Reset to default values
        setResetDialog(false)
        // Show reset message
    }

    return (
        <Container maxWidth="xl" sx={{ py: 3 }}>
            <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
            >
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
                    <Typography
                        variant="h4"
                        sx={{
                            fontWeight: 700,
                            color: '#ffffff',
                            background: 'linear-gradient(45deg, #00ff88, #00ccff)',
                            WebkitBackgroundClip: 'text',
                            WebkitTextFillColor: 'transparent',
                        }}
                    >
                        System Settings
                    </Typography>

                    <Box sx={{ display: 'flex', gap: 2 }}>
                        <Button
                            variant="outlined"
                            startIcon={<Restore />}
                            onClick={() => setResetDialog(true)}
                            sx={{
                                borderColor: '#ffa726',
                                color: '#ffa726',
                                '&:hover': {
                                    borderColor: '#ffa726',
                                    backgroundColor: 'rgba(255, 167, 38, 0.1)',
                                },
                            }}
                        >
                            Reset to Defaults
                        </Button>

                        <Button
                            variant="contained"
                            startIcon={<Save />}
                            onClick={() => setSaveDialog(true)}
                            sx={{
                                background: 'linear-gradient(45deg, #00ff88, #00cc6a)',
                                fontWeight: 600,
                            }}
                        >
                            Save Changes
                        </Button>
                    </Box>
                </Box>
            </motion.div>

            {/* Settings Tabs */}
            <Box sx={{ borderBottom: 1, borderColor: 'rgba(255, 255, 255, 0.1)', mb: 3 }}>
                <Tabs
                    value={activeTab}
                    onChange={(e, newValue) => setActiveTab(newValue)}
                    sx={{
                        '& .MuiTab-root': {
                            color: '#b3b3b3',
                            fontWeight: 600,
                            '&.Mui-selected': {
                                color: '#00ff88',
                            },
                        },
                        '& .MuiTabs-indicator': {
                            backgroundColor: '#00ff88',
                        },
                    }}
                >
                    <Tab label="Trading" icon={<TrendingUp />} />
                    <Tab label="Risk Management" icon={<Security />} />
                    <Tab label="AI Configuration" icon={<Speed />} />
                    <Tab label="Notifications" icon={<Notifications />} />
                    <Tab label="API & Security" icon={<VpnKey />} />
                </Tabs>
            </Box>

            {/* Trading Settings Tab */}
            <TabPanel value={activeTab} index={0}>
                <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                        <motion.div
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.6, delay: 0.1 }}
                        >
                            <Card
                                sx={{
                                    background: 'rgba(255, 255, 255, 0.03)',
                                    backdropFilter: 'blur(20px)',
                                    border: '1px solid rgba(255, 255, 255, 0.1)',
                                    borderRadius: 3,
                                }}
                            >
                                <CardContent>
                                    <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600, mb: 3 }}>
                                        General Trading Settings
                                    </Typography>

                                    <FormControlLabel
                                        control={
                                            <Switch
                                                checked={tradingSettings.autoTrading}
                                                onChange={(e) => setTradingSettings({ ...tradingSettings, autoTrading: e.target.checked })}
                                                sx={{
                                                    '& .MuiSwitch-switchBase.Mui-checked': {
                                                        color: '#00ff88',
                                                    },
                                                    '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                                                        backgroundColor: '#00ff88',
                                                    },
                                                }}
                                            />
                                        }
                                        label={
                                            <Typography sx={{ color: '#ffffff', fontWeight: 600 }}>
                                                Enable Auto Trading
                                            </Typography>
                                        }
                                        sx={{ mb: 3 }}
                                    />

                                    <Box sx={{ mb: 3 }}>
                                        <Typography variant="body2" sx={{ color: '#b3b3b3', mb: 2 }}>
                                            Maximum Position Size: {tradingSettings.maxPositionSize}%
                                        </Typography>
                                        <Slider
                                            value={tradingSettings.maxPositionSize}
                                            onChange={(e, value) => setTradingSettings({ ...tradingSettings, maxPositionSize: value })}
                                            min={1}
                                            max={100}
                                            sx={{
                                                color: '#00ff88',
                                                '& .MuiSlider-thumb': {
                                                    backgroundColor: '#00ff88',
                                                },
                                                '& .MuiSlider-track': {
                                                    backgroundColor: '#00ff88',
                                                },
                                                '& .MuiSlider-rail': {
                                                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                                                },
                                            }}
                                        />
                                    </Box>

                                    <TextField
                                        select
                                        fullWidth
                                        label="Risk Level"
                                        value={tradingSettings.riskLevel}
                                        onChange={(e) => setTradingSettings({ ...tradingSettings, riskLevel: e.target.value })}
                                        sx={{
                                            mb: 3,
                                            '& .MuiOutlinedInput-root': {
                                                color: '#ffffff',
                                                '& fieldset': {
                                                    borderColor: 'rgba(255, 255, 255, 0.3)',
                                                },
                                                '&:hover fieldset': {
                                                    borderColor: '#00ff88',
                                                },
                                                '&.Mui-focused fieldset': {
                                                    borderColor: '#00ff88',
                                                },
                                            },
                                            '& .MuiInputLabel-root': {
                                                color: '#b3b3b3',
                                            },
                                        }}
                                    >
                                        <MenuItem value="low">Low Risk</MenuItem>
                                        <MenuItem value="medium">Medium Risk</MenuItem>
                                        <MenuItem value="high">High Risk</MenuItem>
                                        <MenuItem value="aggressive">Aggressive</MenuItem>
                                    </TextField>

                                    <Grid container spacing={2}>
                                        <Grid item xs={6}>
                                            <TextField
                                                fullWidth
                                                label="Stop Loss (%)"
                                                type="number"
                                                value={tradingSettings.stopLoss}
                                                onChange={(e) => setTradingSettings({ ...tradingSettings, stopLoss: parseFloat(e.target.value) })}
                                                sx={{
                                                    '& .MuiOutlinedInput-root': {
                                                        color: '#ffffff',
                                                        '& fieldset': {
                                                            borderColor: 'rgba(255, 255, 255, 0.3)',
                                                        },
                                                        '&:hover fieldset': {
                                                            borderColor: '#00ff88',
                                                        },
                                                        '&.Mui-focused fieldset': {
                                                            borderColor: '#00ff88',
                                                        },
                                                    },
                                                    '& .MuiInputLabel-root': {
                                                        color: '#b3b3b3',
                                                    },
                                                }}
                                            />
                                        </Grid>
                                        <Grid item xs={6}>
                                            <TextField
                                                fullWidth
                                                label="Take Profit (%)"
                                                type="number"
                                                value={tradingSettings.takeProfit}
                                                onChange={(e) => setTradingSettings({ ...tradingSettings, takeProfit: parseFloat(e.target.value) })}
                                                sx={{
                                                    '& .MuiOutlinedInput-root': {
                                                        color: '#ffffff',
                                                        '& fieldset': {
                                                            borderColor: 'rgba(255, 255, 255, 0.3)',
                                                        },
                                                        '&:hover fieldset': {
                                                            borderColor: '#00ff88',
                                                        },
                                                        '&.Mui-focused fieldset': {
                                                            borderColor: '#00ff88',
                                                        },
                                                    },
                                                    '& .MuiInputLabel-root': {
                                                        color: '#b3b3b3',
                                                    },
                                                }}
                                            />
                                        </Grid>
                                    </Grid>
                                </CardContent>
                            </Card>
                        </motion.div>
                    </Grid>

                    <Grid item xs={12} md={6}>
                        <motion.div
                            initial={{ opacity: 0, x: 20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.6, delay: 0.2 }}
                        >
                            <Card
                                sx={{
                                    background: 'rgba(255, 255, 255, 0.03)',
                                    backdropFilter: 'blur(20px)',
                                    border: '1px solid rgba(255, 255, 255, 0.1)',
                                    borderRadius: 3,
                                }}
                            >
                                <CardContent>
                                    <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600, mb: 3 }}>
                                        Trading Pairs
                                    </Typography>

                                    <Box sx={{ mb: 3 }}>
                                        {tradingSettings.tradingPairs.map((pair, index) => (
                                            <Chip
                                                key={index}
                                                label={pair}
                                                onDelete={() => {
                                                    const newPairs = tradingSettings.tradingPairs.filter((_, i) => i !== index)
                                                    setTradingSettings({ ...tradingSettings, tradingPairs: newPairs })
                                                }}
                                                sx={{
                                                    mr: 1,
                                                    mb: 1,
                                                    backgroundColor: 'rgba(0, 255, 136, 0.1)',
                                                    color: '#00ff88',
                                                    border: '1px solid rgba(0, 255, 136, 0.3)',
                                                }}
                                            />
                                        ))}
                                    </Box>

                                    <Button
                                        variant="outlined"
                                        startIcon={<Add />}
                                        fullWidth
                                        sx={{
                                            borderColor: '#42a5f5',
                                            color: '#42a5f5',
                                            mb: 3,
                                            '&:hover': {
                                                borderColor: '#42a5f5',
                                                backgroundColor: 'rgba(66, 165, 245, 0.1)',
                                            },
                                        }}
                                    >
                                        Add Trading Pair
                                    </Button>

                                    <Box sx={{ mb: 3 }}>
                                        <Typography variant="body2" sx={{ color: '#b3b3b3', mb: 2 }}>
                                            Leverage Multiplier: {tradingSettings.leverageMultiplier}x
                                        </Typography>
                                        <Slider
                                            value={tradingSettings.leverageMultiplier}
                                            onChange={(e, value) => setTradingSettings({ ...tradingSettings, leverageMultiplier: value })}
                                            min={1}
                                            max={10}
                                            step={1}
                                            marks
                                            sx={{
                                                color: '#ffa726',
                                                '& .MuiSlider-thumb': {
                                                    backgroundColor: '#ffa726',
                                                },
                                                '& .MuiSlider-track': {
                                                    backgroundColor: '#ffa726',
                                                },
                                                '& .MuiSlider-rail': {
                                                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                                                },
                                            }}
                                        />
                                    </Box>

                                    <TextField
                                        fullWidth
                                        label="Slippage Tolerance (%)"
                                        type="number"
                                        value={tradingSettings.slippageTolerance}
                                        onChange={(e) => setTradingSettings({ ...tradingSettings, slippageTolerance: parseFloat(e.target.value) })}
                                        sx={{
                                            '& .MuiOutlinedInput-root': {
                                                color: '#ffffff',
                                                '& fieldset': {
                                                    borderColor: 'rgba(255, 255, 255, 0.3)',
                                                },
                                                '&:hover fieldset': {
                                                    borderColor: '#00ff88',
                                                },
                                                '&.Mui-focused fieldset': {
                                                    borderColor: '#00ff88',
                                                },
                                            },
                                            '& .MuiInputLabel-root': {
                                                color: '#b3b3b3',
                                            },
                                        }}
                                    />
                                </CardContent>
                            </Card>
                        </motion.div>
                    </Grid>
                </Grid>
            </TabPanel>

            {/* Risk Management Tab */}
            <TabPanel value={activeTab} index={1}>
                <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                        <Card
                            sx={{
                                background: 'rgba(255, 255, 255, 0.03)',
                                backdropFilter: 'blur(20px)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: 3,
                            }}
                        >
                            <CardContent>
                                <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600, mb: 3 }}>
                                    Portfolio Risk Limits
                                </Typography>

                                <Box sx={{ mb: 3 }}>
                                    <Typography variant="body2" sx={{ color: '#b3b3b3', mb: 2 }}>
                                        Maximum Portfolio Risk: {riskSettings.maxPortfolioRisk}%
                                    </Typography>
                                    <Slider
                                        value={riskSettings.maxPortfolioRisk}
                                        onChange={(e, value) => setRiskSettings({ ...riskSettings, maxPortfolioRisk: value })}
                                        min={1}
                                        max={50}
                                        sx={{
                                            color: '#ff4757',
                                            '& .MuiSlider-thumb': {
                                                backgroundColor: '#ff4757',
                                            },
                                            '& .MuiSlider-track': {
                                                backgroundColor: '#ff4757',
                                            },
                                        }}
                                    />
                                </Box>

                                <TextField
                                    fullWidth
                                    label="Risk Budget ($)"
                                    type="number"
                                    value={riskSettings.riskBudget}
                                    onChange={(e) => setRiskSettings({ ...riskSettings, riskBudget: parseFloat(e.target.value) })}
                                    sx={{
                                        mb: 3,
                                        '& .MuiOutlinedInput-root': {
                                            color: '#ffffff',
                                            '& fieldset': {
                                                borderColor: 'rgba(255, 255, 255, 0.3)',
                                            },
                                            '&:hover fieldset': {
                                                borderColor: '#00ff88',
                                            },
                                            '&.Mui-focused fieldset': {
                                                borderColor: '#00ff88',
                                            },
                                        },
                                        '& .MuiInputLabel-root': {
                                            color: '#b3b3b3',
                                        },
                                    }}
                                />

                                <TextField
                                    select
                                    fullWidth
                                    label="Position Sizing Method"
                                    value={riskSettings.positionSizing}
                                    onChange={(e) => setRiskSettings({ ...riskSettings, positionSizing: e.target.value })}
                                    sx={{
                                        '& .MuiOutlinedInput-root': {
                                            color: '#ffffff',
                                            '& fieldset': {
                                                borderColor: 'rgba(255, 255, 255, 0.3)',
                                            },
                                            '&:hover fieldset': {
                                                borderColor: '#00ff88',
                                            },
                                            '&.Mui-focused fieldset': {
                                                borderColor: '#00ff88',
                                            },
                                        },
                                        '& .MuiInputLabel-root': {
                                            color: '#b3b3b3',
                                        },
                                    }}
                                >
                                    <MenuItem value="fixed">Fixed Amount</MenuItem>
                                    <MenuItem value="percentage">Percentage</MenuItem>
                                    <MenuItem value="kelly">Kelly Criterion</MenuItem>
                                    <MenuItem value="volatility">Volatility Based</MenuItem>
                                </TextField>
                            </CardContent>
                        </Card>
                    </Grid>

                    <Grid item xs={12} md={6}>
                        <Card
                            sx={{
                                background: 'rgba(255, 255, 255, 0.03)',
                                backdropFilter: 'blur(20px)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: 3,
                            }}
                        >
                            <CardContent>
                                <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600, mb: 3 }}>
                                    Advanced Risk Controls
                                </Typography>

                                <Box sx={{ mb: 3 }}>
                                    <Typography variant="body2" sx={{ color: '#b3b3b3', mb: 2 }}>
                                        Correlation Limit: {riskSettings.correlationLimit}
                                    </Typography>
                                    <Slider
                                        value={riskSettings.correlationLimit}
                                        onChange={(e, value) => setRiskSettings({ ...riskSettings, correlationLimit: value })}
                                        min={0.1}
                                        max={1}
                                        step={0.1}
                                        sx={{
                                            color: '#8e24aa',
                                            '& .MuiSlider-thumb': {
                                                backgroundColor: '#8e24aa',
                                            },
                                            '& .MuiSlider-track': {
                                                backgroundColor: '#8e24aa',
                                            },
                                        }}
                                    />
                                </Box>

                                <Box sx={{ mb: 3 }}>
                                    <Typography variant="body2" sx={{ color: '#b3b3b3', mb: 2 }}>
                                        Drawdown Limit: {riskSettings.drawdownLimit}%
                                    </Typography>
                                    <Slider
                                        value={riskSettings.drawdownLimit}
                                        onChange={(e, value) => setRiskSettings({ ...riskSettings, drawdownLimit: value })}
                                        min={5}
                                        max={50}
                                        sx={{
                                            color: '#ff4757',
                                            '& .MuiSlider-thumb': {
                                                backgroundColor: '#ff4757',
                                            },
                                            '& .MuiSlider-track': {
                                                backgroundColor: '#ff4757',
                                            },
                                        }}
                                    />
                                </Box>

                                <Alert severity="warning" sx={{ mb: 2 }}>
                                    <Typography variant="body2">
                                        Emergency stop-loss will trigger at {riskSettings.emergencyStopLoss}% portfolio loss
                                    </Typography>
                                </Alert>
                            </CardContent>
                        </Card>
                    </Grid>
                </Grid>
            </TabPanel>

            {/* AI Configuration Tab */}
            <TabPanel value={activeTab} index={2}>
                <Grid container spacing={3}>
                    <Grid item xs={12}>
                        <Card
                            sx={{
                                background: 'rgba(255, 255, 255, 0.03)',
                                backdropFilter: 'blur(20px)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: 3,
                            }}
                        >
                            <CardContent>
                                <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600, mb: 3 }}>
                                    AI & Machine Learning Configuration
                                </Typography>

                                <Grid container spacing={3}>
                                    <Grid item xs={12} md={6}>
                                        <Box sx={{ mb: 3 }}>
                                            <FormControlLabel
                                                control={
                                                    <Switch
                                                        checked={aiSettings.aiTradingEnabled}
                                                        onChange={(e) => setAiSettings({ ...aiSettings, aiTradingEnabled: e.target.checked })}
                                                        sx={{
                                                            '& .MuiSwitch-switchBase.Mui-checked': {
                                                                color: '#00ff88',
                                                            },
                                                            '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                                                                backgroundColor: '#00ff88',
                                                            },
                                                        }}
                                                    />
                                                }
                                                label={<Typography sx={{ color: '#ffffff' }}>Enable AI Trading</Typography>}
                                            />
                                        </Box>

                                        <Box sx={{ mb: 3 }}>
                                            <FormControlLabel
                                                control={
                                                    <Switch
                                                        checked={aiSettings.mlPredictions}
                                                        onChange={(e) => setAiSettings({ ...aiSettings, mlPredictions: e.target.checked })}
                                                        sx={{
                                                            '& .MuiSwitch-switchBase.Mui-checked': {
                                                                color: '#42a5f5',
                                                            },
                                                            '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                                                                backgroundColor: '#42a5f5',
                                                            },
                                                        }}
                                                    />
                                                }
                                                label={<Typography sx={{ color: '#ffffff' }}>ML Price Predictions</Typography>}
                                            />
                                        </Box>

                                        <Box sx={{ mb: 3 }}>
                                            <FormControlLabel
                                                control={
                                                    <Switch
                                                        checked={aiSettings.sentimentAnalysis}
                                                        onChange={(e) => setAiSettings({ ...aiSettings, sentimentAnalysis: e.target.checked })}
                                                        sx={{
                                                            '& .MuiSwitch-switchBase.Mui-checked': {
                                                                color: '#ffa726',
                                                            },
                                                            '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                                                                backgroundColor: '#ffa726',
                                                            },
                                                        }}
                                                    />
                                                }
                                                label={<Typography sx={{ color: '#ffffff' }}>Sentiment Analysis</Typography>}
                                            />
                                        </Box>

                                        <Box sx={{ mb: 3 }}>
                                            <FormControlLabel
                                                control={
                                                    <Switch
                                                        checked={aiSettings.newsAnalysis}
                                                        onChange={(e) => setAiSettings({ ...aiSettings, newsAnalysis: e.target.checked })}
                                                        sx={{
                                                            '& .MuiSwitch-switchBase.Mui-checked': {
                                                                color: '#8e24aa',
                                                            },
                                                            '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                                                                backgroundColor: '#8e24aa',
                                                            },
                                                        }}
                                                    />
                                                }
                                                label={<Typography sx={{ color: '#ffffff' }}>News Impact Analysis</Typography>}
                                            />
                                        </Box>
                                    </Grid>

                                    <Grid item xs={12} md={6}>
                                        <Box sx={{ mb: 3 }}>
                                            <Typography variant="body2" sx={{ color: '#b3b3b3', mb: 2 }}>
                                                AI Confidence Threshold: {aiSettings.confidenceThreshold}%
                                            </Typography>
                                            <Slider
                                                value={aiSettings.confidenceThreshold}
                                                onChange={(e, value) => setAiSettings({ ...aiSettings, confidenceThreshold: value })}
                                                min={50}
                                                max={95}
                                                sx={{
                                                    color: '#00ff88',
                                                    '& .MuiSlider-thumb': {
                                                        backgroundColor: '#00ff88',
                                                    },
                                                    '& .MuiSlider-track': {
                                                        backgroundColor: '#00ff88',
                                                    },
                                                }}
                                            />
                                        </Box>

                                        <TextField
                                            select
                                            fullWidth
                                            label="Model Retrain Frequency"
                                            value={aiSettings.retrainFrequency}
                                            onChange={(e) => setAiSettings({ ...aiSettings, retrainFrequency: e.target.value })}
                                            sx={{
                                                mb: 3,
                                                '& .MuiOutlinedInput-root': {
                                                    color: '#ffffff',
                                                    '& fieldset': {
                                                        borderColor: 'rgba(255, 255, 255, 0.3)',
                                                    },
                                                    '&:hover fieldset': {
                                                        borderColor: '#00ff88',
                                                    },
                                                    '&.Mui-focused fieldset': {
                                                        borderColor: '#00ff88',
                                                    },
                                                },
                                                '& .MuiInputLabel-root': {
                                                    color: '#b3b3b3',
                                                },
                                            }}
                                        >
                                            <MenuItem value="hourly">Hourly</MenuItem>
                                            <MenuItem value="daily">Daily</MenuItem>
                                            <MenuItem value="weekly">Weekly</MenuItem>
                                            <MenuItem value="monthly">Monthly</MenuItem>
                                        </TextField>

                                        <TextField
                                            fullWidth
                                            label="Learning Rate"
                                            type="number"
                                            value={aiSettings.learningRate}
                                            onChange={(e) => setAiSettings({ ...aiSettings, learningRate: parseFloat(e.target.value) })}
                                            inputProps={{ step: 0.0001, min: 0.0001, max: 0.1 }}
                                            sx={{
                                                '& .MuiOutlinedInput-root': {
                                                    color: '#ffffff',
                                                    '& fieldset': {
                                                        borderColor: 'rgba(255, 255, 255, 0.3)',
                                                    },
                                                    '&:hover fieldset': {
                                                        borderColor: '#00ff88',
                                                    },
                                                    '&.Mui-focused fieldset': {
                                                        borderColor: '#00ff88',
                                                    },
                                                },
                                                '& .MuiInputLabel-root': {
                                                    color: '#b3b3b3',
                                                },
                                            }}
                                        />
                                    </Grid>
                                </Grid>
                            </CardContent>
                        </Card>
                    </Grid>
                </Grid>
            </TabPanel>

            {/* Save Confirmation Dialog */}
            <Dialog
                open={saveDialog}
                onClose={() => setSaveDialog(false)}
                PaperProps={{
                    sx: {
                        background: 'rgba(20, 20, 20, 0.95)',
                        backdropFilter: 'blur(20px)',
                        border: '1px solid rgba(255, 255, 255, 0.1)',
                        borderRadius: 3,
                    },
                }}
            >
                <DialogTitle sx={{ color: '#ffffff' }}>
                    Save Settings
                </DialogTitle>
                <DialogContent>
                    <Typography sx={{ color: '#b3b3b3' }}>
                        Are you sure you want to save all changes? This will update your trading system configuration.
                    </Typography>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setSaveDialog(false)} sx={{ color: '#b3b3b3' }}>
                        Cancel
                    </Button>
                    <Button onClick={handleSaveSettings} variant="contained" color="primary">
                        Save Changes
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Reset Confirmation Dialog */}
            <Dialog
                open={resetDialog}
                onClose={() => setResetDialog(false)}
                PaperProps={{
                    sx: {
                        background: 'rgba(20, 20, 20, 0.95)',
                        backdropFilter: 'blur(20px)',
                        border: '1px solid rgba(255, 255, 255, 0.1)',
                        borderRadius: 3,
                    },
                }}
            >
                <DialogTitle sx={{ color: '#ffffff' }}>
                    Reset Settings
                </DialogTitle>
                <DialogContent>
                    <Alert severity="warning" sx={{ mb: 2 }}>
                        This will reset all settings to their default values.
                    </Alert>
                    <Typography sx={{ color: '#b3b3b3' }}>
                        Are you sure you want to reset all settings? This action cannot be undone.
                    </Typography>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setResetDialog(false)} sx={{ color: '#b3b3b3' }}>
                        Cancel
                    </Button>
                    <Button onClick={handleResetSettings} color="error" variant="contained">
                        Reset to Defaults
                    </Button>
                </DialogActions>
            </Dialog>
        </Container>
    )
}

export default Settings
