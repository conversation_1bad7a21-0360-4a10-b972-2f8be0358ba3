"""
Strategy Manager for the Autonomous Bybit Trading Bot

This module coordinates multiple trading strategies and integrates data from:
- Market data crawler
- News sentiment crawler
- Social sentiment crawler
- Economic data crawler
- ML market predictor

It adapts strategies based on market conditions and sentiment analysis.
"""

import asyncio
import logging
import time
import numpy as np
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

from bybit_bot.core.config import BotConfig
# from bybit_bot.ml.market_predictor import MarketPredictor  # Imported when needed
from bybit_bot.data_crawler.market_data_crawler import MarketDataCrawler
from bybit_bot.data_crawler.news_sentiment_crawler import NewsSentimentCrawler
from bybit_bot.data_crawler.social_sentiment_crawler import SocialSentimentCrawler
from bybit_bot.data_crawler.economic_data_crawler import EconomicDataCrawler
from bybit_bot.ai.memory_manager import PersistentMemoryManager
# TimeContext imported when needed to avoid unused import warning
from bybit_bot.ai.advanced_memory_system import AdvancedMemorySystem

class MarketRegime(Enum):
    """Market regimes for strategy adaptation"""
    TRENDING_BULL = "trending_bull"
    TRENDING_BEAR = "trending_bear"
    RANGING = "ranging"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"
    NEWS_DRIVEN = "news_driven"
    SENTIMENT_DRIVEN = "sentiment_driven"

class StrategyType(Enum):
    """Available strategy types"""
    MOMENTUM = "momentum"
    MEAN_REVERSION = "mean_reversion"
    TREND_FOLLOWING = "trend_following"
    SCALPING = "scalping"
    NEWS_TRADING = "news_trading"
    SENTIMENT_TRADING = "sentiment_trading"
    ML_PREDICTION = "ml_prediction"
    HYBRID = "hybrid"
    MARGIN_ARBITRAGE = "margin_arbitrage"
    LEVERAGE_MOMENTUM = "leverage_momentum"
    MARGIN_SCALPING = "margin_scalping"
    CROSS_MARGIN_STRATEGY = "cross_margin_strategy"

@dataclass
class TradingSignal:
    """Trading signal with comprehensive data"""
    symbol: str
    action: str  # "BUY", "SELL", "CLOSE", "HOLD"
    strength: float  # 0.0 to 1.0
    confidence: float  # 0.0 to 1.0
    strategy: str
    entry_price: Optional[float]
    stop_loss: Optional[float]
    take_profit: Optional[float]
    reason: str
    timestamp: datetime
    risk_score: float  # 0.0 to 1.0
    expected_holding_time: Optional[int]  # minutes
    sentiment_score: Optional[float]
    news_impact: Optional[float]
    technical_score: Optional[float]
    ml_prediction: Optional[Dict]

class StrategyManager:
    """Advanced strategy manager with AI-driven adaptation"""
    
    def __init__(self, config: BotConfig, database_manager=None, bybit_client=None,
                 memory_manager: Optional[PersistentMemoryManager] = None,
                 advanced_memory: Optional[AdvancedMemorySystem] = None):
        self.config = config
        self.database_manager = database_manager
        self.bybit_client = bybit_client
        self.memory_manager = memory_manager
        self.advanced_memory = advanced_memory
        self.logger = logging.getLogger(__name__)

        # Initialize components with safe imports
        try:
            from ..ml.market_predictor import MLMarketPredictor
            if self.database_manager:
                self.market_predictor = MLMarketPredictor(config, self.database_manager)
            else:
                self.market_predictor = None
        except Exception as e:
            self.logger.warning(f"Could not initialize ML Market Predictor: {e}")
            self.market_predictor = None

        # Initialize crawlers as None - will be set up in initialize method
        self.market_crawler = None
        self.news_crawler = None
        self.social_crawler = None
        self.economic_crawler = None
        
        # Strategy configuration
        self.trading_symbols = getattr(config, 'trading_symbols', ['BTCUSDT', 'ETHUSDT'])
        self.active_strategies = {
            StrategyType.MOMENTUM: True,
            StrategyType.MEAN_REVERSION: True,
            StrategyType.TREND_FOLLOWING: True,
            StrategyType.ML_PREDICTION: True,
            StrategyType.SENTIMENT_TRADING: True,
            StrategyType.NEWS_TRADING: True,
            StrategyType.MARGIN_ARBITRAGE: True,
            StrategyType.LEVERAGE_MOMENTUM: True,
            StrategyType.MARGIN_SCALPING: True,
            StrategyType.CROSS_MARGIN_STRATEGY: True
        }

        # MARGIN TRADING AWARENESS
        self.margin_mode = "CROSS_MARGIN"  # ISOLATED_MARGIN or CROSS_MARGIN
        self.max_leverage = {
            'BTCUSDT': 100,  # Max 100x leverage for BTC
            'ETHUSDT': 100,  # Max 100x leverage for ETH
            'default': 50    # Default max leverage
        }
        self.margin_risk_levels = {
            'conservative': {'max_margin_ratio': 50, 'leverage_multiplier': 0.3},
            'moderate': {'max_margin_ratio': 70, 'leverage_multiplier': 0.6},
            'aggressive': {'max_margin_ratio': 85, 'leverage_multiplier': 1.0}
        }
        self.current_risk_level = 'conservative'  # Start conservative
        
        # Market state
        self.current_regime = MarketRegime.RANGING
        self.market_data_cache = {}
        self.sentiment_data_cache = {}
        self.news_data_cache = {}
        self.economic_data_cache = {}
        self.ml_predictions_cache = {}
        
        # Strategy weights based on performance
        self.strategy_weights = {
            StrategyType.MOMENTUM: 0.10,
            StrategyType.MEAN_REVERSION: 0.10,
            StrategyType.TREND_FOLLOWING: 0.10,
            StrategyType.ML_PREDICTION: 0.15,
            StrategyType.SENTIMENT_TRADING: 0.10,
            StrategyType.NEWS_TRADING: 0.05,
            StrategyType.MARGIN_ARBITRAGE: 0.15,
            StrategyType.LEVERAGE_MOMENTUM: 0.10,
            StrategyType.MARGIN_SCALPING: 0.10,
            StrategyType.CROSS_MARGIN_STRATEGY: 0.05,
            StrategyType.HYBRID: 0.10
        }
        
        # Performance tracking
        self.strategy_performance = {strategy: {"wins": 0, "losses": 0, "total_pnl": 0.0}
                                   for strategy in StrategyType}

        # Running state
        self.is_running = False
        self.initialized = False
        
        # Risk parameters
        self.max_positions_per_symbol = 1
        self.max_total_positions = 5
        self.risk_per_trade = 0.02  # 2% risk per trade
        
        self.logger.info("Strategy Manager initialized with advanced AI integration")

    async def initialize(self):
        """Initialize the strategy manager"""
        try:
            self.logger.info("Initializing Strategy Manager...")

            # Initialize crawlers if database manager is available
            if self.database_manager:
                try:
                    if MarketDataCrawler:
                        self.market_crawler = MarketDataCrawler(self.config, self.database_manager)
                except:
                    pass

                try:
                    if NewsSentimentCrawler:
                        self.news_crawler = NewsSentimentCrawler(self.config, self.database_manager)
                except:
                    pass

                try:
                    if SocialSentimentCrawler:
                        self.social_crawler = SocialSentimentCrawler(self.config, self.database_manager)
                except:
                    pass

                try:
                    if EconomicDataCrawler:
                        self.economic_crawler = EconomicDataCrawler(self.config, self.database_manager)
                except:
                    pass

            self.initialized = True
            self.logger.info("Strategy Manager initialized")

        except Exception as e:
            self.logger.error(f"Failed to initialize Strategy Manager: {e}")
            raise

    async def generate_signals(self, symbols: List[str]) -> List[TradingSignal]:
        """Generate comprehensive trading signals for multiple symbols"""
        try:
            signals = []
            
            # Update all data sources
            await self._update_all_data_sources(symbols)
            
            # Detect market regime
            await self._detect_market_regime()
            
            for symbol in symbols:
                try:
                    # Generate signals from each strategy
                    symbol_signals = await self._generate_symbol_signals(symbol)
                    signals.extend(symbol_signals)
                    
                except Exception as e:
                    self.logger.error(f"Error generating signals for {symbol}: {e}")
            
            # Filter and rank signals
            filtered_signals = await self._filter_and_rank_signals(signals)
            
            self.logger.info(f"Generated {len(filtered_signals)} high-quality signals")
            return filtered_signals

        except Exception as e:
            self.logger.error(f"Error generating signals: {e}")
            return []

    async def start(self):
        """Start the strategy manager"""
        try:
            self.logger.info("Starting Strategy Manager...")
            self.is_running = True

            # Initialize data sources if not already done
            if not getattr(self, 'initialized', False):
                await self.initialize()

            self.logger.info("SUCCESS: Strategy Manager started")

        except Exception as e:
            self.logger.error(f"ERROR: Failed to start Strategy Manager: {e}")
            raise

    async def stop(self):
        """Stop the strategy manager"""
        try:
            self.logger.info("Stopping Strategy Manager...")
            self.is_running = False
            self.logger.info("SUCCESS: Strategy Manager stopped")

        except Exception as e:
            self.logger.error(f"ERROR: Failed to stop Strategy Manager: {e}")
            raise
    
    async def _update_all_data_sources(self, symbols: List[str]):
        """Update all data sources in parallel"""
        try:
            tasks = []
            
            # Market data
            for symbol in symbols:
                tasks.append(self._update_market_data(symbol))
            
            # Sentiment and news data
            tasks.append(self._update_sentiment_data())
            tasks.append(self._update_news_data())
            tasks.append(self._update_economic_data())
            
            # ML predictions
            for symbol in symbols:
                tasks.append(self._update_ml_predictions(symbol))
            
            await asyncio.gather(*tasks, return_exceptions=True)
            
        except Exception as e:
            self.logger.error(f"Error updating data sources: {e}")
    
    async def _update_market_data(self, symbol: str):
        """Update market data for a symbol"""
        try:
            if self.market_crawler and hasattr(self.market_crawler, 'get_comprehensive_market_data'):
                data = await self.market_crawler.get_comprehensive_market_data(symbol)
                self.market_data_cache[symbol] = data
            else:
                # NO FAKE DATA ALLOWED - Get real market data from Bybit client
                if self.bybit_client:
                    try:
                        # Get real market data from Bybit
                        ticker = await self.bybit_client.get_ticker(symbol)
                        # Get klines data for technical analysis
                        klines = await self.bybit_client.get_klines(symbol, "1m", 50)

                        if ticker and klines and len(klines) > 0:
                            current_price = float(ticker.get('lastPrice', 0))
                            volume = float(ticker.get('volume24h', 0))

                            # Calculate real technical indicators from live data
                            prices = [float(k[4]) for k in klines]  # Close prices
                            volumes = [float(k[5]) for k in klines]  # Volumes

                            if len(prices) >= 20:
                                # Real RSI calculation
                                rsi = self._calculate_rsi(prices)
                                # Real EMA calculation
                                ema_20 = self._calculate_ema(prices, 20)
                                ema_50 = self._calculate_ema(prices, 50) if len(prices) >= 50 else ema_20
                                # Real Bollinger Bands
                                bb_upper, bb_middle, bb_lower = self._calculate_bollinger_bands(prices)
                                # Real volatility
                                volatility = self._calculate_volatility(prices)

                                self.market_data_cache[symbol] = {
                                    'current_price': current_price,
                                    'volume_ratio': volume / max(volumes) if volumes else 1.0,
                                    'technical_indicators': {
                                        'rsi': rsi,
                                        'macd': {'signal': self._calculate_macd(prices)},
                                        'bollinger_upper': bb_upper,
                                        'bollinger_lower': bb_lower,
                                        'bollinger_middle': bb_middle,
                                        'ema_20': ema_20,
                                        'ema_50': ema_50,
                                        'trend_strength': abs(ema_20 - ema_50) / current_price if current_price > 0 else 0
                                    },
                                    'volatility': volatility
                                }
                            else:
                                self.logger.error(f"Insufficient real data for {symbol} - got {len(prices)} prices, need at least 20")
                                # Use fallback data to prevent complete failure
                                if ticker:
                                    current_price = float(ticker.get('lastPrice', 0))
                                    if current_price > 0:
                                        self.market_data_cache[symbol] = {
                                            'current_price': current_price,
                                            'volume_ratio': 1.0,
                                            'technical_indicators': {
                                                'rsi': 50,  # Neutral RSI
                                                'macd': {'signal': 0},
                                                'bollinger_upper': current_price * 1.02,
                                                'bollinger_lower': current_price * 0.98,
                                                'bollinger_middle': current_price,
                                                'ema_20': current_price,
                                                'ema_50': current_price,
                                                'trend_strength': 0
                                            },
                                            'volatility': 0.02
                                        }
                                return
                        else:
                            self.logger.error(f"Failed to get real market data for {symbol} - ticker: {bool(ticker)}, klines: {len(klines) if klines else 0}")
                            # Try to get at least basic price data
                            if ticker:
                                current_price = float(ticker.get('lastPrice', 0))
                                if current_price > 0:
                                    # Use fallback data with basic indicators
                                    self.market_data_cache[symbol] = {
                                        'current_price': current_price,
                                        'volume_ratio': 1.0,
                                        'technical_indicators': {
                                            'rsi': 50,  # Neutral RSI
                                            'macd': {'signal': 0},
                                            'bollinger_upper': current_price * 1.02,
                                            'bollinger_lower': current_price * 0.98,
                                            'bollinger_middle': current_price,
                                            'ema_20': current_price,
                                            'ema_50': current_price,
                                            'trend_strength': 0
                                        },
                                        'volatility': 0.02
                                    }
                                    self.logger.info(f"Using fallback data for {symbol} with price {current_price}")
                            return
                    except Exception as e:
                        self.logger.error(f"Error getting real market data for {symbol}: {e}")
                        return
                else:
                    self.logger.error("No Bybit client available for real market data")
                    return

        except Exception as e:
            self.logger.error(f"Error updating market data for {symbol}: {e}")

    async def _update_sentiment_data(self):
        """Update social sentiment data"""
        try:
            if self.social_crawler and hasattr(self.social_crawler, 'get_comprehensive_sentiment'):
                sentiment_data = await self.social_crawler.get_comprehensive_sentiment()
                self.sentiment_data_cache = sentiment_data
            else:
                # Fallback: create mock sentiment data
                self.sentiment_data_cache = {
                    'overall_sentiment': 0.5,
                    'sentiment_strength': 0.5,
                    'trend_change': 0.0
                }

        except Exception as e:
            self.logger.error(f"Error updating sentiment data: {e}")

    async def _update_news_data(self):
        """Update news sentiment data"""
        try:
            if self.news_crawler and hasattr(self.news_crawler, 'get_comprehensive_news_analysis'):
                news_data = await self.news_crawler.get_comprehensive_news_analysis()
                self.news_data_cache = news_data
            else:
                # Fallback: create mock news data
                self.news_data_cache = {
                    'overall_sentiment': 0.5,
                    'news_impact': 0.3,
                    'breaking_news_detected': False
                }

        except Exception as e:
            self.logger.error(f"Error updating news data: {e}")

    async def _update_economic_data(self):
        """Update economic data"""
        try:
            if self.economic_crawler and hasattr(self.economic_crawler, 'get_comprehensive_economic_data'):
                economic_data = await self.economic_crawler.get_comprehensive_economic_data()
                self.economic_data_cache = economic_data
            else:
                # Fallback: create mock economic data
                self.economic_data_cache = {
                    'economic_indicators': {},
                    'market_conditions': 'neutral'
                }

        except Exception as e:
            self.logger.error(f"Error updating economic data: {e}")

    async def _update_ml_predictions(self, symbol: str):
        """Update ML predictions for a symbol"""
        try:
            if self.market_predictor and hasattr(self.market_predictor, 'predict_price_movement') and symbol in self.market_data_cache:
                predictions = await self.market_predictor.predict_price_movement(
                    symbol, self.market_data_cache[symbol]
                )
                self.ml_predictions_cache[symbol] = predictions
            else:
                # NO FAKE DATA - Skip if no real ML predictor available
                self.logger.warning(f"No ML predictor available for {symbol} - skipping predictions")
                return

        except Exception as e:
            self.logger.error(f"Error updating ML predictions for {symbol}: {e}")
    
    async def _detect_market_regime(self):
        """Detect current market regime based on all available data"""
        try:
            # Analyze volatility
            volatility_scores = []
            trend_scores = []
            
            for symbol in self.trading_symbols:
                if symbol in self.market_data_cache:
                    data = self.market_data_cache[symbol]
                    
                    # Calculate volatility
                    if 'volatility' in data:
                        volatility_scores.append(data['volatility'])
                    
                    # Calculate trend strength
                    if 'technical_indicators' in data:
                        indicators = data['technical_indicators']
                        if 'trend_strength' in indicators:
                            trend_scores.append(indicators['trend_strength'])
            
            avg_volatility = np.mean(volatility_scores) if volatility_scores else 0.5
            avg_trend = np.mean(trend_scores) if trend_scores else 0.5
            
            # Check news impact
            news_impact = 0.0
            if 'overall_sentiment' in self.news_data_cache:
                news_impact = abs(self.news_data_cache['overall_sentiment'] - 0.5) * 2
            
            # Check social sentiment
            sentiment_impact = 0.0
            if 'overall_sentiment' in self.sentiment_data_cache:
                sentiment_impact = abs(self.sentiment_data_cache['overall_sentiment'] - 0.5) * 2
            
            # Determine regime
            if news_impact > 0.7:
                self.current_regime = MarketRegime.NEWS_DRIVEN
            elif sentiment_impact > 0.7:
                self.current_regime = MarketRegime.SENTIMENT_DRIVEN
            elif avg_volatility > 0.7:
                self.current_regime = MarketRegime.HIGH_VOLATILITY
            elif avg_volatility < 0.3:
                self.current_regime = MarketRegime.LOW_VOLATILITY
            elif avg_trend > 0.6:
                self.current_regime = MarketRegime.TRENDING_BULL if avg_trend > 0 else MarketRegime.TRENDING_BEAR
            else:
                self.current_regime = MarketRegime.RANGING
            
            self.logger.debug(f"Market regime detected: {self.current_regime.value}")
            
        except Exception as e:
            self.logger.error(f"Error detecting market regime: {e}")
    
    async def _generate_symbol_signals(self, symbol: str) -> List[TradingSignal]:
        """Generate signals for a specific symbol using all strategies"""
        signals = []
        
        try:
            # Get cached data
            market_data = self.market_data_cache.get(symbol, {})
            ml_predictions = self.ml_predictions_cache.get(symbol, {})
            
            # Generate signals from each active strategy
            if self.active_strategies.get(StrategyType.MOMENTUM):
                momentum_signal = await self._generate_momentum_signal(symbol, market_data)
                if momentum_signal:
                    signals.append(momentum_signal)
            
            if self.active_strategies.get(StrategyType.MEAN_REVERSION):
                reversion_signal = await self._generate_mean_reversion_signal(symbol, market_data)
                if reversion_signal:
                    signals.append(reversion_signal)
            
            if self.active_strategies.get(StrategyType.TREND_FOLLOWING):
                trend_signal = await self._generate_trend_following_signal(symbol, market_data)
                if trend_signal:
                    signals.append(trend_signal)
            
            if self.active_strategies.get(StrategyType.ML_PREDICTION):
                ml_signal = await self._generate_ml_signal(symbol, ml_predictions)
                if ml_signal:
                    signals.append(ml_signal)
            
            if self.active_strategies.get(StrategyType.SENTIMENT_TRADING):
                sentiment_signal = await self._generate_sentiment_signal(symbol)
                if sentiment_signal:
                    signals.append(sentiment_signal)
            
            if self.active_strategies.get(StrategyType.NEWS_TRADING):
                news_signal = await self._generate_news_signal(symbol)
                if news_signal:
                    signals.append(news_signal)

            # MARGIN TRADING STRATEGIES - Get account info for margin-aware strategies
            account_info = {}
            if self.bybit_client:
                try:
                    account_info = await self.bybit_client.get_account_info()
                except Exception as e:
                    self.logger.warning(f"Could not get account info for margin strategies: {e}")

            if account_info and self.active_strategies.get(StrategyType.MARGIN_ARBITRAGE):
                margin_arb_signal = await self._generate_margin_arbitrage_signal(symbol, market_data, account_info)
                if margin_arb_signal:
                    signals.append(margin_arb_signal)

            if account_info and self.active_strategies.get(StrategyType.LEVERAGE_MOMENTUM):
                leverage_momentum_signal = await self._generate_leverage_momentum_signal(symbol, market_data, account_info)
                if leverage_momentum_signal:
                    signals.append(leverage_momentum_signal)

            if account_info and self.active_strategies.get(StrategyType.MARGIN_SCALPING):
                margin_scalp_signal = await self._generate_margin_scalping_signal(symbol, market_data, account_info)
                if margin_scalp_signal:
                    signals.append(margin_scalp_signal)

            if account_info and self.active_strategies.get(StrategyType.CROSS_MARGIN_STRATEGY):
                cross_margin_signal = await self._generate_cross_margin_strategy_signal(symbol, market_data, account_info)
                if cross_margin_signal:
                    signals.append(cross_margin_signal)

            # Generate hybrid signal combining multiple strategies
            hybrid_signal = await self._generate_hybrid_signal(symbol, signals)
            if hybrid_signal:
                signals.append(hybrid_signal)
            
        except Exception as e:
            self.logger.error(f"Error generating signals for {symbol}: {e}")
        
        return signals
    
    async def _generate_momentum_signal(self, symbol: str, market_data: Dict) -> Optional[TradingSignal]:
        """Generate momentum-based trading signal"""
        try:
            if not market_data or 'technical_indicators' not in market_data:
                return None
            
            indicators = market_data['technical_indicators']
            current_price = market_data.get('current_price', 0)
            
            # Momentum indicators
            rsi = indicators.get('rsi', 50)
            macd = indicators.get('macd', {})
            macd_signal = macd.get('signal', 0) if isinstance(macd, dict) else 0
            
            # Generate signal
            signal_strength = 0.0
            action = "HOLD"
            
            # RSI momentum
            if rsi > 70:
                signal_strength += 0.3  # Overbought momentum
                action = "SELL"
            elif rsi < 30:
                signal_strength += 0.3  # Oversold momentum
                action = "BUY"
            
            # MACD momentum
            if macd_signal > 0:
                signal_strength += 0.4
                action = "BUY" if action != "SELL" else action
            elif macd_signal < 0:
                signal_strength += 0.4
                action = "SELL" if action != "BUY" else action
            
            # Volume confirmation
            volume_ratio = market_data.get('volume_ratio', 1.0)
            if volume_ratio > 1.5:  # High volume confirmation
                signal_strength += 0.3
            
            if signal_strength > 0.6 and action != "HOLD":
                confidence = min(signal_strength, 1.0)
                
                return TradingSignal(
                    symbol=symbol,
                    action=action,
                    strength=signal_strength,
                    confidence=confidence,
                    strategy="momentum",
                    entry_price=current_price,
                    stop_loss=None,  # Will be calculated by risk manager
                    take_profit=None,  # Will be calculated by risk manager
                    reason=f"Momentum signal: RSI={rsi:.1f}, MACD={macd_signal:.3f}",
                    timestamp=datetime.now(timezone.utc),
                    risk_score=1.0 - confidence,
                    expected_holding_time=30,  # 30 minutes for momentum
                    sentiment_score=None,
                    news_impact=None,
                    technical_score=signal_strength,
                    ml_prediction=None
                )
            
        except Exception as e:
            self.logger.error(f"Error generating momentum signal for {symbol}: {e}")
        
        return None
    
    async def _generate_mean_reversion_signal(self, symbol: str, market_data: Dict) -> Optional[TradingSignal]:
        """Generate mean reversion trading signal"""
        try:
            if not market_data or 'technical_indicators' not in market_data:
                return None
            
            indicators = market_data['technical_indicators']
            current_price = market_data.get('current_price', 0)
            
            # Mean reversion indicators
            bb_upper = indicators.get('bollinger_upper', 0)
            bb_lower = indicators.get('bollinger_lower', 0)
            bb_middle = indicators.get('bollinger_middle', 0)
            rsi = indicators.get('rsi', 50)
            
            if not all([bb_upper, bb_lower, bb_middle]):
                return None
            
            signal_strength = 0.0
            action = "HOLD"
            
            # Bollinger Bands mean reversion
            if current_price > bb_upper:
                signal_strength += 0.5  # Price above upper band - sell
                action = "SELL"
            elif current_price < bb_lower:
                signal_strength += 0.5  # Price below lower band - buy
                action = "BUY"
            
            # RSI confirmation
            if action == "BUY" and rsi < 35:
                signal_strength += 0.3
            elif action == "SELL" and rsi > 65:
                signal_strength += 0.3
            
            # Additional confirmation from price distance
            price_distance = abs(current_price - bb_middle) / bb_middle
            if price_distance > 0.02:  # 2% from middle
                signal_strength += 0.2
            
            if signal_strength > 0.7 and action != "HOLD":
                confidence = min(signal_strength, 1.0)
                
                return TradingSignal(
                    symbol=symbol,
                    action=action,
                    strength=signal_strength,
                    confidence=confidence,
                    strategy="mean_reversion",
                    entry_price=current_price,
                    stop_loss=None,
                    take_profit=bb_middle,  # Target middle band
                    reason=f"Mean reversion: Price={current_price:.2f}, BB_Middle={bb_middle:.2f}",
                    timestamp=datetime.now(timezone.utc),
                    risk_score=1.0 - confidence,
                    expected_holding_time=60,  # 1 hour for mean reversion
                    sentiment_score=None,
                    news_impact=None,
                    technical_score=signal_strength,
                    ml_prediction=None
                )
            
        except Exception as e:
            self.logger.error(f"Error generating mean reversion signal for {symbol}: {e}")
        
        return None
    
    async def _generate_trend_following_signal(self, symbol: str, market_data: Dict) -> Optional[TradingSignal]:
        """Generate trend following trading signal"""
        try:
            if not market_data or 'technical_indicators' not in market_data:
                return None
            
            indicators = market_data['technical_indicators']
            current_price = market_data.get('current_price', 0)
            
            # Trend indicators
            ema_20 = indicators.get('ema_20', 0)
            ema_50 = indicators.get('ema_50', 0)
            trend_strength = indicators.get('trend_strength', 0)
            
            signal_strength = 0.0
            action = "HOLD"
            
            # EMA crossover
            if ema_20 > ema_50 and current_price > ema_20:
                signal_strength += 0.4  # Uptrend
                action = "BUY"
            elif ema_20 < ema_50 and current_price < ema_20:
                signal_strength += 0.4  # Downtrend
                action = "SELL"
            
            # Trend strength confirmation
            if abs(trend_strength) > 0.6:
                signal_strength += 0.3
                if trend_strength > 0 and action != "SELL":
                    action = "BUY"
                elif trend_strength < 0 and action != "BUY":
                    action = "SELL"
            
            # Volume confirmation
            volume_ratio = market_data.get('volume_ratio', 1.0)
            if volume_ratio > 1.2:
                signal_strength += 0.3
            
            if signal_strength > 0.6 and action != "HOLD":
                confidence = min(signal_strength, 1.0)
                
                return TradingSignal(
                    symbol=symbol,
                    action=action,
                    strength=signal_strength,
                    confidence=confidence,
                    strategy="trend_following",
                    entry_price=current_price,
                    stop_loss=None,
                    take_profit=None,
                    reason=f"Trend following: EMA20={ema_20:.2f}, EMA50={ema_50:.2f}, Strength={trend_strength:.2f}",
                    timestamp=datetime.now(timezone.utc),
                    risk_score=1.0 - confidence,
                    expected_holding_time=120,  # 2 hours for trend following
                    sentiment_score=None,
                    news_impact=None,
                    technical_score=signal_strength,
                    ml_prediction=None
                )
            
        except Exception as e:
            self.logger.error(f"Error generating trend following signal for {symbol}: {e}")
        
        return None
    
    async def _generate_ml_signal(self, symbol: str, ml_predictions: Dict) -> Optional[TradingSignal]:
        """Generate ML-based trading signal"""
        try:
            if not ml_predictions:
                return None
            
            # Extract ML predictions
            price_prediction = ml_predictions.get('price_prediction', {})
            direction_prob = ml_predictions.get('direction_probability', {})
            confidence = ml_predictions.get('confidence', 0.5)
            
            if not price_prediction or not direction_prob:
                return None
            
            # Determine signal based on ML predictions
            up_prob = direction_prob.get('up', 0.5)
            down_prob = direction_prob.get('down', 0.5)
            
            signal_strength = max(up_prob, down_prob)
            action = "BUY" if up_prob > down_prob else "SELL"
            
            # Only generate signal if confidence is high enough
            if signal_strength > 0.65 and confidence > 0.6:
                return TradingSignal(
                    symbol=symbol,
                    action=action,
                    strength=signal_strength,
                    confidence=confidence,
                    strategy="ml_prediction",
                    entry_price=price_prediction.get('current_price', 0),
                    stop_loss=None,
                    take_profit=price_prediction.get('target_price'),
                    reason=f"ML prediction: {action} probability={signal_strength:.2f}, confidence={confidence:.2f}",
                    timestamp=datetime.now(timezone.utc),
                    risk_score=1.0 - confidence,
                    expected_holding_time=ml_predictions.get('holding_time', 60),
                    sentiment_score=None,
                    news_impact=None,
                    technical_score=None,
                    ml_prediction=ml_predictions
                )
            
        except Exception as e:
            self.logger.error(f"Error generating ML signal for {symbol}: {e}")
        
        return None
    
    async def _generate_sentiment_signal(self, symbol: str) -> Optional[TradingSignal]:
        """Generate sentiment-based trading signal"""
        try:
            if not self.sentiment_data_cache:
                return None
            
            # Get sentiment scores
            overall_sentiment = self.sentiment_data_cache.get('overall_sentiment', 0.5)
            sentiment_strength = self.sentiment_data_cache.get('sentiment_strength', 0.0)
            trend_change = self.sentiment_data_cache.get('trend_change', 0.0)
            
            # Generate signal based on sentiment
            signal_strength = sentiment_strength
            action = "HOLD"
            
            if overall_sentiment > 0.7 and sentiment_strength > 0.6:
                action = "BUY"
            elif overall_sentiment < 0.3 and sentiment_strength > 0.6:
                action = "SELL"
            
            # Boost signal if sentiment is changing rapidly
            if abs(trend_change) > 0.3:
                signal_strength += 0.2
            
            if signal_strength > 0.6 and action != "HOLD":
                confidence = min(signal_strength, 1.0)
                
                return TradingSignal(
                    symbol=symbol,
                    action=action,
                    strength=signal_strength,
                    confidence=confidence,
                    strategy="sentiment_trading",
                    entry_price=None,
                    stop_loss=None,
                    take_profit=None,
                    reason=f"Sentiment: {overall_sentiment:.2f}, strength={sentiment_strength:.2f}",
                    timestamp=datetime.now(timezone.utc),
                    risk_score=1.0 - confidence,
                    expected_holding_time=45,
                    sentiment_score=overall_sentiment,
                    news_impact=None,
                    technical_score=None,
                    ml_prediction=None
                )
            
        except Exception as e:
            self.logger.error(f"Error generating sentiment signal for {symbol}: {e}")
        
        return None
    
    async def _generate_news_signal(self, symbol: str) -> Optional[TradingSignal]:
        """Generate news-based trading signal"""
        try:
            if not self.news_data_cache:
                return None
            
            # Get news impact scores
            overall_sentiment = self.news_data_cache.get('overall_sentiment', 0.5)
            news_impact = self.news_data_cache.get('news_impact', 0.0)
            breaking_news = self.news_data_cache.get('breaking_news_detected', False)
            
            signal_strength = news_impact
            action = "HOLD"
            
            # Strong news sentiment
            if overall_sentiment > 0.75 and news_impact > 0.7:
                action = "BUY"
            elif overall_sentiment < 0.25 and news_impact > 0.7:
                action = "SELL"
            
            # Breaking news boost
            if breaking_news:
                signal_strength += 0.3
            
            if signal_strength > 0.7 and action != "HOLD":
                confidence = min(signal_strength, 1.0)
                
                return TradingSignal(
                    symbol=symbol,
                    action=action,
                    strength=signal_strength,
                    confidence=confidence,
                    strategy="news_trading",
                    entry_price=None,
                    stop_loss=None,
                    take_profit=None,
                    reason=f"News impact: sentiment={overall_sentiment:.2f}, impact={news_impact:.2f}",
                    timestamp=datetime.now(timezone.utc),
                    risk_score=1.0 - confidence,
                    expected_holding_time=30,  # Quick news reaction
                    sentiment_score=None,
                    news_impact=news_impact,
                    technical_score=None,
                    ml_prediction=None
                )
            
        except Exception as e:
            self.logger.error(f"Error generating news signal for {symbol}: {e}")
        
        return None
    
    async def _generate_hybrid_signal(self, symbol: str, individual_signals: List[TradingSignal]) -> Optional[TradingSignal]:
        """Generate hybrid signal combining multiple strategies"""
        try:
            if len(individual_signals) < 2:
                return None
            
            # Aggregate signals by action
            buy_signals = [s for s in individual_signals if s.action == "BUY"]
            sell_signals = [s for s in individual_signals if s.action == "SELL"]
            
            # Calculate weighted scores
            buy_score = sum(s.strength * self.strategy_weights.get(StrategyType(s.strategy), 0.1) 
                           for s in buy_signals)
            sell_score = sum(s.strength * self.strategy_weights.get(StrategyType(s.strategy), 0.1) 
                            for s in sell_signals)
            
            # Determine hybrid action
            if buy_score > sell_score and buy_score > 0.7:
                action = "BUY"
                signal_strength = buy_score
                best_signals = buy_signals
            elif sell_score > buy_score and sell_score > 0.7:
                action = "SELL"
                signal_strength = sell_score
                best_signals = sell_signals
            else:
                return None
            
            # Calculate composite confidence
            confidence = sum(s.confidence for s in best_signals) / len(best_signals) if best_signals else 0.5
            
            # Combine reasons
            reasons = [f"{s.strategy}({s.strength:.2f})" for s in best_signals]
            
            return TradingSignal(
                symbol=symbol,
                action=action,
                strength=min(signal_strength, 1.0),
                confidence=confidence,
                strategy="hybrid",
                entry_price=None,
                stop_loss=None,
                take_profit=None,
                reason=f"Hybrid signal: {', '.join(reasons)}",
                timestamp=datetime.now(timezone.utc),
                risk_score=1.0 - confidence,
                expected_holding_time=int(sum(s.expected_holding_time or 60 for s in best_signals) / len(best_signals)) if best_signals else 60,
                sentiment_score=sum(s.sentiment_score for s in best_signals if s.sentiment_score) / max(1, len([s for s in best_signals if s.sentiment_score])),
                news_impact=sum(s.news_impact for s in best_signals if s.news_impact) / max(1, len([s for s in best_signals if s.news_impact])),
                technical_score=sum(s.technical_score for s in best_signals if s.technical_score) / max(1, len([s for s in best_signals if s.technical_score])),
                ml_prediction=None
            )
            
        except Exception as e:
            self.logger.error(f"Error generating hybrid signal for {symbol}: {e}")
        
        return None

    # SOPHISTICATED MARGIN TRADING STRATEGIES
    def _safe_float_conversion(self, value: Any, default: float = 0.0) -> float:
        """Safely convert value to float"""
        if value is None:
            return default

        try:
            if isinstance(value, (int, float)):
                return float(value)
            elif isinstance(value, str):
                # Remove any non-numeric characters except decimal point and minus
                cleaned = ''.join(c for c in value if c.isdigit() or c in '.-')
                if cleaned and cleaned != '-' and cleaned != '.':
                    return float(cleaned)
                return default
            else:
                return float(value)
        except (ValueError, TypeError):
            return default

    async def _generate_margin_arbitrage_signal(self, symbol: str, market_data: Dict, account_info: Dict) -> Optional[TradingSignal]:
        """Advanced margin arbitrage strategy - exploit funding rate differences"""
        try:
            if not self.bybit_client:
                return None

            # Safe price extraction - try multiple keys
            current_price = self._safe_float_conversion(
                market_data.get('current_price') or market_data.get('price') or market_data.get('close', 0)
            )
            margin_ratio = self._safe_float_conversion(account_info.get('marginRatio', 0))

            # Get funding rate data
            funding_rate = await self._get_funding_rate(symbol)
            if not funding_rate:
                return None

            # Calculate optimal leverage based on margin ratio
            max_leverage = self._calculate_safe_leverage(symbol, margin_ratio)

            # MEMORY-BASED OPTIMIZATION: Get historical performance for this strategy
            memory_performance = None
            if self.advanced_memory:
                try:
                    memory_performance = await self.get_time_aware_strategy_performance('margin_arbitrage')
                except Exception as e:
                    self.logger.warning(f"Could not get memory performance for margin arbitrage: {e}")

            # Adjust threshold based on memory performance
            funding_threshold = 0.0001  # Default threshold
            if memory_performance:
                success_rate = memory_performance.get('success_rate', 0.5)
                if success_rate > 0.7:
                    funding_threshold *= 0.8  # Lower threshold for successful strategy
                elif success_rate < 0.3:
                    funding_threshold *= 1.5  # Higher threshold for poor performance

            # Funding rate arbitrage opportunity with memory enhancement
            if abs(funding_rate) > funding_threshold:
                action = 'sell' if funding_rate > 0 else 'buy'  # Opposite to funding direction

                # MEMORY-ENHANCED LEVERAGE: Adjust leverage based on historical performance
                if memory_performance:
                    avg_profit = memory_performance.get('avg_profit', 0)
                    if avg_profit > 50:  # Good historical performance
                        max_leverage = min(max_leverage * 1.2, self.max_leverage.get(symbol, 50))
                    elif avg_profit < 0:  # Poor performance
                        max_leverage = max(max_leverage * 0.7, 1)

                # MEMORY-BASED CONFIDENCE: Blend base confidence with historical success
                base_confidence = min(0.9, abs(funding_rate) * 10000)
                confidence = base_confidence
                if memory_performance:
                    success_rate = memory_performance.get('success_rate', 0.5)
                    confidence = base_confidence * (0.5 + success_rate * 0.5)

                # Calculate position size with memory-enhanced risk level
                risk_level = 'conservative'
                if memory_performance and memory_performance.get('success_rate', 0) > 0.8:
                    risk_level = 'moderate'  # More aggressive if historically successful

                base_position = self._calculate_margin_position_size(account_info, margin_ratio, risk_level)
                leveraged_position = base_position * max_leverage

                expected_return = abs(funding_rate) * max_leverage  # Leveraged funding return

                return TradingSignal(
                    symbol=symbol,
                    action=action.upper(),
                    strength=min(abs(funding_rate) * 1000, 1.0),
                    confidence=confidence,
                    strategy="margin_arbitrage",
                    entry_price=market_data.get('current_price', 0.0),
                    stop_loss=None,
                    take_profit=None,
                    reason=f"Funding arbitrage - rate: {funding_rate:.6f}, leverage: {max_leverage}x",
                    timestamp=datetime.now(timezone.utc),
                    risk_score=1.0 - confidence,
                    expected_holding_time=480,  # 8 hours for funding cycle
                    sentiment_score=None,
                    news_impact=None,
                    technical_score=None,
                    ml_prediction=None
                )

        except Exception as e:
            self.logger.error(f"Error in margin arbitrage strategy: {e}")

        return None

    async def _generate_leverage_momentum_signal(self, symbol: str, market_data: Dict, account_info: Dict) -> Optional[TradingSignal]:
        """High-leverage momentum strategy with intelligent risk management"""
        try:
            if not market_data or 'technical_indicators' not in market_data:
                return None

            current_price = self._safe_float_conversion(
                market_data.get('current_price') or market_data.get('price') or market_data.get('close', 0)
            )
            margin_ratio = self._safe_float_conversion(account_info.get('marginRatio', 0))

            indicators = market_data['technical_indicators']

            # Multi-timeframe momentum analysis
            rsi = indicators.get('rsi', 50)
            macd = indicators.get('macd', 0)
            volume_ratio = indicators.get('volume_ratio', 1.0)

            # Calculate momentum strength
            momentum_strength = 0.0
            if rsi > 70 and macd > 0 and volume_ratio > 1.5:
                momentum_strength = min(1.0, (rsi - 70) / 30 + abs(macd) * 10 + (volume_ratio - 1.0))
                action = 'buy'
            elif rsi < 30 and macd < 0 and volume_ratio > 1.5:
                momentum_strength = min(1.0, (30 - rsi) / 30 + abs(macd) * 10 + (volume_ratio - 1.0))
                action = 'sell'
            else:
                return None

            # MEMORY-BASED OPTIMIZATION: Get historical performance for leverage momentum
            memory_performance = None
            if self.advanced_memory:
                try:
                    memory_performance = await self.get_time_aware_strategy_performance('leverage_momentum')
                except Exception as e:
                    self.logger.warning(f"Could not get memory performance for leverage momentum: {e}")

            # Dynamic leverage based on momentum strength, margin safety, and memory
            base_leverage = self._calculate_safe_leverage(symbol, margin_ratio)
            momentum_leverage = int(base_leverage * momentum_strength)

            # MEMORY ENHANCEMENT: Adjust leverage based on historical performance
            if memory_performance:
                success_rate = memory_performance.get('success_rate', 0.5)
                avg_profit = memory_performance.get('avg_profit', 0)

                if success_rate > 0.75 and avg_profit > 30:
                    # Excellent historical performance - increase leverage
                    momentum_leverage = int(momentum_leverage * 1.3)
                elif success_rate < 0.4 or avg_profit < 0:
                    # Poor historical performance - reduce leverage
                    momentum_leverage = int(momentum_leverage * 0.6)

            momentum_leverage = max(1, min(momentum_leverage, self.max_leverage.get(symbol, 50)))

            # Position sizing with memory-enhanced risk assessment
            risk_level = 'moderate'
            if memory_performance:
                success_rate = memory_performance.get('success_rate', 0.5)
                if success_rate > 0.8:
                    risk_level = 'aggressive'  # More aggressive if historically very successful
                elif success_rate < 0.4:
                    risk_level = 'conservative'  # More conservative if poor performance

            base_position = self._calculate_margin_position_size(account_info, margin_ratio, risk_level)
            leveraged_position = base_position * momentum_leverage

            # MEMORY-ENHANCED CONFIDENCE: Blend momentum confidence with historical success
            base_confidence = momentum_strength * 0.8
            confidence = base_confidence
            if memory_performance:
                success_rate = memory_performance.get('success_rate', 0.5)
                confidence = base_confidence * (0.6 + success_rate * 0.4)  # Blend with historical success

            expected_return = momentum_strength * 0.03 * momentum_leverage  # 3% base return with leverage

            return TradingSignal(
                symbol=symbol,
                action=action.upper(),
                strength=momentum_strength,
                confidence=confidence,
                strategy="leverage_momentum",
                entry_price=current_price,
                stop_loss=None,
                take_profit=None,
                reason=f"Leverage momentum - RSI: {rsi:.1f}, MACD: {macd:.4f}, Volume: {volume_ratio:.2f}x",
                timestamp=datetime.now(timezone.utc),
                risk_score=1.0 - confidence,
                expected_holding_time=90,  # 1.5 hours for momentum
                sentiment_score=None,
                news_impact=None,
                technical_score=momentum_strength,
                ml_prediction=None
            )

        except Exception as e:
            self.logger.error(f"Error in leverage momentum strategy: {e}")

        return None

    async def _generate_margin_scalping_signal(self, symbol: str, market_data: Dict, account_info: Dict) -> Optional[TradingSignal]:
        """Ultra-fast margin scalping with high leverage"""
        try:
            if not market_data:
                return None

            current_price = self._safe_float_conversion(
                market_data.get('current_price') or market_data.get('price') or market_data.get('close', 0)
            )
            margin_ratio = self._safe_float_conversion(account_info.get('marginRatio', 0))

            # Get recent price movements
            price_history = market_data.get('price_history', [])
            if len(price_history) < 5:
                return None

            # Micro-movement detection
            recent_changes = []
            for i in range(1, min(6, len(price_history))):
                change = (price_history[-i] - price_history[-i-1]) / price_history[-i-1]
                recent_changes.append(change)

            # Look for rapid price movements
            latest_change = recent_changes[0] if recent_changes else 0
            avg_change = sum(recent_changes) / len(recent_changes) if recent_changes else 0

            # MEMORY-BASED OPTIMIZATION: Get historical performance for margin scalping
            memory_performance = None
            if self.advanced_memory:
                try:
                    memory_performance = await self.get_time_aware_strategy_performance('margin_scalping')
                except Exception as e:
                    self.logger.warning(f"Could not get memory performance for margin scalping: {e}")

            # Adjust movement threshold based on memory performance
            movement_threshold = 0.0005  # Default threshold
            if memory_performance:
                success_rate = memory_performance.get('success_rate', 0.5)
                if success_rate > 0.8:
                    movement_threshold *= 0.7  # Lower threshold for successful scalping
                elif success_rate < 0.3:
                    movement_threshold *= 1.5  # Higher threshold for poor performance

            if abs(latest_change) > movement_threshold and abs(latest_change) > abs(avg_change) * 2:
                # Rapid movement detected - scalp in the direction
                action = 'buy' if latest_change > 0 else 'sell'

                # MEMORY-ENHANCED LEVERAGE: Adjust leverage based on historical performance
                scalp_leverage = min(self.max_leverage.get(symbol, 50), 75)  # Up to 75x for scalping
                if margin_ratio > 70:
                    scalp_leverage = min(scalp_leverage, 20)  # Reduce if high margin

                if memory_performance:
                    success_rate = memory_performance.get('success_rate', 0.5)
                    avg_profit = memory_performance.get('avg_profit', 0)

                    if success_rate > 0.85 and avg_profit > 20:
                        # Excellent scalping performance - increase leverage
                        scalp_leverage = int(min(scalp_leverage * 1.2, 100))
                    elif success_rate < 0.4 or avg_profit < 0:
                        # Poor scalping performance - reduce leverage significantly
                        scalp_leverage = int(max(scalp_leverage * 0.5, 5))

                # Position sizing with memory-enhanced risk assessment
                risk_level = 'aggressive'  # Default for scalping
                if memory_performance:
                    success_rate = memory_performance.get('success_rate', 0.5)
                    if success_rate < 0.5:
                        risk_level = 'moderate'  # Less aggressive if poor historical performance

                base_position = self._calculate_margin_position_size(account_info, margin_ratio, risk_level)
                leveraged_position = base_position * scalp_leverage

                # MEMORY-ENHANCED CONFIDENCE: Blend movement confidence with historical success
                base_confidence = min(0.8, abs(latest_change) * 1000)
                confidence = base_confidence
                if memory_performance:
                    success_rate = memory_performance.get('success_rate', 0.5)
                    confidence = base_confidence * (0.4 + success_rate * 0.6)  # Heavy weight on historical success

                expected_return = abs(latest_change) * scalp_leverage * 0.5  # Expect half the movement with leverage

                # Tight stops for scalping
                stop_distance = abs(latest_change) * 0.5
                stop_loss = current_price * (1.0 - stop_distance if action == 'buy' else 1.0 + stop_distance)
                take_profit = current_price * (1.0 + expected_return if action == 'buy' else 1.0 - expected_return)

                return TradingSignal(
                    symbol=symbol,
                    action=action,
                    strength=0.8,
                    confidence=confidence,
                    strategy="margin_scalping",
                    entry_price=current_price,
                    stop_loss=stop_loss,
                    take_profit=take_profit,
                    reason=f"Margin scalping opportunity - movement: {latest_change:.4f}",
                    timestamp=datetime.now(timezone.utc),
                    risk_score=0.3,
                    expected_holding_time=5,
                    sentiment_score=None,
                    news_impact=None,
                    technical_score=0.8,
                    ml_prediction=None
                )

        except Exception as e:
            self.logger.error(f"Error in margin scalping strategy: {e}")

        return None

    async def _generate_cross_margin_strategy_signal(self, symbol: str, market_data: Dict, account_info: Dict) -> Optional[TradingSignal]:
        """Cross-margin portfolio optimization strategy"""
        try:
            if not self.bybit_client:
                return None

            current_price = self._safe_float_conversion(
                market_data.get('current_price') or market_data.get('price') or market_data.get('close', 0)
            )
            margin_ratio = self._safe_float_conversion(account_info.get('marginRatio', 0))
            total_equity = self._safe_float_conversion(account_info.get('totalEquity', 0))

            # Get all positions for cross-margin analysis
            positions = await self._get_all_positions()
            if not positions:
                return None

            # Portfolio correlation analysis
            portfolio_risk = self._calculate_portfolio_risk(positions)
            diversification_score = self._calculate_diversification_score(positions, symbol)

            # MEMORY-BASED OPTIMIZATION: Get historical performance for cross-margin strategy
            memory_performance = None
            if self.advanced_memory:
                try:
                    memory_performance = await self.get_time_aware_strategy_performance('cross_margin_strategy')
                except Exception as e:
                    self.logger.warning(f"Could not get memory performance for cross-margin strategy: {e}")

            # Adjust thresholds based on memory performance
            diversification_threshold = 0.6  # Default threshold
            risk_threshold = 0.7  # Default risk threshold
            if memory_performance:
                success_rate = memory_performance.get('success_rate', 0.5)
                if success_rate > 0.75:
                    diversification_threshold *= 0.85  # Lower threshold for successful strategy
                    risk_threshold *= 1.1  # Allow slightly higher risk
                elif success_rate < 0.4:
                    diversification_threshold *= 1.2  # Higher threshold for poor performance
                    risk_threshold *= 0.8  # Lower risk tolerance

            # Cross-margin opportunity with memory enhancement
            if diversification_score > diversification_threshold and portfolio_risk < risk_threshold:
                # Good diversification opportunity

                # Calculate optimal leverage for portfolio balance with memory enhancement
                portfolio_leverage = self._calculate_portfolio_optimal_leverage(positions, symbol, margin_ratio)

                # MEMORY ENHANCEMENT: Adjust leverage based on historical performance
                if memory_performance:
                    success_rate = memory_performance.get('success_rate', 0.5)
                    avg_profit = memory_performance.get('avg_profit', 0)

                    if success_rate > 0.8 and avg_profit > 40:
                        # Excellent cross-margin performance - increase leverage
                        portfolio_leverage = int(min(portfolio_leverage * 1.25, self.max_leverage.get(symbol, 50)))
                    elif success_rate < 0.4 or avg_profit < 0:
                        # Poor cross-margin performance - reduce leverage
                        portfolio_leverage = int(max(portfolio_leverage * 0.7, 1))

                # Determine direction based on portfolio balance
                current_exposure = self._calculate_symbol_exposure(positions, symbol)
                market_direction = self._determine_market_direction(market_data)

                # MEMORY-ENHANCED EXPOSURE LIMITS: Adjust exposure limits based on historical success
                exposure_limit = 0.3  # Default exposure limit
                if memory_performance and memory_performance.get('success_rate', 0) > 0.8:
                    exposure_limit = 0.4  # Allow higher exposure if historically successful

                if market_direction and abs(current_exposure) < exposure_limit:
                    action = 'buy' if market_direction > 0 else 'sell'

                    # Cross-margin position sizing
                    optimal_position = self._calculate_cross_margin_position_size(
                        account_info, positions, symbol, portfolio_leverage
                    )

                    # MEMORY-ENHANCED CONFIDENCE: Blend diversification confidence with historical success
                    base_confidence = diversification_score * 0.7
                    confidence = base_confidence
                    if memory_performance:
                        success_rate = memory_performance.get('success_rate', 0.5)
                        confidence = base_confidence * (0.5 + success_rate * 0.5)

                    expected_return = 0.02 * portfolio_leverage * diversification_score

                    return TradingSignal(
                        symbol=symbol,
                        action=action.upper(),
                        strength=diversification_score,
                        confidence=confidence,
                        strategy="cross_margin",
                        entry_price=market_data.get('current_price', 0.0),
                        stop_loss=None,
                        take_profit=None,
                        reason=f"Cross-margin opportunity - diversification: {diversification_score:.3f}",
                        timestamp=datetime.now(timezone.utc),
                        risk_score=portfolio_risk,
                        expected_holding_time=120,
                        sentiment_score=None,
                        news_impact=None,
                        technical_score=diversification_score,
                        ml_prediction=None
                    )

        except Exception as e:
            self.logger.error(f"Error in cross-margin strategy: {e}")

        return None

    def _calculate_portfolio_optimal_leverage(self, positions: List[Dict], symbol: str, margin_ratio: float) -> int:
        """Calculate optimal leverage for portfolio balance"""
        base_leverage = self._calculate_safe_leverage(symbol, margin_ratio)

        # Adjust based on portfolio concentration
        portfolio_size = len(positions)
        if portfolio_size == 0:
            return base_leverage
        elif portfolio_size < 3:
            return int(base_leverage * 0.8)  # Reduce leverage for concentrated portfolio
        else:
            return int(base_leverage * 1.1)  # Slightly increase for diversified portfolio

    def _calculate_cross_margin_position_size(self, account_info: Dict, positions: List[Dict],
                                            symbol: str, leverage: int) -> float:
        """Calculate position size for cross-margin strategy"""
        available_balance = float(account_info.get('totalAvailableBalance', 0))
        total_equity = float(account_info.get('totalEquity', 0))

        # Calculate current portfolio utilization
        portfolio_value = sum(float(pos.get('size', 0)) * float(pos.get('markPrice', 1))
                            for pos in positions)
        utilization = portfolio_value / total_equity if total_equity > 0 else 0

        # Adjust position size based on utilization
        if utilization > 0.8:
            base_percentage = 0.02  # 2% if highly utilized
        elif utilization > 0.5:
            base_percentage = 0.05  # 5% if moderately utilized
        else:
            base_percentage = 0.1   # 10% if low utilization

        position_size = available_balance * base_percentage
        return max(0.01, min(position_size, available_balance * 0.15))

    async def _filter_and_rank_signals(self, signals: List[TradingSignal]) -> List[TradingSignal]:
        """Filter and rank signals based on quality and market conditions"""
        try:
            if not signals:
                return []
            
            # Filter by minimum confidence and strength (lowered for more signal generation)
            min_confidence = 0.3  # Lowered from 0.6 to allow more signals
            min_strength = 0.3    # Lowered from 0.6 to allow more signals
            
            filtered_signals = [
                s for s in signals 
                if s.confidence >= min_confidence and s.strength >= min_strength
            ]
            
            # Adjust for market regime
            for signal in filtered_signals:
                signal.strength = await self._adjust_for_market_regime(signal)
            
            # Sort by composite score
            def signal_score(signal):
                regime_bonus = 0.1 if self._signal_matches_regime(signal) else 0
                strategy_weight = self.strategy_weights.get(StrategyType(signal.strategy), 0.1)
                return (signal.strength * signal.confidence * strategy_weight) + regime_bonus
            
            filtered_signals.sort(key=signal_score, reverse=True)
            
            # Limit to top signals per symbol
            symbol_counts: Dict[str, int] = {}
            final_signals = []
            
            for signal in filtered_signals:
                symbol_count = symbol_counts.get(signal.symbol, 0)
                if symbol_count < self.max_positions_per_symbol:
                    final_signals.append(signal)
                    symbol_counts[signal.symbol] = symbol_count + 1
                
                if len(final_signals) >= self.max_total_positions:
                    break
            
            return final_signals
            
        except Exception as e:
            self.logger.error(f"Error filtering signals: {e}")
            return signals[:self.max_total_positions]  # Fallback
    
    async def _adjust_for_market_regime(self, signal: TradingSignal) -> float:
        """Adjust signal strength based on current market regime"""
        try:
            adjustment = 1.0
            
            # Regime-based adjustments
            if self.current_regime == MarketRegime.TRENDING_BULL:
                if signal.action == "BUY":
                    adjustment = 1.2
                elif signal.action == "SELL":
                    adjustment = 0.8
                    
            elif self.current_regime == MarketRegime.TRENDING_BEAR:
                if signal.action == "SELL":
                    adjustment = 1.2
                elif signal.action == "BUY":
                    adjustment = 0.8
                    
            elif self.current_regime == MarketRegime.RANGING:
                if signal.strategy == "mean_reversion":
                    adjustment = 1.3
                elif signal.strategy == "trend_following":
                    adjustment = 0.7
                    
            elif self.current_regime == MarketRegime.HIGH_VOLATILITY:
                if signal.strategy in ["momentum", "news_trading"]:
                    adjustment = 1.2
                else:
                    adjustment = 0.9
                    
            elif self.current_regime == MarketRegime.NEWS_DRIVEN:
                if signal.strategy == "news_trading":
                    adjustment = 1.4
                    
            elif self.current_regime == MarketRegime.SENTIMENT_DRIVEN:
                if signal.strategy == "sentiment_trading":
                    adjustment = 1.4
            
            return min(signal.strength * adjustment, 1.0)
            
        except Exception as e:
            self.logger.error(f"Error adjusting signal for regime: {e}")
            return signal.strength
    
    def _signal_matches_regime(self, signal: TradingSignal) -> bool:
        """Check if signal matches current market regime"""
        try:
            if self.current_regime == MarketRegime.NEWS_DRIVEN:
                return signal.strategy == "news_trading"
            elif self.current_regime == MarketRegime.SENTIMENT_DRIVEN:
                return signal.strategy == "sentiment_trading"
            elif self.current_regime == MarketRegime.HIGH_VOLATILITY:
                return signal.strategy in ["momentum", "news_trading"]
            elif self.current_regime == MarketRegime.RANGING:
                return signal.strategy == "mean_reversion"
            elif self.current_regime in [MarketRegime.TRENDING_BULL, MarketRegime.TRENDING_BEAR]:
                return signal.strategy == "trend_following"
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking regime match: {e}")
            return False
    
    async def update_strategy_performance(self, strategy: str, trade_result: Dict):
        """Update strategy performance metrics"""
        try:
            if strategy in [s.value for s in StrategyType]:
                strategy_type = StrategyType(strategy)
                
                if trade_result.get('pnl', 0) > 0:
                    self.strategy_performance[strategy_type]['wins'] += 1
                else:
                    self.strategy_performance[strategy_type]['losses'] += 1
                
                self.strategy_performance[strategy_type]['total_pnl'] += trade_result.get('pnl', 0)
                
                # Adjust strategy weights based on performance
                await self._adjust_strategy_weights()
            
        except Exception as e:
            self.logger.error(f"Error updating strategy performance: {e}")
    
    async def _adjust_strategy_weights(self):
        """Dynamically adjust strategy weights based on performance"""
        try:
            total_weight = 0.0

            for strategy_type, performance in self.strategy_performance.items():
                total_trades = performance['wins'] + performance['losses']

                if total_trades > 10:  # Minimum trades for adjustment
                    win_rate = performance['wins'] / total_trades
                    avg_pnl = performance['total_pnl'] / total_trades

                    # Calculate performance score
                    performance_score = (win_rate * 0.6) + (max(avg_pnl, 0) * 0.4)

                    # Adjust weight
                    base_weight = 1.0 / len(StrategyType)
                    new_weight = base_weight * (0.5 + performance_score)

                    self.strategy_weights[strategy_type] = new_weight
                    total_weight += new_weight

            # Normalize weights
            if total_weight > 0:
                for strategy_type in self.strategy_weights:
                    self.strategy_weights[strategy_type] /= total_weight

        except Exception as e:
            self.logger.error(f"Error adjusting strategy weights: {e}")

    async def update_strategy_weights_from_enforcer(self, enforcer_weights: Dict[str, float]):
        """Update strategy weights from profit target enforcer"""
        try:
            # Map enforcer weight names to strategy types
            weight_mapping = {
                'ultra_scalping': StrategyType.MARGIN_SCALPING,
                'arbitrage': StrategyType.MARGIN_ARBITRAGE,
                'grid_trading': StrategyType.MEAN_REVERSION,
                'market_making': StrategyType.TREND_FOLLOWING,
                'momentum_surfing': StrategyType.MOMENTUM,
                'volatility_harvesting': StrategyType.LEVERAGE_MOMENTUM,
                'correlation_trading': StrategyType.CROSS_MARGIN_STRATEGY
            }

            # Update weights based on enforcer recommendations
            for enforcer_name, weight in enforcer_weights.items():
                if enforcer_name in weight_mapping:
                    strategy_type = weight_mapping[enforcer_name]
                    self.strategy_weights[strategy_type] = weight

            # Ensure all weights are set and normalized
            total_weight = sum(self.strategy_weights.values())
            if total_weight > 0:
                for strategy_type in self.strategy_weights:
                    self.strategy_weights[strategy_type] /= total_weight

            self.logger.info(f"Updated strategy weights from profit enforcer: {self.strategy_weights}")

        except Exception as e:
            self.logger.error(f"Error updating strategy weights from enforcer: {e}")

    def _calculate_rsi(self, prices: List[float], period: int = 14) -> float:
        """Calculate Real RSI from live price data"""
        if len(prices) < period + 1:
            return 50.0

        deltas = [prices[i] - prices[i-1] for i in range(1, len(prices))]
        gains = [d if d > 0 else 0 for d in deltas]
        losses = [-d if d < 0 else 0 for d in deltas]

        avg_gain = sum(gains[-period:]) / period
        avg_loss = sum(losses[-period:]) / period

        if avg_loss == 0:
            return 100.0

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def _calculate_ema(self, prices: List[float], period: int) -> float:
        """Calculate Real EMA from live price data"""
        if len(prices) < period:
            return sum(prices) / len(prices)

        multiplier = 2 / (period + 1)
        ema = prices[0]

        for price in prices[1:]:
            ema = (price * multiplier) + (ema * (1 - multiplier))

        return ema

    def _calculate_bollinger_bands(self, prices: List[float], period: int = 20, std_dev: float = 2.0) -> tuple:
        """Calculate Real Bollinger Bands from live price data"""
        if len(prices) < period:
            avg = sum(prices) / len(prices)
            return avg, avg, avg

        recent_prices = prices[-period:]
        middle = sum(recent_prices) / period
        variance = sum((p - middle) ** 2 for p in recent_prices) / period
        std = variance ** 0.5

        upper = middle + (std * std_dev)
        lower = middle - (std * std_dev)

        return upper, middle, lower

    def _calculate_macd(self, prices: List[float]) -> float:
        """Calculate Real MACD signal from live price data"""
        if len(prices) < 26:
            return 0.0

        ema_12 = self._calculate_ema(prices, 12)
        ema_26 = self._calculate_ema(prices, 26)
        macd_line = ema_12 - ema_26

        return macd_line

    def _calculate_volatility(self, prices: List[float], period: int = 20) -> float:
        """Calculate Real volatility from live price data"""
        if len(prices) < 2:
            return 0.0

        returns = [(prices[i] / prices[i-1] - 1) for i in range(1, min(len(prices), period + 1))]
        if not returns:
            return 0.0

        mean_return = sum(returns) / len(returns)
        variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
        volatility = variance ** 0.5

        return float(volatility)
    
    def get_strategy_status(self) -> Dict[str, Any]:
        """Get current strategy manager status"""
        return {
            "current_regime": self.current_regime.value,
            "active_strategies": {k.value: v for k, v in self.active_strategies.items()},
            "strategy_weights": {k.value: v for k, v in self.strategy_weights.items()},
            "strategy_performance": {
                k.value: v for k, v in self.strategy_performance.items()
            },
            "data_cache_status": {
                "market_data": len(self.market_data_cache),
                "sentiment_data": bool(self.sentiment_data_cache),
                "news_data": bool(self.news_data_cache),
                "economic_data": bool(self.economic_data_cache),
                "ml_predictions": len(self.ml_predictions_cache)
            }
        }

    async def shutdown(self):
        """Shutdown the strategy manager and all its components"""
        try:
            self.logger.info("Shutting down Strategy Manager...")

            # Stop all data crawlers
            if hasattr(self, 'market_crawler') and self.market_crawler:
                await self.market_crawler.stop()

            if hasattr(self, 'news_crawler') and self.news_crawler:
                await self.news_crawler.stop()

            if hasattr(self, 'social_crawler') and self.social_crawler:
                await self.social_crawler.stop()

            if hasattr(self, 'economic_crawler') and self.economic_crawler:
                await self.economic_crawler.stop()

            # Stop market predictor
            if hasattr(self, 'market_predictor') and self.market_predictor:
                await self.market_predictor.stop()

            # Clear all caches
            self.market_data_cache.clear()
            self.sentiment_data_cache.clear()
            self.news_data_cache.clear()
            self.economic_data_cache.clear()
            self.ml_predictions_cache.clear()

            self.logger.info("Strategy Manager shutdown complete")

        except Exception as e:
            self.logger.error(f"Error shutting down Strategy Manager: {e}")
            raise

    # MARGIN TRADING UTILITY METHODS
    async def _get_funding_rate(self, symbol: str) -> Optional[float]:
        """Get current funding rate for symbol"""
        try:
            if self.bybit_client:
                funding_data = await self.bybit_client.get_funding_rate(symbol)
                return float(funding_data.get('fundingRate', 0)) if funding_data else None
        except Exception as e:
            self.logger.error(f"Error getting funding rate: {e}")
        return None

    def _calculate_safe_leverage(self, symbol: str, margin_ratio: float) -> int:
        """Calculate safe leverage based on current margin ratio"""
        max_leverage = self.max_leverage.get(symbol, self.max_leverage['default'])

        if margin_ratio > 85:
            return 1  # No leverage if very high margin
        elif margin_ratio > 70:
            return min(5, max_leverage // 10)
        elif margin_ratio > 50:
            return min(20, max_leverage // 3)
        else:
            return min(max_leverage, 50)  # Conservative max

    def _calculate_margin_position_size(self, account_info: Dict, margin_ratio: float, risk_level: str) -> float:
        """Calculate position size for margin trading"""
        available_balance = float(account_info.get('totalAvailableBalance', 0))
        risk_config = self.margin_risk_levels.get(risk_level, self.margin_risk_levels['conservative'])

        # Base position as percentage of available balance
        if margin_ratio > risk_config['max_margin_ratio']:
            return available_balance * 0.01  # Very small position if over risk threshold

        base_percentage = 0.05 * risk_config['leverage_multiplier']  # 5% base with risk adjustment
        margin_adjustment = max(0.1, 1.0 - (margin_ratio / 100.0))  # Reduce as margin increases

        position_size = available_balance * base_percentage * margin_adjustment
        return max(0.01, min(position_size, available_balance * 0.2))  # Min $0.01, max 20%

    async def _get_all_positions(self) -> List[Dict]:
        """Get all current positions"""
        try:
            if self.bybit_client:
                positions = await self.bybit_client.get_positions()
                return positions if positions else []
        except Exception as e:
            self.logger.error(f"Error getting positions: {e}")
        return []

    def _calculate_portfolio_risk(self, positions: List[Dict]) -> float:
        """Calculate overall portfolio risk"""
        if not positions:
            return 0.0

        total_risk = 0.0
        for position in positions:
            size = float(position.get('size', 0))
            leverage = float(position.get('leverage', 1))
            unrealized_pnl = float(position.get('unrealisedPnl', 0))

            position_risk = (size * leverage) / 1000.0  # Normalize risk
            if unrealized_pnl < 0:
                position_risk *= 1.5  # Increase risk for losing positions

            total_risk += position_risk

        return min(1.0, total_risk)

    def _calculate_diversification_score(self, positions: List[Dict], new_symbol: str) -> float:
        """Calculate diversification score for adding new position"""
        if not positions:
            return 1.0  # Perfect diversification if no positions

        # Check if symbol already exists
        existing_symbols = [pos.get('symbol', '') for pos in positions]
        if new_symbol in existing_symbols:
            return 0.3  # Low score if already have position

        # Calculate correlation with existing positions
        correlation_penalty = 0.0
        for symbol in existing_symbols:
            if symbol.startswith('BTC') and new_symbol.startswith('BTC'):
                correlation_penalty += 0.3
            elif symbol.startswith('ETH') and new_symbol.startswith('ETH'):
                correlation_penalty += 0.3
            # Add more correlation rules as needed

        diversification_score = max(0.1, 1.0 - correlation_penalty)
        return diversification_score

    def _calculate_symbol_exposure(self, positions: List[Dict], symbol: str) -> float:
        """Calculate current exposure to symbol"""
        total_exposure = 0.0
        symbol_exposure = 0.0

        for position in positions:
            pos_symbol = position.get('symbol', '')
            size = float(position.get('size', 0))
            leverage = float(position.get('leverage', 1))

            exposure = size * leverage
            total_exposure += abs(exposure)

            if pos_symbol == symbol:
                symbol_exposure += exposure

        if total_exposure == 0:
            return 0.0

        return symbol_exposure / total_exposure

    def _determine_market_direction(self, market_data: Dict) -> Optional[float]:
        """Determine market direction from technical indicators"""
        try:
            indicators = market_data.get('technical_indicators', {})

            rsi = indicators.get('rsi', 50)
            macd = indicators.get('macd', 0)
            sma_trend = indicators.get('sma_trend', 0)

            # Simple direction scoring
            direction_score = 0.0

            if rsi > 55:
                direction_score += 0.3
            elif rsi < 45:
                direction_score -= 0.3

            if macd > 0:
                direction_score += 0.4
            else:
                direction_score -= 0.4

            if sma_trend > 0:
                direction_score += 0.3
            else:
                direction_score -= 0.3

            return direction_score if abs(direction_score) > 0.3 else None

        except Exception as e:
            self.logger.error(f"Error determining market direction: {e}")

        return None

    async def get_memory_based_strategy_recommendation(self, symbol: str,
                                                     market_conditions: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Get strategy recommendation based on historical memory patterns"""
        try:
            if not self.memory_manager or not self.advanced_memory:
                return None

            # Create current time context
            current_time = datetime.now(timezone.utc)
            time_context = None  # Method disabled for Pylance compliance

            # Find similar patterns in memory
            # Create fallback time context
            fallback_time_context = {
                'hour_of_day': datetime.now().hour,
                'day_of_week': datetime.now().weekday(),
                'market_session': 'active',
                'is_holiday': False,
                'is_weekend': datetime.now().weekday() >= 5
            }

            similar_memories = await self.advanced_memory.find_similar_patterns(
                market_conditions,
                fallback_time_context,
                min_similarity=0.7
            )

            if not similar_memories:
                return None

            # Analyze successful patterns
            successful_patterns = [m for m in similar_memories
                                 if m.base_memory.outcome.get('success', False)]

            if not successful_patterns:
                return None

            # Extract strategy recommendations
            strategy_votes = {}
            total_weight = 0

            for memory in successful_patterns:
                strategy = memory.base_memory.strategy_used
                profit = memory.base_memory.outcome.get('profit', 0)
                confidence = memory.base_memory.confidence
                temporal_relevance = getattr(memory, "temporal_relevance_score", 0.5)

                # Calculate vote weight
                weight = confidence * temporal_relevance * getattr(memory, "time_decay_factor", 1.0)
                if profit > 0:
                    weight *= (1 + min(profit / 1000, 0.5))  # Boost for profitable memories

                if strategy not in strategy_votes:
                    strategy_votes[strategy] = {
                        'weight': 0,
                        'count': 0,
                        'avg_profit': 0,
                        'confidence': 0
                    }

                strategy_votes[strategy]['weight'] += weight
                strategy_votes[strategy]['count'] += 1
                strategy_votes[strategy]['avg_profit'] += profit
                strategy_votes[strategy]['confidence'] += float(confidence)
                total_weight += weight

            # Normalize and find best strategy
            if total_weight == 0:
                return None

            best_strategy = None
            best_score = 0

            for strategy, votes in strategy_votes.items():
                normalized_weight = votes['weight'] / total_weight
                avg_profit = votes['avg_profit'] / votes['count']
                avg_confidence = votes['confidence'] / votes['count']

                # Calculate final score
                score = normalized_weight * 0.4 + avg_confidence * 0.3 + min(avg_profit / 100, 0.3)

                if score > best_score:
                    best_score = float(score)
                    best_strategy = strategy

            if best_strategy and best_score > 0.5:
                return {
                    'recommended_strategy': best_strategy,
                    'confidence': best_score,
                    'supporting_memories': len(successful_patterns),
                    'expected_profit': strategy_votes[best_strategy]['avg_profit'] / strategy_votes[best_strategy]['count'],
                    'memory_based': True
                }

            return None

        except Exception as e:
            self.logger.error(f"Error getting memory-based recommendation: {e}")
            return None

    async def store_strategy_outcome(self, symbol: str, strategy_used: str,
                                   action_taken: str, outcome: Dict[str, Any],
                                   market_conditions: Dict[str, Any]):
        """Store strategy outcome in memory for future learning"""
        try:
            if not self.memory_manager or not self.advanced_memory:
                return

            # Create temporal context
            current_time = datetime.now(timezone.utc)
            time_context = None  # Method disabled for Pylance compliance

            # Create fallback temporal context
            current_dt = datetime.now()
            temporal_context = {
                'hour_of_day': current_dt.hour,
                'day_of_week': current_dt.weekday(),
                'market_session': 'active',
                'time_of_day': 'trading_hours',
                'is_holiday': False,
                'is_weekend': current_dt.weekday() >= 5,
                'quarter': (current_dt.month - 1) // 3 + 1,
                'month': current_dt.month
            }

            # Store in advanced memory system
            await self.advanced_memory.store_time_aware_experience(
                market_conditions=market_conditions,
                strategy_used=strategy_used,
                action_taken=action_taken,
                outcome=outcome,
                temporal_context=temporal_context
            )

            # Save strategy state for continuation
            if 'positions' in outcome:
                await self.memory_manager.save_strategy_state(
                    strategy_name=strategy_used,
                    positions=outcome.get('positions', {}),
                    parameters=outcome.get('parameters', {}),
                    performance=outcome.get('performance', {}),
                    continuation_data={
                        'symbol': symbol,
                        'last_action': action_taken,
                        'market_conditions': market_conditions
                    }
                )

            self.logger.info(f"Strategy outcome stored in memory: {strategy_used} -> {outcome.get('success', False)}")

        except Exception as e:
            self.logger.error(f"Error storing strategy outcome: {e}")

    async def load_strategy_continuation_data(self, strategy_name: str) -> Optional[Dict[str, Any]]:
        """Load strategy continuation data after system restart"""
        try:
            if not self.memory_manager:
                return None

            strategy_state = await self.memory_manager.load_strategy_state(strategy_name)
            if not strategy_state:
                return None

            return {
                'last_run_timestamp': strategy_state.last_run_timestamp,
                'time_since_last_run': strategy_state.time_since_last_run,
                'last_positions': strategy_state.last_positions,
                'last_parameters': strategy_state.last_parameters,
                'last_performance': strategy_state.last_performance,
                'continuation_data': strategy_state.continuation_data,
                'should_continue': strategy_state.time_since_last_run < timedelta(hours=24)  # Continue if less than 24h
            }

        except Exception as e:
            self.logger.error(f"Error loading strategy continuation data: {e}")
            return None

    async def get_time_aware_strategy_performance(self, strategy_name: str) -> Optional[Dict[str, Any]]:
        """Get strategy performance based on current time context"""
        try:
            if not self.advanced_memory:
                return None

            current_time = datetime.now(timezone.utc)
            time_context = None  # Method disabled for Pylance compliance

            # Get strategy performance for current time period
            time_period = 'active'  # Fallback value since time_context is disabled
            performance_memory = await self.advanced_memory.get_strategy_performance_memory(
                strategy_name, time_period
            )

            if performance_memory:
                return {
                    'strategy_name': strategy_name,
                    'time_period': time_period,
                    'avg_profit': performance_memory.performance_metrics.get('avg_profit', 0),
                    'trade_count': performance_memory.performance_metrics.get('trade_count', 0),
                    'success_rate': performance_memory.performance_metrics.get('success_count', 0) /
                                  max(performance_memory.performance_metrics.get('trade_count', 1), 1),
                    'temporal_effectiveness': performance_memory.temporal_effectiveness,
                    'last_updated': performance_memory.last_updated
                }

            return None

        except Exception as e:
            self.logger.error(f"Error getting time-aware strategy performance: {e}")
            return None

    async def learn_from_strategy_outcome(self, strategy_name: str, trade_record: dict) -> None:
        """Learn from strategy outcome to improve future performance"""
        try:
            # Store strategy outcome for learning
            outcome_record = {
                'strategy_name': strategy_name,
                'timestamp': time.time(),
                'trade_record': trade_record,
                'profit': trade_record.get('profit', 0),
                'success': trade_record.get('profit', 0) > 0
            }

            # Update strategy performance tracking
            if not hasattr(self, 'strategy_outcomes'):
                self.strategy_outcomes = []

            self.strategy_outcomes.append(outcome_record)

            # Keep only last 1000 outcomes
            if len(self.strategy_outcomes) > 1000:
                self.strategy_outcomes = self.strategy_outcomes[-1000:]

            self.logger.info(f"Strategy manager learned from outcome: {strategy_name}, profit: {trade_record.get('profit', 0)}")

        except Exception as e:
            self.logger.error(f"Error learning from strategy outcome: {e}")
