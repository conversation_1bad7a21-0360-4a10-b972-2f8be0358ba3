#!/usr/bin/env python3
"""
DIRECT TRADE EXECUTION - GUARANTEED REAL TRADES
This will execute actual trades on Bybit with minimal complexity
"""

import asyncio
import sqlite3
from datetime import datetime
from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybit<PERSON>lient
from bybit_bot.core.config import BotConfig

async def execute_direct_trade():
    """Execute a direct real trade on Bybit"""

    print("DIRECT TRADE EXECUTION - REAL MONEY")
    print("=" * 50)

    # Initialize config and client
    config = BotConfig()
    client = EnhancedBybitClient(config)
    await client.initialize()
    print("SUCCESS: Bybit client initialized")
    
    # Get account balance
    balance_info = await client.get_account_balance()
    if balance_info and 'list' in balance_info:
        initial_balance = float(balance_info['list'][0]['totalEquity'])
        available_balance = float(balance_info['list'][0]['totalAvailableBalance'])
        print(f"Initial Balance: ${initial_balance:.2f}")
        print(f"Available Balance: ${available_balance:.2f}")
    else:
        print("ERROR: Could not get account balance")
        return
    
    # Get current SOL price (use derivatives market - lower minimum requirements)
    symbol = "SOLUSDT"
    current_price = await client.get_current_price(symbol, category="linear")

    if current_price <= 0:
        print(f"ERROR: Could not get current price for {symbol}")
        return
    print(f"Current SOL Price: ${current_price:.2f}")

    # Calculate trade parameters for derivatives (use simple round number)
    # Use a simple round quantity that should be valid
    trade_size = 0.1  # 0.1 SOL
    trade_amount_usd = trade_size * current_price

    if available_balance < trade_amount_usd:
        print(f"ERROR: Insufficient balance for trade. Need ${trade_amount_usd:.2f}, have ${available_balance:.2f}")
        return

    print(f"Trade Amount: ${trade_amount_usd:.2f}")
    print(f"Trade Size: {trade_size:.6f} SOL")

    if available_balance < trade_amount_usd:
        print(f"ERROR: Insufficient balance. Need ${trade_amount_usd:.2f}, have ${available_balance:.2f}")
        return
    
    # Execute BUY order (Market order for guaranteed execution)
    print(f"\nExecuting MARKET BUY order...")
    
    try:
        # Use derivatives trading with proper parameters
        buy_order = await client.place_order(
            symbol=symbol,
            side="Buy",
            order_type="Market",
            qty=str(trade_size),
            category="linear",
            time_in_force="IOC"  # Immediate or Cancel
        )
        
        print(f"Buy order response: {buy_order}")
        
        if buy_order and buy_order.get('retCode') == 0:
            order_id = buy_order['result']['orderId']
            print(f"SUCCESS: BUY order placed - Order ID: {order_id}")
            
            # Record in database
            conn = sqlite3.connect('bybit_trading_bot.db')
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO trades (timestamp, symbol, side, quantity, price, strategy, order_id, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (datetime.now(), symbol, "Buy", trade_size, current_price, "Direct_Market", order_id, "executed"))
            conn.commit()
            conn.close()
            
            print("SUCCESS: Trade recorded in database")
            
            # Wait a moment then check new balance
            await asyncio.sleep(3)
            
            new_balance_info = await client.get_account_balance()
            if new_balance_info and 'list' in new_balance_info:
                new_balance = float(new_balance_info['list'][0]['totalEquity'])
                balance_change = new_balance - initial_balance
                print(f"\nBalance Update:")
                print(f"  Initial: ${initial_balance:.2f}")
                print(f"  Current: ${new_balance:.2f}")
                print(f"  Change: ${balance_change:+.2f}")
                
                if abs(balance_change) > 0.01:
                    print("*** REAL TRADE CONFIRMED - BALANCE CHANGED ***")
                else:
                    print("*** WARNING: No significant balance change detected ***")
            
            # Check positions
            positions = await client.get_positions()
            if positions:
                sol_positions = [p for p in positions if p.get('symbol') == symbol and float(p.get('size', 0)) > 0]
                if sol_positions:
                    print(f"\nActive SOL Position:")
                    pos = sol_positions[0]
                    print(f"  Size: {pos.get('size')} SOL")
                    print(f"  Entry Price: ${pos.get('avgPrice', 'N/A')}")
                    print(f"  Unrealized PnL: ${pos.get('unrealisedPnl', 'N/A')}")
                    print("*** POSITION CONFIRMED - REAL TRADE EXECUTED ***")
            
            return True
            
        else:
            print(f"ERROR: Buy order failed: {buy_order}")
            return False
            
    except Exception as e:
        print(f"ERROR: Trade execution failed: {e}")
        return False

async def main():
    """Main execution"""
    print("STARTING DIRECT TRADE EXECUTION")
    print("This will execute a real SOL trade")
    print("NO FAKE DATA - REAL MONEY TRADE")
    print()
    
    success = await execute_direct_trade()
    
    if success:
        print("\n*** TRADE EXECUTION SUCCESSFUL ***")
        print("Real trade has been executed on your Bybit account")
    else:
        print("\n*** TRADE EXECUTION FAILED ***")
        print("No real trades were executed")

if __name__ == "__main__":
    asyncio.run(main())
