import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import sqlite3
import json
import numpy as np
from dataclasses import dataclass

@dataclass
class LearningMetric:
    metric_name: str
    value: float
    timestamp: datetime
    context: Dict[str, Any]

class AdaptiveLearningEngine:
    def __init__(self, db_path: str = "bybit_trading_bot.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self.learning_rate = 0.1
        self.adaptation_threshold = 0.05
        self.memory_window = timedelta(hours=24)
        
        # Critical learning parameters that adapt based on performance
        self.position_size_multiplier = 0.1  # Start very conservative after losses
        self.risk_tolerance = 0.005  # 0.5% risk per trade (very conservative)
        self.confidence_score = 0.1  # Low confidence after account losses
        self.market_regime = "high_risk"  # Current market assessment
        self.max_daily_loss = 5.0  # Maximum daily loss in EUR
        self.consecutive_losses = 0
        self.total_daily_pnl = 0.0
        
        # Learning thresholds
        self.loss_adaptation_threshold = -2.0  # Adapt after 2 EUR loss
        self.win_confidence_threshold = 1.0   # Build confidence after 1 EUR profit
        self.max_position_size = 0.01  # Maximum position size (very small)
        
        self._initialize_database()
    
    def _initialize_database(self):
        """Initialize learning database tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Learning metrics table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS learning_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                metric_name TEXT NOT NULL,
                value REAL NOT NULL,
                timestamp DATETIME NOT NULL,
                context TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Adaptation history table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS adaptation_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                parameter_name TEXT NOT NULL,
                old_value REAL NOT NULL,
                new_value REAL NOT NULL,
                reason TEXT,
                timestamp DATETIME NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Performance analysis table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS performance_analysis (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                analysis_type TEXT NOT NULL,
                result TEXT NOT NULL,
                recommendations TEXT,
                timestamp DATETIME NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Daily performance tracking
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS daily_performance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date TEXT NOT NULL,
                total_pnl REAL NOT NULL,
                trade_count INTEGER NOT NULL,
                win_rate REAL NOT NULL,
                max_drawdown REAL NOT NULL,
                adaptations_made INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    async def learn_from_trade(self, trade_data: Dict[str, Any]) -> Dict[str, Any]:
        """Learn from individual trade outcomes with aggressive adaptation"""
        try:
            symbol = trade_data.get('symbol', 'UNKNOWN')
            profit_loss = float(trade_data.get('profit_loss', 0))
            position_size = float(trade_data.get('quantity', 0))
            execution_time = trade_data.get('execution_time', 0)
            
            # Update daily tracking
            self.total_daily_pnl += profit_loss
            
            # Analyze trade outcome
            is_profitable = profit_loss > 0
            risk_reward_ratio = abs(profit_loss) / max(position_size * 0.01, 0.001)
            
            # Store learning metrics
            await self._store_learning_metric("trade_profit_loss", profit_loss, {
                "symbol": symbol,
                "position_size": position_size,
                "execution_time": execution_time,
                "daily_pnl": self.total_daily_pnl
            })
            
            # Critical adaptation logic
            adaptations = {}
            
            if not is_profitable:
                self.consecutive_losses += 1
                
                # Immediate adaptation after any loss
                if profit_loss < -0.5:  # Any loss over 0.5 EUR triggers adaptation
                    old_multiplier = self.position_size_multiplier
                    self.position_size_multiplier *= 0.5  # Halve position size
                    self.position_size_multiplier = max(0.001, self.position_size_multiplier)  # Minimum size
                    
                    adaptations['position_size_multiplier'] = {
                        'old': old_multiplier,
                        'new': self.position_size_multiplier,
                        'reason': f'Loss of {profit_loss} EUR - reducing position size'
                    }
                
                # Reduce risk tolerance after consecutive losses
                if self.consecutive_losses >= 2:
                    old_risk = self.risk_tolerance
                    self.risk_tolerance *= 0.7
                    self.risk_tolerance = max(0.001, self.risk_tolerance)
                    
                    adaptations['risk_tolerance'] = {
                        'old': old_risk,
                        'new': self.risk_tolerance,
                        'reason': f'{self.consecutive_losses} consecutive losses'
                    }
                
                # Emergency stop if daily loss exceeds threshold
                if self.total_daily_pnl < -self.max_daily_loss:
                    adaptations['emergency_stop'] = {
                        'old': False,
                        'new': True,
                        'reason': f'Daily loss {self.total_daily_pnl} exceeds limit {self.max_daily_loss}'
                    }
                    
            else:
                # Reset consecutive losses on profit
                self.consecutive_losses = 0

                # PROFIT MAXIMIZATION: Increase position size after profitable trades
                if profit_loss > self.win_confidence_threshold:
                    # Increase position size multiplier for profitable trades
                    old_multiplier = self.position_size_multiplier

                    # Aggressive profit maximization: increase by 20% after each profitable trade
                    if self.total_daily_pnl > 0:  # Only if overall daily PnL is positive
                        self.position_size_multiplier = min(1.0, self.position_size_multiplier * 1.2)

                        adaptations['position_size_multiplier'] = {
                            'old': old_multiplier,
                            'new': self.position_size_multiplier,
                            'reason': f'PROFIT MAXIMIZATION: Profitable trade {profit_loss} EUR - increasing position size'
                        }

                # Gradually increase confidence after consistent profits
                if profit_loss > self.win_confidence_threshold and self.total_daily_pnl > 0:
                    old_confidence = self.confidence_score
                    self.confidence_score = min(1.0, self.confidence_score + 0.05)  # Faster confidence increase

                    adaptations['confidence_score'] = {
                        'old': old_confidence,
                        'new': self.confidence_score,
                        'reason': f'PROFIT MAXIMIZATION: Profitable trade {profit_loss} EUR - increasing confidence'
                    }

                # Increase risk tolerance after multiple profitable trades
                if self.total_daily_pnl > 2.0:  # After 2 EUR profit
                    old_risk = self.risk_tolerance
                    self.risk_tolerance = min(0.02, self.risk_tolerance * 1.1)  # Gradual risk increase

                    adaptations['risk_tolerance'] = {
                        'old': old_risk,
                        'new': self.risk_tolerance,
                        'reason': f'PROFIT MAXIMIZATION: Daily profit {self.total_daily_pnl} EUR - increasing risk tolerance'
                    }

                # Increase maximum position size after consistent profitability
                if self.total_daily_pnl > 5.0:  # After 5 EUR profit
                    old_max_pos = self.max_position_size
                    self.max_position_size = min(0.1, self.max_position_size * 1.15)  # Gradual increase

                    adaptations['max_position_size'] = {
                        'old': old_max_pos,
                        'new': self.max_position_size,
                        'reason': f'PROFIT MAXIMIZATION: Daily profit {self.total_daily_pnl} EUR - increasing max position size'
                    }

                # AGGRESSIVE PROFIT ACCELERATION: When daily profit > 10 EUR
                if self.total_daily_pnl > 10.0:
                    # Switch to aggressive profit mode
                    self.market_regime = "profit_acceleration"

                    # Increase position multiplier more aggressively
                    old_multiplier = self.position_size_multiplier
                    self.position_size_multiplier = min(2.0, self.position_size_multiplier * 1.5)

                    adaptations['profit_acceleration'] = {
                        'old_multiplier': old_multiplier,
                        'new_multiplier': self.position_size_multiplier,
                        'daily_profit': self.total_daily_pnl,
                        'reason': 'AGGRESSIVE PROFIT ACCELERATION MODE ACTIVATED'
                    }
            
            # Store all adaptations
            for param, change in adaptations.items():
                await self._store_adaptation(param, change['old'], change['new'], change['reason'])
            
            return {
                "learned": True,
                "adaptations": adaptations,
                "new_position_size_multiplier": self.position_size_multiplier,
                "new_risk_tolerance": self.risk_tolerance,
                "new_confidence_score": self.confidence_score,
                "consecutive_losses": self.consecutive_losses,
                "daily_pnl": self.total_daily_pnl,
                "emergency_stop_triggered": self.total_daily_pnl < -self.max_daily_loss
            }
            
        except Exception as e:
            self.logger.error(f"Error learning from trade: {e}")
            return {"learned": False, "error": str(e)}
    
    async def _store_learning_metric(self, metric_name: str, value: float, context: Dict[str, Any]):
        """Store learning metric in database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO learning_metrics (metric_name, value, timestamp, context)
                VALUES (?, ?, ?, ?)
            ''', (metric_name, value, datetime.now().isoformat(), json.dumps(context)))
            
            conn.commit()
            conn.close()
        except Exception as e:
            self.logger.error(f"Error storing learning metric: {e}")
    
    async def _store_adaptation(self, parameter_name: str, old_value: float, new_value: float, reason: str):
        """Store parameter adaptation in database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO adaptation_history (parameter_name, old_value, new_value, reason, timestamp)
                VALUES (?, ?, ?, ?, ?)
            ''', (parameter_name, old_value, new_value, reason, datetime.now().isoformat()))
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"ADAPTATION: {parameter_name} changed from {old_value} to {new_value} - {reason}")
        except Exception as e:
            self.logger.error(f"Error storing adaptation: {e}")

    async def analyze_performance_patterns(self) -> Dict[str, Any]:
        """Analyze trading patterns and adapt strategy"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Get recent trades for analysis
            cursor.execute('''
                SELECT symbol, profit_loss, quantity, timestamp
                FROM trades
                WHERE timestamp > datetime('now', '-24 hours')
                ORDER BY timestamp DESC
            ''')
            recent_trades = cursor.fetchall()

            if not recent_trades:
                return {"analysis": "No recent trades to analyze"}

            # Calculate performance metrics
            total_pnl = sum(trade[1] for trade in recent_trades if trade[1] is not None)
            profitable_trades = [trade for trade in recent_trades if trade[1] and trade[1] > 0]
            losing_trades = [trade for trade in recent_trades if trade[1] and trade[1] < 0]

            win_rate = len(profitable_trades) / len(recent_trades) if recent_trades else 0
            avg_win = np.mean([trade[1] for trade in profitable_trades]) if profitable_trades else 0
            avg_loss = np.mean([trade[1] for trade in losing_trades]) if losing_trades else 0

            # Analyze patterns and adapt
            adaptations = {}

            # Pattern 1: Low win rate adaptation
            if win_rate < 0.4:  # Less than 40% win rate
                old_multiplier = self.position_size_multiplier
                self.position_size_multiplier *= 0.6  # Reduce position size
                adaptations['low_win_rate'] = {
                    'old_multiplier': old_multiplier,
                    'new_multiplier': self.position_size_multiplier,
                    'win_rate': win_rate
                }

            # Pattern 2: Large average losses
            if avg_loss < -2.0:  # Average loss > 2 EUR
                old_risk = self.risk_tolerance
                self.risk_tolerance *= 0.5  # Halve risk tolerance
                adaptations['large_avg_loss'] = {
                    'old_risk': old_risk,
                    'new_risk': self.risk_tolerance,
                    'avg_loss': avg_loss
                }

            # Pattern 3: Negative total PnL
            if total_pnl < 0:
                self.market_regime = "bearish"
                adaptations['market_regime'] = {
                    'regime': 'bearish',
                    'total_pnl': total_pnl
                }

            # PROFIT MAXIMIZATION PATTERNS
            # Pattern 4: High win rate with positive PnL - AMPLIFY SUCCESS
            if win_rate > 0.7 and total_pnl > 0:  # 70%+ win rate with profits
                old_multiplier = self.position_size_multiplier
                self.position_size_multiplier = min(3.0, self.position_size_multiplier * 1.3)  # Aggressive increase

                adaptations['high_win_rate_amplification'] = {
                    'old_multiplier': old_multiplier,
                    'new_multiplier': self.position_size_multiplier,
                    'win_rate': win_rate,
                    'total_pnl': total_pnl,
                    'reason': 'AMPLIFYING SUCCESS: High win rate with profits'
                }

            # Pattern 5: Excellent profit-to-loss ratio - MAXIMIZE POSITION SIZE
            if profitable_trades and losing_trades:
                profit_loss_ratio = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')

                if profit_loss_ratio > 2.0 and total_pnl > 0:  # Profits 2x larger than losses
                    old_max_pos = self.max_position_size
                    self.max_position_size = min(0.5, self.max_position_size * 1.4)

                    adaptations['profit_loss_ratio_optimization'] = {
                        'old_max_pos': old_max_pos,
                        'new_max_pos': self.max_position_size,
                        'profit_loss_ratio': profit_loss_ratio,
                        'reason': 'MAXIMIZING POSITION: Excellent profit-to-loss ratio'
                    }

            # Pattern 6: Consistent profitability - ENTER HYPER PROFIT MODE
            if len(profitable_trades) >= 5 and total_pnl > 5.0:  # 5+ profitable trades with 5+ EUR profit
                self.market_regime = "hyper_profit"

                # Dramatically increase all profit parameters
                old_multiplier = self.position_size_multiplier
                old_risk = self.risk_tolerance

                self.position_size_multiplier = min(5.0, self.position_size_multiplier * 2.0)
                self.risk_tolerance = min(0.05, self.risk_tolerance * 1.5)

                adaptations['hyper_profit_mode'] = {
                    'old_multiplier': old_multiplier,
                    'new_multiplier': self.position_size_multiplier,
                    'old_risk': old_risk,
                    'new_risk': self.risk_tolerance,
                    'profitable_trades': len(profitable_trades),
                    'total_profit': total_pnl,
                    'reason': 'HYPER PROFIT MODE: Consistent profitability detected'
                }

            # Store analysis results
            analysis_result = {
                "total_pnl": total_pnl,
                "win_rate": win_rate,
                "avg_win": avg_win,
                "avg_loss": avg_loss,
                "trade_count": len(recent_trades),
                "adaptations": adaptations
            }

            cursor.execute('''
                INSERT INTO performance_analysis (analysis_type, result, recommendations, timestamp)
                VALUES (?, ?, ?, ?)
            ''', ("pattern_analysis", json.dumps(analysis_result),
                  json.dumps(adaptations), datetime.now().isoformat()))

            conn.commit()
            conn.close()

            return analysis_result

        except Exception as e:
            self.logger.error(f"Error analyzing performance patterns: {e}")
            return {"error": str(e)}

    async def learn_from_market_conditions(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Learn from current market conditions and adapt"""
        try:
            volatility = market_data.get('volatility', 0)
            volume = market_data.get('volume', 0)
            price_change = market_data.get('price_change_24h', 0)

            adaptations = {}

            # High volatility adaptation
            if volatility > 0.05:  # 5% volatility
                old_multiplier = self.position_size_multiplier
                self.position_size_multiplier *= 0.7  # Reduce size in high volatility
                adaptations['high_volatility'] = {
                    'old_multiplier': old_multiplier,
                    'new_multiplier': self.position_size_multiplier,
                    'volatility': volatility
                }

            # Low volume adaptation
            if volume < 1000000:  # Low volume threshold
                old_confidence = self.confidence_score
                self.confidence_score *= 0.8  # Reduce confidence in low volume
                adaptations['low_volume'] = {
                    'old_confidence': old_confidence,
                    'new_confidence': self.confidence_score,
                    'volume': volume
                }

            # Store market learning
            await self._store_learning_metric("market_volatility", volatility, market_data)
            await self._store_learning_metric("market_volume", volume, market_data)

            return {
                "learned_from_market": True,
                "adaptations": adaptations,
                "market_regime": self.market_regime
            }

        except Exception as e:
            self.logger.error(f"Error learning from market conditions: {e}")
            return {"error": str(e)}

    def get_adapted_parameters(self) -> Dict[str, float]:
        """Get current adapted parameters for trading system"""
        return {
            "position_size_multiplier": self.position_size_multiplier,
            "risk_tolerance": self.risk_tolerance,
            "confidence_score": self.confidence_score,
            "max_position_size": self.max_position_size,
            "max_daily_loss": self.max_daily_loss,
            "consecutive_losses": self.consecutive_losses,
            "daily_pnl": self.total_daily_pnl,
            "market_regime": self.market_regime
        }

    async def reset_daily_metrics(self):
        """Reset daily tracking metrics"""
        # Store daily performance before reset
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO daily_performance (date, total_pnl, trade_count, win_rate, max_drawdown, adaptations_made)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (datetime.now().date().isoformat(), self.total_daily_pnl, 0, 0.0, 0.0, 0))

        conn.commit()
        conn.close()

        # Reset metrics
        self.total_daily_pnl = 0.0
        self.consecutive_losses = 0

        self.logger.info("Daily metrics reset - new trading day started")
