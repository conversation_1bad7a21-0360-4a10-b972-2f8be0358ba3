import axios from 'axios'

// Create axios instance with default config
const api = axios.create({
    baseURL: '/api',
    timeout: 30000,
    headers: {
        'Content-Type': 'application/json',
    },
})

// Request interceptor
api.interceptors.request.use(
    (config) => {
        console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`)
        return config
    },
    (error) => {
        return Promise.reject(error)
    }
)

// Response interceptor
api.interceptors.response.use(
    (response) => {
        return response.data
    },
    (error) => {
        console.error('API Error:', error.response?.data || error.message)
        return Promise.reject(error)
    }
)

// System Status Services
export const systemStatusService = {
    getStatus: () => api.get('/status'),
    getSystemInfo: () => api.get('/'),
    getAIStatus: () => api.get('/ai-status'),
    getProfitStatus: () => api.get('/profit-status'),
    emergencyStop: () => api.post('/emergency-stop'),
}

// Trading Services - Live endpoints
export const tradingService = {
    getPositions: () => api.get('/positions'),
    getOrders: () => api.get('/orders'),
    placeOrder: (orderData) => api.post('/orders', orderData),
    cancelOrder: (orderId) => api.delete(`/orders/${orderId}`),
    getTradingPairs: () => api.get('/trading-pairs'),
    getMarketData: (symbol) => api.get(`/market-data/${symbol}`),
    getActiveStrategies: () => api.get('/strategies'),
    updateStrategy: (strategyId, data) => api.put(`/strategies/${strategyId}`, data),
    pauseStrategy: (strategyId) => api.post(`/strategies/${strategyId}/pause`),
    resumeStrategy: (strategyId) => api.post(`/strategies/${strategyId}/resume`),
}

// Portfolio Services - Live endpoints
export const portfolioService = {
    getBalance: () => api.get('/portfolio/balance'),
    getPerformance: () => api.get('/portfolio/performance'),
    getTradeHistory: () => api.get('/portfolio/trades'),
    getAssets: () => api.get('/portfolio/assets'),
    getPortfolioStats: () => api.get('/portfolio/stats'),
    getAssetAllocation: () => api.get('/portfolio/allocation'),
    getTransactions: () => api.get('/portfolio/transactions'),
}

// Analytics Services - Live endpoints
export const analyticsService = {
    getMetrics: () => api.get('/analytics/metrics'),
    getChartData: (symbol, timeframe) => api.get(`/analytics/chart/${symbol}/${timeframe}`),
    getStrategies: () => api.get('/analytics/strategies'),
    getRiskMetrics: () => api.get('/analytics/risk'),
    getPerformanceData: (timeframe) => api.get(`/analytics/performance/${timeframe}`),
    getTradeAnalysis: () => api.get('/analytics/trades'),
    getCorrelationMatrix: () => api.get('/analytics/correlation'),
    getVolatilityData: () => api.get('/analytics/volatility'),
}

// AI Services - Live endpoints
export const aiService = {
    getAIMetrics: () => api.get('/ai/metrics'),
    getMLPredictions: () => api.get('/ai/predictions'),
    getAgentStatus: () => api.get('/ai/agents'),
    getSelfHealingStatus: () => api.get('/ai/self-healing'),
    getModelPerformance: () => api.get('/ai/model-performance'),
    getConfidenceHistory: () => api.get('/ai/confidence-history'),
    getPredictionHistory: () => api.get('/ai/prediction-history'),
    retrainModel: (modelId) => api.post(`/ai/models/${modelId}/retrain`),
    updateAISettings: (settings) => api.put('/ai/settings', settings),
}

// Settings Services - Live endpoints
export const settingsService = {
    getTradingSettings: () => api.get('/settings/trading'),
    updateTradingSettings: (settings) => api.put('/settings/trading', settings),
    getRiskSettings: () => api.get('/settings/risk'),
    updateRiskSettings: (settings) => api.put('/settings/risk', settings),
    getAISettings: () => api.get('/settings/ai'),
    updateAISettings: (settings) => api.put('/settings/ai', settings),
    getNotificationSettings: () => api.get('/settings/notifications'),
    updateNotificationSettings: (settings) => api.put('/settings/notifications', settings),
    getAPISettings: () => api.get('/settings/api'),
    updateAPISettings: (settings) => api.put('/settings/api', settings),
    resetToDefaults: () => api.post('/settings/reset'),
    exportSettings: () => api.get('/settings/export'),
    importSettings: (settings) => api.post('/settings/import', settings),
}

// WebSocket connection for real-time data
export const createWebSocketConnection = (url, onMessage, onError) => {
    const ws = new WebSocket(url)

    ws.onopen = () => {
        console.log('WebSocket connected')
    }

    ws.onmessage = (event) => {
        try {
            const data = JSON.parse(event.data)
            onMessage(data)
        } catch (error) {
            console.error('WebSocket message parse error:', error)
        }
    }

    ws.onerror = (error) => {
        console.error('WebSocket error:', error)
        if (onError) onError(error)
    }

    ws.onclose = () => {
        console.log('WebSocket disconnected')
    }

    return ws
}

export default api
