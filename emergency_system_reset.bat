@echo off
echo ========================================
echo EMERGENCY SYSTEM RESET
echo ========================================
echo.

echo Step 1: Killing all Python processes...
taskkill /F /IM python.exe /T 2>nul
taskkill /F /IM pythonw.exe /T 2>nul
echo Python processes killed.
echo.

echo Step 2: Waiting for processes to terminate...
timeout /t 5 /nobreak >nul
echo.

echo Step 3: Checking for remaining Python processes...
tasklist /FI "IMAGENAME eq python.exe" 2>nul | find "python.exe" && (
    echo WARNING: Python processes still running
    taskkill /F /IM python.exe /T 2>nul
) || echo No Python processes found

tasklist /FI "IMAGENAME eq pythonw.exe" 2>nul | find "pythonw.exe" && (
    echo WARNING: Pythonw processes still running  
    taskkill /F /IM pythonw.exe /T 2>nul
) || echo No Pythonw processes found
echo.

echo Step 4: Removing problematic database file...
if exist "bybit_trading_bot.db" (
    del "bybit_trading_bot.db" 2>nul
    echo Removed existing SQLite database
) else (
    echo No existing SQLite database found
)
echo.

echo Step 5: Removing any lock files...
if exist "*.lock" (
    del "*.lock" 2>nul
    echo Removed lock files
)
echo.

echo Step 6: Clearing Python cache...
if exist "__pycache__" (
    rmdir /s /q "__pycache__" 2>nul
    echo Cleared Python cache
)
if exist "bybit_bot\__pycache__" (
    rmdir /s /q "bybit_bot\__pycache__" 2>nul
    echo Cleared bybit_bot cache
)
echo.

echo ========================================
echo SYSTEM RESET COMPLETE
echo ========================================
echo.
echo The system should now be ready to restart.
echo You can now try running: python main.py
echo.
pause
