import React, { useState, useEffect } from 'react';
import { StyleSheet, View, ScrollView, Alert } from 'react-native';
import { <PERSON>, Card, Button, Divider, ActivityIndicator } from 'react-native-paper';
import { showMessage } from 'react-native-flash-message';
import ToggleSwitch from './ToggleSwitch';
import LogLevelSelector from './LogLevelSelector';
import loggingService, { LoggingConfig, LoggingCategory } from '../services/loggingService';
import { theme } from '../styles/theme';

interface LoggingControlsProps {
    onConfigChange?: (config: LoggingConfig) => void;
}

const LoggingControls: React.FC<LoggingControlsProps> = ({ onConfigChange }) => {
    const [config, setConfig] = useState<LoggingConfig | null>(null);
    const [categories, setCategories] = useState<LoggingCategory[]>([]);
    const [loading, setLoading] = useState(true);
    const [updating, setUpdating] = useState(false);

    useEffect(() => {
        loadData();
    }, []);

    const loadData = async () => {
        try {
            setLoading(true);
            const [configData, categoriesData] = await Promise.all([
                loggingService.getLoggingConfig(),
                loggingService.getLoggingCategories(),
            ]);
            
            setConfig(configData);
            setCategories(categoriesData.categories);
        } catch (error) {
            showMessage({
                message: 'Failed to load logging configuration',
                description: error instanceof Error ? error.message : 'Unknown error',
                type: 'danger',
            });
        } finally {
            setLoading(false);
        }
    };

    const updateConfig = async (newConfig: LoggingConfig) => {
        try {
            setUpdating(true);
            await loggingService.updateLoggingConfig(newConfig);
            setConfig(newConfig);
            onConfigChange?.(newConfig);
            
            showMessage({
                message: 'Configuration updated',
                type: 'success',
            });
        } catch (error) {
            showMessage({
                message: 'Failed to update configuration',
                description: error instanceof Error ? error.message : 'Unknown error',
                type: 'danger',
            });
        } finally {
            setUpdating(false);
        }
    };

    const handleMasterToggle = async (enabled: boolean) => {
        if (!config) return;

        const newConfig = {
            ...config,
            master_enabled: enabled,
            categories: { ...config.categories },
        };

        // Toggle all categories
        Object.keys(newConfig.categories).forEach(category => {
            newConfig.categories[category].enabled = enabled;
        });

        await updateConfig(newConfig);
    };

    const handleCategoryToggle = async (categoryId: string, enabled: boolean) => {
        if (!config) return;

        const newConfig = {
            ...config,
            categories: {
                ...config.categories,
                [categoryId]: {
                    ...config.categories[categoryId],
                    enabled,
                },
            },
        };

        await updateConfig(newConfig);
    };

    const handleLevelChange = async (categoryId: string, level: string) => {
        if (!config) return;

        const newConfig = {
            ...config,
            categories: {
                ...config.categories,
                [categoryId]: {
                    ...config.categories[categoryId],
                    level,
                },
            },
        };

        await updateConfig(newConfig);
    };

    const handleClearAllLogs = () => {
        Alert.alert(
            'Clear All Logs',
            'Are you sure you want to clear all logs? This action cannot be undone.',
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Clear All',
                    style: 'destructive',
                    onPress: async () => {
                        try {
                            const promises = categories.map(category =>
                                loggingService.clearLogsByCategory(category.id)
                            );
                            await Promise.all(promises);
                            
                            showMessage({
                                message: 'All logs cleared',
                                type: 'success',
                            });
                        } catch (error) {
                            showMessage({
                                message: 'Failed to clear logs',
                                description: error instanceof Error ? error.message : 'Unknown error',
                                type: 'danger',
                            });
                        }
                    },
                },
            ]
        );
    };

    if (loading) {
        return (
            <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={theme.colors.primary} />
                <Text variant="bodyMedium" style={styles.loadingText}>
                    Loading logging configuration...
                </Text>
            </View>
        );
    }

    if (!config) {
        return (
            <View style={styles.errorContainer}>
                <Text variant="bodyMedium" style={styles.errorText}>
                    Failed to load logging configuration
                </Text>
                <Button mode="outlined" onPress={loadData} style={styles.retryButton}>
                    Retry
                </Button>
            </View>
        );
    }

    return (
        <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
            {/* Master Control */}
            <Card style={styles.masterCard}>
                <Card.Content>
                    <Text variant="titleLarge" style={styles.sectionTitle}>
                        Master Logging Control
                    </Text>
                    <Text variant="bodyMedium" style={styles.sectionDescription}>
                        Enable or disable all logging categories at once
                    </Text>
                    <View style={styles.masterToggleContainer}>
                        <ToggleSwitch
                            title="Enable All Logging"
                            description="Master switch for all logging categories"
                            value={config.master_enabled}
                            onValueChange={handleMasterToggle}
                            icon="power-settings-new"
                            disabled={updating}
                        />
                    </View>
                </Card.Content>
            </Card>

            <Divider style={styles.divider} />

            {/* Category Controls */}
            <Card style={styles.categoriesCard}>
                <Card.Content>
                    <Text variant="titleLarge" style={styles.sectionTitle}>
                        Logging Categories
                    </Text>
                    <Text variant="bodyMedium" style={styles.sectionDescription}>
                        Configure individual logging categories and levels
                    </Text>

                    {categories.map((category) => {
                        const categoryConfig = config.categories[category.id];
                        if (!categoryConfig) return null;

                        return (
                            <View key={category.id} style={styles.categoryContainer}>
                                <ToggleSwitch
                                    title={category.name}
                                    description={category.description}
                                    value={categoryConfig.enabled}
                                    onValueChange={(enabled) => handleCategoryToggle(category.id, enabled)}
                                    icon={category.icon}
                                    disabled={updating || !config.master_enabled}
                                />
                                
                                {categoryConfig.enabled && (
                                    <View style={styles.levelSelectorContainer}>
                                        <LogLevelSelector
                                            title={`${category.name} Log Level`}
                                            value={categoryConfig.level}
                                            onValueChange={(level) => handleLevelChange(category.id, level)}
                                            levels={config.log_levels}
                                            disabled={updating || !config.master_enabled}
                                            icon="tune"
                                        />
                                    </View>
                                )}
                            </View>
                        );
                    })}
                </Card.Content>
            </Card>

            <Divider style={styles.divider} />

            {/* Quick Actions */}
            <Card style={styles.actionsCard}>
                <Card.Content>
                    <Text variant="titleLarge" style={styles.sectionTitle}>
                        Quick Actions
                    </Text>
                    <View style={styles.actionsContainer}>
                        <Button
                            mode="outlined"
                            onPress={handleClearAllLogs}
                            style={styles.actionButton}
                            icon="delete-sweep"
                        >
                            Clear All Logs
                        </Button>
                        <Button
                            mode="outlined"
                            onPress={loadData}
                            style={styles.actionButton}
                            icon="refresh"
                            loading={loading}
                        >
                            Refresh Config
                        </Button>
                    </View>
                </Card.Content>
            </Card>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: theme.colors.background,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 32,
    },
    loadingText: {
        marginTop: 16,
        color: theme.colors.onSurface,
    },
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 32,
    },
    errorText: {
        color: theme.colors.error,
        textAlign: 'center',
        marginBottom: 16,
    },
    retryButton: {
        marginTop: 8,
    },
    masterCard: {
        margin: 16,
        backgroundColor: theme.colors.surface,
    },
    categoriesCard: {
        marginHorizontal: 16,
        backgroundColor: theme.colors.surface,
    },
    actionsCard: {
        margin: 16,
        backgroundColor: theme.colors.surface,
    },
    sectionTitle: {
        color: theme.colors.onSurface,
        fontWeight: '700',
        marginBottom: 8,
    },
    sectionDescription: {
        color: theme.colors.onSurfaceVariant,
        marginBottom: 16,
    },
    masterToggleContainer: {
        marginTop: 8,
    },
    categoryContainer: {
        marginBottom: 8,
    },
    levelSelectorContainer: {
        marginTop: 8,
        marginLeft: 16,
    },
    divider: {
        marginVertical: 8,
        backgroundColor: theme.colors.outline,
    },
    actionsContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        gap: 12,
    },
    actionButton: {
        flex: 1,
    },
});

export default LoggingControls;
