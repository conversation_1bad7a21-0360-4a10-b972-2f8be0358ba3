import { useEffect, useState } from 'react'
import websocketService from '../services/websocket'

export const useRealTimeData = () => {
    const [connectionStatus, setConnectionStatus] = useState('disconnected')
    const [marketData, setMarketData] = useState({})
    const [trades, setTrades] = useState([])
    const [positions, setPositions] = useState([])
    const [balance, setBalance] = useState(null)
    const [systemHealth, setSystemHealth] = useState(null)
    const [profitData, setProfitData] = useState(null)
    const [aiPredictions, setAiPredictions] = useState([])
    const [strategyUpdates, setStrategyUpdates] = useState([])
    const [performanceMetrics, setPerformanceMetrics] = useState(null)

    useEffect(() => {
        // Connection status
        const unsubscribeConnection = websocketService.subscribe('connection', (data) => {
            setConnectionStatus(data.status)
        })

        // Market data updates
        const unsubscribeMarketData = websocketService.subscribe('market_data', (data) => {
            setMarketData(prev => ({
                ...prev,
                [data.symbol]: data
            }))
        })

        // Trade updates
        const unsubscribeTrades = websocketService.subscribe('trade_update', (data) => {
            setTrades(prev => {
                const updatedTrades = [data, ...prev.slice(0, 99)] // Keep last 100 trades
                return updatedTrades
            })
        })

        // Position updates
        const unsubscribePositions = websocketService.subscribe('position_update', (data) => {
            setPositions(prev => {
                const index = prev.findIndex(p => p.symbol === data.symbol)
                if (index >= 0) {
                    const updated = [...prev]
                    updated[index] = data
                    return updated
                } else {
                    return [...prev, data]
                }
            })
        })

        // Balance updates
        const unsubscribeBalance = websocketService.subscribe('balance_update', (data) => {
            setBalance(data)
        })

        // System health updates
        const unsubscribeSystemHealth = websocketService.subscribe('system_health', (data) => {
            setSystemHealth(data)
        })

        // Profit updates
        const unsubscribeProfitUpdate = websocketService.subscribe('profit_update', (data) => {
            setProfitData(data)
        })

        // AI predictions
        const unsubscribeAIPredictions = websocketService.subscribe('ai_prediction', (data) => {
            setAiPredictions(prev => {
                const updated = [data, ...prev.slice(0, 49)] // Keep last 50 predictions
                return updated
            })
        })

        // Strategy updates
        const unsubscribeStrategyUpdates = websocketService.subscribe('strategy_update', (data) => {
            setStrategyUpdates(prev => {
                const index = prev.findIndex(s => s.id === data.id)
                if (index >= 0) {
                    const updated = [...prev]
                    updated[index] = data
                    return updated
                } else {
                    return [...prev, data]
                }
            })
        })

        // Performance metrics
        const unsubscribePerformanceMetrics = websocketService.subscribe('performance_metrics', (data) => {
            setPerformanceMetrics(data)
        })

        // Cleanup subscriptions
        return () => {
            unsubscribeConnection()
            unsubscribeMarketData()
            unsubscribeTrades()
            unsubscribePositions()
            unsubscribeBalance()
            unsubscribeSystemHealth()
            unsubscribeProfitUpdate()
            unsubscribeAIPredictions()
            unsubscribeStrategyUpdates()
            unsubscribePerformanceMetrics()
        }
    }, [])

    return {
        connectionStatus,
        marketData,
        trades,
        positions,
        balance,
        systemHealth,
        profitData,
        aiPredictions,
        strategyUpdates,
        performanceMetrics,
        isConnected: connectionStatus === 'connected'
    }
}

export const useMarketData = (symbol) => {
    const [data, setData] = useState(null)
    const [loading, setLoading] = useState(true)

    useEffect(() => {
        // Subscribe to specific symbol
        websocketService.subscribeToSymbol(symbol)

        const unsubscribe = websocketService.subscribe('market_data', (marketData) => {
            if (marketData.symbol === symbol) {
                setData(marketData)
                setLoading(false)
            }
        })

        return () => {
            websocketService.unsubscribeFromSymbol(symbol)
            unsubscribe()
        }
    }, [symbol])

    return { data, loading }
}

export const useSystemStatus = () => {
    const [status, setStatus] = useState(null)
    const [lastUpdate, setLastUpdate] = useState(null)

    useEffect(() => {
        const unsubscribe = websocketService.subscribe('system_health', (data) => {
            setStatus(data)
            setLastUpdate(new Date())
        })

        return unsubscribe
    }, [])

    return { status, lastUpdate }
}

export const useTradeHistory = (limit = 50) => {
    const [trades, setTrades] = useState([])
    const [totalCount, setTotalCount] = useState(0)

    useEffect(() => {
        const unsubscribe = websocketService.subscribe('trade_update', (trade) => {
            setTrades(prev => {
                const updated = [trade, ...prev.slice(0, limit - 1)]
                setTotalCount(prev => prev + 1)
                return updated
            })
        })

        return unsubscribe
    }, [limit])

    return { trades, totalCount }
}

export const useAIInsights = () => {
    const [predictions, setPredictions] = useState([])
    const [confidence, setConfidence] = useState(0)
    const [accuracy, setAccuracy] = useState(0)

    useEffect(() => {
        const unsubscribePredictions = websocketService.subscribe('ai_prediction', (prediction) => {
            setPredictions(prev => [prediction, ...prev.slice(0, 19)]) // Keep last 20
        })

        const unsubscribeMetrics = websocketService.subscribe('performance_metrics', (metrics) => {
            if (metrics.ai) {
                setConfidence(metrics.ai.confidence || 0)
                setAccuracy(metrics.ai.accuracy || 0)
            }
        })

        return () => {
            unsubscribePredictions()
            unsubscribeMetrics()
        }
    }, [])

    return { predictions, confidence, accuracy }
}
