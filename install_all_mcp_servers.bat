@echo off
echo [INFO] Installing ALL MCP Servers on E Drive - COMPREHENSIVE SETUP
echo ==================================================================

REM Set working directory to E drive BOT folder
cd /d "E:\The_real_deal_copy\Bybit_Bot\BOT"

REM 1. Initialize NPM project if needed
echo [STEP 1] Initializing NPM project on E drive...
if not exist "package.json" (
    npm init -y
    echo [OK] NPM project initialized
) else (
    echo [OK] NPM project already exists
)

REM 2. Install all MCP packages locally on E drive
echo [STEP 2] Installing all MCP packages locally on E drive...

echo   Installing filesystem server...
npm install @modelcontextprotocol/server-filesystem
if %errorlevel% equ 0 (echo [OK] Filesystem server installed) else (echo [ERROR] Filesystem server failed)

echo   Installing GitHub server...
npm install @modelcontextprotocol/server-github
if %errorlevel% equ 0 (echo [OK] GitHub server installed) else (echo [ERROR] GitHub server failed)

echo   Installing Context7 server...
npm install @modelcontextprotocol/server-context7
if %errorlevel% equ 0 (echo [OK] Context7 server installed) else (echo [ERROR] Context7 server failed)

echo   Installing Hugging Face server...
npm install @modelcontextprotocol/server-huggingface
if %errorlevel% equ 0 (echo [OK] Hugging Face server installed) else (echo [ERROR] Hugging Face server failed)

echo   Installing Microsoft server...
npm install @modelcontextprotocol/server-microsoft
if %errorlevel% equ 0 (echo [OK] Microsoft server installed) else (echo [ERROR] Microsoft server failed)

echo   Installing web search server...
npm install websearch-mcp
if %errorlevel% equ 0 (echo [OK] Web search server installed) else (echo [ERROR] Web search server failed)

echo   Installing git server...
npm install @cyanheads/git-mcp-server
if %errorlevel% equ 0 (echo [OK] Git server installed) else (echo [ERROR] Git server failed)

echo   Installing performance boost server...
npm install @modelcontextprotocol/mcp-stdio-server
if %errorlevel% equ 0 (echo [OK] Performance boost server installed) else (echo [ERROR] Performance boost server failed)

REM 3. Install Python MCP packages in conda environment
echo [STEP 3] Installing Python MCP packages...
call "E:\Miniconda\Scripts\activate.bat" bybit-trader

echo   Installing MCP server packages...
pip install mcp
if %errorlevel% equ 0 (echo [OK] MCP Python package installed) else (echo [ERROR] MCP Python package failed)

pip install psycopg2-binary
if %errorlevel% equ 0 (echo [OK] PostgreSQL adapter installed) else (echo [ERROR] PostgreSQL adapter failed)

pip install pyodbc
if %errorlevel% equ 0 (echo [OK] SQL Server adapter installed) else (echo [ERROR] SQL Server adapter failed)

REM 4. Verify Python MCP servers exist on E drive
echo [STEP 4] Verifying Python MCP servers on E drive...

if exist "mcp_server_memory.py" (
    echo [OK] Memory server exists on E drive
) else (
    echo [ERROR] Memory server missing on E drive
)

if exist "mcp_server_bybit.py" (
    echo [OK] Bybit trading server exists on E drive  
) else (
    echo [ERROR] Bybit trading server missing on E drive
)

if exist "bybit_bot\mcp\bybit_server.py" (
    echo [OK] Bybit MCP module exists on E drive
) else (
    echo [ERROR] Bybit MCP module missing on E drive
)

REM 5. Test all installations on E drive
echo [STEP 5] Testing installations...
echo   Node.js version:
node --version

echo   NPM packages installed on E drive:
npm list --depth=0

echo   Python environment on E drive:
"E:\Miniconda\envs\bybit-trader\python.exe" --version

echo   Testing Python MCP server:
"E:\Miniconda\envs\bybit-trader\python.exe" -c "import sys; sys.path.append('E:\\The_real_deal_copy\\Bybit_Bot\\BOT'); print('Python MCP path configured for E drive')"

echo   Testing memory server:
if exist "memory.db" (
    echo [OK] Memory database exists on E drive
) else (
    echo [INFO] Memory database will be created on first run
)

echo.
echo [SUCCESS] All MCP servers installed and configured on E drive!
echo [INFO] Memory server: E:\The_real_deal_copy\Bybit_Bot\BOT\mcp_server_memory.py
echo [INFO] Bybit server: E:\The_real_deal_copy\Bybit_Bot\BOT\mcp_server_bybit.py
echo [INFO] All Node.js packages: E:\The_real_deal_copy\Bybit_Bot\BOT\node_modules
echo [INFO] Restart VS Code Insiders to activate all servers
echo.
pause
