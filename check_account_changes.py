#!/usr/bin/env python3
"""
Check Account Changes - Verify if real trades are affecting account balance
"""

import asyncio
import sys
import os
from datetime import datetime, timed<PERSON><PERSON>

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from bybit_bot.core.config import ConfigManager
from bybit_bot.core.enhanced_bybit_client import EnhancedBybitClient

async def check_account_status():
    """Check current account status and recent activity"""
    print("=== ACCOUNT STATUS VERIFICATION ===")
    print(f"Timestamp: {datetime.now()}")
    print("=" * 50)
    
    try:
        # Initialize config and client
        config_manager = ConfigManager()
        config = config_manager.get_config()
        
        client = EnhancedBybitClient(
            api_key=config['bybit']['api_key'],
            api_secret=config['bybit']['api_secret'],
            testnet=config['bybit'].get('testnet', False)
        )
        
        print(f"Connected to: {'TESTNET' if config['bybit'].get('testnet', False) else 'MAINNET'}")
        print()
        
        # 1. Check wallet balance
        print("1. WALLET BALANCE CHECK:")
        try:
            balance_response = await client.get_wallet_balance(accountType="UNIFIED")
            print(f"Balance Response: {balance_response}")
            
            if balance_response and 'result' in balance_response:
                for account in balance_response['result']['list']:
                    print(f"Account Type: {account.get('accountType', 'Unknown')}")
                    for coin in account.get('coin', []):
                        if float(coin.get('walletBalance', 0)) > 0:
                            print(f"  {coin['coin']}: {coin['walletBalance']} (Available: {coin.get('availableToWithdraw', 'N/A')})")
            else:
                print("No balance data received")
        except Exception as e:
            print(f"Balance check error: {e}")
        
        print()
        
        # 2. Check recent orders
        print("2. RECENT ORDERS CHECK:")
        try:
            # Check orders for major pairs
            symbols = ['BTCUSDT', 'ETHUSDT', '10000QUBICUSDT']
            
            for symbol in symbols:
                try:
                    orders_response = await client.get_open_orders(
                        category="spot",
                        symbol=symbol
                    )
                    print(f"Open orders for {symbol}: {orders_response}")
                    
                    # Check order history
                    history_response = await client.get_order_history(
                        category="spot",
                        symbol=symbol,
                        limit=10
                    )
                    print(f"Order history for {symbol}: {history_response}")
                    
                except Exception as e:
                    print(f"Orders check error for {symbol}: {e}")
        except Exception as e:
            print(f"Orders check error: {e}")
        
        print()
        
        # 3. Check execution history
        print("3. EXECUTION HISTORY CHECK:")
        try:
            execution_response = await client.get_executions(
                category="spot",
                limit=20
            )
            print(f"Recent executions: {execution_response}")
        except Exception as e:
            print(f"Execution history error: {e}")
        
        print()
        
        # 4. Check account info
        print("4. ACCOUNT INFO CHECK:")
        try:
            account_info = await client.get_account_info()
            print(f"Account info: {account_info}")
        except Exception as e:
            print(f"Account info error: {e}")
        
        print()
        
        # 5. Check if we're in testnet mode
        print("5. ENVIRONMENT CHECK:")
        print(f"API Endpoint: {client.base_url}")
        print(f"Testnet Mode: {config['bybit'].get('testnet', False)}")
        
        # 6. Test a small API call to verify connection
        print("\n6. CONNECTION TEST:")
        try:
            server_time = await client.get_server_time()
            print(f"Server time response: {server_time}")
        except Exception as e:
            print(f"Connection test error: {e}")
        
        await client.close()
        
    except Exception as e:
        print(f"Critical error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(check_account_status())
