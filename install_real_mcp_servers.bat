@echo off
echo [INFO] Installing REAL MCP Servers (verified packages only)
echo ========================================================

cd /d "E:\The_real_deal_copy\Bybit_Bot\BOT"

echo [1/4] Installing Git MCP server...
npm install @cyanheads/git-mcp-server

echo [2/4] Installing Sequential Thinking server...
npm install @modelcontextprotocol/server-sequentialthinking

echo [3/4] Installing Memory server...
npm install @modelcontextprotocol/server-memory

echo [4/4] Installing Brave Search server...
npm install @modelcontextprotocol/server-brave-search

echo.
echo [INFO] Installing Python MCP packages...
call "E:\Miniconda\Scripts\activate.bat" bybit-trader
pip install mcp psycopg2-binary pyodbc

echo.
echo [SUCCESS] All REAL MCP servers installed!
echo.
echo Current packages:
npm list --depth=0

echo.
echo [INFO] Now update settings to use the real servers
pause
