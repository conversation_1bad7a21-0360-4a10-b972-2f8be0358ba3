#!/usr/bin/env python3
"""
Verify Balance Access
Check different methods to access account balance
"""

import asyncio
import sys
import os
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def log_to_file(message):
    """Log message to file and print"""
    with open("balance_verification_output.txt", "a", encoding="utf-8") as f:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_msg = f"[{timestamp}] {message}"
        print(log_msg)
        f.write(log_msg + "\n")
        f.flush()

async def verify_balance_access():
    """Verify different methods to access balance"""
    try:
        log_to_file("=== BALANCE ACCESS VERIFICATION ===")
        log_to_file("Testing different methods to access account balance")
        log_to_file("=" * 50)
        
        # Initialize client
        log_to_file("1. Initializing Enhanced Bybit Client...")
        from bybit_bot.core.config import BotConfig
        from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient

        config = BotConfig()
        client = EnhancedBybitClient(config)
        await client.initialize()
        log_to_file("SUCCESS: Client initialized")
        
        # Method 1: get_account_balance()
        log_to_file("\n2. Testing get_account_balance() method...")
        try:
            balance1 = await client.get_account_balance()
            log_to_file(f"get_account_balance() result: {balance1}")
        except Exception as e:
            log_to_file(f"get_account_balance() error: {e}")
        
        # Method 2: get_wallet_balance()
        log_to_file("\n3. Testing get_wallet_balance() method...")
        try:
            balance2 = await client.get_wallet_balance()
            log_to_file(f"get_wallet_balance() result: {balance2}")
        except Exception as e:
            log_to_file(f"get_wallet_balance() error: {e}")
        
        # Method 3: Direct API call to wallet balance
        log_to_file("\n4. Testing direct API wallet balance call...")
        try:
            if hasattr(client, 'session'):
                response = await client.session.get_wallet_balance(accountType="UNIFIED")
                log_to_file(f"Direct wallet balance (UNIFIED): {response}")
        except Exception as e:
            log_to_file(f"Direct wallet balance error: {e}")
        
        # Method 4: Try different account types
        log_to_file("\n5. Testing different account types...")
        account_types = ["UNIFIED", "SPOT", "CONTRACT", "INVESTMENT", "OPTION"]
        
        for acc_type in account_types:
            try:
                if hasattr(client, 'session'):
                    response = await client.session.get_wallet_balance(accountType=acc_type)
                    log_to_file(f"Account type {acc_type}: {response}")
            except Exception as e:
                log_to_file(f"Account type {acc_type} error: {e}")
        
        # Method 5: Get account info
        log_to_file("\n6. Testing get_account_info()...")
        try:
            account_info = await client.get_account_info()
            log_to_file(f"Account info: {account_info}")
        except Exception as e:
            log_to_file(f"Account info error: {e}")
        
        # Method 6: Check API key permissions
        log_to_file("\n7. Checking API key permissions...")
        try:
            if hasattr(client, 'session'):
                api_info = await client.session.get_api_key_information()
                log_to_file(f"API key info: {api_info}")
        except Exception as e:
            log_to_file(f"API key info error: {e}")
        
        # Method 7: Test with simple pybit client
        log_to_file("\n8. Testing with simple pybit client...")
        try:
            from pybit.unified_trading import HTTP
            
            # Get API credentials from config
            api_key = config.BYBIT_API_KEY
            api_secret = config.BYBIT_API_SECRET
            testnet = getattr(config, 'BYBIT_TESTNET', False)
            
            simple_client = HTTP(
                api_key=api_key,
                api_secret=api_secret,
                testnet=testnet
            )
            
            # Test wallet balance
            wallet_result = simple_client.get_wallet_balance(accountType="UNIFIED")
            log_to_file(f"Simple client wallet balance: {wallet_result}")
            
        except Exception as e:
            log_to_file(f"Simple client error: {e}")
        
        log_to_file("\n=== BALANCE VERIFICATION COMPLETED ===")
        log_to_file("Check results above to identify the correct balance access method")
        return True
        
    except Exception as e:
        log_to_file(f"CRITICAL ERROR: {e}")
        import traceback
        log_to_file(f"TRACEBACK: {traceback.format_exc()}")
        return False

async def main():
    """Main function"""
    # Clear previous output
    with open("balance_verification_output.txt", "w") as f:
        f.write("")

    success = await verify_balance_access()

    if success:
        log_to_file("=== VERIFICATION COMPLETED ===")
    else:
        log_to_file("=== VERIFICATION FAILED ===")

# Clear the output file before running
with open("balance_verification_output.txt", "w") as f:
    f.write("")

if __name__ == "__main__":
    asyncio.run(main())
