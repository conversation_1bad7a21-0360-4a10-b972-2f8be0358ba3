#!/usr/bin/env python3
"""
MINIMAL TEST - Find what's causing the hang
"""

print("Step 1: Python working")

try:
    import os
    print("Step 2: OS import OK")
    
    import sys
    print("Step 3: Sys import OK")
    
    from dotenv import load_dotenv
    print("Step 4: Dotenv import OK")
    
    load_dotenv()
    print("Step 5: Environment loaded")
    
    # Add path
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    print("Step 6: Path added")
    
    # Test basic imports
    from bybit_bot.core.config import BotConfig
    print("Step 7: Config import OK")
    
    config = BotConfig()
    print("Step 8: Config created OK")
    
    from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
    print("Step 9: Client import OK")
    
    client = EnhancedBybitClient(config)
    print("Step 10: Client created OK")
    
    print("ALL IMPORTS SUCCESSFUL - NO HANGING DETECTED")
    
except Exception as e:
    print(f"ERROR at step: {e}")
    import traceback
    traceback.print_exc()
