import { Box, LinearProgress, Typography } from '@mui/material'
import { motion } from 'framer-motion'
import { useEffect, useState } from 'react'

const LoadingScreen = () => {
    const [progress, setProgress] = useState(0)
    const [currentMessage, setCurrentMessage] = useState(0)

    const loadingMessages = [
        'Initializing Autonomous Trading System...',
        'Loading SuperGPT AI Engine...',
        'Connecting to Exchange APIs...',
        'Initializing Risk Management...',
        'Starting Profit Maximization Engines...',
        'System Ready!'
    ]

    useEffect(() => {
        const timer = setInterval(() => {
            setProgress((oldProgress) => {
                const diff = Math.random() * 10
                const newProgress = Math.min(oldProgress + diff, 100)

                // Update message based on progress
                const messageIndex = Math.min(Math.floor(newProgress / 20), loadingMessages.length - 1)
                setCurrentMessage(messageIndex)

                return newProgress
            })
        }, 200)

        return () => {
            clearInterval(timer)
        }
    }, [])

    return (
        <Box
            sx={{
                position: 'fixed',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%)',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                zIndex: 9999,
            }}
        >
            {/* Animated Logo */}
            <motion.div
                initial={{ scale: 0.5, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.8, ease: 'easeOut' }}
                style={{ marginBottom: '3rem' }}
            >
                <Box
                    sx={{
                        width: 120,
                        height: 120,
                        borderRadius: '50%',
                        background: 'linear-gradient(45deg, #00ff88, #00cc6a)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        boxShadow: '0 0 30px rgba(0, 255, 136, 0.3)',
                        animation: 'pulse 2s infinite',
                    }}
                >
                    <Typography
                        variant="h3"
                        sx={{
                            color: '#000',
                            fontWeight: 700,
                            fontSize: '2.5rem'
                        }}
                    >
                        AT
                    </Typography>
                </Box>
            </motion.div>

            {/* System Name */}
            <motion.div
                initial={{ y: 30, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.8, delay: 0.3 }}
            >
                <Typography
                    variant="h3"
                    sx={{
                        fontWeight: 700,
                        background: 'linear-gradient(45deg, #00ff88, #ffffff)',
                        backgroundClip: 'text',
                        WebkitBackgroundClip: 'text',
                        WebkitTextFillColor: 'transparent',
                        textAlign: 'center',
                        marginBottom: 1,
                    }}
                >
                    Autonomous Trader
                </Typography>
                <Typography
                    variant="h6"
                    sx={{
                        color: '#b3b3b3',
                        textAlign: 'center',
                        marginBottom: 4,
                    }}
                >
                    Professional Trading System
                </Typography>
            </motion.div>

            {/* Loading Progress */}
            <motion.div
                initial={{ width: 0 }}
                animate={{ width: '400px' }}
                transition={{ duration: 0.8, delay: 0.6 }}
                style={{ width: '100%', maxWidth: '400px' }}
            >
                <Box sx={{ mb: 2 }}>
                    <Typography
                        variant="body2"
                        sx={{
                            color: '#b3b3b3',
                            textAlign: 'center',
                            minHeight: '20px',
                        }}
                    >
                        {loadingMessages[currentMessage]}
                    </Typography>
                </Box>

                <LinearProgress
                    variant="determinate"
                    value={progress}
                    sx={{
                        height: 8,
                        borderRadius: 4,
                        backgroundColor: 'rgba(255, 255, 255, 0.1)',
                        '& .MuiLinearProgress-bar': {
                            background: 'linear-gradient(90deg, #00ff88, #00cc6a)',
                            borderRadius: 4,
                        },
                    }}
                />

                <Box sx={{ mt: 1, display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="caption" sx={{ color: '#666' }}>
                        {Math.round(progress)}%
                    </Typography>
                    <Typography variant="caption" sx={{ color: '#666' }}>
                        System Initialization
                    </Typography>
                </Box>
            </motion.div>

            {/* Floating particles effect */}
            <Box
                sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    overflow: 'hidden',
                    pointerEvents: 'none',
                    zIndex: -1,
                }}
            >
                {[...Array(20)].map((_, i) => (
                    <motion.div
                        key={i}
                        initial={{
                            x: Math.random() * window.innerWidth,
                            y: window.innerHeight + 100,
                        }}
                        animate={{
                            y: -100,
                        }}
                        transition={{
                            duration: Math.random() * 10 + 10,
                            repeat: Infinity,
                            ease: 'linear',
                        }}
                        style={{
                            position: 'absolute',
                            width: 2,
                            height: 2,
                            background: 'rgba(0, 255, 136, 0.5)',
                            borderRadius: '50%',
                        }}
                    />
                ))}
            </Box>
        </Box>
    )
}

export default LoadingScreen
