import React from 'react';
import { StyleSheet, View, ScrollView } from 'react-native';
import { Text, Card, Chip, ProgressBar, DataTable, Button } from 'react-native-paper';
import { RouteProp, useRoute } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { theme } from '../styles/theme';

type StrategyDetailsRouteProp = RouteProp<{
    StrategyDetails: {
        strategyId: string;
        strategy?: any;
    };
}, 'StrategyDetails'>;

const StrategyDetailsScreen: React.FC = () => {
    const route = useRoute<StrategyDetailsRouteProp>();
    const { strategyId, strategy } = route.params;

    // Mock strategy data - in real app, fetch from API using strategyId
    const strategyData = strategy || {
        id: strategyId,
        name: 'AI Momentum Strategy',
        description: 'Advanced momentum trading strategy powered by machine learning algorithms',
        status: 'Active',
        type: 'AI-Powered',
        created: '2025-01-01',
        lastUpdated: '2025-01-09 14:30:25',
        performance: {
            totalTrades: 156,
            winRate: 68.5,
            totalProfit: 2450.75,
            avgProfit: 15.71,
            maxDrawdown: 8.2,
            sharpeRatio: 1.85,
            profitFactor: 2.34,
        },
        parameters: {
            timeframe: '15m',
            riskPerTrade: 2.0,
            maxPositions: 3,
            stopLoss: 2.5,
            takeProfit: 5.0,
            aiConfidenceThreshold: 75,
        },
        recentTrades: [
            { symbol: 'BTCUSDT', side: 'Buy', pnl: 125.50, timestamp: '2025-01-09 14:30' },
            { symbol: 'ETHUSDT', side: 'Sell', pnl: -45.20, timestamp: '2025-01-09 13:45' },
            { symbol: 'ADAUSDT', side: 'Buy', pnl: 78.90, timestamp: '2025-01-09 12:15' },
        ],
    };

    const getStatusColor = (status: string) => {
        switch (status.toLowerCase()) {
            case 'active':
                return theme.colors.primary;
            case 'paused':
                return '#FF9800';
            case 'stopped':
                return theme.colors.error;
            default:
                return theme.colors.onSurface;
        }
    };

    return (
        <View style={styles.container}>
            <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
                {/* Strategy Overview */}
                <Card style={styles.card}>
                    <Card.Content>
                        <View style={styles.header}>
                            <View style={styles.titleContainer}>
                                <Text variant="headlineSmall" style={styles.strategyName}>
                                    {strategyData.name}
                                </Text>
                                <Text variant="bodyMedium" style={styles.strategyType}>
                                    {strategyData.type}
                                </Text>
                            </View>
                            <Chip
                                mode="outlined"
                                style={[
                                    styles.statusChip,
                                    { borderColor: getStatusColor(strategyData.status) }
                                ]}
                                textStyle={{ color: getStatusColor(strategyData.status) }}
                            >
                                {strategyData.status}
                            </Chip>
                        </View>

                        <Text variant="bodyMedium" style={styles.description}>
                            {strategyData.description}
                        </Text>

                        <View style={styles.metaInfo}>
                            <View style={styles.metaItem}>
                                <Text variant="bodySmall" style={styles.metaLabel}>
                                    Created:
                                </Text>
                                <Text variant="bodySmall" style={styles.metaValue}>
                                    {strategyData.created}
                                </Text>
                            </View>
                            <View style={styles.metaItem}>
                                <Text variant="bodySmall" style={styles.metaLabel}>
                                    Last Updated:
                                </Text>
                                <Text variant="bodySmall" style={styles.metaValue}>
                                    {strategyData.lastUpdated}
                                </Text>
                            </View>
                        </View>
                    </Card.Content>
                </Card>

                {/* Performance Metrics */}
                <Card style={styles.card}>
                    <Card.Content>
                        <Text variant="titleMedium" style={styles.cardTitle}>
                            Performance Metrics
                        </Text>
                        <View style={styles.performanceGrid}>
                            <View style={styles.performanceItem}>
                                <Text variant="headlineSmall" style={styles.performanceValue}>
                                    ${strategyData.performance.totalProfit.toFixed(2)}
                                </Text>
                                <Text variant="bodySmall" style={styles.performanceLabel}>
                                    Total Profit
                                </Text>
                            </View>
                            <View style={styles.performanceItem}>
                                <Text variant="headlineSmall" style={styles.performanceValue}>
                                    {strategyData.performance.winRate.toFixed(1)}%
                                </Text>
                                <Text variant="bodySmall" style={styles.performanceLabel}>
                                    Win Rate
                                </Text>
                            </View>
                            <View style={styles.performanceItem}>
                                <Text variant="headlineSmall" style={styles.performanceValue}>
                                    {strategyData.performance.totalTrades}
                                </Text>
                                <Text variant="bodySmall" style={styles.performanceLabel}>
                                    Total Trades
                                </Text>
                            </View>
                            <View style={styles.performanceItem}>
                                <Text variant="headlineSmall" style={styles.performanceValue}>
                                    ${strategyData.performance.avgProfit.toFixed(2)}
                                </Text>
                                <Text variant="bodySmall" style={styles.performanceLabel}>
                                    Avg Profit
                                </Text>
                            </View>
                            <View style={styles.performanceItem}>
                                <Text variant="headlineSmall" style={styles.performanceValue}>
                                    {strategyData.performance.maxDrawdown.toFixed(1)}%
                                </Text>
                                <Text variant="bodySmall" style={styles.performanceLabel}>
                                    Max Drawdown
                                </Text>
                            </View>
                            <View style={styles.performanceItem}>
                                <Text variant="headlineSmall" style={styles.performanceValue}>
                                    {strategyData.performance.sharpeRatio.toFixed(2)}
                                </Text>
                                <Text variant="bodySmall" style={styles.performanceLabel}>
                                    Sharpe Ratio
                                </Text>
                            </View>
                        </View>

                        {/* Win Rate Progress Bar */}
                        <View style={styles.progressContainer}>
                            <Text variant="bodyMedium" style={styles.progressLabel}>
                                Win Rate: {strategyData.performance.winRate.toFixed(1)}%
                            </Text>
                            <ProgressBar
                                progress={strategyData.performance.winRate / 100}
                                color={theme.colors.primary}
                                style={styles.progressBar}
                            />
                        </View>
                    </Card.Content>
                </Card>

                {/* Strategy Parameters */}
                <Card style={styles.card}>
                    <Card.Content>
                        <Text variant="titleMedium" style={styles.cardTitle}>
                            Strategy Parameters
                        </Text>
                        <View style={styles.parametersContainer}>
                            {Object.entries(strategyData.parameters).map(([key, value]) => (
                                <View key={key} style={styles.parameterRow}>
                                    <Text variant="bodyMedium" style={styles.parameterLabel}>
                                        {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}:
                                    </Text>
                                    <Text variant="bodyMedium" style={styles.parameterValue}>
                                        {typeof value === 'number' ? 
                                            (key.includes('Percentage') || key.includes('Rate') ? `${value}%` : value) : 
                                            value}
                                    </Text>
                                </View>
                            ))}
                        </View>
                    </Card.Content>
                </Card>

                {/* Recent Trades */}
                <Card style={styles.card}>
                    <Card.Content>
                        <Text variant="titleMedium" style={styles.cardTitle}>
                            Recent Trades
                        </Text>
                        <DataTable>
                            <DataTable.Header>
                                <DataTable.Title>Symbol</DataTable.Title>
                                <DataTable.Title>Side</DataTable.Title>
                                <DataTable.Title numeric>P&L</DataTable.Title>
                                <DataTable.Title>Time</DataTable.Title>
                            </DataTable.Header>
                            {strategyData.recentTrades.map((trade: any, index: number) => (
                                <DataTable.Row key={index}>
                                    <DataTable.Cell>{trade.symbol}</DataTable.Cell>
                                    <DataTable.Cell>
                                        <Chip
                                            mode="outlined"
                                            style={[
                                                styles.sideChip,
                                                { borderColor: trade.side === 'Buy' ? theme.colors.primary : theme.colors.error }
                                            ]}
                                            textStyle={{ 
                                                color: trade.side === 'Buy' ? theme.colors.primary : theme.colors.error,
                                                fontSize: 12 
                                            }}
                                        >
                                            {trade.side}
                                        </Chip>
                                    </DataTable.Cell>
                                    <DataTable.Cell numeric>
                                        <Text
                                            style={[
                                                styles.pnlText,
                                                { color: trade.pnl >= 0 ? theme.colors.primary : theme.colors.error }
                                            ]}
                                        >
                                            ${trade.pnl.toFixed(2)}
                                        </Text>
                                    </DataTable.Cell>
                                    <DataTable.Cell>{trade.timestamp}</DataTable.Cell>
                                </DataTable.Row>
                            ))}
                        </DataTable>
                    </Card.Content>
                </Card>

                {/* Strategy Actions */}
                <Card style={styles.card}>
                    <Card.Content>
                        <Text variant="titleMedium" style={styles.cardTitle}>
                            Actions
                        </Text>
                        <View style={styles.actionsContainer}>
                            <Button
                                mode="contained"
                                onPress={() => {
                                    // TODO: Toggle strategy status
                                }}
                                style={styles.actionButton}
                                icon={strategyData.status === 'Active' ? 'pause' : 'play-arrow'}
                            >
                                {strategyData.status === 'Active' ? 'Pause' : 'Start'}
                            </Button>
                            <Button
                                mode="outlined"
                                onPress={() => {
                                    // TODO: Edit strategy
                                }}
                                style={styles.actionButton}
                                icon="edit"
                            >
                                Edit
                            </Button>
                        </View>
                        <View style={styles.actionsContainer}>
                            <Button
                                mode="outlined"
                                onPress={() => {
                                    // TODO: Export strategy data
                                }}
                                style={styles.actionButton}
                                icon="download"
                            >
                                Export Data
                            </Button>
                            <Button
                                mode="outlined"
                                onPress={() => {
                                    // TODO: Clone strategy
                                }}
                                style={styles.actionButton}
                                icon="content-copy"
                            >
                                Clone
                            </Button>
                        </View>
                    </Card.Content>
                </Card>
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: theme.colors.background,
    },
    scrollView: {
        flex: 1,
    },
    card: {
        marginHorizontal: 16,
        marginBottom: 16,
        backgroundColor: theme.colors.surface,
    },
    cardTitle: {
        color: theme.colors.onSurface,
        fontWeight: '700',
        marginBottom: 16,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 16,
    },
    titleContainer: {
        flex: 1,
        marginRight: 16,
    },
    strategyName: {
        color: theme.colors.onSurface,
        fontWeight: '700',
        marginBottom: 4,
    },
    strategyType: {
        color: theme.colors.onSurfaceVariant,
    },
    statusChip: {
        height: 32,
    },
    description: {
        color: theme.colors.onSurface,
        lineHeight: 20,
        marginBottom: 16,
    },
    metaInfo: {
        gap: 8,
    },
    metaItem: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    metaLabel: {
        color: theme.colors.onSurfaceVariant,
    },
    metaValue: {
        color: theme.colors.onSurface,
        fontWeight: '500',
    },
    performanceGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
        marginBottom: 20,
    },
    performanceItem: {
        width: '48%',
        alignItems: 'center',
        marginBottom: 16,
        padding: 12,
        backgroundColor: theme.colors.surfaceVariant,
        borderRadius: 8,
    },
    performanceValue: {
        color: theme.colors.primary,
        fontWeight: '700',
        marginBottom: 4,
    },
    performanceLabel: {
        color: theme.colors.onSurfaceVariant,
        textAlign: 'center',
    },
    progressContainer: {
        marginTop: 8,
    },
    progressLabel: {
        color: theme.colors.onSurface,
        marginBottom: 8,
        fontWeight: '500',
    },
    progressBar: {
        height: 8,
        borderRadius: 4,
    },
    parametersContainer: {
        gap: 12,
    },
    parameterRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: 4,
    },
    parameterLabel: {
        color: theme.colors.onSurfaceVariant,
        fontWeight: '500',
    },
    parameterValue: {
        color: theme.colors.onSurface,
        fontWeight: '600',
    },
    sideChip: {
        height: 24,
    },
    pnlText: {
        fontWeight: '600',
    },
    actionsContainer: {
        flexDirection: 'row',
        gap: 12,
        marginBottom: 12,
    },
    actionButton: {
        flex: 1,
    },
});

export default StrategyDetailsScreen;
