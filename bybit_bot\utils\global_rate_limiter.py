"""
Global Rate Limiter for Bybit API
Ensures all API requests across the entire system respect rate limits
"""

import asyncio
import time
import logging
from typing import Optional, Dict, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class RateLimitConfig:
    """Rate limit configuration - OPTIMIZED for profit generation"""
    requests_per_second: float = 8.0    # OPTIMIZED: 8 requests per second (well below Bybit's 10/sec limit)
    requests_per_minute: int = 400       # OPTIMIZED: 400 requests per minute (well below Bybit's 600/min limit)
    burst_limit: int = 5                 # REASONABLE BURST: 5 requests at once
    emergency_delay: float = 60.0        # REASONABLE EMERGENCY: 1 minute delay
    max_concurrent: int = 4              # REASONABLE CONCURRENCY: 4 concurrent requests

class GlobalRateLimiter:
    """
    Global rate limiter that ensures all API requests across the system
    respect Bybit's rate limits to prevent "Access too frequent" errors
    """
    
    _instance: Optional['GlobalRateLimiter'] = None
    _lock = asyncio.Lock()
    
    def __new__(cls) -> 'GlobalRateLimiter':
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
            
        self._initialized = True
        self.config = RateLimitConfig()
        
        # Request tracking
        self.last_request_time = 0.0
        self.request_count_second = 0
        self.request_count_minute = 0
        self.minute_start_time = time.time()
        self.second_start_time = time.time()
        
        # Error tracking
        self.consecutive_errors = 0
        self.last_error_time = 0.0
        self.emergency_mode = False
        
        # Concurrency control
        self.semaphore = asyncio.Semaphore(self.config.max_concurrent)
        self.request_queue = asyncio.Queue(maxsize=10)
        
        logger.info(f"Global Rate Limiter initialized: {self.config.requests_per_second} req/sec")
        logger.info(f"Rate limiter state: emergency_mode={self.emergency_mode}, consecutive_errors={self.consecutive_errors}")
    
    async def acquire(self, priority: str = "normal") -> None:
        """
        Acquire permission to make an API request
        This method MUST be called before any Bybit API request
        """
        async with self.semaphore:
            await self._enforce_rate_limits()
            
            # Track the request
            current_time = time.time()
            self.last_request_time = current_time
            self.request_count_second += 1
            self.request_count_minute += 1
            
            logger.debug(f"API request permitted (priority: {priority})")
    
    async def _enforce_rate_limits(self) -> None:
        """Enforce all rate limiting rules"""
        current_time = time.time()
        
        # Handle emergency mode (only after ACTUAL rate limit errors from Bybit)
        if self.emergency_mode:
            emergency_duration = getattr(self.config, 'emergency_delay', 60)
            time_remaining = emergency_duration - (current_time - self.last_error_time)

            if time_remaining > 0:
                logger.warning(f"EMERGENCY MODE: Waiting {time_remaining:.0f} seconds before resuming API activity")
                # Only wait if we have a significant time remaining (>5 seconds)
                if time_remaining > 5:
                    await asyncio.sleep(min(time_remaining, 10))  # Cap sleep at 10 seconds per check
                else:
                    await asyncio.sleep(time_remaining)

                # Check if we can exit emergency mode
                if time_remaining <= 5:
                    self.emergency_mode = False
                    self.consecutive_errors = 0
                    # Reset to aggressive rate after emergency
                    self.config.requests_per_second = 25.0  # Start at half normal rate
                    logger.info("Emergency mode deactivated, rate limit reset to 25.0 req/sec")
            else:
                # Emergency period has passed
                self.emergency_mode = False
                self.consecutive_errors = 0
                self.config.requests_per_second = 25.0  # Start at half normal rate
                logger.info("Emergency mode deactivated, rate limit reset to 25.0 req/sec")
        
        # Reset counters
        if current_time - self.second_start_time >= 1.0:
            self.request_count_second = 0
            self.second_start_time = current_time
            
        if current_time - self.minute_start_time >= 60.0:
            self.request_count_minute = 0
            self.minute_start_time = current_time
        
        # Check per-second limit
        if self.request_count_second >= self.config.requests_per_second:
            sleep_time = 1.0 - (current_time - self.second_start_time)
            if sleep_time > 0:
                logger.debug(f"Per-second rate limit hit, waiting {sleep_time:.2f}s")
                await asyncio.sleep(sleep_time)
                self.request_count_second = 0
                self.second_start_time = time.time()
        
        # Check per-minute limit
        if self.request_count_minute >= self.config.requests_per_minute:
            sleep_time = 60.0 - (current_time - self.minute_start_time)
            if sleep_time > 0:
                logger.warning(f"Per-minute rate limit hit, waiting {sleep_time:.1f}s")
                await asyncio.sleep(sleep_time)
                self.request_count_minute = 0
                self.minute_start_time = time.time()
        
        # Minimum delay between requests
        min_delay = 1.0 / self.config.requests_per_second
        time_since_last = current_time - self.last_request_time
        if time_since_last < min_delay:
            sleep_time = min_delay - time_since_last
            logger.debug(f"Minimum delay enforcement, waiting {sleep_time:.2f}s")
            await asyncio.sleep(sleep_time)
    
    def report_error(self, error_message: str) -> None:
        """Report an API error (only trigger emergency for ACTUAL Bybit rate limit errors)"""
        logger.debug(f"Rate limiter received error report: {error_message}")
        self.consecutive_errors += 1
        self.last_error_time = time.time()

        # Only trigger emergency mode for ACTUAL Bybit rate limit responses
        if ("Access too frequent" in error_message and "try again" in error_message) or \
           ("rate_limit" in error_message and "10003" in error_message):
            logger.error(f"ACTUAL RATE LIMIT ERROR FROM BYBIT: {error_message}")

            # Enter emergency mode only for confirmed Bybit rate limit errors
            self.emergency_mode = True

            # If we get the "5 minutes" message from Bybit, respect it but stay functional
            if "5 minutes" in error_message or "300" in error_message:
                self.config.requests_per_second = 3.0  # Reduced but functional: 3 requests per second
                self.config.emergency_delay = 300  # 5 minutes as requested by Bybit
                logger.error("BYBIT RATE LIMIT: Entering 5-minute emergency mode - reducing to 3 req/sec")
                return
            else:
                # For other rate limit errors, reduce but stay functional
                self.config.requests_per_second = 5.0  # Reduced but functional: 5 requests per second
                self.config.emergency_delay = 60  # 1 minute delay
                logger.error("BYBIT RATE LIMIT: Entering 1-minute emergency mode - reducing to 5 req/sec")

        else:
            logger.debug(f"Non-rate-limit API error: {error_message}")
            # For non-rate-limit errors, only slow down after many consecutive errors
            if self.consecutive_errors > 10:  # Increased threshold
                self.config.requests_per_second = max(25.0, self.config.requests_per_second * 0.98)  # Minimal reduction
                logger.debug(f"Slightly reducing rate limit to {self.config.requests_per_second} req/sec after {self.consecutive_errors} errors")
    
    def report_success(self) -> None:
        """Report a successful API request"""
        if self.consecutive_errors > 0:
            self.consecutive_errors = max(0, self.consecutive_errors - 1)

            # Gradually increase rate limit after successful requests
            if self.consecutive_errors == 0:
                # Restore normal rate limit after clearing all errors
                self.config.requests_per_second = 8.0  # Restore to normal optimized rate
                self.emergency_mode = False  # Exit emergency mode
                logger.info(f"Rate limit restored to normal: {self.config.requests_per_second} req/sec - emergency mode disabled")
    
    def reset_emergency_mode(self) -> None:
        """Force reset emergency mode (for startup/debugging)"""
        self.emergency_mode = False
        self.consecutive_errors = 0
        self.config.requests_per_second = 8.0  # Reset to normal optimized rate
        self.last_error_time = 0.0
        logger.info("Emergency mode forcefully reset - rate limit restored to 8.0 req/sec")

    def get_stats(self) -> Dict[str, Any]:
        """Get current rate limiter statistics"""
        return {
            "requests_per_second": self.config.requests_per_second,
            "requests_per_minute": self.config.requests_per_minute,
            "consecutive_errors": self.consecutive_errors,
            "emergency_mode": self.emergency_mode,
            "request_count_second": self.request_count_second,
            "request_count_minute": self.request_count_minute,
            "last_error_time": self.last_error_time
        }

# Global instance
rate_limiter = GlobalRateLimiter()
