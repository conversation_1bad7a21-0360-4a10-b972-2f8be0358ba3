import React from 'react'
import {
    <PERSON>,
    <PERSON><PERSON>,
    Card,
    CardContent,
    Collapse,
    IconButton,
    Typography,
    Alert,
    AlertTitle
} from '@mui/material'
import {
    ErrorOutline,
    Refresh,
    ExpandMore,
    ExpandLess,
    BugReport
} from '@mui/icons-material'
import { motion } from 'framer-motion'

/**
 * Enhanced Error Boundary with user-friendly error handling
 * Provides detailed error information and recovery options
 */
class ErrorBoundary extends React.Component {
    constructor(props) {
        super(props)
        this.state = {
            hasError: false,
            error: null,
            errorInfo: null,
            showDetails: false,
            retryCount: 0
        }
    }
    
    static getDerivedStateFromError(error) {
        return { hasError: true }
    }
    
    componentDidCatch(error, errorInfo) {
        this.setState({
            error,
            errorInfo
        })
        
        // Log error to console in development
        if (process.env.NODE_ENV === 'development') {
            console.error('Error Boundary caught an error:', error, errorInfo)
        }
        
        // Send error to monitoring service in production
        if (process.env.NODE_ENV === 'production') {
            this.logErrorToService(error, errorInfo)
        }
    }
    
    logErrorToService = (error, errorInfo) => {
        try {
            // Send error to your monitoring service
            fetch('/api/errors', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: error.message,
                    stack: error.stack,
                    componentStack: errorInfo.componentStack,
                    timestamp: new Date().toISOString(),
                    userAgent: navigator.userAgent,
                    url: window.location.href
                })
            }).catch(() => {
                // Silently fail if error reporting fails
            })
        } catch (e) {
            // Silently fail if error reporting fails
        }
    }
    
    handleRetry = () => {
        this.setState(prevState => ({
            hasError: false,
            error: null,
            errorInfo: null,
            showDetails: false,
            retryCount: prevState.retryCount + 1
        }))
    }
    
    handleReload = () => {
        window.location.reload()
    }
    
    toggleDetails = () => {
        this.setState(prevState => ({
            showDetails: !prevState.showDetails
        }))
    }
    
    render() {
        if (this.state.hasError) {
            const { error, errorInfo, showDetails, retryCount } = this.state
            const { fallback: CustomFallback, minimal = false } = this.props
            
            // Use custom fallback if provided
            if (CustomFallback) {
                return (
                    <CustomFallback
                        error={error}
                        errorInfo={errorInfo}
                        onRetry={this.handleRetry}
                        onReload={this.handleReload}
                        retryCount={retryCount}
                    />
                )
            }
            
            // Minimal error display for small components
            if (minimal) {
                return (
                    <Alert 
                        severity="error" 
                        action={
                            <Button 
                                color="inherit" 
                                size="small" 
                                onClick={this.handleRetry}
                            >
                                Retry
                            </Button>
                        }
                    >
                        Something went wrong
                    </Alert>
                )
            }
            
            // Full error display
            return (
                <Box
                    sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        minHeight: '400px',
                        p: 3,
                        background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%)'
                    }}
                >
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5 }}
                    >
                        <Card
                            sx={{
                                maxWidth: 600,
                                background: 'rgba(255, 255, 255, 0.05)',
                                border: '1px solid rgba(255, 82, 82, 0.3)',
                                borderRadius: 2
                            }}
                        >
                            <CardContent sx={{ p: 4 }}>
                                {/* Error Icon and Title */}
                                <Box
                                    sx={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        mb: 3
                                    }}
                                >
                                    <ErrorOutline
                                        sx={{
                                            fontSize: 48,
                                            color: '#ff5252',
                                            mr: 2
                                        }}
                                    />
                                    <Box>
                                        <Typography
                                            variant="h5"
                                            sx={{
                                                color: '#fff',
                                                fontWeight: 600,
                                                mb: 1
                                            }}
                                        >
                                            Oops! Something went wrong
                                        </Typography>
                                        <Typography
                                            variant="body2"
                                            sx={{ color: '#b3b3b3' }}
                                        >
                                            We encountered an unexpected error
                                        </Typography>
                                    </Box>
                                </Box>
                                
                                {/* Error Message */}
                                {error && (
                                    <Alert
                                        severity="error"
                                        sx={{
                                            mb: 3,
                                            backgroundColor: 'rgba(255, 82, 82, 0.1)',
                                            border: '1px solid rgba(255, 82, 82, 0.2)',
                                            '& .MuiAlert-message': {
                                                color: '#fff'
                                            }
                                        }}
                                    >
                                        <AlertTitle>Error Details</AlertTitle>
                                        {error.message || 'An unknown error occurred'}
                                    </Alert>
                                )}
                                
                                {/* Action Buttons */}
                                <Box
                                    sx={{
                                        display: 'flex',
                                        gap: 2,
                                        mb: 3,
                                        flexWrap: 'wrap'
                                    }}
                                >
                                    <Button
                                        variant="contained"
                                        startIcon={<Refresh />}
                                        onClick={this.handleRetry}
                                        sx={{
                                            backgroundColor: '#00ff88',
                                            color: '#000',
                                            '&:hover': {
                                                backgroundColor: '#00cc6a'
                                            }
                                        }}
                                    >
                                        Try Again
                                    </Button>
                                    
                                    <Button
                                        variant="outlined"
                                        onClick={this.handleReload}
                                        sx={{
                                            borderColor: 'rgba(255, 255, 255, 0.3)',
                                            color: '#fff',
                                            '&:hover': {
                                                borderColor: 'rgba(255, 255, 255, 0.5)',
                                                backgroundColor: 'rgba(255, 255, 255, 0.05)'
                                            }
                                        }}
                                    >
                                        Reload Page
                                    </Button>
                                    
                                    {process.env.NODE_ENV === 'development' && (
                                        <IconButton
                                            onClick={this.toggleDetails}
                                            sx={{ color: '#b3b3b3' }}
                                        >
                                            {showDetails ? <ExpandLess /> : <ExpandMore />}
                                        </IconButton>
                                    )}
                                </Box>
                                
                                {/* Retry Count */}
                                {retryCount > 0 && (
                                    <Typography
                                        variant="caption"
                                        sx={{
                                            color: '#b3b3b3',
                                            display: 'block',
                                            mb: 2
                                        }}
                                    >
                                        Retry attempts: {retryCount}
                                    </Typography>
                                )}
                                
                                {/* Developer Details */}
                                {process.env.NODE_ENV === 'development' && (
                                    <Collapse in={showDetails}>
                                        <Box
                                            sx={{
                                                mt: 2,
                                                p: 2,
                                                backgroundColor: 'rgba(0, 0, 0, 0.3)',
                                                borderRadius: 1,
                                                border: '1px solid rgba(255, 255, 255, 0.1)'
                                            }}
                                        >
                                            <Typography
                                                variant="subtitle2"
                                                sx={{
                                                    color: '#ff7043',
                                                    mb: 1,
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    gap: 1
                                                }}
                                            >
                                                <BugReport fontSize="small" />
                                                Developer Information
                                            </Typography>
                                            
                                            {error && (
                                                <Box sx={{ mb: 2 }}>
                                                    <Typography
                                                        variant="caption"
                                                        sx={{ color: '#b3b3b3' }}
                                                    >
                                                        Error Stack:
                                                    </Typography>
                                                    <pre
                                                        style={{
                                                            fontSize: '11px',
                                                            color: '#ff5252',
                                                            whiteSpace: 'pre-wrap',
                                                            wordBreak: 'break-word',
                                                            margin: '8px 0',
                                                            maxHeight: '200px',
                                                            overflow: 'auto'
                                                        }}
                                                    >
                                                        {error.stack}
                                                    </pre>
                                                </Box>
                                            )}
                                            
                                            {errorInfo && (
                                                <Box>
                                                    <Typography
                                                        variant="caption"
                                                        sx={{ color: '#b3b3b3' }}
                                                    >
                                                        Component Stack:
                                                    </Typography>
                                                    <pre
                                                        style={{
                                                            fontSize: '11px',
                                                            color: '#ffa726',
                                                            whiteSpace: 'pre-wrap',
                                                            wordBreak: 'break-word',
                                                            margin: '8px 0',
                                                            maxHeight: '200px',
                                                            overflow: 'auto'
                                                        }}
                                                    >
                                                        {errorInfo.componentStack}
                                                    </pre>
                                                </Box>
                                            )}
                                        </Box>
                                    </Collapse>
                                )}
                            </CardContent>
                        </Card>
                    </motion.div>
                </Box>
            )
        }
        
        return this.props.children
    }
}

export default ErrorBoundary
