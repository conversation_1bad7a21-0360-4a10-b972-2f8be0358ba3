import { Box, Card, CardContent, CircularProgress, Typography } from '@mui/material'
import { motion } from 'framer-motion'

const MetricCard = ({ title, value, icon, color, loading = false, subtitle }) => {
    return (
        <motion.div
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
        >
            <Card
                sx={{
                    background: 'rgba(255, 255, 255, 0.03)',
                    backdropFilter: 'blur(20px)',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    borderRadius: 3,
                    height: '100%',
                    position: 'relative',
                    overflow: 'hidden',
                    '&:hover': {
                        border: `1px solid ${color}30`,
                        boxShadow: `0 8px 32px ${color}20`,
                    },
                }}
            >
                {/* Animated background */}
                <Box
                    sx={{
                        position: 'absolute',
                        top: 0,
                        right: 0,
                        width: 100,
                        height: 100,
                        background: `radial-gradient(circle, ${color}15 0%, transparent 70%)`,
                        opacity: 0.6,
                    }}
                />

                <CardContent sx={{ position: 'relative', zIndex: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                        <Typography
                            variant="subtitle2"
                            sx={{
                                color: '#b3b3b3',
                                fontWeight: 500,
                                textTransform: 'uppercase',
                                letterSpacing: 1,
                            }}
                        >
                            {title}
                        </Typography>
                        <Box
                            sx={{
                                color: color,
                                opacity: 0.8,
                                display: 'flex',
                                alignItems: 'center',
                            }}
                        >
                            {icon}
                        </Box>
                    </Box>

                    {loading ? (
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', py: 2 }}>
                            <CircularProgress size={24} sx={{ color: color }} />
                        </Box>
                    ) : (
                        <>
                            <Typography
                                variant="h4"
                                sx={{
                                    fontWeight: 700,
                                    color: '#ffffff',
                                    mb: subtitle ? 0.5 : 0,
                                    lineHeight: 1.2,
                                }}
                            >
                                {value}
                            </Typography>
                            {subtitle && (
                                <Typography
                                    variant="caption"
                                    sx={{
                                        color: '#888',
                                        fontSize: '0.75rem',
                                    }}
                                >
                                    {subtitle}
                                </Typography>
                            )}
                        </>
                    )}
                </CardContent>
            </Card>
        </motion.div>
    )
}

export default MetricCard
