# 🔧 COMPLETE SETUP GUIDE FOR MSI CYBORG 15 A12VF

# Step-by-step installation for Autonomous Trading System UI

## 📋 PREREQUISITES FOR YOUR MOTOROLA MOTO G32 & MSI LAPTOP

### 🖥️ MSI Cyborg 15 A12VF Setup (Intel i5-12450H, 16GB RAM)

## STEP 1: Install Node.js

1. **Download Node.js 18+ LTS** (recommended for React Native)
   - Go to: <https://nodejs.org/>
   - Download "LTS" version (18.x or higher)
   - Choose "Windows Installer (.msi)" for 64-bit

2. **Install Node.js**
   - Run the downloaded .msi file
   - Accept all defaults
   - Check "Automatically install necessary tools" if prompted
   - Restart PowerShell after installation

3. **Verify Installation**

   ```powershell
   node --version    # Should show v18.x.x or higher
   npm --version     # Should show 9.x.x or higher
   ```

## STEP 2: Install Git (if not already installed)

1. Download from: <https://git-scm.com/download/win>
2. Install with default settings

## STEP 3: Install Android Studio (for mobile app)

1. **Download Android Studio**
   - Go to: <https://developer.android.com/studio>
   - Download "Android Studio Electric Eel" or newer

2. **Install Android Studio**
   - Run the installer
   - Choose "Standard" installation
   - Let it download Android SDK components

3. **Setup Android SDK**
   - Open Android Studio
   - Go to Settings > Appearance & Behavior > System Settings > Android SDK
   - Install "Android 13 (API 33)" - matches your Moto G32
   - Note the SDK path (usually C:\Users\<USER>\AppData\Local\Android\Sdk)

4. **Set Environment Variables**

   ```powershell
   # Add to system PATH
   $env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
   $env:PATH += ";$env:ANDROID_HOME\platform-tools;$env:ANDROID_HOME\tools"
   ```

## STEP 4: Install React Native CLI

```powershell
npm install -g react-native-cli
npm install -g @react-native-community/cli
```

## STEP 5: Install Java JDK 11 (required for Android builds)

1. Download from: <https://www.oracle.com/java/technologies/javase/jdk11-archive-downloads.html>
2. Or use OpenJDK: <https://adoptium.net/>
3. Install and note the installation path
4. Set JAVA_HOME environment variable

---

## 📱 MOTOROLA MOTO G32 SETUP (XT2235-2, Android 13)

### Enable Developer Mode

1. Go to **Settings > About phone**
2. Tap **Build number** 7 times
3. Enter your PIN/password
4. You'll see "You are now a developer!"

### Enable USB Debugging

1. Go to **Settings > System > Developer options**
2. Turn on **USB debugging**
3. Turn on **Install via USB** (if available)

### Enable Unknown Sources

1. Go to **Settings > Apps & notifications > Special app access**
2. Select **Install unknown apps**
3. Choose your file manager app
4. Allow **Install unknown apps**

---

## 🚀 QUICK START COMMANDS

### After installing all prerequisites

1. **Install Web UI Dependencies**

   ```powershell
   cd "E:\The_real_deal_copy\Bybit_Bot\BOT\frontend"
   npm install
   ```

2. **Install Mobile App Dependencies**

   ```powershell
   cd "E:\The_real_deal_copy\Bybit_Bot\BOT\mobile"
   npm install
   ```

3. **Start Web Development Server**

   ```powershell
   cd "E:\The_real_deal_copy\Bybit_Bot\BOT\frontend"
   npm run dev
   ```

4. **Build Mobile App for Moto G32**

   ```powershell
   cd "E:\The_real_deal_copy\Bybit_Bot\BOT\mobile"
   .\build-moto-g32.ps1
   ```

---

## 🔧 TROUBLESHOOTING

### If build fails

1. Clear cache: `npm cache clean --force`
2. Delete node_modules: `Remove-Item -Recurse node_modules`
3. Reinstall: `npm install`

### If Android build fails

1. Check Android SDK path
2. Verify Java JDK installation
3. Clean project: `cd android && .\gradlew clean`

---

## 🎯 HARDWARE OPTIMIZATIONS FOR MSI CYBORG 15

Your Intel i5-12450H and 16GB RAM are perfect for development:

- **Use all CPU cores**: Builds will use 8 threads (4P + 4E cores)
- **Memory allocation**: Set to 8GB for Node.js processes
- **Storage**: Use SSD if available for faster builds
- **Power mode**: Set to "High Performance" during development

---

## 📱 EXPECTED RESULTS

**Web UI**: Modern, responsive trading dashboard at <http://localhost:3000>
**Mobile App**: Native Android APK optimized for Moto G32's 6.5" display

The mobile app will be specifically tuned for:

- ✅ MediaTek Helio G37 processor
- ✅ 4GB/6GB RAM variants
- ✅ 1600x720 resolution
- ✅ Android 13 features
- ✅ Fingerprint authentication

---

**🎉 Once setup is complete, you'll have a professional trading interface that works perfectly with your devices!**
