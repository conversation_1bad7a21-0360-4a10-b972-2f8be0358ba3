#!/usr/bin/env python3
"""
Create missing database tables for memory system
"""

import asyncio
from bybit_bot.database.connection import DatabaseManager
from pathlib import Path
import yaml

async def create_missing_tables():
    print('Creating missing database tables...')
    
    # Load config
    config_path = Path('config.yaml')
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    db_manager = DatabaseManager(config)
    await db_manager.initialize()
    
    # Create missing tables
    missing_tables = [
        '''CREATE TABLE IF NOT EXISTS time_aware_memories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            memory_type TEXT NOT NULL,
            content TEXT NOT NULL,
            context TEXT,
            importance REAL DEFAULT 0.5,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            access_count INTEGER DEFAULT 0,
            time_context TEXT,
            temporal_relevance REAL DEFAULT 1.0
        )''',
        '''CREATE TABLE IF NOT EXISTS market_correlations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol_pair TEXT NOT NULL,
            correlation_value REAL NOT NULL,
            timeframe TEXT NOT NULL,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            confidence REAL DEFAULT 0.5
        )''',
        '''CREATE TABLE IF NOT EXISTS strategy_performance_memories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            strategy_name TEXT NOT NULL,
            symbol TEXT NOT NULL,
            performance_data TEXT NOT NULL,
            success_rate REAL DEFAULT 0.0,
            avg_profit REAL DEFAULT 0.0,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            trade_count INTEGER DEFAULT 0
        )'''
    ]
    
    for table_sql in missing_tables:
        try:
            await db_manager.execute_sql(table_sql)
            table_name = table_sql.split()[5]
            print(f'SUCCESS: Created table {table_name}')
        except Exception as e:
            print(f'Error creating table: {e}')
    
    await db_manager.close()
    print('Database tables creation completed successfully')

if __name__ == "__main__":
    asyncio.run(create_missing_tables())
