import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Alert, Share } from 'react-native';
import { Text, Card, SegmentedButtons, FAB, Portal, Modal, Button } from 'react-native-paper';
import { showMessage } from 'react-native-flash-message';
import LogViewer from '../components/LogViewer';
import LoggingControls from '../components/LoggingControls';
import loggingService, { LogStats } from '../services/loggingService';
import { theme } from '../styles/theme';

const LogsScreen: React.FC = () => {
    const [activeTab, setActiveTab] = useState('viewer');
    const [stats, setStats] = useState<LogStats | null>(null);
    const [settingsModalVisible, setSettingsModalVisible] = useState(false);
    const [autoRefresh, setAutoRefresh] = useState(false);

    useEffect(() => {
        loadStats();
        const interval = setInterval(loadStats, 30000); // Update stats every 30 seconds
        return () => clearInterval(interval);
    }, []);

    const loadStats = async () => {
        try {
            const statsData = await loggingService.getLoggingStats();
            setStats(statsData);
        } catch (error) {
            console.error('Failed to load stats:', error);
        }
    };

    const handleExportAllLogs = async () => {
        try {
            Alert.alert(
                'Export All Logs',
                'This will export all logs from all categories. This may take a moment.',
                [
                    { text: 'Cancel', style: 'cancel' },
                    {
                        text: 'Export',
                        onPress: async () => {
                            try {
                                const categories = await loggingService.getLoggingCategories();
                                let allLogsText = '# BYBIT TRADING BOT - ALL LOGS EXPORT\n';
                                allLogsText += `# Exported: ${new Date().toISOString()}\n\n`;

                                for (const category of categories.categories) {
                                    try {
                                        const exportText = await loggingService.exportLogs(category.id);
                                        allLogsText += exportText + '\n\n';
                                    } catch (error) {
                                        allLogsText += `# Failed to export ${category.name} logs\n\n`;
                                    }
                                }

                                await Share.share({
                                    message: allLogsText,
                                    title: 'Trading Bot Logs Export',
                                });

                                showMessage({
                                    message: 'Logs exported successfully',
                                    type: 'success',
                                });
                            } catch (error) {
                                showMessage({
                                    message: 'Failed to export logs',
                                    description: error instanceof Error ? error.message : 'Unknown error',
                                    type: 'danger',
                                });
                            }
                        },
                    },
                ]
            );
        } catch (error) {
            showMessage({
                message: 'Failed to prepare export',
                type: 'danger',
            });
        }
    };

    const handleClearAllLogs = () => {
        Alert.alert(
            'Clear All Logs',
            'Are you sure you want to clear all logs from all categories? This action cannot be undone.',
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Clear All',
                    style: 'destructive',
                    onPress: async () => {
                        try {
                            const categories = await loggingService.getLoggingCategories();
                            const promises = categories.categories.map(category =>
                                loggingService.clearLogsByCategory(category.id)
                            );
                            await Promise.all(promises);
                            
                            loadStats(); // Refresh stats
                            showMessage({
                                message: 'All logs cleared successfully',
                                type: 'success',
                            });
                        } catch (error) {
                            showMessage({
                                message: 'Failed to clear logs',
                                description: error instanceof Error ? error.message : 'Unknown error',
                                type: 'danger',
                            });
                        }
                    },
                },
            ]
        );
    };

    const renderStatsCard = () => {
        if (!stats) return null;

        return (
            <Card style={styles.statsCard}>
                <Card.Content>
                    <Text variant="titleMedium" style={styles.statsTitle}>
                        Logging Statistics
                    </Text>
                    <View style={styles.statsGrid}>
                        <View style={styles.statItem}>
                            <Text variant="headlineSmall" style={styles.statValue}>
                                {stats.total_lines.toLocaleString()}
                            </Text>
                            <Text variant="bodySmall" style={styles.statLabel}>
                                Total Log Entries
                            </Text>
                        </View>
                        <View style={styles.statItem}>
                            <Text variant="headlineSmall" style={styles.statValue}>
                                {stats.total_size_mb.toFixed(1)} MB
                            </Text>
                            <Text variant="bodySmall" style={styles.statLabel}>
                                Total Size
                            </Text>
                        </View>
                        <View style={styles.statItem}>
                            <Text variant="headlineSmall" style={styles.statValue}>
                                {Object.keys(stats.categories).length}
                            </Text>
                            <Text variant="bodySmall" style={styles.statLabel}>
                                Active Categories
                            </Text>
                        </View>
                        <View style={styles.statItem}>
                            <Text variant="headlineSmall" style={styles.statValue}>
                                {Object.values(stats.categories).filter(cat => cat.exists).length}
                            </Text>
                            <Text variant="bodySmall" style={styles.statLabel}>
                                Log Files
                            </Text>
                        </View>
                    </View>
                </Card.Content>
            </Card>
        );
    };

    const renderContent = () => {
        switch (activeTab) {
            case 'viewer':
                return (
                    <View style={styles.viewerContainer}>
                        {renderStatsCard()}
                        <LogViewer autoRefresh={autoRefresh} />
                    </View>
                );
            case 'settings':
                return <LoggingControls onConfigChange={loadStats} />;
            default:
                return null;
        }
    };

    return (
        <View style={styles.container}>
            {/* Tab Navigation */}
            <View style={styles.tabContainer}>
                <SegmentedButtons
                    value={activeTab}
                    onValueChange={setActiveTab}
                    buttons={[
                        {
                            value: 'viewer',
                            label: 'Log Viewer',
                            icon: 'visibility',
                        },
                        {
                            value: 'settings',
                            label: 'Log Settings',
                            icon: 'settings',
                        },
                    ]}
                    style={styles.segmentedButtons}
                />
            </View>

            {/* Content */}
            <View style={styles.contentContainer}>
                {renderContent()}
            </View>

            {/* Floating Action Buttons */}
            {activeTab === 'viewer' && (
                <>
                    <FAB
                        icon={autoRefresh ? 'pause' : 'play-arrow'}
                        style={[styles.fab, styles.refreshFab]}
                        onPress={() => setAutoRefresh(!autoRefresh)}
                        label={autoRefresh ? 'Pause' : 'Auto'}
                        variant="tertiary"
                    />
                    <FAB
                        icon="more-vert"
                        style={styles.fab}
                        onPress={() => setSettingsModalVisible(true)}
                    />
                </>
            )}

            {/* Settings Modal */}
            <Portal>
                <Modal
                    visible={settingsModalVisible}
                    onDismiss={() => setSettingsModalVisible(false)}
                    contentContainerStyle={styles.modalContent}
                >
                    <Text variant="titleLarge" style={styles.modalTitle}>
                        Log Actions
                    </Text>
                    <View style={styles.modalActions}>
                        <Button
                            mode="outlined"
                            onPress={handleExportAllLogs}
                            style={styles.modalButton}
                            icon="download"
                        >
                            Export All Logs
                        </Button>
                        <Button
                            mode="outlined"
                            onPress={handleClearAllLogs}
                            style={styles.modalButton}
                            icon="delete-sweep"
                            textColor={theme.colors.error}
                        >
                            Clear All Logs
                        </Button>
                        <Button
                            mode="outlined"
                            onPress={loadStats}
                            style={styles.modalButton}
                            icon="refresh"
                        >
                            Refresh Stats
                        </Button>
                        <Button
                            mode="contained"
                            onPress={() => setSettingsModalVisible(false)}
                            style={styles.modalButton}
                        >
                            Close
                        </Button>
                    </View>
                </Modal>
            </Portal>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: theme.colors.background,
    },
    tabContainer: {
        padding: 16,
        backgroundColor: theme.colors.surface,
        elevation: 2,
    },
    segmentedButtons: {
        backgroundColor: theme.colors.surfaceVariant,
    },
    contentContainer: {
        flex: 1,
    },
    viewerContainer: {
        flex: 1,
    },
    statsCard: {
        margin: 16,
        backgroundColor: theme.colors.surface,
    },
    statsTitle: {
        color: theme.colors.onSurface,
        fontWeight: '700',
        marginBottom: 16,
    },
    statsGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
    },
    statItem: {
        width: '48%',
        alignItems: 'center',
        marginBottom: 16,
        padding: 12,
        backgroundColor: theme.colors.surfaceVariant,
        borderRadius: 8,
    },
    statValue: {
        color: theme.colors.primary,
        fontWeight: '700',
        marginBottom: 4,
    },
    statLabel: {
        color: theme.colors.onSurfaceVariant,
        textAlign: 'center',
    },
    fab: {
        position: 'absolute',
        margin: 16,
        right: 0,
        bottom: 0,
        backgroundColor: theme.colors.primary,
    },
    refreshFab: {
        bottom: 80, // Position above the main FAB
        backgroundColor: theme.colors.secondary,
    },
    modalContent: {
        backgroundColor: theme.colors.surface,
        padding: 24,
        margin: 20,
        borderRadius: 16,
    },
    modalTitle: {
        color: theme.colors.onSurface,
        fontWeight: '700',
        marginBottom: 20,
        textAlign: 'center',
    },
    modalActions: {
        gap: 12,
    },
    modalButton: {
        marginVertical: 4,
    },
});

export default LogsScreen;
