#!/usr/bin/env python3
"""
Check margin ratio and risk management status
"""

from dotenv import load_dotenv
import os
from pybit.unified_trading import HTTP

load_dotenv()

def check_margin_status():
    """Check current margin ratio and risk levels"""
    try:
        print("CHECKING MARGIN RATIO AND RISK STATUS")
        print("=" * 50)
        
        # Initialize Bybit session
        session = HTTP(
            testnet=False,
            api_key=os.getenv('BYBIT_API_KEY'),
            api_secret=os.getenv('BYBIT_API_SECRET')
        )
        
        # Get account info
        account_info = session.get_wallet_balance(accountType='UNIFIED')
        if account_info['retCode'] == 0:
            account_data = account_info['result']['list'][0]
            
            total_equity = float(account_data['totalEquity'])
            available_balance = float(account_data['totalAvailableBalance'])
            total_margin_balance = float(account_data['totalMarginBalance'])
            total_initial_margin = float(account_data['totalInitialMargin'])
            total_maintenance_margin = float(account_data['totalMaintenanceMargin'])
            
            print(f"Account Status:")
            print(f"  Total Equity: ${total_equity:.2f}")
            print(f"  Available Balance: ${available_balance:.2f}")
            print(f"  Total Margin Balance: ${total_margin_balance:.2f}")
            print(f"  Initial Margin: ${total_initial_margin:.2f}")
            print(f"  Maintenance Margin: ${total_maintenance_margin:.2f}")
            
            # Calculate margin ratio
            if total_margin_balance > 0:
                margin_ratio = (total_initial_margin / total_margin_balance) * 100
                print(f"\nMARGIN RATIO: {margin_ratio:.2f}%")
                
                # Check risk levels based on main.py logic
                if margin_ratio > 95:
                    print("  RISK LEVEL: EXTREME DANGER - EMERGENCY CLOSE ALL POSITIONS")
                    print("  TRADING STATUS: BLOCKED - System will close all positions")
                elif margin_ratio > 90:
                    print("  RISK LEVEL: HIGH RISK - NO NEW POSITIONS")
                    print("  TRADING STATUS: BLOCKED - No new positions allowed")
                elif margin_ratio > 80:
                    print("  RISK LEVEL: ELEVATED RISK - MICRO POSITIONS ONLY")
                    print("  TRADING STATUS: RESTRICTED - Only micro positions")
                elif margin_ratio > 70:
                    print("  RISK LEVEL: MODERATE RISK - SMALL POSITIONS")
                    print("  TRADING STATUS: LIMITED - Small positions only")
                elif margin_ratio > 50:
                    print("  RISK LEVEL: LOW RISK - NORMAL TRADING")
                    print("  TRADING STATUS: NORMAL - Full trading allowed")
                else:
                    print("  RISK LEVEL: SAFE ZONE - MAXIMUM TRADING")
                    print("  TRADING STATUS: OPTIMAL - Maximum trading allowed")
                    
                # Check if this explains the lack of ultra-scalping trades
                if margin_ratio > 80:
                    print(f"\n*** PROBLEM IDENTIFIED ***")
                    print(f"Margin ratio {margin_ratio:.2f}% is blocking ultra-scalping trades!")
                    print(f"System only allows micro/small positions at this level.")
                else:
                    print(f"\nMargin ratio {margin_ratio:.2f}% should allow normal trading.")
                    print(f"Issue may be elsewhere in the execution pipeline.")
                    
            else:
                print("No margin balance - likely spot trading only")
                
        else:
            print(f"ERROR getting account info: {account_info['retMsg']}")
            
    except Exception as e:
        print(f"ERROR: {e}")

if __name__ == "__main__":
    check_margin_status()
