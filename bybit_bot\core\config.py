"""
Enhanced Configuration management for the Autonomous Bybit Trading Bot
All features and SuperGPT functions enabled
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, field
from dotenv import load_dotenv
import logging

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

@dataclass
class SystemConfig:
    """Core system configuration"""
    name: str = "Autonomous Bybit Trading Bot"
    version: str = "2.0.0"
    environment: str = "production"
    debug_mode: bool = True
    auto_start: bool = True
    self_healing: bool = True
    autonomous_mode: bool = True
    log_level: str = "INFO"
    # Conda environment paths
    conda_base_path: str = "E:\\conda\\miniconda3"
    conda_env_path: str = "E:\\conda\\envs\\bybit-trader"
    python_executable: str = "E:\\conda\\envs\\bybit-trader\\python.exe"
    conda_executable: str = "E:\\conda\\miniconda3\\Scripts\\conda.exe"
    pip_cache_dir: str = "E:\\conda\\pip_cache"
    conda_pkgs_dir: str = "E:\\conda\\pkgs"
    conda_scripts_dir: str = "E:\\conda\\scripts"
    # Node.js paths
    node_js_path: str = "E:\\The_real_deal_copy\\Bybit_Bot\\BOT\\temp_node_extract\\node-v20.11.0-win-x64"
    npm_path: str = "E:\\The_real_deal_copy\\Bybit_Bot\\BOT\\temp_node_extract\\node-v20.11.0-win-x64\\npm.cmd"
    python_path: str = "E:\\conda\\miniconda3\\envs\\bybit-trader\\python.exe"
    conda_path: str = "E:\\conda\\miniconda3\\Scripts\\conda.exe"
    conda_env: str = "bybit-trader"
    max_workers: int = 4
    health_check_interval: int = 30

@dataclass
class SuperGPTConfig:
    """SuperGPT functions configuration"""
    enabled: bool = True
    auto_learning: bool = True
    self_improvement: bool = True
    meta_cognition: bool = True
    advanced_reasoning: bool = True
    auto_optimization: bool = True
    max_profit_mode: bool = True
    self_evolution: bool = True
    recursive_improvement: bool = True
    autonomous_learning: bool = True
    profit_maximization: bool = True
    capabilities: Dict[str, bool] = field(default_factory=lambda: {
        "natural_language_processing": True,
        "code_generation": True,
        "strategy_optimization": True,
        "market_analysis": True,
        "risk_assessment": True,
        "performance_prediction": True,
        "anomaly_detection": True,
        "adaptive_learning": True
    })
    models: Dict[str, str] = field(default_factory=lambda: {
        "primary": "gpt-4-turbo",
        "fallback": "gpt-3.5-turbo"
    })
    learning: Dict[str, bool] = field(default_factory=lambda: {
        "continuous_learning": True,
        "experience_replay": True,
        "meta_learning": True,
        "transfer_learning": True,
        "reinforcement_learning": True
    })

@dataclass
class EnhancedTradingConfig:
    """Enhanced trading configuration with margin trading support"""
    enabled: bool = True
    mode: str = "autonomous"
    environment: str = "production"
    paper_trading: bool = False
    live_trading: bool = True
    testnet: bool = False
    exchange: str = "bybit"
    max_risk_per_trade: float = 0.005  # REDUCED from 0.02 to 0.005 (0.5% per trade)
    max_daily_loss: float = 0.02       # REDUCED from 0.05 to 0.02 (2% daily loss)
    max_position_size: float = 50.0    # REDUCED from 25000.0 to 50.0 USD
    leverage_range: List[int] = field(default_factory=lambda: [1, 2])  # REDUCED from [1,5] to [1,2]
    stop_loss_percentage: float = 0.02
    take_profit_percentage: float = 0.04
    trailing_stop_percentage: float = 0.01
    position_sizing_method: str = "conservative"
    profit_compounding: bool = True
    dynamic_position_sizing: bool = True
    trading_pairs: List[str] = field(default_factory=lambda: ["BTCUSDT", "ETHUSDT"])
    primary_trading_pairs: List[str] = field(default_factory=lambda: ["BTCUSDT", "ETHUSDT"])
    order_timeout: int = 30
    slippage_tolerance: float = 0.005
    trading_cycle_interval: int = 30
    max_daily_trades: int = 2000
    risk_per_trade: float = 0.25
    stop_loss_pct: float = 0.002
    take_profit_pct: float = 0.50
    aggressive_mode: bool = True
    maximum_profit_mode: bool = True
    profit_first_mode: bool = True
    
    # ULTRA PROFIT AMPLIFICATION PARAMETERS
    ultra_profit_mode: bool = True
    ultra_scalping_enabled: bool = True
    nano_arbitrage_enabled: bool = True
    velocity_trading_enabled: bool = True
    compound_scaling_enabled: bool = True
    parallel_execution_enabled: bool = True
    profit_acceleration_enabled: bool = True
    ultra_margin_optimization: bool = True
    ai_margin_enhancement: bool = True
    meta_learning_margin_control: bool = True
    
    risk_optimization: str = "maximum_profit"
    execution_mode: str = "immediate"
    trade_frequency: str = "high"
    position_sizing: str = "aggressive"
    daily_profit_target: float = 1875.0
    weekly_profit_target: float = 13125.0
    monthly_profit_target: float = 56250.0
    strategy_count: int = 12
    success_rate_target: float = 0.70
    risk_level: str = "optimized"
    profit_mode: str = "MAXIMUM"
    max_concurrent_orders: int = 5
    order_retry_attempts: int = 3
    position_update_interval: int = 10
    risk_management: Dict[str, Any] = field(default_factory=dict)
    portfolio: Dict[str, Any] = field(default_factory=dict)
    strategies: Dict[str, Any] = field(default_factory=dict)
    execution: Dict[str, Any] = field(default_factory=dict)
    symbols: List[str] = field(default_factory=list)

    # Profit optimization strategies
    scalping_enabled: bool = True
    arbitrage_enabled: bool = True
    grid_trading_enabled: bool = True
    momentum_trading_enabled: bool = True
    market_making_enabled: bool = True
    copy_trading_enabled: bool = True
    social_trading_enabled: bool = True
    ai_prediction_enabled: bool = True
    ml_optimization_enabled: bool = True

    # MARGIN TRADING CONFIGURATION - CONSERVATIVE SETTINGS FOR SAFETY
    margin_trading_enabled: bool = True
    cross_margin_enabled: bool = True
    isolated_margin_enabled: bool = True
    default_leverage: int = 2  # REDUCED from 10 to 2 for safety
    max_leverage: int = 5      # REDUCED from 100 to 5 for safety
    margin_mode: str = "cross"  # cross, isolated
    auto_add_margin: bool = True
    position_mode: str = "hedge"  # one-way, hedge
    leverage_optimization: bool = True
    dynamic_leverage: bool = True
    risk_based_leverage: bool = True

    # Advanced margin features - CONSERVATIVE SETTINGS
    liquidation_prevention: bool = True
    margin_call_alerts: bool = True
    cross_margin_ratio_target: float = 0.15  # REDUCED Target 15% margin ratio (was 30%)
    isolated_margin_buffer: float = 0.3      # INCREASED 30% buffer for isolated positions (was 20%)
    leverage_scaling_factor: float = 1.2     # REDUCED Scale leverage based on confidence (was 1.5)
    max_margin_utilization: float = 0.4      # REDUCED Max 40% margin utilization (was 80%)
    
    # Position management
    hedge_mode_enabled: bool = True
    one_way_mode_enabled: bool = True
    partial_close_enabled: bool = True
    position_scaling_enabled: bool = True
    
    # Advanced order types
    conditional_orders_enabled: bool = True
    stop_limit_orders_enabled: bool = True
    take_profit_orders_enabled: bool = True
    trailing_stop_enabled: bool = True
    iceberg_orders_enabled: bool = True
    twap_orders_enabled: bool = True
    
    # Risk controls for margin trading - ULTRA CONSERVATIVE SETTINGS
    margin_risk_controls: Dict[str, Any] = field(default_factory=lambda: {
        "max_margin_ratio": 0.5,         # REDUCED Emergency close at 50% (was 95%)
        "warning_margin_ratio": 0.3,     # REDUCED Warning at 30% (was 80%)
        "target_margin_ratio": 0.15,     # REDUCED Target 15% (was 30%)
        "liquidation_buffer": 0.2,       # INCREASED 20% buffer from liquidation (was 5%)
        "position_size_scaling": True,   # Scale position size based on margin
        "dynamic_stop_loss": True,       # Adjust stops based on margin ratio
        "emergency_close_enabled": True, # Auto-close on high margin ratio
        "margin_monitoring_interval": 2, # INCREASED Check every 2 seconds (was 5)
        "max_position_value": 50.0,      # NEW: Max $50 position value
        "conservative_mode": True        # NEW: Enable ultra-conservative mode
    })

    # Hardware monitoring
    hardware_check_interval: int = 5

@dataclass
class EnhancedAPIKeysConfig:
    """Enhanced API keys configuration"""
    bybit: Dict[str, Any] = field(default_factory=dict)
    openai: Dict[str, str] = field(default_factory=dict)
    anthropic: Dict[str, str] = field(default_factory=dict)
    google: Dict[str, str] = field(default_factory=dict)
    newsapi: Dict[str, str] = field(default_factory=dict)
    news_api: Dict[str, str] = field(default_factory=dict)
    alpha_vantage: Dict[str, str] = field(default_factory=dict)
    fred: Dict[str, str] = field(default_factory=dict)
    twitter: Dict[str, str] = field(default_factory=dict)
    reddit: Dict[str, str] = field(default_factory=dict)
    openrouter: Dict[str, str] = field(default_factory=dict)

@dataclass
class EnhancedDatabaseConfig:
    """Enhanced database configuration"""
    path: str = "bybit_trading_bot.db"
    type: str = "sqlite"
    url: str = "sqlite:///./bybit_trading_bot.db"
    host: str = "localhost"
    port: int = 5432
    database: str = "bybit_trading_bot"
    user: str = "postgres"
    password: str = "password"
    pool_size: int = 20
    max_overflow: int = 50
    pool_timeout: int = 30
    pool_pre_ping: bool = True
    pool_recycle: int = 3600
    echo: bool = False
    ssl_mode: str = "prefer"
    connect_args: Dict[str, Any] = field(default_factory=dict)
    pragma: Dict[str, Any] = field(default_factory=dict)
    primary: Dict[str, Any] = field(default_factory=dict)
    redis: Dict[str, Any] = field(default_factory=dict)
    timeseries: Dict[str, Any] = field(default_factory=dict)

@dataclass
class EnhancedAIConfig:
    """Enhanced AI/ML configuration"""
    enabled: bool = True
    learning_rate: float = 0.001
    memory_size: int = 10000
    prediction_horizon: int = 24
    learning_enabled: bool = True
    self_healing_enabled: bool = True
    meta_learning_enabled: bool = True
    model_selection_enabled: bool = True
    autonomy_enabled: bool = True
    training: Dict[str, Any] = field(default_factory=dict)
    models: Dict[str, Any] = field(default_factory=dict)
    features: Dict[str, bool] = field(default_factory=dict)
    prediction: Dict[str, Any] = field(default_factory=dict)

# ...existing dataclasses...

class EnhancedBotConfig:
    """Enhanced configuration class with all features enabled"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or "config.yaml"
        self.config_path = Path(self.config_file)
        
        # Initialize all components
        self._load_enhanced_config()
        self._setup_logging()
        self._validate_enhanced_config()
        
    def _load_enhanced_config(self):
        """Load enhanced configuration with all features"""
        if self.config_path.exists():
            with open(self.config_path, 'r') as f:
                config_data = yaml.safe_load(f)
        else:
            # Create default enhanced config
            config_data = self._get_enhanced_default_config()
            self._save_config_to_file(config_data)
        
        # Override with environment variables
        self._override_with_env_vars(config_data)
        
        # Parse enhanced configuration
        self._parse_enhanced_config(config_data)
    
    def _parse_enhanced_config(self, config_data: Dict[str, Any]):
        """Parse enhanced configuration data"""
        # Core components
        self.system = SystemConfig(**config_data.get("system", {}))
        self.supergpt = SuperGPTConfig(**config_data.get("supergpt", {}))
        self.trading = EnhancedTradingConfig(**config_data.get("trading", {}))
        self.api_keys = EnhancedAPIKeysConfig(**config_data.get("api_keys", {}))
        self.database = EnhancedDatabaseConfig(**config_data.get("database", {}))
        self.ai = EnhancedAIConfig(**config_data.get("ai", {}))
        
        # Additional components
        self.data_crawler = config_data.get("data_crawler", {})
        self.performance = config_data.get("performance", {})
        self.hardware = config_data.get("hardware", {})
        self.logging_config = config_data.get("logging", {})
        self.api = config_data.get("api", {})
        self.security = config_data.get("security", {})
        self.notifications = config_data.get("notifications", {})
        self.backtesting = config_data.get("backtesting", {})
        self.development = config_data.get("development", {})
        self.integrations = config_data.get("integrations", {})
        self.experimental = config_data.get("experimental", {})
        
        # Convenience properties
        self._debug_mode = self.system.debug_mode
        self.autonomous_mode = self.system.autonomous_mode
        self.supergpt_enabled = self.supergpt.enabled
        self.trading_enabled = self.trading.enabled
    
    def _get_enhanced_default_config(self) -> Dict[str, Any]:
        """Get enhanced default configuration with all features enabled"""
        return {
            "system": {
                "name": "Autonomous Bybit Trading Bot",
                "version": "2.0.0",
                "environment": "production",
                "debug_mode": True,
                "auto_start": True,
                "self_healing": True,
                "autonomous_mode": True
            },
            "supergpt": {
                "enabled": True,
                "auto_learning": True,
                "self_improvement": True,
                "meta_cognition": True,
                "advanced_reasoning": True,
                "capabilities": {
                    "natural_language_processing": True,
                    "code_generation": True,
                    "strategy_optimization": True,
                    "market_analysis": True,
                    "risk_assessment": True,
                    "performance_prediction": True,
                    "anomaly_detection": True,
                    "adaptive_learning": True
                },
                "models": {
                    "primary": "gpt-4-turbo",
                    "fallback": "gpt-3.5-turbo"
                },
                "learning": {
                    "continuous_learning": True,
                    "experience_replay": True,
                    "meta_learning": True,
                    "transfer_learning": True,
                    "reinforcement_learning": True
                }
            },
            # ...rest of enhanced default config...
        }
    
    def _setup_logging(self):
        """Setup enhanced logging"""
        log_config = self.logging_config
        logging.basicConfig(
            level=getattr(logging, log_config.get("level", "INFO")),
            format=log_config.get("format", "%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        )
        
    def _validate_enhanced_config(self) -> List[str]:
        """Validate enhanced configuration"""
        errors = []
        
        # Validate SuperGPT configuration
        if self.supergpt.enabled and not self.api_keys.openai.get("api_key"):
            errors.append("OpenAI API key required for SuperGPT functions")
        
        # Validate trading configuration
        if self.trading.enabled and not self.api_keys.bybit.get("api_key"):
            errors.append("Bybit API credentials required for trading")
        
        # Validate AI configuration
        if self.ai.enabled and not any([
            self.api_keys.openai.get("api_key"),
            self.api_keys.anthropic.get("api_key"),
            self.api_keys.google.get("api_key")
        ]):
            errors.append("At least one AI API key required for AI functions")
        
        if errors:
            logger.warning(f"Configuration validation errors: {errors}")
        
        return errors
    
    def _override_with_env_vars(self, config_data: Dict[str, Any]):
        """Override configuration with environment variables"""
        # Conda environment paths
        if os.getenv("CONDA_BASE_PATH"):
            config_data.setdefault("system", {})["conda_base_path"] = os.getenv("CONDA_BASE_PATH")
        if os.getenv("CONDA_ENV_PATH"):
            config_data.setdefault("system", {})["conda_env_path"] = os.getenv("CONDA_ENV_PATH")
        if os.getenv("PYTHON_EXECUTABLE"):
            config_data.setdefault("system", {})["python_executable"] = os.getenv("PYTHON_EXECUTABLE")
        if os.getenv("CONDA_EXECUTABLE"):
            config_data.setdefault("system", {})["conda_executable"] = os.getenv("CONDA_EXECUTABLE")
        if os.getenv("PIP_CACHE_DIR"):
            config_data.setdefault("system", {})["pip_cache_dir"] = os.getenv("PIP_CACHE_DIR")
        if os.getenv("CONDA_PKGS_DIR"):
            config_data.setdefault("system", {})["conda_pkgs_dir"] = os.getenv("CONDA_PKGS_DIR")
        if os.getenv("CONDA_SCRIPTS_DIR"):
            config_data.setdefault("system", {})["conda_scripts_dir"] = os.getenv("CONDA_SCRIPTS_DIR")

        # Bybit API credentials
        if os.getenv("BYBIT_API_KEY"):
            config_data.setdefault("api_keys", {}).setdefault("bybit", {})["api_key"] = os.getenv("BYBIT_API_KEY")
        if os.getenv("BYBIT_API_SECRET"):
            config_data.setdefault("api_keys", {}).setdefault("bybit", {})["api_secret"] = os.getenv("BYBIT_API_SECRET")
        if os.getenv("BYBIT_TESTNET"):
            testnet_value = os.getenv("BYBIT_TESTNET")
            config_data.setdefault("api_keys", {}).setdefault("bybit", {})["testnet"] = testnet_value.lower() == "true" if testnet_value else False

        # Trading configuration
        if os.getenv("ENABLE_LIVE_TRADING"):
            live_trading_value = os.getenv("ENABLE_LIVE_TRADING")
            config_data.setdefault("trading", {})["live_trading"] = live_trading_value.lower() == "true" if live_trading_value else False

        # SuperGPT API keys
        if os.getenv("OPENAI_API_KEY"):
            config_data.setdefault("api_keys", {}).setdefault("openai", {})["api_key"] = os.getenv("OPENAI_API_KEY")
        if os.getenv("ANTHROPIC_API_KEY"):
            config_data.setdefault("api_keys", {}).setdefault("anthropic", {})["api_key"] = os.getenv("ANTHROPIC_API_KEY")
        if os.getenv("GOOGLE_API_KEY"):
            config_data.setdefault("api_keys", {}).setdefault("google", {})["api_key"] = os.getenv("GOOGLE_API_KEY")
    
    def _save_config_to_file(self, config_data: Dict[str, Any]):
        """Save configuration to file"""
        with open(self.config_path, 'w') as f:
            yaml.dump(config_data, f, default_flow_style=False, indent=2)
    
    def is_feature_enabled(self, feature_path: str) -> bool:
        """Check if a feature is enabled using dot notation"""
        parts = feature_path.split('.')
        current = self
        
        try:
            for part in parts:
                if hasattr(current, part):
                    current = getattr(current, part)
                elif isinstance(current, dict):
                    current = current.get(part, False)
                else:
                    return False
            return bool(current)
        except:
            return False
    
    def get_feature_config(self, feature_path: str) -> Any:
        """Get configuration for a feature using dot notation"""
        parts = feature_path.split('.')
        current = self
        
        try:
            for part in parts:
                if hasattr(current, part):
                    current = getattr(current, part)
                elif isinstance(current, dict):
                    current = current.get(part, {})
                else:
                    return {}
            return current
        except:
            return {}
    
    def enable_all_features(self):
        """Enable all available features"""
        self.supergpt.enabled = True
        self.trading.enabled = True
        self.ai.enabled = True
        self.ai.learning_enabled = True
        self.ai.self_healing_enabled = True
        self.ai.meta_learning_enabled = True
        self.ai.model_selection_enabled = True
        self.ai.autonomy_enabled = True
        
        # Enable all strategies
        for strategy in self.trading.strategies:
            if isinstance(self.trading.strategies[strategy], dict):
                self.trading.strategies[strategy]["enabled"] = True
        
        logger.info("All features enabled")

    def get_api_key(self, service: str) -> Dict[str, Any]:
        """Get API key configuration for a service"""
        return getattr(self.api_keys, service, {})

    def to_dict(self) -> Dict[str, Any]:
        """Convert config object to dictionary for compatibility with AI components"""
        return {
            "system": {
                "name": self.system.name,
                "version": self.system.version,
                "environment": self.system.environment,
                "debug_mode": self.system.debug_mode,
                "autonomous_mode": self.system.autonomous_mode
            },
            "trading": {
                "enabled": self.trading.enabled,
                "mode": self.trading.mode,
                "environment": self.trading.environment,
                "testnet": self.trading.testnet,
                "exchange": self.trading.exchange,
                "max_risk_per_trade": self.trading.max_risk_per_trade,
                "max_daily_loss": self.trading.max_daily_loss,
                "max_position_size": self.trading.max_position_size,
                "daily_profit_target": self.trading.daily_profit_target
            },
            "ai": {
                "enabled": self.ai.enabled,
                "learning_enabled": self.ai.learning_enabled,
                "autonomy_enabled": self.ai.autonomy_enabled
            },
            "api_keys": {
                "bybit": self.api_keys.bybit,
                "openai": self.api_keys.openai
            }
        }

    def get_trading_pairs(self) -> List[str]:
        """Get list of trading pairs"""
        return getattr(self.trading, 'trading_pairs', ['BTCUSDT', 'ETHUSDT'])

    @property
    def api_host(self) -> str:
        """Get API host"""
        return "127.0.0.1"

    @property
    def api_port(self) -> int:
        """Get API port"""
        return 8000

    @property
    def debug_mode(self) -> bool:
        """Get debug mode"""
        return getattr(self, '_debug_mode', True)

    @property
    def paper_trading(self) -> bool:
        """Get paper trading mode"""
        return getattr(self.trading, 'paper_trading', True)

    @property
    def max_risk_percentage(self) -> float:
        """Get max risk percentage - converted from decimal"""
        return getattr(self.trading, 'max_risk_per_trade', 0.02) * 100

    @property
    def max_drawdown_percentage(self) -> float:
        """Get max drawdown percentage - converted from decimal"""
        return getattr(self.trading, 'max_drawdown', 0.1) * 100

    @property
    def max_open_positions(self) -> int:
        """Get max open positions"""
        return getattr(self.trading, 'max_open_positions', 5)

    @property
    def min_order_size(self) -> float:
        """Get minimum order size"""
        return getattr(self.trading, 'min_order_size', 10.0)

    def save_config(self):
        """Save current configuration to file"""
        config_data = {
            "trading": self.trading.__dict__,
            "api_keys": self.api_keys.__dict__,
            "database": self.database.__dict__,
            "backtesting": self.backtesting.__dict__,
            "development": self.development.__dict__
        }

        with open(self.config_path, 'w') as f:
            yaml.dump(config_data, f, default_flow_style=False)

    def validate_config(self) -> List[str]:
        """Validate configuration and return list of errors"""
        errors = []
        
        # Check required API keys
        if not self.api_keys.bybit.get("api_key") or not self.api_keys.bybit.get("api_secret"):
            errors.append("Bybit API credentials are required")
        
        # Check trading pairs
        if not self.trading.trading_pairs:
            errors.append("At least one trading pair must be configured")
        
        # Check strategies
        enabled_strategies = [name for name, config in self.trading.strategies.items() 
                            if config.get("enabled", False)]
        if not enabled_strategies:
            errors.append("At least one trading strategy must be enabled")
        
        # Check database configuration
        if not hasattr(self.database, 'primary') or not self.database.primary:
            errors.append("Database configuration must be properly configured")

        return errors


# Create global config instance
config = None

# Alias for backward compatibility
BotConfig = EnhancedBotConfig

def get_config(config_file: Optional[str] = None) -> EnhancedBotConfig:
    """Get global configuration instance"""
    global config
    if config is None:
        config = EnhancedBotConfig(config_file)
    return config

def reload_config(config_file: Optional[str] = None):
    """Reload configuration"""
    global config
    config = EnhancedBotConfig(config_file)
    return config
