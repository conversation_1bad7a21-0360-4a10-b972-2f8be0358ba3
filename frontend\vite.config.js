import react from '@vitejs/plugin-react'
import { defineConfig } from 'vite'

export default defineConfig({
    plugins: [react()],
    server: {
        port: 3000,
        host: '0.0.0.0', // Allow network access for testing on mobile
        proxy: {
            '/api': {
                target: 'http://*************:8000',
                changeOrigin: true,
                rewrite: (path) => path.replace(/^\/api/, ''),
                secure: false,
                ws: true // Enable websocket proxy
            }
        },
        // Optimized for MSI Cyborg 15 A12VF
        hmr: {
            overlay: true
        },
        watch: {
            usePolling: false, // Better performance on Windows 11
            interval: 100
        }
    },
    build: {
        outDir: 'dist',
        assetsDir: 'assets',
        // Optimized for 16GB RAM and Intel i5-12450H
        chunkSizeWarningLimit: 1000,
        rollupOptions: {
            output: {
                manualChunks: {
                    'react-vendor': ['react', 'react-dom'],
                    'mui-vendor': ['@mui/material', '@mui/icons-material'],
                    'chart-vendor': ['recharts'],
                    'query-vendor': ['@tanstack/react-query']
                }
            }
        },
        // Enable source maps for debugging
        sourcemap: true,
        // Optimize for production
        minify: 'terser',
        terserOptions: {
            compress: {
                drop_console: false, // Keep console logs for debugging
                drop_debugger: true
            }
        }
    },
    // Optimize for development on MSI hardware
    optimizeDeps: {
        include: [
            'react',
            'react-dom',
            '@mui/material',
            '@mui/icons-material',
            'recharts',
            '@tanstack/react-query'
        ]
    },
    // Better performance on Windows 11
    resolve: {
        alias: {
            '@': new URL('./src', import.meta.url).pathname
        }
    },
    // Development optimizations
    define: {
        __DEV__: JSON.stringify(process.env.NODE_ENV === 'development'),
        __DEVICE_INFO__: JSON.stringify({
            platform: 'web',
            deviceModel: 'MSI Cyborg 15 A12VF',
            cpu: 'Intel Core i5-12450H',
            ram: '16GB'
        })
    }
})
