import { TrendingDown, TrendingUp } from '@mui/icons-material'
import {
    Box,
    Card,
    CardContent,
    Grid,
    LinearProgress,
    Typography
} from '@mui/material'
import { motion } from 'framer-motion'
import { useState } from 'react'

const MarketOverview = () => {
    const [selectedTimeframe, setSelectedTimeframe] = useState('24h')

    const marketData = {
        overview: {
            totalMarketCap: 2340000000000,
            totalVolume: 89000000000,
            btcDominance: 42.3,
            fearGreedIndex: 68,
        },
        topCoins: [
            {
                symbol: 'BTC',
                name: 'Bitcoin',
                price: 42350.50,
                change24h: 3.45,
                volume: 28500000000,
                marketCap: 830000000000,
            },
            {
                symbol: 'ETH',
                name: 'Ethereum',
                price: 2543.20,
                change24h: -1.23,
                volume: 15200000000,
                marketCap: 305000000000,
            },
            {
                symbol: 'BNB',
                name: 'Binance Coin',
                price: 315.67,
                change24h: 2.87,
                volume: 2100000000,
                marketCap: 47000000000,
            },
            {
                symbol: 'ADA',
                name: '<PERSON><PERSON>',
                price: 0.4523,
                change24h: 5.32,
                volume: 890000000,
                marketCap: 16000000000,
            },
            {
                symbol: 'SOL',
                name: 'Solana',
                price: 98.76,
                change24h: -2.15,
                volume: 1200000000,
                marketCap: 42000000000,
            },
        ],
    }

    const formatCurrency = (value) => {
        if (value >= 1e12) return `$${(value / 1e12).toFixed(2)}T`
        if (value >= 1e9) return `$${(value / 1e9).toFixed(2)}B`
        if (value >= 1e6) return `$${(value / 1e6).toFixed(2)}M`
        return `$${value.toLocaleString()}`
    }

    const getFearGreedColor = (index) => {
        if (index >= 75) return '#00ff88'
        if (index >= 55) return '#ffa726'
        if (index >= 35) return '#ff9800'
        if (index >= 25) return '#ff5722'
        return '#ff4757'
    }

    const getFearGreedLabel = (index) => {
        if (index >= 75) return 'Extreme Greed'
        if (index >= 55) return 'Greed'
        if (index >= 45) return 'Neutral'
        if (index >= 25) return 'Fear'
        return 'Extreme Fear'
    }

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
        >
            <Card
                sx={{
                    background: 'rgba(255, 255, 255, 0.03)',
                    backdropFilter: 'blur(20px)',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    borderRadius: 3,
                    height: '400px',
                }}
            >
                <CardContent sx={{ height: '100%', p: 3 }}>
                    <Typography
                        variant="h6"
                        sx={{
                            fontWeight: 600,
                            color: '#ffffff',
                            mb: 3,
                        }}
                    >
                        Market Overview
                    </Typography>

                    <Box sx={{ mb: 3 }}>
                        <Grid container spacing={2}>
                            <Grid item xs={6}>
                                <Box sx={{ textAlign: 'center' }}>
                                    <Typography variant="caption" sx={{ color: '#b3b3b3' }}>
                                        Market Cap
                                    </Typography>
                                    <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                        {formatCurrency(marketData.overview.totalMarketCap)}
                                    </Typography>
                                </Box>
                            </Grid>
                            <Grid item xs={6}>
                                <Box sx={{ textAlign: 'center' }}>
                                    <Typography variant="caption" sx={{ color: '#b3b3b3' }}>
                                        24h Volume
                                    </Typography>
                                    <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                        {formatCurrency(marketData.overview.totalVolume)}
                                    </Typography>
                                </Box>
                            </Grid>
                        </Grid>
                    </Box>

                    <Box sx={{ mb: 3 }}>
                        <Grid container spacing={2}>
                            <Grid item xs={6}>
                                <Box sx={{ textAlign: 'center' }}>
                                    <Typography variant="caption" sx={{ color: '#b3b3b3' }}>
                                        BTC Dominance
                                    </Typography>
                                    <Typography variant="body1" sx={{ color: '#ffa726', fontWeight: 600 }}>
                                        {marketData.overview.btcDominance}%
                                    </Typography>
                                    <LinearProgress
                                        variant="determinate"
                                        value={marketData.overview.btcDominance}
                                        sx={{
                                            mt: 0.5,
                                            height: 4,
                                            borderRadius: 2,
                                            backgroundColor: 'rgba(255, 255, 255, 0.05)',
                                            '& .MuiLinearProgress-bar': {
                                                backgroundColor: '#ffa726',
                                                borderRadius: 2,
                                            },
                                        }}
                                    />
                                </Box>
                            </Grid>
                            <Grid item xs={6}>
                                <Box sx={{ textAlign: 'center' }}>
                                    <Typography variant="caption" sx={{ color: '#b3b3b3' }}>
                                        Fear & Greed
                                    </Typography>
                                    <Typography
                                        variant="body1"
                                        sx={{
                                            color: getFearGreedColor(marketData.overview.fearGreedIndex),
                                            fontWeight: 600
                                        }}
                                    >
                                        {marketData.overview.fearGreedIndex}
                                    </Typography>
                                    <Typography
                                        variant="caption"
                                        sx={{
                                            color: getFearGreedColor(marketData.overview.fearGreedIndex),
                                            display: 'block'
                                        }}
                                    >
                                        {getFearGreedLabel(marketData.overview.fearGreedIndex)}
                                    </Typography>
                                </Box>
                            </Grid>
                        </Grid>
                    </Box>

                    <Typography
                        variant="subtitle2"
                        sx={{
                            color: '#ffffff',
                            mb: 2,
                            fontWeight: 600,
                        }}
                    >
                        Top Performers
                    </Typography>

                    <Box sx={{ overflow: 'auto', maxHeight: 'calc(100% - 200px)' }}>
                        {marketData.topCoins.map((coin, index) => (
                            <motion.div
                                key={coin.symbol}
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.4, delay: index * 0.1 }}
                            >
                                <Box
                                    sx={{
                                        display: 'flex',
                                        justifyContent: 'space-between',
                                        alignItems: 'center',
                                        py: 1,
                                        borderBottom: index < marketData.topCoins.length - 1 ? '1px solid rgba(255, 255, 255, 0.05)' : 'none',
                                    }}
                                >
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flex: 1 }}>
                                        <Typography variant="body2" sx={{ color: '#ffffff', fontWeight: 600, minWidth: 40 }}>
                                            {coin.symbol}
                                        </Typography>
                                        <Typography variant="caption" sx={{ color: '#b3b3b3', fontSize: '0.7rem' }}>
                                            {coin.name}
                                        </Typography>
                                    </Box>

                                    <Box sx={{ textAlign: 'right', minWidth: 80 }}>
                                        <Typography variant="body2" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                            ${coin.price.toLocaleString()}
                                        </Typography>
                                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', gap: 0.5 }}>
                                            {coin.change24h > 0 ? (
                                                <TrendingUp sx={{ color: '#00ff88', fontSize: 12 }} />
                                            ) : (
                                                <TrendingDown sx={{ color: '#ff4757', fontSize: 12 }} />
                                            )}
                                            <Typography
                                                variant="caption"
                                                sx={{
                                                    color: coin.change24h > 0 ? '#00ff88' : '#ff4757',
                                                    fontWeight: 600,
                                                }}
                                            >
                                                {coin.change24h > 0 ? '+' : ''}{coin.change24h}%
                                            </Typography>
                                        </Box>
                                    </Box>
                                </Box>
                            </motion.div>
                        ))}
                    </Box>
                </CardContent>
            </Card>
        </motion.div>
    )
}

export default MarketOverview
