# Bybit Trading Bot Mobile - Android Build Configuration

## Prerequisites

1. **Android Studio**: Latest version with Android SDK
2. **Java JDK**: Version 11 or higher
3. **Android SDK**: API level 33 (Android 13) or higher
4. **Node.js**: Version 16 or higher

## Setup Instructions

### 1. Environment Setup

Set the following environment variables:

```bash
# Windows
set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
set PATH=%PATH%;%ANDROID_HOME%\tools;%ANDROID_HOME%\platform-tools

# macOS/Linux
export ANDROID_HOME=$HOME/Android/Sdk
export PATH=$PATH:$ANDROID_HOME/tools:$ANDROID_HOME/platform-tools
```

### 2. Project Configuration

1. Run the setup script:

   ```bash
   # Windows
   setup.bat
   
   # macOS/Linux
   chmod +x setup.sh
   ./setup.sh
   ```

2. Open Android Studio and import the `android` folder

3. Sync the project and resolve any dependency issues

### 3. Device Setup

#### Physical Device (Motorola)

1. Enable Developer Options:
   - Go to Settings > About Phone
   - Tap "Build Number" 7 times

2. Enable USB Debugging:
   - Go to Settings > Developer Options
   - Turn on "USB Debugging"

3. Connect device and verify:

   ```bash
   adb devices
   ```

#### Emulator

1. Create AVD in Android Studio
2. Choose API level 33 (Android 13)
3. Start the emulator

### 4. Build and Run

```bash
# Start Metro bundler
npm start

# Run on connected device/emulator
npm run android

# For release build
npm run build:android
```

## Key Features for Mobile

### 📱 Mobile-Optimized UI

- Touch-friendly interface
- Swipe gestures for navigation
- Pull-to-refresh functionality
- Responsive design for different screen sizes

### 🔔 Push Notifications

- Trade execution alerts
- Profit/loss notifications
- System status updates
- AI prediction alerts

### 🔐 Security Features

- Biometric authentication (fingerprint/face)
- Secure storage for API keys
- App lock with timeout
- Encrypted local data storage

### 📊 Real-time Data

- Live profit tracking
- Portfolio updates
- Position monitoring
- Market data streaming

### ⚡ Performance

- Optimized for mobile CPU/memory
- Efficient data caching
- Background sync
- Battery optimization

## Configuration Files

### API Configuration

Update `src/services/api.ts` with your FastAPI backend URL:

```typescript
this.baseURL = 'http://*************:8000';
```

### Notification Setup

Configure push notifications in `src/services/notifications.ts`

### Security Settings

Set up authentication in `src/context/AuthContext.tsx`

## Release Build

### 1. Generate Signing Key

```bash
keytool -genkey -v -keystore bybit-trading-bot.keystore -alias bybit-key -keyalg RSA -keysize 2048 -validity 10000
```

### 2. Configure Gradle

Add to `android/gradle.properties`:

```
MYAPP_UPLOAD_STORE_FILE=bybit-trading-bot.keystore
MYAPP_UPLOAD_KEY_ALIAS=bybit-key
MYAPP_UPLOAD_STORE_PASSWORD=your_password
MYAPP_UPLOAD_KEY_PASSWORD=your_password
```

### 3. Build Release APK

```bash
cd android
./gradlew assembleRelease
```

The APK will be generated at:
`android/app/build/outputs/apk/release/app-release.apk`

## Troubleshooting

### Common Issues

1. **Metro bundler fails to start**

   ```bash
   npx react-native start --reset-cache
   ```

2. **Build fails on Android**

   ```bash
   cd android
   ./gradlew clean
   cd ..
   npm run android
   ```

3. **Device not detected**

   ```bash
   adb kill-server
   adb start-server
   adb devices
   ```

4. **Network connectivity issues**
   - Ensure your computer and phone are on the same WiFi network
   - Update the API endpoint IP address
   - Check firewall settings

### Performance Tips

1. **Enable Hermes** (JavaScript engine)
2. **Use ProGuard** for release builds
3. **Optimize bundle size** with selective imports
4. **Implement lazy loading** for heavy components

## Installation on Motorola Device

1. Build the release APK following the instructions above
2. Transfer the APK to your Motorola phone
3. Enable "Install from Unknown Sources" in Settings
4. Install the APK
5. Grant necessary permissions (camera, storage, notifications)
6. Configure the app with your trading system details

## Security Recommendations

1. **Never store API keys in plain text**
2. **Use Android Keystore** for sensitive data
3. **Implement certificate pinning** for API calls
4. **Enable app obfuscation** for release builds
5. **Regular security updates** and dependency checks
