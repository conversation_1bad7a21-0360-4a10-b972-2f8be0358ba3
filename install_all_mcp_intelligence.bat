@echo off
echo [INFO] Installing ALL AVAILABLE MCP Servers for Maximum Copilot Intelligence
echo ==========================================================================

cd /d "E:\The_real_deal_copy\Bybit_Bot\BOT"

echo [INFO] Installing Node.js MCP Servers...

echo [1/15] Installing Sequential Thinking server...
npm install @modelcontextprotocol/server-sequentialthinking

echo [2/15] Installing Memory server...
npm install @modelcontextprotocol/server-memory

echo [3/15] Installing Brave Search server...
npm install @modelcontextprotocol/server-brave-search

echo [4/15] Installing Postgres server...
npm install @modelcontextprotocol/server-postgres

echo [5/15] Installing SQLite server...
npm install @modelcontextprotocol/server-sqlite

echo [6/15] Installing Puppeteer server...
npm install @modelcontextprotocol/server-puppeteer

echo [7/15] Installing Fetch server...
npm install @modelcontextprotocol/server-fetch

echo [8/15] Installing Everything search server...
npm install @modelcontextprotocol/server-everything

echo [9/15] Installing AWS Knowledge Base server...
npm install @modelcontextprotocol/server-aws-kb

echo [10/15] Installing Time server...
npm install @modelcontextprotocol/server-time

echo [11/15] Installing Slack server...
npm install @modelcontextprotocol/server-slack

echo [12/15] Installing YouTube Transcript server...
npm install @modelcontextprotocol/server-youtube-transcript

echo [13/15] Installing EverArt server...
npm install @modelcontextprotocol/server-everart

echo [14/15] Installing Computer Use server...
npm install @modelcontextprotocol/server-computer-use

echo [15/15] Installing Claude Computer Use server...
npm install claude-computer-use

echo.
echo [INFO] Installing Python MCP packages for enhanced functionality...
call "E:\Miniconda\Scripts\activate.bat" bybit-trader

pip install mcp
pip install psycopg2-binary
pip install pyodbc
pip install sqlite3
pip install requests
pip install beautifulsoup4
pip install selenium
pip install openai
pip install anthropic

echo.
echo [SUCCESS] All MCP servers installed for maximum Copilot intelligence!
echo [INFO] Now configuring all servers in settings...

echo.
npm list --depth=0

pause
