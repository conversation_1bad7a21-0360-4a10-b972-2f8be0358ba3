import {
    Assessment,
    ShowChart,
    Speed,
    TrendingUp
} from '@mui/icons-material'
import {
    Box,
    Card,
    CardContent,
    Container,
    FormControl,
    Grid,
    InputLabel,
    MenuItem,
    Select,
    Tab,
    Tabs,
    Typography
} from '@mui/material'
import { motion } from 'framer-motion'
import { useState } from 'react'
import {
    Area,
    AreaChart,
    Bar,
    CartesianGrid,
    Line,
    LineChart,
    BarChart as RechartsBarChart,
    ResponsiveContainer,
    Scatter,
    ScatterChart,
    Tooltip,
    XAxis,
    YAxis
} from 'recharts'
import { useRealTimeData } from '../../hooks/useRealTimeData'

const Analytics = () => {
    const [activeTab, setActiveTab] = useState(0)
    const [timeframe, setTimeframe] = useState('30d')
    const [selectedMetric, setSelectedMetric] = useState('profit')

    // Real-time data hook
    const {
        profitData,
        trades,
        performanceMetrics,
        isConnected
    } = useRealTimeData()

    // Mock data for analytics
    const performanceData = [
        { date: '2024-01-01', profit: 1250, trades: 45, winRate: 72, drawdown: -2.1 },
        { date: '2024-01-02', profit: 1890, trades: 52, winRate: 75, drawdown: -1.8 },
        { date: '2024-01-03', profit: 2340, trades: 38, winRate: 68, drawdown: -3.2 },
        { date: '2024-01-04', profit: 3120, trades: 61, winRate: 78, drawdown: -1.5 },
        { date: '2024-01-05', profit: 2780, trades: 47, winRate: 71, drawdown: -2.8 },
        { date: '2024-01-06', profit: 3890, trades: 55, winRate: 80, drawdown: -1.2 },
        { date: '2024-01-07', profit: 4230, trades: 49, winRate: 73, drawdown: -2.3 },
    ]

    const strategyComparison = [
        { strategy: 'Arbitrage', profit: 15420, trades: 234, winRate: 82, sharpe: 2.1, maxDrawdown: -3.2 },
        { strategy: 'Grid Trading', profit: 12890, trades: 189, winRate: 74, sharpe: 1.8, maxDrawdown: -5.1 },
        { strategy: 'Momentum', profit: 18650, trades: 156, winRate: 69, sharpe: 2.4, maxDrawdown: -7.3 },
        { strategy: 'Mean Reversion', profit: 9870, trades: 98, winRate: 78, sharpe: 1.6, maxDrawdown: -4.8 },
        { strategy: 'News Trading', profit: 22340, trades: 67, winRate: 85, sharpe: 2.8, maxDrawdown: -2.9 },
    ]

    const riskMetrics = {
        sharpeRatio: 2.34,
        calmarRatio: 1.87,
        maxDrawdown: -5.2,
        volatility: 12.8,
        beta: 0.75,
        alpha: 8.9,
        informationRatio: 1.92,
        winRate: 76.4,
    }

    const correlationData = [
        { asset: 'BTC', correlation: 0.85 },
        { asset: 'ETH', correlation: 0.72 },
        { asset: 'BNB', correlation: 0.68 },
        { asset: 'ADA', correlation: 0.45 },
        { asset: 'SOL', correlation: 0.53 },
        { asset: 'DOT', correlation: 0.41 },
    ]

    const tradeDistribution = [
        { range: '0-100', count: 45, percentage: 23.5 },
        { range: '100-500', count: 78, percentage: 40.8 },
        { range: '500-1K', count: 34, percentage: 17.8 },
        { range: '1K-5K', count: 28, percentage: 14.7 },
        { range: '5K+', count: 6, percentage: 3.1 },
    ]

    const COLORS = ['#00ff88', '#42a5f5', '#ffa726', '#8e24aa', '#ff4757']

    const TabPanel = ({ children, value, index }) => (
        <div role="tabpanel" hidden={value !== index}>
            {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
        </div>
    )

    return (
        <Container maxWidth="xl" sx={{ py: 3 }}>
            <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
            >
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
                    <Typography
                        variant="h4"
                        sx={{
                            fontWeight: 700,
                            color: '#ffffff',
                            background: 'linear-gradient(45deg, #00ff88, #00ccff)',
                            WebkitBackgroundClip: 'text',
                            WebkitTextFillColor: 'transparent',
                        }}
                    >
                        Advanced Analytics
                    </Typography>

                    <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                        <FormControl size="small" sx={{ minWidth: 120 }}>
                            <InputLabel sx={{ color: '#b3b3b3' }}>Timeframe</InputLabel>
                            <Select
                                value={timeframe}
                                label="Timeframe"
                                onChange={(e) => setTimeframe(e.target.value)}
                                sx={{
                                    color: '#ffffff',
                                    '& .MuiOutlinedInput-notchedOutline': {
                                        borderColor: 'rgba(255, 255, 255, 0.3)',
                                    },
                                    '&:hover .MuiOutlinedInput-notchedOutline': {
                                        borderColor: '#00ff88',
                                    },
                                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                        borderColor: '#00ff88',
                                    },
                                }}
                            >
                                <MenuItem value="7d">7 Days</MenuItem>
                                <MenuItem value="30d">30 Days</MenuItem>
                                <MenuItem value="90d">90 Days</MenuItem>
                                <MenuItem value="1y">1 Year</MenuItem>
                            </Select>
                        </FormControl>
                    </Box>
                </Box>
            </motion.div>

            {/* Analytics Tabs */}
            <Box sx={{ borderBottom: 1, borderColor: 'rgba(255, 255, 255, 0.1)', mb: 3 }}>
                <Tabs
                    value={activeTab}
                    onChange={(e, newValue) => setActiveTab(newValue)}
                    sx={{
                        '& .MuiTab-root': {
                            color: '#b3b3b3',
                            fontWeight: 600,
                            '&.Mui-selected': {
                                color: '#00ff88',
                            },
                        },
                        '& .MuiTabs-indicator': {
                            backgroundColor: '#00ff88',
                        },
                    }}
                >
                    <Tab label="Performance" />
                    <Tab label="Risk Analysis" />
                    <Tab label="Strategy Comparison" />
                    <Tab label="Market Correlation" />
                </Tabs>
            </Box>

            {/* Performance Tab */}
            <TabPanel value={activeTab} index={0}>
                <Grid container spacing={3}>
                    {/* Key Performance Metrics */}
                    <Grid item xs={12} md={3}>
                        <motion.div
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.6, delay: 0.1 }}
                        >
                            <Card
                                sx={{
                                    background: 'rgba(255, 255, 255, 0.03)',
                                    backdropFilter: 'blur(20px)',
                                    border: '1px solid rgba(255, 255, 255, 0.1)',
                                    borderRadius: 3,
                                }}
                            >
                                <CardContent>
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                                        <TrendingUp sx={{ color: '#00ff88' }} />
                                        <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                            Total Return
                                        </Typography>
                                    </Box>
                                    <Typography variant="h3" sx={{ color: '#00ff88', fontWeight: 700 }}>
                                        +34.7%
                                    </Typography>
                                    <Typography variant="body2" sx={{ color: '#b3b3b3' }}>
                                        Last 30 days
                                    </Typography>
                                </CardContent>
                            </Card>
                        </motion.div>
                    </Grid>

                    <Grid item xs={12} md={3}>
                        <motion.div
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.6, delay: 0.2 }}
                        >
                            <Card
                                sx={{
                                    background: 'rgba(255, 255, 255, 0.03)',
                                    backdropFilter: 'blur(20px)',
                                    border: '1px solid rgba(255, 255, 255, 0.1)',
                                    borderRadius: 3,
                                }}
                            >
                                <CardContent>
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                                        <Assessment sx={{ color: '#42a5f5' }} />
                                        <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                            Sharpe Ratio
                                        </Typography>
                                    </Box>
                                    <Typography variant="h3" sx={{ color: '#42a5f5', fontWeight: 700 }}>
                                        {riskMetrics.sharpeRatio}
                                    </Typography>
                                    <Typography variant="body2" sx={{ color: '#b3b3b3' }}>
                                        Risk-adjusted return
                                    </Typography>
                                </CardContent>
                            </Card>
                        </motion.div>
                    </Grid>

                    <Grid item xs={12} md={3}>
                        <motion.div
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.6, delay: 0.3 }}
                        >
                            <Card
                                sx={{
                                    background: 'rgba(255, 255, 255, 0.03)',
                                    backdropFilter: 'blur(20px)',
                                    border: '1px solid rgba(255, 255, 255, 0.1)',
                                    borderRadius: 3,
                                }}
                            >
                                <CardContent>
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                                        <Speed sx={{ color: '#ffa726' }} />
                                        <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                            Win Rate
                                        </Typography>
                                    </Box>
                                    <Typography variant="h3" sx={{ color: '#ffa726', fontWeight: 700 }}>
                                        {riskMetrics.winRate}%
                                    </Typography>
                                    <Typography variant="body2" sx={{ color: '#b3b3b3' }}>
                                        Successful trades
                                    </Typography>
                                </CardContent>
                            </Card>
                        </motion.div>
                    </Grid>

                    <Grid item xs={12} md={3}>
                        <motion.div
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.6, delay: 0.4 }}
                        >
                            <Card
                                sx={{
                                    background: 'rgba(255, 255, 255, 0.03)',
                                    backdropFilter: 'blur(20px)',
                                    border: '1px solid rgba(255, 255, 255, 0.1)',
                                    borderRadius: 3,
                                }}
                            >
                                <CardContent>
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                                        <ShowChart sx={{ color: '#8e24aa' }} />
                                        <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                            Max Drawdown
                                        </Typography>
                                    </Box>
                                    <Typography variant="h3" sx={{ color: '#8e24aa', fontWeight: 700 }}>
                                        {riskMetrics.maxDrawdown}%
                                    </Typography>
                                    <Typography variant="body2" sx={{ color: '#b3b3b3' }}>
                                        Worst decline
                                    </Typography>
                                </CardContent>
                            </Card>
                        </motion.div>
                    </Grid>

                    {/* Performance Chart */}
                    <Grid item xs={12} lg={8}>
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.5 }}
                        >
                            <Card
                                sx={{
                                    background: 'rgba(255, 255, 255, 0.03)',
                                    backdropFilter: 'blur(20px)',
                                    border: '1px solid rgba(255, 255, 255, 0.1)',
                                    borderRadius: 3,
                                    height: '400px',
                                }}
                            >
                                <CardContent sx={{ height: '100%' }}>
                                    <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600, mb: 3 }}>
                                        Cumulative Performance
                                    </Typography>

                                    <ResponsiveContainer width="100%" height="85%">
                                        <AreaChart data={performanceData}>
                                            <defs>
                                                <linearGradient id="colorProfit" x1="0" y1="0" x2="0" y2="1">
                                                    <stop offset="5%" stopColor="#00ff88" stopOpacity={0.3} />
                                                    <stop offset="95%" stopColor="#00ff88" stopOpacity={0} />
                                                </linearGradient>
                                            </defs>
                                            <CartesianGrid strokeDasharray="3 3" stroke="rgba(255, 255, 255, 0.1)" />
                                            <XAxis dataKey="date" stroke="#b3b3b3" />
                                            <YAxis stroke="#b3b3b3" />
                                            <Tooltip
                                                contentStyle={{
                                                    backgroundColor: 'rgba(20, 20, 20, 0.9)',
                                                    border: '1px solid rgba(255, 255, 255, 0.1)',
                                                    borderRadius: '8px',
                                                    color: '#ffffff',
                                                }}
                                            />
                                            <Area
                                                type="monotone"
                                                dataKey="profit"
                                                stroke="#00ff88"
                                                strokeWidth={2}
                                                fillOpacity={1}
                                                fill="url(#colorProfit)"
                                            />
                                        </AreaChart>
                                    </ResponsiveContainer>
                                </CardContent>
                            </Card>
                        </motion.div>
                    </Grid>

                    {/* Trade Distribution */}
                    <Grid item xs={12} lg={4}>
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.6 }}
                        >
                            <Card
                                sx={{
                                    background: 'rgba(255, 255, 255, 0.03)',
                                    backdropFilter: 'blur(20px)',
                                    border: '1px solid rgba(255, 255, 255, 0.1)',
                                    borderRadius: 3,
                                    height: '400px',
                                }}
                            >
                                <CardContent sx={{ height: '100%' }}>
                                    <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600, mb: 3 }}>
                                        Trade Size Distribution
                                    </Typography>

                                    <ResponsiveContainer width="100%" height="70%">
                                        <RechartsBarChart data={tradeDistribution}>
                                            <CartesianGrid strokeDasharray="3 3" stroke="rgba(255, 255, 255, 0.1)" />
                                            <XAxis dataKey="range" stroke="#b3b3b3" />
                                            <YAxis stroke="#b3b3b3" />
                                            <Tooltip
                                                contentStyle={{
                                                    backgroundColor: 'rgba(20, 20, 20, 0.9)',
                                                    border: '1px solid rgba(255, 255, 255, 0.1)',
                                                    borderRadius: '8px',
                                                    color: '#ffffff',
                                                }}
                                            />
                                            <Bar dataKey="count" fill="#42a5f5" />
                                        </RechartsBarChart>
                                    </ResponsiveContainer>
                                </CardContent>
                            </Card>
                        </motion.div>
                    </Grid>
                </Grid>
            </TabPanel>

            {/* Risk Analysis Tab */}
            <TabPanel value={activeTab} index={1}>
                <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                        <Card
                            sx={{
                                background: 'rgba(255, 255, 255, 0.03)',
                                backdropFilter: 'blur(20px)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: 3,
                            }}
                        >
                            <CardContent>
                                <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600, mb: 3 }}>
                                    Risk Metrics
                                </Typography>

                                <Grid container spacing={2}>
                                    {Object.entries(riskMetrics).map(([key, value]) => (
                                        <Grid item xs={6} key={key}>
                                            <Box sx={{ p: 2, background: 'rgba(255, 255, 255, 0.02)', borderRadius: 2 }}>
                                                <Typography variant="caption" sx={{ color: '#b3b3b3' }}>
                                                    {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                                                </Typography>
                                                <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                                    {typeof value === 'number' ?
                                                        (key.includes('Ratio') || key === 'alpha' || key === 'beta' ? value.toFixed(2) :
                                                            key.includes('Rate') || key.includes('volatility') ? `${value}%` :
                                                                key.includes('Drawdown') ? `${value}%` : value) : value}
                                                </Typography>
                                            </Box>
                                        </Grid>
                                    ))}
                                </Grid>
                            </CardContent>
                        </Card>
                    </Grid>

                    <Grid item xs={12} md={6}>
                        <Card
                            sx={{
                                background: 'rgba(255, 255, 255, 0.03)',
                                backdropFilter: 'blur(20px)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: 3,
                                height: '400px',
                            }}
                        >
                            <CardContent sx={{ height: '100%' }}>
                                <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600, mb: 3 }}>
                                    Drawdown Analysis
                                </Typography>

                                <ResponsiveContainer width="100%" height="85%">
                                    <LineChart data={performanceData}>
                                        <CartesianGrid strokeDasharray="3 3" stroke="rgba(255, 255, 255, 0.1)" />
                                        <XAxis dataKey="date" stroke="#b3b3b3" />
                                        <YAxis stroke="#b3b3b3" />
                                        <Tooltip
                                            contentStyle={{
                                                backgroundColor: 'rgba(20, 20, 20, 0.9)',
                                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                                borderRadius: '8px',
                                                color: '#ffffff',
                                            }}
                                        />
                                        <Line
                                            type="monotone"
                                            dataKey="drawdown"
                                            stroke="#ff4757"
                                            strokeWidth={2}
                                            dot={{ fill: '#ff4757' }}
                                        />
                                    </LineChart>
                                </ResponsiveContainer>
                            </CardContent>
                        </Card>
                    </Grid>
                </Grid>
            </TabPanel>

            {/* Strategy Comparison Tab */}
            <TabPanel value={activeTab} index={2}>
                <Card
                    sx={{
                        background: 'rgba(255, 255, 255, 0.03)',
                        backdropFilter: 'blur(20px)',
                        border: '1px solid rgba(255, 255, 255, 0.1)',
                        borderRadius: 3,
                    }}
                >
                    <CardContent>
                        <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600, mb: 3 }}>
                            Strategy Performance Comparison
                        </Typography>

                        <ResponsiveContainer width="100%" height={400}>
                            <ScatterChart data={strategyComparison}>
                                <CartesianGrid strokeDasharray="3 3" stroke="rgba(255, 255, 255, 0.1)" />
                                <XAxis dataKey="sharpe" stroke="#b3b3b3" name="Sharpe Ratio" />
                                <YAxis dataKey="profit" stroke="#b3b3b3" name="Profit" />
                                <Tooltip
                                    contentStyle={{
                                        backgroundColor: 'rgba(20, 20, 20, 0.9)',
                                        border: '1px solid rgba(255, 255, 255, 0.1)',
                                        borderRadius: '8px',
                                        color: '#ffffff',
                                    }}
                                    formatter={(value, name) => [value, name]}
                                />
                                <Scatter dataKey="profit" fill="#00ff88" />
                            </ScatterChart>
                        </ResponsiveContainer>
                    </CardContent>
                </Card>
            </TabPanel>

            {/* Market Correlation Tab */}
            <TabPanel value={activeTab} index={3}>
                <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                        <Card
                            sx={{
                                background: 'rgba(255, 255, 255, 0.03)',
                                backdropFilter: 'blur(20px)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: 3,
                            }}
                        >
                            <CardContent>
                                <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600, mb: 3 }}>
                                    Asset Correlation Matrix
                                </Typography>

                                <ResponsiveContainer width="100%" height={300}>
                                    <RechartsBarChart data={correlationData} layout="horizontal">
                                        <CartesianGrid strokeDasharray="3 3" stroke="rgba(255, 255, 255, 0.1)" />
                                        <XAxis type="number" stroke="#b3b3b3" domain={[0, 1]} />
                                        <YAxis dataKey="asset" type="category" stroke="#b3b3b3" />
                                        <Tooltip
                                            contentStyle={{
                                                backgroundColor: 'rgba(20, 20, 20, 0.9)',
                                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                                borderRadius: '8px',
                                                color: '#ffffff',
                                            }}
                                        />
                                        <Bar dataKey="correlation" fill="#8e24aa" />
                                    </RechartsBarChart>
                                </ResponsiveContainer>
                            </CardContent>
                        </Card>
                    </Grid>

                    <Grid item xs={12} md={6}>
                        <Card
                            sx={{
                                background: 'rgba(255, 255, 255, 0.03)',
                                backdropFilter: 'blur(20px)',
                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                borderRadius: 3,
                            }}
                        >
                            <CardContent>
                                <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 600, mb: 3 }}>
                                    Portfolio Beta Analysis
                                </Typography>

                                <Box sx={{ p: 3, textAlign: 'center' }}>
                                    <Typography variant="h2" sx={{ color: '#ffa726', fontWeight: 700, mb: 2 }}>
                                        {riskMetrics.beta}
                                    </Typography>
                                    <Typography variant="h6" sx={{ color: '#ffffff', mb: 1 }}>
                                        Portfolio Beta
                                    </Typography>
                                    <Typography variant="body2" sx={{ color: '#b3b3b3' }}>
                                        Lower volatility than market
                                    </Typography>

                                    <Box sx={{ mt: 4 }}>
                                        <Typography variant="h4" sx={{ color: '#00ff88', fontWeight: 700, mb: 1 }}>
                                            {riskMetrics.alpha}%
                                        </Typography>
                                        <Typography variant="h6" sx={{ color: '#ffffff', mb: 1 }}>
                                            Alpha (Excess Return)
                                        </Typography>
                                        <Typography variant="body2" sx={{ color: '#b3b3b3' }}>
                                            Outperforming market benchmark
                                        </Typography>
                                    </Box>
                                </Box>
                            </CardContent>
                        </Card>
                    </Grid>
                </Grid>
            </TabPanel>
        </Container>
    )
}

export default Analytics
