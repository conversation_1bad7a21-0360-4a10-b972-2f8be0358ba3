import React, { useState } from 'react'
import {
    <PERSON><PERSON>,
    Box,
    <PERSON><PERSON>,
    Card,
    CardContent,
    <PERSON>,
    Di<PERSON>r,
    <PERSON>er,
    IconButton,
    List,
    ListItem,
    ListItemText,
    Menu,
    MenuItem,
    Popover,
    Typography,
    Tooltip
} from '@mui/material'
import {
    Notifications,
    NotificationsActive,
    Close,
    Delete,
    DeleteSweep,
    FilterList,
    MarkEmailRead,
    Circle,
    TrendingUp,
    Warning,
    Info,
    CheckCircle
} from '@mui/icons-material'
import { motion, AnimatePresence } from 'framer-motion'
import { formatDistanceToNow } from 'date-fns'
import { useNotifications } from '../../hooks/useNotifications'
import { useResponsive } from '../../hooks/useResponsive'

/**
 * Advanced notification center with categorization, filtering, and actions
 * Provides comprehensive notification management for the trading system
 */
const NotificationCenter = ({ 
    anchorEl, 
    open, 
    onClose,
    maxHeight = 400,
    showAsDrawer = false 
}) => {
    const { isMobile } = useResponsive()
    const {
        notifications,
        unreadCount,
        mark<PERSON>Read,
        markAllAsRead,
        removeNotification,
        clearAll
    } = useNotifications()
    
    const [filterAnchor, setFilterAnchor] = useState(null)
    const [selectedFilter, setSelectedFilter] = useState('all')
    
    // Filter options
    const filterOptions = [
        { value: 'all', label: 'All Notifications', count: notifications.length },
        { value: 'unread', label: 'Unread', count: unreadCount },
        { value: 'trade', label: 'Trades', count: notifications.filter(n => n.type === 'trade').length },
        { value: 'alert', label: 'Alerts', count: notifications.filter(n => n.type === 'alert').length },
        { value: 'system', label: 'System', count: notifications.filter(n => n.type === 'system').length },
        { value: 'profit', label: 'Profits', count: notifications.filter(n => n.type === 'profit').length }
    ]
    
    // Filter notifications
    const filteredNotifications = notifications.filter(notification => {
        switch (selectedFilter) {
            case 'unread':
                return !notification.read
            case 'trade':
            case 'alert':
            case 'system':
            case 'profit':
            case 'loss':
                return notification.type === selectedFilter
            default:
                return true
        }
    })
    
    // Get notification icon
    const getNotificationIcon = (type, priority) => {
        const iconProps = {
            fontSize: 'small',
            sx: { mr: 1 }
        }
        
        switch (type) {
            case 'trade':
                return <TrendingUp {...iconProps} sx={{ ...iconProps.sx, color: '#00ff88' }} />
            case 'alert':
                return <Warning {...iconProps} sx={{ ...iconProps.sx, color: '#ff5252' }} />
            case 'profit':
                return <CheckCircle {...iconProps} sx={{ ...iconProps.sx, color: '#00ff88' }} />
            case 'loss':
                return <Warning {...iconProps} sx={{ ...iconProps.sx, color: '#ff5252' }} />
            case 'system':
                return <Info {...iconProps} sx={{ ...iconProps.sx, color: '#42a5f5' }} />
            default:
                return <Circle {...iconProps} sx={{ ...iconProps.sx, color: '#b3b3b3' }} />
        }
    }
    
    // Get priority color
    const getPriorityColor = (priority) => {
        switch (priority) {
            case 'high': return '#ff5252'
            case 'medium': return '#ffa726'
            case 'low': return '#42a5f5'
            default: return '#b3b3b3'
        }
    }
    
    // Handle notification click
    const handleNotificationClick = (notification) => {
        if (!notification.read) {
            markAsRead(notification.id)
        }
        
        // Handle notification-specific actions
        if (notification.action) {
            notification.action(notification.data)
        }
    }
    
    // Notification item component
    const NotificationItem = ({ notification, index }) => (
        <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.2, delay: index * 0.05 }}
        >
            <ListItem
                sx={{
                    cursor: 'pointer',
                    backgroundColor: notification.read 
                        ? 'transparent' 
                        : 'rgba(0, 255, 136, 0.05)',
                    borderLeft: notification.read 
                        ? 'none' 
                        : `3px solid ${getPriorityColor(notification.priority)}`,
                    '&:hover': {
                        backgroundColor: 'rgba(255, 255, 255, 0.05)'
                    },
                    py: 1.5
                }}
                onClick={() => handleNotificationClick(notification)}
            >
                <Box sx={{ display: 'flex', alignItems: 'flex-start', width: '100%' }}>
                    {getNotificationIcon(notification.type, notification.priority)}
                    
                    <Box sx={{ flex: 1, minWidth: 0 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                            <Typography
                                variant="subtitle2"
                                sx={{
                                    color: '#fff',
                                    fontWeight: notification.read ? 400 : 600,
                                    flex: 1
                                }}
                            >
                                {notification.title}
                            </Typography>
                            
                            {!notification.read && (
                                <Circle sx={{ fontSize: 8, color: '#00ff88', ml: 1 }} />
                            )}
                        </Box>
                        
                        <Typography
                            variant="body2"
                            sx={{
                                color: '#b3b3b3',
                                mb: 1,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                display: '-webkit-box',
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: 'vertical'
                            }}
                        >
                            {notification.message}
                        </Typography>
                        
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                            <Typography variant="caption" sx={{ color: '#666' }}>
                                {formatDistanceToNow(new Date(notification.timestamp), { addSuffix: true })}
                            </Typography>
                            
                            <Box sx={{ display: 'flex', gap: 0.5 }}>
                                <Chip
                                    label={notification.type}
                                    size="small"
                                    sx={{
                                        height: 20,
                                        fontSize: '0.7rem',
                                        backgroundColor: `${getPriorityColor(notification.priority)}20`,
                                        color: getPriorityColor(notification.priority),
                                        border: `1px solid ${getPriorityColor(notification.priority)}40`
                                    }}
                                />
                                
                                <IconButton
                                    size="small"
                                    onClick={(e) => {
                                        e.stopPropagation()
                                        removeNotification(notification.id)
                                    }}
                                    sx={{ 
                                        color: '#666',
                                        '&:hover': { color: '#ff5252' }
                                    }}
                                >
                                    <Delete fontSize="small" />
                                </IconButton>
                            </Box>
                        </Box>
                    </Box>
                </Box>
            </ListItem>
            <Divider sx={{ borderColor: 'rgba(255, 255, 255, 0.1)' }} />
        </motion.div>
    )
    
    // Notification content
    const NotificationContent = () => (
        <Box sx={{ width: isMobile ? '100vw' : 400, maxHeight, display: 'flex', flexDirection: 'column' }}>
            {/* Header */}
            <Box
                sx={{
                    p: 2,
                    borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between'
                }}
            >
                <Typography variant="h6" sx={{ color: '#fff', fontWeight: 600 }}>
                    Notifications
                </Typography>
                
                <Box sx={{ display: 'flex', gap: 1 }}>
                    {/* Filter Button */}
                    <Tooltip title="Filter">
                        <IconButton
                            size="small"
                            onClick={(e) => setFilterAnchor(e.currentTarget)}
                            sx={{ color: '#b3b3b3' }}
                        >
                            <Badge badgeContent={selectedFilter !== 'all' ? '1' : 0} color="primary">
                                <FilterList />
                            </Badge>
                        </IconButton>
                    </Tooltip>
                    
                    {/* Mark All Read */}
                    {unreadCount > 0 && (
                        <Tooltip title="Mark All Read">
                            <IconButton
                                size="small"
                                onClick={markAllAsRead}
                                sx={{ color: '#b3b3b3' }}
                            >
                                <MarkEmailRead />
                            </IconButton>
                        </Tooltip>
                    )}
                    
                    {/* Clear All */}
                    <Tooltip title="Clear All">
                        <IconButton
                            size="small"
                            onClick={clearAll}
                            sx={{ color: '#b3b3b3' }}
                        >
                            <DeleteSweep />
                        </IconButton>
                    </Tooltip>
                    
                    {/* Close */}
                    <IconButton
                        size="small"
                        onClick={onClose}
                        sx={{ color: '#b3b3b3' }}
                    >
                        <Close />
                    </IconButton>
                </Box>
            </Box>
            
            {/* Filter Info */}
            {selectedFilter !== 'all' && (
                <Box sx={{ p: 1, backgroundColor: 'rgba(0, 255, 136, 0.1)' }}>
                    <Chip
                        label={`Showing ${filterOptions.find(f => f.value === selectedFilter)?.label}`}
                        size="small"
                        onDelete={() => setSelectedFilter('all')}
                        sx={{
                            backgroundColor: 'rgba(0, 255, 136, 0.2)',
                            color: '#00ff88'
                        }}
                    />
                </Box>
            )}
            
            {/* Notifications List */}
            <Box sx={{ flex: 1, overflow: 'auto' }}>
                {filteredNotifications.length === 0 ? (
                    <Box
                        sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            justifyContent: 'center',
                            height: 200,
                            color: '#b3b3b3'
                        }}
                    >
                        <Notifications sx={{ fontSize: 48, mb: 2, opacity: 0.5 }} />
                        <Typography variant="body1">
                            {selectedFilter === 'all' ? 'No notifications' : `No ${selectedFilter} notifications`}
                        </Typography>
                    </Box>
                ) : (
                    <List sx={{ p: 0 }}>
                        <AnimatePresence>
                            {filteredNotifications.map((notification, index) => (
                                <NotificationItem
                                    key={notification.id}
                                    notification={notification}
                                    index={index}
                                />
                            ))}
                        </AnimatePresence>
                    </List>
                )}
            </Box>
        </Box>
    )
    
    // Filter menu
    const FilterMenu = () => (
        <Menu
            anchorEl={filterAnchor}
            open={Boolean(filterAnchor)}
            onClose={() => setFilterAnchor(null)}
            PaperProps={{
                sx: {
                    backgroundColor: 'rgba(0, 0, 0, 0.9)',
                    border: '1px solid rgba(255, 255, 255, 0.1)'
                }
            }}
        >
            {filterOptions.map(option => (
                <MenuItem
                    key={option.value}
                    selected={selectedFilter === option.value}
                    onClick={() => {
                        setSelectedFilter(option.value)
                        setFilterAnchor(null)
                    }}
                    sx={{ color: '#fff' }}
                >
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
                        <span>{option.label}</span>
                        <Chip
                            label={option.count}
                            size="small"
                            sx={{
                                height: 20,
                                backgroundColor: 'rgba(0, 255, 136, 0.2)',
                                color: '#00ff88'
                            }}
                        />
                    </Box>
                </MenuItem>
            ))}
        </Menu>
    )
    
    if (showAsDrawer || isMobile) {
        return (
            <>
                <Drawer
                    anchor="right"
                    open={open}
                    onClose={onClose}
                    PaperProps={{
                        sx: {
                            backgroundColor: 'rgba(0, 0, 0, 0.95)',
                            border: '1px solid rgba(255, 255, 255, 0.1)'
                        }
                    }}
                >
                    <NotificationContent />
                </Drawer>
                <FilterMenu />
            </>
        )
    }
    
    return (
        <>
            <Popover
                anchorEl={anchorEl}
                open={open}
                onClose={onClose}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right'
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'right'
                }}
                PaperProps={{
                    sx: {
                        backgroundColor: 'rgba(0, 0, 0, 0.95)',
                        border: '1px solid rgba(255, 255, 255, 0.1)',
                        borderRadius: 2,
                        mt: 1
                    }
                }}
            >
                <NotificationContent />
            </Popover>
            <FilterMenu />
        </>
    )
}

export default NotificationCenter
