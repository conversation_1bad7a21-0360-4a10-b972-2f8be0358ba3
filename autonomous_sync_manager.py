#!/usr/bin/env python3
"""
Autonomous Development Sync Manager
Syncs autonomous improvements from GitHub to local workspace
"""
import os
import sys
import asyncio
import subprocess
import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional

# Add project root to path
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

from bybit_bot.core.config import BotConfig
from bybit_bot.core.logger import TradingBotLogger
from bybit_bot.database.connection import DatabaseManager


class AutonomousSyncManager:
    """
    Manages synchronization of autonomous improvements from GitHub
    """
    
    def __init__(self):
        self.logger = TradingBotLogger("AutonomousSyncManager")
        self.workspace_path = project_root
        self.config_path = project_root / "config.yaml"
        self.sync_log_path = project_root / "logs" / "autonomous_sync.log"
        
        # Git configuration
        self.repo_url = "https://github.com/Hermansilius/Bybit-trader.git"
        self.branch = "main"
        
        # Sync settings
        self.sync_interval = 300  # 5 minutes
        self.backup_before_sync = True
        self.auto_restart_after_sync = True
        
        # Initialize
        os.makedirs(self.sync_log_path.parent, exist_ok=True)
    
    async def start_autonomous_sync(self):
        """Start the autonomous sync process"""
        try:
            self.logger.info("🚀 Starting Autonomous Development Sync Manager")
            
            # Initial setup
            await self._setup_git_tracking()
            await self._verify_workspace()
            
            # Start sync loop
            await self._sync_loop()
            
        except Exception as e:
            self.logger.error(f"Failed to start autonomous sync: {e}")
            raise
    
    async def _sync_loop(self):
        """Main synchronization loop"""
        while True:
            try:
                self.logger.info("🔄 Checking for autonomous improvements...")
                
                # Check for remote changes
                has_changes = await self._check_remote_changes()
                
                if has_changes:
                    # Create backup before sync
                    if self.backup_before_sync:
                        backup_path = await self._create_backup()
                        self.logger.info(f"📦 Created backup: {backup_path}")
                    
                    # Sync changes
                    sync_result = await self._sync_changes()
                    
                    if sync_result['success']:
                        self.logger.info(f"✅ Successfully synced {sync_result['changes_count']} changes")
                        
                        # Analyze changes
                        change_analysis = await self._analyze_changes(sync_result['changes'])
                        
                        # Handle different types of changes
                        await self._handle_synced_changes(change_analysis)
                        
                        # Log sync event
                        await self._log_sync_event(sync_result, change_analysis)
                        
                        # Restart system if needed
                        if self.auto_restart_after_sync and change_analysis['requires_restart']:
                            await self._restart_system()
                    else:
                        self.logger.warning(f"⚠️ Sync failed: {sync_result['error']}")
                        
                        # Attempt to recover
                        if self.backup_before_sync:
                            await self._restore_backup(backup_path)
                else:
                    self.logger.debug("ℹ️ No new autonomous improvements found")
                
                # Wait for next sync
                await asyncio.sleep(self.sync_interval)
                
            except Exception as e:
                self.logger.error(f"Error in sync loop: {e}")
                await asyncio.sleep(60)  # Wait a minute before retrying
    
    async def _setup_git_tracking(self):
        """Setup git for tracking autonomous changes"""
        try:
            # Ensure we're in a git repository
            if not (self.workspace_path / ".git").exists():
                self.logger.info("Initializing git repository...")
                await self._run_git_command(["init"])
                await self._run_git_command(["remote", "add", "origin", self.repo_url])
            
            # Configure git for autonomous operations
            await self._run_git_command(["config", "user.name", "Autonomous Sync Manager"])
            await self._run_git_command(["config", "user.email", "<EMAIL>"])
            
            # Set up tracking
            await self._run_git_command(["fetch", "origin"])
            
            try:
                await self._run_git_command(["branch", "--set-upstream-to=origin/main", "main"])
            except:
                # Branch might not exist locally yet
                await self._run_git_command(["checkout", "-b", "main", "origin/main"])
            
            self.logger.info("✅ Git tracking setup complete")
            
        except Exception as e:
            self.logger.error(f"Failed to setup git tracking: {e}")
            raise
    
    async def _verify_workspace(self):
        """Verify workspace integrity"""
        try:
            # Check critical files
            critical_files = [
                "main.py",
                "requirements.txt",
                ".gitignore",
                "bybit_bot/core/config.py"
            ]
            
            missing_files = []
            for file_path in critical_files:
                if not (self.workspace_path / file_path).exists():
                    missing_files.append(file_path)
            
            if missing_files:
                self.logger.warning(f"Missing critical files: {missing_files}")
                # Attempt to restore from remote
                await self._restore_critical_files(missing_files)
            
            self.logger.info("✅ Workspace verification complete")
            
        except Exception as e:
            self.logger.error(f"Workspace verification failed: {e}")
            raise
    
    async def _check_remote_changes(self) -> bool:
        """Check if there are remote changes to pull"""
        try:
            # Fetch latest changes
            await self._run_git_command(["fetch", "origin"])
            
            # Check if local is behind remote
            result = await self._run_git_command(["rev-list", "--count", "HEAD..origin/main"])
            
            changes_count = int(result.stdout.strip()) if result.stdout.strip() else 0
            
            return changes_count > 0
            
        except Exception as e:
            self.logger.error(f"Failed to check remote changes: {e}")
            return False
    
    async def _sync_changes(self) -> Dict[str, Any]:
        """Sync changes from remote repository"""
        try:
            # Get current commit hash
            current_commit = await self._run_git_command(["rev-parse", "HEAD"])
            current_hash = current_commit.stdout.strip()
            
            # Pull changes
            pull_result = await self._run_git_command(["pull", "origin", "main"])
            
            # Get new commit hash
            new_commit = await self._run_git_command(["rev-parse", "HEAD"])
            new_hash = new_commit.stdout.strip()
            
            # Get list of changed files
            if current_hash != new_hash:
                changed_files_result = await self._run_git_command([
                    "diff", "--name-only", f"{current_hash}..{new_hash}"
                ])
                changed_files = changed_files_result.stdout.strip().split('\n') if changed_files_result.stdout.strip() else []
                
                # Get commit messages
                commit_messages_result = await self._run_git_command([
                    "log", "--oneline", f"{current_hash}..{new_hash}"
                ])
                commit_messages = commit_messages_result.stdout.strip().split('\n') if commit_messages_result.stdout.strip() else []
                
                return {
                    'success': True,
                    'changes_count': len(changed_files),
                    'changes': {
                        'files': changed_files,
                        'commits': commit_messages,
                        'from_hash': current_hash,
                        'to_hash': new_hash
                    }
                }
            else:
                return {
                    'success': True,
                    'changes_count': 0,
                    'changes': {}
                }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'changes_count': 0,
                'changes': {}
            }
    
    async def _analyze_changes(self, changes: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze the types of changes that were synced"""
        try:
            if not changes:
                return {'requires_restart': False, 'change_types': []}
            
            changed_files = changes.get('files', [])
            commit_messages = changes.get('commits', [])
            
            # Analyze change types
            change_types = []
            requires_restart = False
            
            # Check file patterns
            for file_path in changed_files:
                if file_path.startswith('bybit_bot/'):
                    change_types.append('code_change')
                    requires_restart = True
                elif file_path == 'requirements.txt':
                    change_types.append('dependency_change')
                    requires_restart = True
                elif file_path == 'config_template.yaml':
                    change_types.append('config_change')
                elif file_path.startswith('tests/'):
                    change_types.append('test_change')
                elif file_path.endswith('.md'):
                    change_types.append('documentation_change')
                elif file_path.startswith('.github/'):
                    change_types.append('workflow_change')
            
            # Check commit message patterns
            for commit in commit_messages:
                if 'AUTONOMOUS fixes:' in commit:
                    change_types.append('autonomous_fixes')
                elif '⚡ Autonomous optimization:' in commit:
                    change_types.append('performance_optimization')
                elif '🧪 Autonomous testing:' in commit:
                    change_types.append('test_generation')
                elif '📚 Autonomous documentation:' in commit:
                    change_types.append('documentation_update')
                elif '🔒 Security' in commit:
                    change_types.append('security_improvement')
                    requires_restart = True
            
            return {
                'requires_restart': requires_restart,
                'change_types': list(set(change_types)),
                'analysis': {
                    'files_changed': len(changed_files),
                    'commits_synced': len(commit_messages),
                    'critical_changes': [f for f in changed_files if 'core' in f or 'main.py' in f]
                }
            }
            
        except Exception as e:
            self.logger.error(f"Failed to analyze changes: {e}")
            return {'requires_restart': False, 'change_types': [], 'analysis': {}}
    
    async def _handle_synced_changes(self, analysis: Dict[str, Any]):
        """Handle different types of synced changes"""
        try:
            change_types = analysis.get('change_types', [])
            
            # Handle dependency changes
            if 'dependency_change' in change_types:
                self.logger.info("📦 Installing updated dependencies...")
                await self._update_dependencies()
            
            # Handle config changes
            if 'config_change' in change_types:
                self.logger.info("⚙️ Processing configuration changes...")
                await self._update_configuration()
            
            # Handle test changes
            if 'test_change' in change_types or 'test_generation' in change_types:
                self.logger.info("🧪 Running updated tests...")
                await self._run_tests()
            
            # Handle security improvements
            if 'security_improvement' in change_types:
                self.logger.info("🔒 Processing security improvements...")
                await self._apply_security_updates()
            
            self.logger.info(f"✅ Processed {len(change_types)} types of changes")
            
        except Exception as e:
            self.logger.error(f"Failed to handle synced changes: {e}")
    
    async def _create_backup(self) -> str:
        """Create a backup of the current workspace"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_dir = self.workspace_path / "backups" / f"pre_sync_{timestamp}"
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # Copy critical files
            critical_dirs = ["bybit_bot", "tests", "logs"]
            critical_files = ["main.py", "requirements.txt", "config.yaml"]
            
            import shutil
            
            for dir_name in critical_dirs:
                src_dir = self.workspace_path / dir_name
                if src_dir.exists():
                    dst_dir = backup_dir / dir_name
                    shutil.copytree(src_dir, dst_dir, ignore_errors=True)
            
            for file_name in critical_files:
                src_file = self.workspace_path / file_name
                if src_file.exists():
                    dst_file = backup_dir / file_name
                    shutil.copy2(src_file, dst_file)
            
            return str(backup_dir)
            
        except Exception as e:
            self.logger.error(f"Failed to create backup: {e}")
            return ""
    
    async def _restore_backup(self, backup_path: str):
        """Restore from backup if sync fails"""
        try:
            if not backup_path or not os.path.exists(backup_path):
                self.logger.error("Backup path not found, cannot restore")
                return
            
            self.logger.info(f"🔄 Restoring from backup: {backup_path}")
            
            import shutil
            backup_dir = Path(backup_path)
            
            # Restore directories
            for item in backup_dir.iterdir():
                if item.is_dir():
                    dst_dir = self.workspace_path / item.name
                    if dst_dir.exists():
                        shutil.rmtree(dst_dir)
                    shutil.copytree(item, dst_dir)
                else:
                    dst_file = self.workspace_path / item.name
                    shutil.copy2(item, dst_file)
            
            self.logger.info("✅ Backup restored successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to restore backup: {e}")
    
    async def _restart_system(self):
        """Restart the trading system after autonomous improvements"""
        try:
            self.logger.info("🔄 Restarting system to apply autonomous improvements...")
            
            # Check if system is currently running
            if await self._is_system_running():
                self.logger.info("Stopping current system...")
                await self._stop_system()
                
                # Wait for graceful shutdown
                await asyncio.sleep(10)
            
            # Start system with improvements
            self.logger.info("Starting system with autonomous improvements...")
            await self._start_system()
            
            self.logger.info("✅ System restart complete")
            
        except Exception as e:
            self.logger.error(f"Failed to restart system: {e}")
    
    async def _log_sync_event(self, sync_result: Dict[str, Any], analysis: Dict[str, Any]):
        """Log synchronization event for tracking"""
        try:
            log_entry = {
                'timestamp': datetime.now().isoformat(),
                'sync_result': sync_result,
                'analysis': analysis,
                'workspace_path': str(self.workspace_path)
            }
            
            # Append to sync log
            with open(self.sync_log_path, 'a') as f:
                f.write(json.dumps(log_entry) + '\n')
            
        except Exception as e:
            self.logger.error(f"Failed to log sync event: {e}")
    
    # Helper methods
    async def _run_git_command(self, args: List[str]):
        """Run a git command"""
        cmd = ["git"] + args
        result = subprocess.run(
            cmd, 
            cwd=self.workspace_path,
            capture_output=True,
            text=True
        )
        
        if result.returncode != 0:
            raise Exception(f"Git command failed: {' '.join(cmd)}\nError: {result.stderr}")
        
        return result
    
    async def _update_dependencies(self):
        """Update Python dependencies"""
        try:
            cmd = [sys.executable, "-m", "pip", "install", "-r", "requirements.txt", "--upgrade"]
            result = subprocess.run(cmd, cwd=self.workspace_path, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.logger.info("✅ Dependencies updated successfully")
            else:
                self.logger.error(f"Failed to update dependencies: {result.stderr}")
        except Exception as e:
            self.logger.error(f"Error updating dependencies: {e}")
    
    async def _update_configuration(self):
        """Update configuration files"""
        try:
            # Check if config template was updated
            template_path = self.workspace_path / "config_secure_template.yaml"
            config_path = self.workspace_path / "config.yaml"
            
            if template_path.exists() and config_path.exists():
                self.logger.info("Configuration template updated - review config.yaml")
            
        except Exception as e:
            self.logger.error(f"Error updating configuration: {e}")
    
    async def _run_tests(self):
        """Run the test suite"""
        try:
            cmd = [sys.executable, "-m", "pytest", "tests/", "-v"]
            result = subprocess.run(cmd, cwd=self.workspace_path, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.logger.info("✅ All tests passed")
            else:
                self.logger.warning(f"Some tests failed: {result.stdout}")
        except Exception as e:
            self.logger.error(f"Error running tests: {e}")
    
    async def _apply_security_updates(self):
        """Apply security updates"""
        try:
            self.logger.info("🔒 Applying security updates...")
            # Security updates are applied through the normal sync process
            # This is a placeholder for any additional security-specific actions
            
        except Exception as e:
            self.logger.error(f"Error applying security updates: {e}")
    
    async def _is_system_running(self) -> bool:
        """Check if the trading system is currently running"""
        try:
            # Check for running processes
            cmd = ["pgrep", "-f", "main.py"]
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0
        except:
            return False
    
    async def _stop_system(self):
        """Stop the running trading system"""
        try:
            cmd = ["pkill", "-f", "main.py"]
            subprocess.run(cmd, capture_output=True)
        except Exception as e:
            self.logger.error(f"Error stopping system: {e}")
    
    async def _start_system(self):
        """Start the trading system"""
        try:
            cmd = [sys.executable, "main.py"]
            subprocess.Popen(cmd, cwd=self.workspace_path)
        except Exception as e:
            self.logger.error(f"Error starting system: {e}")
    
    async def _restore_critical_files(self, missing_files: List[str]):
        """Restore missing critical files from remote"""
        try:
            for file_path in missing_files:
                cmd = ["git", "checkout", "origin/main", "--", file_path]
                await self._run_git_command(cmd[1:])  # Remove 'git' as it's added in _run_git_command
            
            self.logger.info(f"✅ Restored {len(missing_files)} critical files")
            
        except Exception as e:
            self.logger.error(f"Failed to restore critical files: {e}")


async def main():
    """Main entry point"""
    try:
        print("🚀 Starting Autonomous Development Sync Manager")
        
        sync_manager = AutonomousSyncManager()
        await sync_manager.start_autonomous_sync()
        
    except KeyboardInterrupt:
        print("\n⏹️ Autonomous sync stopped by user")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # Run the autonomous sync manager
    asyncio.run(main())
