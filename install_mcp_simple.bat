@echo off
echo [INFO] Simple MCP Installation - All Servers on E Drive
echo ===================================================

cd /d "E:\The_real_deal_copy\Bybit_Bot\BOT"

echo [1/8] Installing GitHub server...
npm install @modelcontextprotocol/server-github

echo [2/8] Installing Context7 server...
npm install @modelcontextprotocol/server-context7

echo [3/8] Installing Hugging Face server...
npm install @modelcontextprotocol/server-huggingface

echo [4/8] Installing Microsoft server...
npm install @modelcontextprotocol/server-microsoft

echo [5/8] Installing web search server...
npm install websearch-mcp

echo [6/8] Installing git server...
npm install @cyanheads/git-mcp-server

echo [7/8] Installing performance boost server...
npm install @modelcontextprotocol/mcp-stdio-server

echo [8/8] Installing Python MCP packages...
call "E:\Miniconda\Scripts\activate.bat" bybit-trader
pip install mcp psycopg2-binary pyodbc

echo.
echo [SUCCESS] All MCP servers installed on E drive!
echo [INFO] Restart VS Code Insiders to activate all servers
echo.
echo Installed packages:
npm list --depth=0

pause
