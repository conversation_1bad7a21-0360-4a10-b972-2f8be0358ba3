import React from 'react';
import { StyleSheet, View, Vibration } from 'react-native';
import { Switch, Text, Card } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { theme } from '../styles/theme';

interface ToggleSwitchProps {
    title: string;
    description?: string;
    value: boolean;
    onValueChange: (value: boolean) => void;
    icon?: string;
    disabled?: boolean;
    loading?: boolean;
}

const ToggleSwitch: React.FC<ToggleSwitchProps> = ({
    title,
    description,
    value,
    onValueChange,
    icon,
    disabled = false,
    loading = false,
}) => {
    const handleToggle = (newValue: boolean) => {
        // Haptic feedback for better mobile UX
        if (newValue) {
            Vibration.vibrate(50); // Short vibration for ON
        } else {
            Vibration.vibrate([50, 50, 50]); // Pattern for OFF
        }
        
        onValueChange(newValue);
    };

    return (
        <Card style={[styles.container, disabled && styles.disabled]}>
            <Card.Content style={styles.content}>
                <View style={styles.leftSection}>
                    {icon && (
                        <View style={styles.iconContainer}>
                            <Icon
                                name={icon}
                                size={24}
                                color={value ? theme.colors.primary : theme.colors.onSurfaceVariant}
                            />
                        </View>
                    )}
                    <View style={styles.textContainer}>
                        <Text
                            variant="titleMedium"
                            style={[
                                styles.title,
                                disabled && styles.disabledText,
                                value && styles.activeTitle
                            ]}
                        >
                            {title}
                        </Text>
                        {description && (
                            <Text
                                variant="bodySmall"
                                style={[
                                    styles.description,
                                    disabled && styles.disabledText
                                ]}
                            >
                                {description}
                            </Text>
                        )}
                    </View>
                </View>
                
                <View style={styles.rightSection}>
                    <Switch
                        value={value}
                        onValueChange={handleToggle}
                        disabled={disabled || loading}
                        thumbColor={value ? theme.colors.primary : theme.colors.outline}
                        trackColor={{
                            false: theme.colors.surfaceVariant,
                            true: theme.colors.primaryContainer,
                        }}
                        style={styles.switch}
                    />
                </View>
            </Card.Content>
        </Card>
    );
};

const styles = StyleSheet.create({
    container: {
        marginVertical: 4,
        backgroundColor: theme.colors.surface,
        elevation: 1,
        borderRadius: 12,
    },
    disabled: {
        opacity: 0.6,
    },
    content: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 8,
        paddingHorizontal: 16,
        minHeight: 64, // Minimum touch target size
    },
    leftSection: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
    },
    iconContainer: {
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: theme.colors.surfaceVariant,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 16,
    },
    textContainer: {
        flex: 1,
    },
    title: {
        color: theme.colors.onSurface,
        fontWeight: '600',
        marginBottom: 2,
    },
    activeTitle: {
        color: theme.colors.primary,
    },
    description: {
        color: theme.colors.onSurfaceVariant,
        lineHeight: 16,
    },
    disabledText: {
        color: theme.colors.outline,
    },
    rightSection: {
        marginLeft: 16,
    },
    switch: {
        transform: [{ scaleX: 1.1 }, { scaleY: 1.1 }], // Slightly larger for easier touch
    },
});

export default ToggleSwitch;
