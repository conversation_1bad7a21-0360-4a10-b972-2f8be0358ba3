# INSTALL ALL MISSING DEPENDENCIES AND FIX TIMEZONE ISSUES
Write-Host "INSTALLING ALL MISSING DEPENDENCIES FOR REAL TRADING" -ForegroundColor Green

# Install missing AI/ML dependencies
Write-Host "Installing networkx..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" -m pip install networkx

Write-Host "Installing feedparser..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" -m pip install feedparser

Write-Host "Installing pytz..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" -m pip install pytz

Write-Host "Installing openai..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" -m pip install openai

Write-Host "Installing anthropic..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" -m pip install anthropic

Write-Host "Installing torch..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" -m pip install torch

Write-Host "Installing transformers..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" -m pip install transformers

Write-Host "Installing xgboost..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" -m pip install xgboost

Write-Host "ALL DEPENDENCIES INSTALLED" -ForegroundColor Green

# Set environment variables for API keys
Write-Host "Setting environment variables..." -ForegroundColor Yellow
$env:PYTHONPATH = "E:\The_real_deal_copy\Bybit_Bot\BOT"
$env:OPENROUTER_API_KEY = "your_openrouter_key_here"

# Start the real trading system
Write-Host "STARTING REAL TRADING SYSTEM - ALL DEPENDENCIES RESOLVED!" -ForegroundColor Red
& "E:\conda\Miniconda3\python.exe" main.py

Write-Host "Trading system stopped." -ForegroundColor Yellow
Read-Host "Press Enter to exit"
