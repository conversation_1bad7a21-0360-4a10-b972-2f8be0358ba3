import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import React from 'react';
import Icon from 'react-native-vector-icons/MaterialIcons';

// Screens
import AIStatusScreen from '../screens/AIStatusScreen';
import DashboardScreen from '../screens/DashboardScreen';
import LogsScreen from '../screens/LogsScreen';
import PortfolioScreen from '../screens/PortfolioScreen';
import SettingsScreen from '../screens/SettingsScreen';
import StrategyDetailsScreen from '../screens/StrategyDetailsScreen';
import TradeDetailsScreen from '../screens/TradeDetailsScreen';
import TradingScreen from '../screens/TradingScreen';

import { theme } from '../styles/theme';

const Tab = createBottomTabNavigator();
const Stack = createNativeStackNavigator();

const TabNavigator: React.FC = () => {
    return (
        <Tab.Navigator
            screenOptions={({ route }) => ({
                tabBarIcon: ({ focused, color, size }) => {
                    let iconName: string;

                    switch (route.name) {
                        case 'Dashboard':
                            iconName = 'dashboard';
                            break;
                        case 'Trading':
                            iconName = 'trending-up';
                            break;
                        case 'Portfolio':
                            iconName = 'account-balance-wallet';
                            break;
                        case 'AI Status':
                            iconName = 'psychology';
                            break;
                        case 'Logs':
                            iconName = 'description';
                            break;
                        case 'Settings':
                            iconName = 'settings';
                            break;
                        default:
                            iconName = 'circle';
                    }

                    return <Icon name={iconName} size={size} color={color} />;
                },
                tabBarActiveTintColor: theme.colors.primary,
                tabBarInactiveTintColor: theme.colors.textSecondary,
                tabBarStyle: {
                    backgroundColor: theme.colors.surface,
                    borderTopColor: theme.colors.border,
                    borderTopWidth: 1,
                    paddingBottom: 5,
                    paddingTop: 5,
                    height: 70,
                },
                tabBarLabelStyle: {
                    fontSize: 12,
                    fontWeight: '600',
                },
                headerStyle: {
                    backgroundColor: theme.colors.background,
                    shadowOpacity: 0,
                    elevation: 0,
                },
                headerTintColor: theme.colors.text,
                headerTitleStyle: {
                    fontWeight: '700',
                    fontSize: 20,
                },
            })}
        >
            <Tab.Screen
                name="Dashboard"
                component={DashboardScreen}
                options={{
                    headerTitle: 'Trading Dashboard',
                }}
            />
            <Tab.Screen
                name="Trading"
                component={TradingScreen}
                options={{
                    headerTitle: 'Active Trading',
                }}
            />
            <Tab.Screen
                name="Portfolio"
                component={PortfolioScreen}
                options={{
                    headerTitle: 'Portfolio Overview',
                }}
            />
            <Tab.Screen
                name="AI Status"
                component={AIStatusScreen}
                options={{
                    headerTitle: 'AI System Status',
                }}
            />
            <Tab.Screen
                name="Logs"
                component={LogsScreen}
                options={{
                    headerTitle: 'System Logs',
                }}
            />
            <Tab.Screen
                name="Settings"
                component={SettingsScreen}
                options={{
                    headerTitle: 'System Settings',
                }}
            />
        </Tab.Navigator>
    );
};

const AppNavigator: React.FC = () => {
    return (
        <Stack.Navigator
            screenOptions={{
                headerShown: false,
                gestureEnabled: true,
                animation: 'slide_from_right',
            }}
        >
            <Stack.Screen name="MainTabs" component={TabNavigator} />
            <Stack.Screen
                name="TradeDetails"
                component={TradeDetailsScreen}
                options={{
                    headerShown: true,
                    headerTitle: 'Trade Details',
                    headerStyle: {
                        backgroundColor: theme.colors.background,
                    },
                    headerTintColor: theme.colors.text,
                    headerTitleStyle: {
                        fontWeight: '700',
                    },
                }}
            />
            <Stack.Screen
                name="StrategyDetails"
                component={StrategyDetailsScreen}
                options={{
                    headerShown: true,
                    headerTitle: 'Strategy Details',
                    headerStyle: {
                        backgroundColor: theme.colors.background,
                    },
                    headerTintColor: theme.colors.text,
                    headerTitleStyle: {
                        fontWeight: '700',
                    },
                }}
            />
        </Stack.Navigator>
    );
};

export default AppNavigator;
