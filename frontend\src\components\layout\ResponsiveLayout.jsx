import React, { useState, useEffect } from 'react'
import {
    Box,
    AppBar,
    Toolbar,
    IconButton,
    Typography,
    Badge,
    Avatar,
    Menu,
    MenuItem,
    Divider,
    Toolt<PERSON>,
    Chip
} from '@mui/material'
import {
    Menu as MenuIcon,
    Notifications,
    AccountCircle,
    Settings,
    Logout,
    DarkMode,
    LightMode,
    Fullscreen,
    FullscreenExit
} from '@mui/icons-material'
import { motion, AnimatePresence } from 'framer-motion'
import { useResponsive } from '../../hooks/useResponsive'
import { useUserPreferences } from '../../hooks/useLocalStorage'
import { useNotifications } from '../../hooks/useNotifications'
import { useKeyboardShortcuts } from '../../hooks/useKeyboardShortcuts'
import NotificationCenter from '../common/NotificationCenter'
import Sidebar from '../Sidebar/Sidebar'

/**
 * Responsive layout wrapper with enhanced navigation and user experience
 * Provides consistent layout across all screen sizes with mobile-first approach
 */
const ResponsiveLayout = ({ children, systemStatus }) => {
    const { isMobile, isTablet, getSidebarConfig } = useResponsive()
    const { preferences, updatePreference } = useUserPreferences()
    const { unreadCount } = useNotifications()
    const { shortcuts } = useKeyboardShortcuts()
    
    const [sidebarOpen, setSidebarOpen] = useState(!isMobile)
    const [profileMenuAnchor, setProfileMenuAnchor] = useState(null)
    const [notificationAnchor, setNotificationAnchor] = useState(null)
    const [isFullscreen, setIsFullscreen] = useState(false)
    
    const sidebarConfig = getSidebarConfig()
    
    // Handle sidebar toggle
    const handleSidebarToggle = () => {
        setSidebarOpen(!sidebarOpen)
        updatePreference('sidebarCollapsed', sidebarOpen)
    }
    
    // Handle fullscreen toggle
    const handleFullscreenToggle = () => {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen()
            setIsFullscreen(true)
        } else {
            document.exitFullscreen()
            setIsFullscreen(false)
        }
    }
    
    // Handle theme toggle
    const handleThemeToggle = () => {
        const newTheme = preferences.theme === 'dark' ? 'light' : 'dark'
        updatePreference('theme', newTheme)
    }
    
    // Listen for fullscreen changes
    useEffect(() => {
        const handleFullscreenChange = () => {
            setIsFullscreen(!!document.fullscreenElement)
        }
        
        document.addEventListener('fullscreenchange', handleFullscreenChange)
        return () => document.removeEventListener('fullscreenchange', handleFullscreenChange)
    }, [])
    
    // Auto-collapse sidebar on mobile
    useEffect(() => {
        if (isMobile && sidebarOpen) {
            setSidebarOpen(false)
        }
    }, [isMobile])
    
    // Keyboard shortcuts for layout
    useEffect(() => {
        const handleKeyDown = (e) => {
            // Toggle sidebar with Ctrl+B
            if (e.ctrlKey && e.key === 'b') {
                e.preventDefault()
                handleSidebarToggle()
            }
            
            // Toggle fullscreen with F11
            if (e.key === 'F11') {
                e.preventDefault()
                handleFullscreenToggle()
            }
        }
        
        document.addEventListener('keydown', handleKeyDown)
        return () => document.removeEventListener('keydown', handleKeyDown)
    }, [sidebarOpen])
    
    // Profile menu items
    const profileMenuItems = [
        {
            label: 'Profile Settings',
            icon: <AccountCircle />,
            action: () => console.log('Profile settings')
        },
        {
            label: 'Application Settings',
            icon: <Settings />,
            action: () => console.log('App settings')
        },
        { divider: true },
        {
            label: 'Sign Out',
            icon: <Logout />,
            action: () => console.log('Sign out'),
            color: '#ff5252'
        }
    ]
    
    return (
        <Box sx={{ display: 'flex', minHeight: '100vh' }}>
            {/* Top App Bar */}
            <AppBar
                position="fixed"
                sx={{
                    zIndex: (theme) => theme.zIndex.drawer + 1,
                    backgroundColor: 'rgba(0, 0, 0, 0.9)',
                    backdropFilter: 'blur(10px)',
                    borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'
                }}
            >
                <Toolbar>
                    {/* Menu Button */}
                    <IconButton
                        color="inherit"
                        aria-label="toggle sidebar"
                        edge="start"
                        onClick={handleSidebarToggle}
                        sx={{ mr: 2 }}
                    >
                        <MenuIcon />
                    </IconButton>
                    
                    {/* Logo and Title */}
                    <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>
                        <motion.div
                            initial={{ scale: 0.8, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            transition={{ duration: 0.5 }}
                        >
                            <Box
                                sx={{
                                    width: 32,
                                    height: 32,
                                    borderRadius: '50%',
                                    background: 'linear-gradient(45deg, #00ff88, #00cc6a)',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    mr: 2,
                                    boxShadow: '0 0 15px rgba(0, 255, 136, 0.3)'
                                }}
                            >
                                <Typography
                                    variant="h6"
                                    sx={{
                                        color: '#000',
                                        fontWeight: 700,
                                        fontSize: '1rem'
                                    }}
                                >
                                    AT
                                </Typography>
                            </Box>
                        </motion.div>
                        
                        {!isMobile && (
                            <Typography variant="h6" sx={{ fontWeight: 600 }}>
                                Autonomous Trader
                            </Typography>
                        )}
                    </Box>
                    
                    {/* System Status */}
                    <Chip
                        label={systemStatus?.status === 'running' ? 'LIVE' : 'INITIALIZING'}
                        size="small"
                        sx={{
                            backgroundColor: systemStatus?.status === 'running'
                                ? 'rgba(0, 255, 136, 0.2)'
                                : 'rgba(255, 167, 38, 0.2)',
                            color: systemStatus?.status === 'running' ? '#00ff88' : '#ffa726',
                            border: systemStatus?.status === 'running'
                                ? '1px solid rgba(0, 255, 136, 0.3)'
                                : '1px solid rgba(255, 167, 38, 0.3)',
                            fontWeight: 600,
                            mr: 2
                        }}
                    />
                    
                    {/* Action Buttons */}
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {/* Theme Toggle */}
                        <Tooltip title={`Switch to ${preferences.theme === 'dark' ? 'light' : 'dark'} mode`}>
                            <IconButton
                                color="inherit"
                                onClick={handleThemeToggle}
                                sx={{ color: '#b3b3b3' }}
                            >
                                {preferences.theme === 'dark' ? <LightMode /> : <DarkMode />}
                            </IconButton>
                        </Tooltip>
                        
                        {/* Fullscreen Toggle */}
                        {!isMobile && (
                            <Tooltip title={isFullscreen ? 'Exit Fullscreen' : 'Enter Fullscreen'}>
                                <IconButton
                                    color="inherit"
                                    onClick={handleFullscreenToggle}
                                    sx={{ color: '#b3b3b3' }}
                                >
                                    {isFullscreen ? <FullscreenExit /> : <Fullscreen />}
                                </IconButton>
                            </Tooltip>
                        )}
                        
                        {/* Notifications */}
                        <Tooltip title="Notifications">
                            <IconButton
                                color="inherit"
                                onClick={(e) => setNotificationAnchor(e.currentTarget)}
                                sx={{ color: '#b3b3b3' }}
                            >
                                <Badge badgeContent={unreadCount} color="error">
                                    <Notifications />
                                </Badge>
                            </IconButton>
                        </Tooltip>
                        
                        {/* Profile Menu */}
                        <Tooltip title="Account">
                            <IconButton
                                color="inherit"
                                onClick={(e) => setProfileMenuAnchor(e.currentTarget)}
                                sx={{ color: '#b3b3b3' }}
                            >
                                <Avatar
                                    sx={{
                                        width: 32,
                                        height: 32,
                                        backgroundColor: 'rgba(0, 255, 136, 0.2)',
                                        color: '#00ff88',
                                        fontSize: '0.9rem'
                                    }}
                                >
                                    U
                                </Avatar>
                            </IconButton>
                        </Tooltip>
                    </Box>
                </Toolbar>
            </AppBar>
            
            {/* Sidebar */}
            <AnimatePresence>
                {(sidebarOpen || !isMobile) && (
                    <motion.div
                        initial={{ x: -sidebarConfig.width, opacity: 0 }}
                        animate={{ x: 0, opacity: 1 }}
                        exit={{ x: -sidebarConfig.width, opacity: 0 }}
                        transition={{ duration: 0.3, ease: 'easeInOut' }}
                        style={{
                            position: isMobile ? 'fixed' : 'relative',
                            zIndex: isMobile ? 1200 : 'auto',
                            height: '100vh'
                        }}
                    >
                        <Sidebar
                            systemStatus={systemStatus}
                            open={sidebarOpen}
                            onClose={() => setSidebarOpen(false)}
                            variant={isMobile ? 'temporary' : 'persistent'}
                        />
                    </motion.div>
                )}
            </AnimatePresence>
            
            {/* Mobile Overlay */}
            {isMobile && sidebarOpen && (
                <Box
                    sx={{
                        position: 'fixed',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        backgroundColor: 'rgba(0, 0, 0, 0.5)',
                        zIndex: 1100
                    }}
                    onClick={() => setSidebarOpen(false)}
                />
            )}
            
            {/* Main Content */}
            <Box
                component="main"
                sx={{
                    flexGrow: 1,
                    minHeight: '100vh',
                    marginTop: '64px', // AppBar height
                    transition: 'margin 0.3s ease-in-out',
                    marginLeft: !isMobile && sidebarOpen ? 0 : 0,
                    width: !isMobile && sidebarOpen 
                        ? `calc(100% - ${sidebarConfig.width}px)` 
                        : '100%'
                }}
            >
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    style={{ minHeight: 'calc(100vh - 64px)' }}
                >
                    {children}
                </motion.div>
            </Box>
            
            {/* Profile Menu */}
            <Menu
                anchorEl={profileMenuAnchor}
                open={Boolean(profileMenuAnchor)}
                onClose={() => setProfileMenuAnchor(null)}
                PaperProps={{
                    sx: {
                        backgroundColor: 'rgba(0, 0, 0, 0.9)',
                        border: '1px solid rgba(255, 255, 255, 0.1)',
                        borderRadius: 2,
                        mt: 1,
                        minWidth: 200
                    }
                }}
            >
                {profileMenuItems.map((item, index) => 
                    item.divider ? (
                        <Divider key={index} sx={{ borderColor: 'rgba(255, 255, 255, 0.1)' }} />
                    ) : (
                        <MenuItem
                            key={index}
                            onClick={() => {
                                item.action()
                                setProfileMenuAnchor(null)
                            }}
                            sx={{
                                color: item.color || '#fff',
                                '&:hover': {
                                    backgroundColor: 'rgba(255, 255, 255, 0.05)'
                                }
                            }}
                        >
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                {item.icon}
                                {item.label}
                            </Box>
                        </MenuItem>
                    )
                )}
            </Menu>
            
            {/* Notification Center */}
            <NotificationCenter
                anchorEl={notificationAnchor}
                open={Boolean(notificationAnchor)}
                onClose={() => setNotificationAnchor(null)}
                showAsDrawer={isMobile}
            />
        </Box>
    )
}

export default ResponsiveLayout
