#!/usr/bin/env python3
"""Quick account status check for live trading verification"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
from bybit_bot.core.config import EnhancedBotConfig

async def check_account_status():
    """Check current account status and positions"""
    try:
        config = EnhancedBotConfig()
        client = EnhancedBybitClient(config)
        await client.initialize()
        
        # Get account balance
        balance = await client.get_account_balance()
        print(f"Account Balance: {balance}")
        
        # Get active positions
        positions = await client.get_positions()
        print(f"Active Positions: {len(positions) if positions else 0}")
        
        if positions:
            for pos in positions:
                print(f"Position: {pos}")
        
        print("LIVE TRADING VERIFICATION: SUCCESS")
        return True
        
    except Exception as e:
        print(f"Error checking account: {e}")
        return False

if __name__ == "__main__":
    result = asyncio.run(check_account_status())
    if result:
        print("Account status check completed successfully")
    else:
        print("Account status check failed")
