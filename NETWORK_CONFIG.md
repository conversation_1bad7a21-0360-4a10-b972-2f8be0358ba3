---
type: "manual"
---

# Network Configuration Guide

# Bybit Trading Bot - IP: *************

## Current Configuration

**Your Trading System IP:** `*************`
**FastAPI Backend Port:** `8000`
**Web Frontend Port:** `3000`

## Already Configured

✅ **Mobile App API Service**

- File: `mobile/src/services/api.ts`
- Endpoint: `http://*************:8000`

✅ **Web App Proxy Configuration**

- File: `frontend/vite.config.js`
- Proxy: `/api` → `http://*************:8000`

## Network Requirements

### Firewall Configuration

Ensure these ports are open on your trading system (*************):

```bash
# Windows Firewall
netsh advfirewall firewall add rule name="Bybit Trading Bot API" dir=in action=allow protocol=TCP localport=8000
netsh advfirewall firewall add rule name="Bybit Trading Bot Web" dir=in action=allow protocol=TCP localport=3000

# Linux iptables
sudo iptables -A INPUT -p tcp --dport 8000 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 3000 -j ACCEPT
```

### Router Configuration

If accessing from external networks, configure port forwarding:

- External Port 8000 → Internal *************:8000
- External Port 3000 → Internal *************:3000

### Mobile Device Access

#### Same Network (WiFi)

- Your Motorola phone should be on the same WiFi network
- Direct access to `http://*************:8000`

#### Remote Access (4G/5G)

- Requires router port forwarding or VPN
- Consider security implications of exposing trading API

## Security Recommendations

### 1. API Security

```python
# In your FastAPI backend, add CORS for mobile access
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://*************:3000"],  # Web frontend
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

### 2. HTTPS Configuration (Recommended)

```bash
# Use reverse proxy with SSL
# nginx.conf example:
server {
    listen 443 ssl;
    server_name *************;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 3. Mobile App Security

- API calls use your configured IP
- Secure storage for authentication tokens
- Biometric app lock enabled
- Certificate pinning for production

## Testing Connectivity

### From Your Computer

```bash
# Test FastAPI backend
curl http://*************:8000/

# Test web frontend
curl http://*************:3000/
```

### From Your Motorola Phone

1. Open browser
2. Navigate to `http://*************:8000/`
3. Should see FastAPI documentation

### Mobile App Testing

```bash
# Build and install mobile app
cd mobile
npm run build:android

# Install APK on Motorola device
# Test API connectivity in app logs
```

## Troubleshooting

### Connection Failed

1. **Check IP reachability:**

   ```bash
   ping *************
   ```

2. **Verify port availability:**

   ```bash
   telnet ************* 8000
   ```

3. **Check firewall rules:**

   ```bash
   # Windows
   netsh advfirewall firewall show rule name="Bybit Trading Bot API"
   
   # Linux
   sudo iptables -L | grep 8000
   ```

### Mobile App Issues

1. **Network connectivity:** Ensure phone and computer on same network
2. **API timeout:** Check backend responsiveness
3. **CORS errors:** Verify FastAPI CORS configuration
4. **SSL issues:** Use HTTP for local development

## Production Deployment

### Option 1: Local Network Only

- Keep current IP configuration
- Mobile app works on same WiFi network
- Secure and simple setup

### Option 2: Remote Access

- Set up VPN or port forwarding
- Implement proper SSL certificates
- Add authentication middleware
- Consider rate limiting

### Option 3: Cloud Deployment

- Deploy to cloud service (AWS, Azure, etc.)
- Use proper domain with SSL
- Implement OAuth2/JWT authentication
- Add monitoring and logging

## Current Status: ✅ Ready to Use

Your system is configured with:

- **Backend:** `http://*************:8000`
- **Frontend:** `http://*************:3000`
- **Mobile API:** `http://*************:8000`

Both web and mobile applications are properly configured to communicate with your trading system at the specified IP address.
