#!/usr/bin/env python3
"""
Double Check System Status
Thorough verification of system status and fixes
"""

import os
import sys
from pathlib import Path
from datetime import datetime

def double_check_status():
    """Thoroughly check system status"""
    print("DOUBLE CHECKING SYSTEM STATUS")
    print("=" * 60)
    
    # Check if log file exists
    log_file = Path('logs/bybit_trading_bot.log')
    if not log_file.exists():
        print("ERROR: Log file not found!")
        return False
    
    # Read log file
    try:
        with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
        
        print(f"Log file has {len(lines)} total lines")
        
        # Check for ALL bids errors in entire log
        all_bids_errors = []
        for i, line in enumerate(lines):
            if "'bids'" in line and "Error getting ultra-fast price data" in line:
                all_bids_errors.append((i+1, line.strip()))
        
        print(f"Total 'bids' KeyError count in entire log: {len(all_bids_errors)}")
        
        # Check recent activity (last 200 lines)
        recent_lines = lines[-200:] if len(lines) >= 200 else lines
        recent_bids_errors = []
        for line in recent_lines:
            if "'bids'" in line and "Error getting ultra-fast price data" in line:
                recent_bids_errors.append(line.strip())
        
        print(f"Recent 'bids' KeyError count (last 200 lines): {len(recent_bids_errors)}")
        
        # Check for rate limiting
        rate_limit_errors = []
        for line in recent_lines:
            if "Access too frequent" in line:
                rate_limit_errors.append(line.strip())
        
        print(f"Recent rate limit errors: {len(rate_limit_errors)}")
        
        # Check for trading signals
        trading_signals = []
        for line in recent_lines:
            if any(keyword in line.lower() for keyword in [
                "trading signal", "signal generated", "position opened", 
                "order executed", "trade executed", "profit recorded"
            ]):
                trading_signals.append(line.strip())
        
        print(f"Recent trading activity indicators: {len(trading_signals)}")
        
        # Show last 15 log entries
        print(f"\nLast 15 log entries:")
        for line in lines[-15:]:
            if line.strip():
                print(f"  {line.strip()}")
        
        # Show recent bids errors if any
        if recent_bids_errors:
            print(f"\nRECENT 'BIDS' ERRORS FOUND:")
            for error in recent_bids_errors[-5:]:
                print(f"  {error}")
        
        # Show recent trading activity if any
        if trading_signals:
            print(f"\nRECENT TRADING ACTIVITY:")
            for signal in trading_signals[-3:]:
                print(f"  {signal}")
        
        # Check rate limiter status
        print(f"\nRate Limiter Status:")
        try:
            sys.path.insert(0, str(Path(__file__).parent))
            from bybit_bot.utils.global_rate_limiter import rate_limiter
            print(f"  - Requests per second: {rate_limiter.config.requests_per_second}")
            print(f"  - Emergency mode: {rate_limiter.emergency_mode}")
            print(f"  - Consecutive errors: {rate_limiter.consecutive_errors}")
        except Exception as e:
            print(f"  - Could not check rate limiter: {e}")
        
        # Final assessment
        print(f"\n" + "=" * 60)
        print(f"DOUBLE CHECK RESULTS:")
        
        if len(recent_bids_errors) == 0:
            print(f"✓ SUCCESS: No recent 'bids' KeyError exceptions found!")
            print(f"✓ BYBIT V5 API INTEGRATION FIX IS WORKING!")
            
            if len(rate_limit_errors) > 0:
                print(f"ℹ INFO: Rate limiting detected - this is normal API behavior")
            
            if len(trading_signals) > 0:
                print(f"✓ TRADING ACTIVITY: System is generating trading signals/activity")
            else:
                print(f"⚠ WARNING: No recent trading activity detected")
            
            return True
        else:
            print(f"✗ FAILURE: Recent 'bids' KeyError exceptions still occurring!")
            print(f"✗ ADDITIONAL FIXES NEEDED")
            return False
            
    except Exception as e:
        print(f"ERROR reading log file: {e}")
        return False

if __name__ == "__main__":
    success = double_check_status()
    if success:
        print(f"\nOVERALL STATUS: SYSTEM FIXES VERIFIED - OPERATIONAL")
    else:
        print(f"\nOVERALL STATUS: SYSTEM STILL HAS ISSUES")
    sys.exit(0 if success else 1)
