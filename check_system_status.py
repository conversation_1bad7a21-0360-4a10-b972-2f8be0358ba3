#!/usr/bin/env python3
"""
UNIFIED SYSTEM STATUS CHECKER
Comprehensive status check for all system components
"""

import asyncio
import sys
import importlib
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
import json

# Color codes for terminal output
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'


class ComponentStatus:
    """Component status tracking"""
    def __init__(self, name: str, module_path: str, class_name: str = None, critical: bool = True):
        self.name = name
        self.module_path = module_path
        self.class_name = class_name
        self.critical = critical
        self.available = False
        self.initialized = False
        self.error = None
        self.version = None


class UnifiedSystemStatusChecker:
    """Comprehensive system status checker"""
    
    def __init__(self):
        self.components = []
        self.system_info = {}
        self.overall_status = "unknown"
        
        # Define all system components
        self._define_components()
    
    def _define_components(self):
        """Define all system components to check"""
        
        # Core Components (Critical)
        core_components = [
            ComponentStatus("Bot Configuration", "bybit_bot.core.config", "BotConfig", True),
            ComponentStatus("Bot Logger", "bybit_bot.core.logger", "TradingBotLogger", True),
            ComponentStatus("Database Manager", "bybit_bot.database.connection", "DatabaseManager", True),
            ComponentStatus("Bot Manager", "bybit_bot.core.bot_manager", "BotManager", True),
        ]
        
        # Exchange Components (Critical)
        exchange_components = [
            ComponentStatus("Enhanced Bybit Client", "bybit_bot.exchange.enhanced_bybit_client", "EnhancedBybitClient", True),
            ComponentStatus("Bybit V5 Client", "bybit_bot.exchange.bybit_v5_client", "BybitV5Client", False),
        ]
        
        # AI Components (Important but not critical)
        ai_components = [
            ComponentStatus("Memory Manager", "bybit_bot.ai.memory_manager", "PersistentMemoryManager", False),
            ComponentStatus("Meta-Cognition Engine", "bybit_bot.ai.meta_cognition_engine", "MetaCognitionEngine", False),
            ComponentStatus("Code Evolution", "bybit_bot.ai.self_correcting_code_evolution", "SelfCorrectingCodeEvolution", False),
            ComponentStatus("Recursive Improvement", "bybit_bot.ai.recursive_improvement_system", "RecursiveImprovementSystem", False),
        ]
        
        # Agent Components (Important)
        agent_components = [
            ComponentStatus("Agent Orchestrator", "bybit_bot.agents.agent_orchestrator", "AgentOrchestrator", False),
            ComponentStatus("Learning Agent", "bybit_bot.agents.learning_agent", "LearningAgent", False),
            ComponentStatus("Trading Agent", "bybit_bot.agents.trading_agent", "TradingAgent", False),
            ComponentStatus("Research Agent", "bybit_bot.agents.research_agent", "ResearchAgent", False),
            ComponentStatus("Risk Agent", "bybit_bot.agents.risk_agent", "RiskAgent", False),
        ]
        
        # Profit Components (Critical)
        profit_components = [
            ComponentStatus("Advanced Profit Engine", "bybit_bot.profit_maximization.advanced_profit_engine", "AdvancedProfitEngine", True),
            ComponentStatus("Hyper Profit Engine", "bybit_bot.profit_maximization.hyper_profit_engine", "HyperProfitEngine", True),
        ]
        
        # SuperGPT Components (Important)
        supergpt_components = [
            ComponentStatus("Self-Healing System", "bybit_bot.core.self_healing", "SelfHealingSystem", False),
            ComponentStatus("Autonomy Engine", "bybit_bot.core.autonomy_engine", "AutonomyEngine", False),
            ComponentStatus("Code Optimizer", "bybit_bot.core.code_optimizer", "CodeOptimizer", False),
        ]
        
        # Strategy Components (Important)
        strategy_components = [
            ComponentStatus("Strategy Manager", "bybit_bot.strategies.strategy_manager", "StrategyManager", False),
            ComponentStatus("Adaptive Strategy Engine", "bybit_bot.strategies.adaptive_strategy_engine", "AdaptiveStrategyEngine", False),
        ]
        
        # Data Components (Important)
        data_components = [
            ComponentStatus("Market Data Crawler", "bybit_bot.data_crawler.market_data_crawler", "MarketDataCrawler", False),
            ComponentStatus("News Sentiment Crawler", "bybit_bot.data_crawler.news_sentiment_crawler", "NewsSentimentCrawler", False),
            ComponentStatus("Social Sentiment Crawler", "bybit_bot.data_crawler.social_sentiment_crawler", "SocialSentimentCrawler", False),
            ComponentStatus("Economic Data Crawler", "bybit_bot.data_crawler.economic_data_crawler", "EconomicDataCrawler", False),
        ]
        
        # Analytics Components (Important)
        analytics_components = [
            ComponentStatus("Performance Analyzer", "bybit_bot.analytics.performance_analyzer", "PerformanceAnalyzer", False),
            ComponentStatus("Market Predictor", "bybit_bot.ml.market_predictor", "MarketPredictor", False),
        ]
        
        # Risk Components (Critical)
        risk_components = [
            ComponentStatus("Advanced Risk Manager", "bybit_bot.ai.advanced_risk_manager", "AdvancedRiskManager", True),
        ]
        
        # Monitoring Components (Important)
        monitoring_components = [
            ComponentStatus("Hardware Monitor", "bybit_bot.monitoring.hardware_monitor", "HardwareMonitor", False),
        ]
        
        # Combine all components
        self.components = (
            core_components + exchange_components + ai_components + 
            agent_components + profit_components + supergpt_components + 
            strategy_components + data_components + analytics_components + 
            risk_components + monitoring_components
        )
    
    def check_component(self, component: ComponentStatus) -> ComponentStatus:
        """Check if a component is available and working"""
        try:
            # Try to import the module
            module = importlib.import_module(component.module_path)
            component.available = True
            
            # Try to get the class if specified
            if component.class_name:
                cls = getattr(module, component.class_name)
                component.available = True
                
                # Try to get version if available
                if hasattr(cls, '__version__'):
                    component.version = cls.__version__
                elif hasattr(module, '__version__'):
                    component.version = module.__version__
            
        except ImportError as e:
            component.available = False
            component.error = f"Import error: {e}"
        except AttributeError as e:
            component.available = False
            component.error = f"Class not found: {e}"
        except Exception as e:
            component.available = False
            component.error = f"Unexpected error: {e}"
        
        return component
    
    def collect_system_info(self):
        """Collect general system information"""
        self.system_info = {
            "timestamp": datetime.now().isoformat(),
            "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            "working_directory": str(Path.cwd()),
            "conda_environment": os.environ.get('CONDA_DEFAULT_ENV', 'None'),
            "platform": sys.platform,
            "path": sys.path[:3]  # First 3 paths for brevity
        }
    
    def check_all_components(self):
        """Check all components"""
        print(f"{Colors.CYAN}🔍 Checking all system components...{Colors.END}\n")
        
        # Group components by category
        categories = {
            "Core": [c for c in self.components if "config" in c.module_path.lower() or "logger" in c.module_path.lower() or "database" in c.module_path.lower() or "bot_manager" in c.module_path.lower()],
            "Exchange": [c for c in self.components if "exchange" in c.module_path],
            "AI Systems": [c for c in self.components if "ai." in c.module_path],
            "Agents": [c for c in self.components if "agents" in c.module_path],
            "Profit": [c for c in self.components if "profit" in c.module_path],
            "SuperGPT": [c for c in self.components if "self_healing" in c.module_path or "autonomy" in c.module_path or "code_optimizer" in c.module_path],
            "Strategies": [c for c in self.components if "strategies" in c.module_path],
            "Data": [c for c in self.components if "data_crawler" in c.module_path],
            "Analytics": [c for c in self.components if "analytics" in c.module_path or "ml." in c.module_path],
            "Risk": [c for c in self.components if "risk" in c.module_path],
            "Monitoring": [c for c in self.components if "monitoring" in c.module_path]
        }
        
        for category, components in categories.items():
            if not components:
                continue
                
            print(f"{Colors.BLUE}📦 {category} Components:{Colors.END}")
            
            for component in components:
                self.check_component(component)
                
                if component.available:
                    icon = "✅"
                    color = Colors.GREEN
                    status = "Available"
                    if component.version:
                        status += f" (v{component.version})"
                elif component.critical:
                    icon = "❌"
                    color = Colors.RED
                    status = "MISSING (CRITICAL)"
                else:
                    icon = "⚠️"
                    color = Colors.YELLOW
                    status = "Missing (Optional)"
                
                print(f"  {icon} {color}{component.name}: {status}{Colors.END}")
                
                if component.error and not component.available:
                    print(f"    {Colors.RED}└─ {component.error}{Colors.END}")
            
            print()
    
    def calculate_overall_status(self) -> str:
        """Calculate overall system status"""
        critical_components = [c for c in self.components if c.critical]
        available_critical = [c for c in critical_components if c.available]
        
        total_components = len(self.components)
        available_components = len([c for c in self.components if c.available])
        
        critical_percentage = len(available_critical) / len(critical_components) * 100
        overall_percentage = available_components / total_components * 100
        
        if critical_percentage == 100:
            if overall_percentage >= 90:
                return "Excellent"
            elif overall_percentage >= 75:
                return "Good"
            else:
                return "Functional"
        elif critical_percentage >= 75:
            return "Limited"
        else:
            return "Critical Issues"
    
    def print_summary(self):
        """Print comprehensive summary"""
        self.overall_status = self.calculate_overall_status()
        
        # Count components
        total_components = len(self.components)
        available_components = len([c for c in self.components if c.available])
        critical_components = len([c for c in self.components if c.critical])
        available_critical = len([c for c in self.components if c.critical and c.available])
        
        print(f"{Colors.CYAN}{'=' * 80}{Colors.END}")
        print(f"{Colors.BOLD}{Colors.WHITE}📊 UNIFIED SYSTEM STATUS SUMMARY{Colors.END}")
        print(f"{Colors.CYAN}{'=' * 80}{Colors.END}")
        
        # Overall status
        status_color = Colors.GREEN if self.overall_status in ["Excellent", "Good"] else Colors.YELLOW if self.overall_status == "Functional" else Colors.RED
        print(f"🎯 Overall Status: {status_color}{Colors.BOLD}{self.overall_status}{Colors.END}")
        
        # Component counts
        print(f"📦 Components: {available_components}/{total_components} available ({available_components/total_components*100:.1f}%)")
        print(f"⚡ Critical: {available_critical}/{critical_components} available ({available_critical/critical_components*100:.1f}%)")
        
        # System info
        print(f"\n{Colors.BLUE}💻 System Information:{Colors.END}")
        print(f"  🐍 Python: {self.system_info['python_version']}")
        print(f"  🔧 Environment: {self.system_info['conda_environment']}")
        print(f"  📁 Directory: {self.system_info['working_directory']}")
        print(f"  🕒 Checked: {self.system_info['timestamp']}")
        
        # Recommendations
        print(f"\n{Colors.MAGENTA}💡 Recommendations:{Colors.END}")
        
        missing_critical = [c for c in self.components if c.critical and not c.available]
        if missing_critical:
            print(f"  {Colors.RED}🚨 Install missing critical components:{Colors.END}")
            for component in missing_critical:
                print(f"    {Colors.RED}• {component.name}{Colors.END}")
        
        missing_optional = [c for c in self.components if not c.critical and not c.available]
        if missing_optional and len(missing_optional) <= 10:  # Only show if not too many
            print(f"  {Colors.YELLOW}⚠️ Consider installing optional components:{Colors.END}")
            for component in missing_optional[:5]:  # Show only first 5
                print(f"    {Colors.YELLOW}• {component.name}{Colors.END}")
            if len(missing_optional) > 5:
                print(f"    {Colors.YELLOW}• ... and {len(missing_optional) - 5} more{Colors.END}")
        
        if self.overall_status in ["Excellent", "Good", "Functional"]:
            print(f"  {Colors.GREEN}✅ System ready for startup{Colors.END}")
        else:
            print(f"  {Colors.RED}❌ Fix critical issues before startup{Colors.END}")
        
        print(f"\n{Colors.CYAN}{'=' * 80}{Colors.END}")
    
    def save_status_report(self, filename: str = None):
        """Save status report to file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"system_status_report_{timestamp}.json"
        
        report = {
            "system_info": self.system_info,
            "overall_status": self.overall_status,
            "timestamp": datetime.now().isoformat(),
            "components": []
        }
        
        for component in self.components:
            report["components"].append({
                "name": component.name,
                "module_path": component.module_path,
                "class_name": component.class_name,
                "critical": component.critical,
                "available": component.available,
                "error": component.error,
                "version": component.version
            })
        
        try:
            with open(filename, 'w') as f:
                json.dump(report, f, indent=2)
            print(f"{Colors.GREEN}📄 Status report saved to: {filename}{Colors.END}")
        except Exception as e:
            print(f"{Colors.RED}❌ Failed to save report: {e}{Colors.END}")
    
    def run_comprehensive_check(self):
        """Run comprehensive system check"""
        print(f"{Colors.CYAN}{'=' * 80}{Colors.END}")
        print(f"{Colors.BOLD}{Colors.WHITE}🚀 UNIFIED SYSTEM STATUS CHECKER{Colors.END}")
        print(f"{Colors.CYAN}{'=' * 80}{Colors.END}")
        print(f"{Colors.YELLOW}🔍 Analyzing {len(self.components)} system components...{Colors.END}\n")
        
        # Collect system info
        self.collect_system_info()
        
        # Check all components
        self.check_all_components()
        
        # Print summary
        self.print_summary()
        
        # Ask to save report
        print(f"\n{Colors.CYAN}💾 Save detailed status report? (y/N): {Colors.END}", end="")
        try:
            response = input().strip().lower()
            if response in ['y', 'yes']:
                self.save_status_report()
        except KeyboardInterrupt:
            print(f"\n{Colors.YELLOW}👋 Status check cancelled{Colors.END}")


def main():
    """Main entry point"""
    try:
        checker = UnifiedSystemStatusChecker()
        checker.run_comprehensive_check()
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}👋 Status check interrupted{Colors.END}")
    except Exception as e:
        print(f"\n{Colors.RED}❌ Unexpected error: {e}{Colors.END}")


if __name__ == "__main__":
    main()
