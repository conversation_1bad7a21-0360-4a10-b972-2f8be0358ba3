---
type: "manual"
---

# REDIS SETUP AND CONFIGURATION GUIDE
## FOR BYBIT TRADING BOT SYSTEM

### **PHASE 1: REDIS INSTALLATION**

#### **Step 1: Download Redis for Windows**
1. Open web browser
2. Navigate to: `https://github.com/microsoftarchive/redis/releases`
3. Download: `Redis-x64-3.0.504.msi` (latest stable version)
4. Save to: `E:\The_real_deal_copy\Bybit_Bot\Redis-x64-3.0.504.msi`

#### **Step 2: Install Redis**
1. Run installer: `E:\The_real_deal_copy\Bybit_Bot\Redis-x64-3.0.504.msi`
2. **Install to**: `C:\Program Files\Redis\` (default location)
3. **Check**: "Add Redis to PATH" option
4. **Check**: "Install Redis as Windows Service" option
5. Complete installation

#### **Step 3: Verify Redis Installation**
1. Open Command Prompt as Administrator
2. Test Redis CLI: `redis-cli --version`
3. Test Redis Server: `redis-server --version`

---

### **PHASE 2: REDIS CONFIGURATION**

#### **Step 1: Start Redis Service**
```cmd
net start Redis
```

#### **Step 2: Test Redis Connection**
```cmd
redis-cli ping
```
**Expected Response**: `PONG`

#### **Step 3: Configure Redis for Trading Bot**
```cmd
redis-cli config set maxmemory 1gb
redis-cli config set maxmemory-policy allkeys-lru
redis-cli config set save "900 1 300 10 60 10000"
```

---

### **PHASE 3: REDIS VALIDATION FOR TRADING BOT**

#### **Step 1: Test Basic Redis Operations**
```cmd
redis-cli set test_key "test_value"
redis-cli get test_key
redis-cli del test_key
```

#### **Step 2: Test Redis from Python Environment**
1. Activate bybit-trader environment:
```cmd
E:\The_real_deal_copy\Bybit_Bot\miniconda3\Scripts\activate.bat bybit-trader
```

2. Test Redis connection:
```cmd
python -c "import redis; r = redis.Redis(host='localhost', port=6379, db=0); print('Redis connection:', r.ping())"
```

#### **Step 3: Initialize Trading Bot Redis Schema**
```cmd
python -c "
import redis
r = redis.Redis(host='localhost', port=6379, db=0)
r.hset('trading_bot:status', 'initialized', 'true')
r.hset('trading_bot:status', 'version', '1.0.0')
r.hset('trading_bot:config', 'max_positions', '10')
r.hset('trading_bot:config', 'risk_level', 'moderate')
print('Redis schema initialized successfully')
"
```

---

### **PHASE 4: REDIS MONITORING SETUP**

#### **Step 1: Create Redis Monitoring Script**
Save this as `redis_monitor.py`:

```python
import redis
import time
import json
from datetime import datetime

def monitor_redis():
    r = redis.Redis(host='localhost', port=6379, db=0)
    
    while True:
        try:
            # Get Redis info
            info = r.info()
            
            # Get key count
            key_count = r.dbsize()
            
            # Get memory usage
            memory_used = info.get('used_memory_human', 'Unknown')
            
            # Create status report
            status = {
                'timestamp': datetime.now().isoformat(),
                'key_count': key_count,
                'memory_used': memory_used,
                'connected_clients': info.get('connected_clients', 0),
                'uptime_seconds': info.get('uptime_in_seconds', 0)
            }
            
            print(f"Redis Status: {json.dumps(status, indent=2)}")
            
            # Check if we have minimum required keys for trading bot
            if key_count >= 10:
                print("SUCCESS: Redis has sufficient keys for trading operations")
            else:
                print(f"WARNING: Redis only has {key_count} keys, need minimum 10")
            
            time.sleep(60)  # Check every minute
            
        except Exception as e:
            print(f"Redis monitoring error: {e}")
            time.sleep(10)

if __name__ == "__main__":
    monitor_redis()
```

#### **Step 2: Test Redis Monitoring**
```cmd
python redis_monitor.py
```

---

### **PHASE 5: INTEGRATION WITH TRADING BOT**

#### **Step 1: Verify Redis Configuration in .env**
Check that `.env` file contains:
```
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
```

#### **Step 2: Test Trading Bot Redis Integration**
```cmd
python -c "
from bybit_bot.database.connection import DatabaseManager
db = DatabaseManager()
print('Database manager initialized successfully')
"
```

#### **Step 3: Verify Redis Keys After Bot Startup**
After running `python main.py`, check Redis keys:
```cmd
redis-cli keys "*"
```
**Expected**: Should show multiple trading bot keys

---

### **SUCCESS CRITERIA**

✅ **Redis service running**: `net start Redis` succeeds
✅ **Redis CLI responds**: `redis-cli ping` returns `PONG`
✅ **Python Redis connection**: Import and connection test succeeds
✅ **Minimum 10 keys**: After 1 hour of bot operation
✅ **Trading bot integration**: No Redis connection errors in logs

---

### **TROUBLESHOOTING**

#### **Redis Service Won't Start**
```cmd
sc delete Redis
# Reinstall Redis with administrator privileges
```

#### **Connection Refused**
```cmd
netstat -an | findstr :6379
# Check if Redis is listening on port 6379
```

#### **Memory Issues**
```cmd
redis-cli config get maxmemory
redis-cli config set maxmemory 2gb
```

---

### **REDIS PATHS FOR REFERENCE**
- **Redis Installation**: `C:\Program Files\Redis\`
- **Redis CLI**: `C:\Program Files\Redis\redis-cli.exe`
- **Redis Server**: `C:\Program Files\Redis\redis-server.exe`
- **Redis Config**: `C:\Program Files\Redis\redis.windows.conf`
