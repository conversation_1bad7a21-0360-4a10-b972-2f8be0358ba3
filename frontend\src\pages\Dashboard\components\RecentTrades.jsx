import { AccessTime, TrendingDown, TrendingUp } from '@mui/icons-material'
import {
    Avatar,
    Box,
    Card,
    CardContent,
    Chip,
    List,
    ListItem,
    ListItemText,
    Typography,
} from '@mui/material'
import { motion } from 'framer-motion'

const RecentTrades = ({ trades: realtimeTrades = null }) => {
    // Use real-time trades data or fallback to mock data
    const mockTrades = [
        {
            id: '1',
            symbol: 'BTCUSDT',
            type: 'BUY',
            amount: 0.0145,
            price: 42350.50,
            profit: 145.32,
            timestamp: '2 min ago',
            strategy: 'Arbitrage',
            status: 'completed',
        },
        {
            id: '2',
            symbol: 'ETHUSDT',
            type: 'SELL',
            amount: 0.8234,
            price: 2543.20,
            profit: -23.45,
            timestamp: '5 min ago',
            strategy: 'Grid Trading',
            status: 'completed',
        },
        {
            id: '3',
            symbol: 'ADAUSDT',
            type: 'BUY',
            amount: 2847.5,
            price: 0.4523,
            profit: 67.89,
            timestamp: '8 min ago',
            strategy: 'Momentum',
            status: 'completed',
        },
        {
            id: '4',
            symbol: 'SOLUSDT',
            type: 'SELL',
            amount: 12.45,
            price: 98.76,
            profit: 234.56,
            timestamp: '12 min ago',
            strategy: 'News Trading',
            status: 'completed',
        },
        {
            id: '5',
            symbol: 'DOTUSDT',
            type: 'BUY',
            amount: 45.67,
            price: 6.834,
            profit: 12.34,
            timestamp: '15 min ago',
            strategy: 'Mean Reversion',
            status: 'pending',
        },
    ]

    // Use real-time trades if available, otherwise use mock data
    const trades = realtimeTrades && realtimeTrades.length > 0 ? realtimeTrades.slice(0, 5) : mockTrades

    const getTradeIcon = (symbol) => {
        const symbolMap = {
            'BTCUSDT': '₿',
            'ETHUSDT': 'Ξ',
            'ADAUSDT': '₳',
            'SOLUSDT': '◎',
            'DOTUSDT': '●',
        }
        return symbolMap[symbol] || '◯'
    }

    const getTradeColor = (type) => {
        return type === 'BUY' ? '#00ff88' : '#ff4757'
    }

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
        >
            <Card
                sx={{
                    background: 'rgba(255, 255, 255, 0.03)',
                    backdropFilter: 'blur(20px)',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    borderRadius: 3,
                    height: '400px',
                }}
            >
                <CardContent sx={{ height: '100%', p: 3 }}>
                    <Typography
                        variant="h6"
                        sx={{
                            fontWeight: 600,
                            color: '#ffffff',
                            mb: 3,
                        }}
                    >
                        Recent Trades
                    </Typography>

                    <List sx={{ py: 0, overflow: 'auto', maxHeight: 'calc(100% - 60px)' }}>
                        {trades.map((trade, index) => (
                            <motion.div
                                key={trade.id}
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.4, delay: index * 0.1 }}
                            >
                                <ListItem
                                    sx={{
                                        px: 0,
                                        py: 1.5,
                                        borderBottom: index < trades.length - 1 ? '1px solid rgba(255, 255, 255, 0.05)' : 'none',
                                    }}
                                >
                                    <Avatar
                                        sx={{
                                            width: 32,
                                            height: 32,
                                            mr: 2,
                                            backgroundColor: `${getTradeColor(trade.type)}20`,
                                            color: getTradeColor(trade.type),
                                            fontSize: '0.8rem',
                                            fontWeight: 600,
                                        }}
                                    >
                                        {getTradeIcon(trade.symbol)}
                                    </Avatar>

                                    <ListItemText
                                        primary={
                                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                    <Typography variant="body2" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                                        {trade.symbol}
                                                    </Typography>
                                                    <Chip
                                                        label={trade.type}
                                                        size="small"
                                                        sx={{
                                                            backgroundColor: `${getTradeColor(trade.type)}20`,
                                                            color: getTradeColor(trade.type),
                                                            fontSize: '0.7rem',
                                                            height: 18,
                                                            fontWeight: 600,
                                                        }}
                                                    />
                                                </Box>
                                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                                    {trade.profit > 0 ? (
                                                        <TrendingUp sx={{ color: '#00ff88', fontSize: 14 }} />
                                                    ) : (
                                                        <TrendingDown sx={{ color: '#ff4757', fontSize: 14 }} />
                                                    )}
                                                    <Typography
                                                        variant="caption"
                                                        sx={{
                                                            color: trade.profit > 0 ? '#00ff88' : '#ff4757',
                                                            fontWeight: 600,
                                                        }}
                                                    >
                                                        {trade.profit > 0 ? '+' : ''}${trade.profit.toFixed(2)}
                                                    </Typography>
                                                </Box>
                                            </Box>
                                        }
                                        secondary={
                                            <Box>
                                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                                                    <Typography variant="caption" sx={{ color: '#b3b3b3' }}>
                                                        {trade.amount} @ ${trade.price.toLocaleString()}
                                                    </Typography>
                                                    <Typography variant="caption" sx={{ color: '#666' }}>
                                                        {trade.strategy}
                                                    </Typography>
                                                </Box>
                                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                                        <AccessTime sx={{ color: '#666', fontSize: 12 }} />
                                                        <Typography variant="caption" sx={{ color: '#666' }}>
                                                            {trade.timestamp}
                                                        </Typography>
                                                    </Box>
                                                    <Chip
                                                        label={trade.status}
                                                        size="small"
                                                        sx={{
                                                            backgroundColor: trade.status === 'completed' ? '#00ff8820' : '#ffa72620',
                                                            color: trade.status === 'completed' ? '#00ff88' : '#ffa726',
                                                            fontSize: '0.65rem',
                                                            height: 16,
                                                        }}
                                                    />
                                                </Box>
                                            </Box>
                                        }
                                    />
                                </ListItem>
                            </motion.div>
                        ))}
                    </List>
                </CardContent>
            </Card>
        </motion.div>
    )
}

export default RecentTrades
