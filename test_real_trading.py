#!/usr/bin/env python3
"""
EMERGENCY REAL TRADING TEST - BYPASS HANGING ISSUES
Test the fixed trading execution directly
"""

import asyncio
import os
import sys
from dotenv import load_dotenv

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_real_trading():
    """Test real trading execution with fixed parameters"""
    print("EMERGENCY TRADING TEST - STARTING REAL TRADE EXECUTION")
    
    try:
        # Load environment
        load_dotenv()
        
        # Import required modules
        from bybit_bot.core.config import BotConfig
        from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
        from bybit_bot.profit_maximization.advanced_profit_engine import AdvancedProfitEngine
        
        print("SUCCESS: Modules imported")
        
        # Initialize config and client
        config = BotConfig()
        bybit_client = EnhancedBybitClient(config)
        
        print("SUCCESS: Client created")
        
        # Initialize client
        await bybit_client.initialize()
        print("SUCCESS: Client initialized")
        
        # Test account connection
        account_info = await bybit_client.get_account_info()
        if account_info:
            print(f"SUCCESS: Account connected - Balance available")
        
        # Get current price for testing
        btc_price = await bybit_client.get_current_price("BTCUSDT")
        print(f"SUCCESS: BTC Price: ${btc_price}")
        
        # Test small order placement (REAL TRADE)
        print("PLACING REAL TEST ORDER...")
        
        # Calculate very small position size for testing
        test_quantity = 0.001  # $100 worth at current price
        
        order_result = await bybit_client.place_order(
            symbol="BTCUSDT",
            side="Buy",
            qty=str(test_quantity),
            order_type="Market",
            category="linear"
        )
        
        if order_result and order_result.get('orderId'):
            print(f"SUCCESS: REAL TRADE EXECUTED - Order ID: {order_result.get('orderId')}")
            print(f"Trade Details: Buy {test_quantity} BTCUSDT at Market Price")
            
            # Wait a moment then close the position
            await asyncio.sleep(5)
            
            # Close position
            close_result = await bybit_client.place_order(
                symbol="BTCUSDT",
                side="Sell",
                qty=str(test_quantity),
                order_type="Market",
                category="linear"
            )
            
            if close_result and close_result.get('orderId'):
                print(f"SUCCESS: POSITION CLOSED - Order ID: {close_result.get('orderId')}")
            
        else:
            print(f"FAILED: Order placement failed - {order_result}")
        
        # Test profit engine with real execution
        print("TESTING PROFIT ENGINE WITH REAL EXECUTION...")
        
        profit_engine = AdvancedProfitEngine(bybit_client, config)
        await profit_engine.initialize()
        
        print("SUCCESS: Profit engine initialized")
        
        # Start profit engine for 30 seconds
        print("STARTING 30-SECOND PROFIT GENERATION TEST...")
        
        # Start the engine
        engine_task = asyncio.create_task(profit_engine.start())
        
        # Let it run for 30 seconds
        await asyncio.sleep(30)
        
        # Stop the engine
        await profit_engine.stop()
        
        print("PROFIT ENGINE TEST COMPLETED")
        
        # Get final status
        status = await profit_engine.get_status()
        print(f"Final Status: {status}")
        
        await bybit_client.close()
        print("EMERGENCY TRADING TEST COMPLETED")
        
    except Exception as e:
        print(f"ERROR in emergency trading test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("STARTING EMERGENCY REAL TRADING TEST")
    asyncio.run(test_real_trading())
