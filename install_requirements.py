#!/usr/bin/env python3
"""
Install requirements and run the system
"""
import sys
import subprocess
import os

def install_requirements():
    print("Installing requirements...")
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                              check=True, capture_output=True, text=True)
        print("Requirements installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Failed to install requirements: {e}")
        print("stderr:", e.stderr)
        return False

def test_six():
    try:
        import six
        print(f"SUCCESS: six is available (version {six.__version__})")
        return True
    except ImportError:
        print("ERROR: six still not available")
        return False

def main():
    print("BYBIT TRADING BOT - DEPENDENCY INSTALLER")
    print("=" * 50)
    
    if install_requirements():
        if test_six():
            print("All dependencies ready!")
            print("Starting main system...")
            os.system("python main.py")
        else:
            print("six dependency still missing")
    else:
        print("Failed to install requirements")

if __name__ == "__main__":
    main()
