"""
Risk Agent - Specialized agent for risk management and monitoring
Handles real-time risk assessment, position monitoring, and emergency protocols
"""
import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json
import numpy as np

from ..core.config import BotConfig
from ..core.logger import TradingBotLogger
from ..database.connection import DatabaseManager
from ..risk.advanced_risk_manager import AdvancedRiskManager


class RiskLevel(Enum):
    """Risk level enumeration"""
    VERY_LOW = "very_low"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"
    CRITICAL = "critical"


class AlertType(Enum):
    """Alert type enumeration"""
    POSITION_RISK = "position_risk"
    PORTFOLIO_RISK = "portfolio_risk"
    MARKET_RISK = "market_risk"
    LIQUIDITY_RISK = "liquidity_risk"
    TECHNICAL_RISK = "technical_risk"
    DRAWDOWN_RISK = "drawdown_risk"


@dataclass
class RiskAlert:
    """Risk alert structure"""
    alert_id: str
    alert_type: AlertType
    risk_level: RiskLevel
    message: str
    affected_positions: List[str]
    recommended_actions: List[str]
    timestamp: datetime
    auto_action_taken: bool = False
    resolved: bool = False


@dataclass
class RiskAssessment:
    """Risk assessment structure"""
    assessment_id: str
    symbol: str
    risk_level: RiskLevel
    risk_score: float
    risk_factors: List[str]
    recommendations: List[str]
    confidence: float
    timestamp: datetime
    validity_period: timedelta


@dataclass
class PortfolioRisk:
    """Portfolio risk metrics"""
    total_exposure: float
    diversification_score: float
    correlation_risk: float
    concentration_risk: float
    liquidity_risk: float
    drawdown_risk: float
    var_1d: float  # Value at Risk 1 day
    var_7d: float  # Value at Risk 7 days
    expected_shortfall: float
    risk_adjusted_return: float


class RiskAgent:
    """
    Specialized risk agent for comprehensive risk management
    
    Capabilities:
    - Real-time risk monitoring
    - Position risk assessment
    - Portfolio risk analysis
    - Drawdown monitoring
    - Correlation risk analysis
    - Liquidity risk assessment
    - Market risk evaluation
    - Emergency protocols
    - Automated risk responses
    - Risk reporting
    - Stress testing
    """
    
    def __init__(self, agent_id: str, config: BotConfig, database_manager: DatabaseManager, orchestrator):
        self.agent_id = agent_id
        self.config = config
        self.db_manager = database_manager
        self.orchestrator = orchestrator
        self.logger = TradingBotLogger(f"RiskAgent_{agent_id}")
        
        # Risk management components
        self.risk_manager = None
        
        # Risk monitoring state
        self.active_alerts: Dict[str, RiskAlert] = {}
        self.risk_assessments: Dict[str, RiskAssessment] = {}
        self.portfolio_risk: Optional[PortfolioRisk] = None
        self.risk_history: List[Dict[str, Any]] = []
        
        # Risk limits and thresholds
        self.risk_limits = {
            'max_position_risk': 0.02,  # 2% per position
            'max_portfolio_risk': 0.05,  # 5% total portfolio
            'max_drawdown': 0.10,       # 10% maximum drawdown
            'max_correlation': 0.70,    # 70% maximum correlation
            'min_liquidity': 0.80,      # 80% minimum liquidity
            'var_limit': 0.03           # 3% VaR limit
        }
        
        # Performance metrics
        self.metrics = {
            'alerts_generated': 0,
            'auto_actions_taken': 0,
            'risk_assessments_completed': 0,
            'emergency_stops': 0,
            'risk_score_average': 0.0,
            'alert_accuracy': 0.0
        }
        
        # Control flags
        self.is_running = False
        self.emergency_mode = False
        self.risk_monitoring_interval = 10  # seconds
        
        # Task handlers
        self.task_handlers = {
            'assess_position_risk': self._assess_position_risk_task,
            'assess_portfolio_risk': self._assess_portfolio_risk_task,
            'check_risk_limits': self._check_risk_limits_task,
            'calculate_var': self._calculate_var_task,
            'stress_test': self._stress_test_task,
            'emergency_stop': self._emergency_stop_task,
            'get_risk_report': self._get_risk_report_task,
            'update_risk_limits': self._update_risk_limits_task,
            'correlation_analysis': self._correlation_analysis_task,
            'liquidity_assessment': self._liquidity_assessment_task,
            'drawdown_analysis': self._drawdown_analysis_task
        }
    
    async def initialize(self):
        """Initialize the risk agent"""
        try:
            self.logger.info(f"Initializing Risk Agent {self.agent_id}")
            
            # Initialize advanced risk manager
            self.risk_manager = AdvancedRiskManager(
                self.config, 
                self.db_manager, 
                None  # Will get bybit client from trading agent
            )
            await self.risk_manager.initialize()
            
            # Load risk configuration
            await self._load_risk_configuration()
            
            # Start monitoring loops
            self.is_running = True
            asyncio.create_task(self._risk_monitoring_loop())
            asyncio.create_task(self._portfolio_risk_loop())
            asyncio.create_task(self._alert_management_loop())
            asyncio.create_task(self._emergency_monitoring_loop())
            
            self.logger.info(f"Risk Agent {self.agent_id} initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Risk Agent: {e}")
            raise
    
    async def assign_task(self, task):
        """Assign a task to this agent"""
        try:
            task_type = task.task_type
            handler = self.task_handlers.get(task_type)
            
            if handler:
                result = await handler(task.data)
                
                # Send result back to orchestrator
                await self.orchestrator.send_message(
                    sender=self.agent_id,
                    recipient='orchestrator',
                    message_type='task_response',
                    data={
                        'task_id': task.task_id,
                        'result': result,
                        'status': 'completed'
                    }
                )
                
                self.logger.info(f"Task {task.task_id} completed successfully")
            else:
                self.logger.error(f"Unknown task type: {task_type}")
                
        except Exception as e:
            self.logger.error(f"Error executing task {task.task_id}: {e}")
            
            # Send error response
            await self.orchestrator.send_message(
                sender=self.agent_id,
                recipient='orchestrator',
                message_type='task_response',
                data={
                    'task_id': task.task_id,
                    'error': str(e),
                    'status': 'failed'
                }
            )
    
    async def _load_risk_configuration(self):
        """Load risk configuration from database or config"""
        try:
            # Load custom risk limits if available
            custom_limits = getattr(self.config, 'risk_limits', {})
            self.risk_limits.update(custom_limits)
            
            self.logger.info("Risk configuration loaded successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to load risk configuration: {e}")
    
    async def _assess_position_risk_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Assess risk for a specific position"""
        try:
            position = data['position']
            market_data = data.get('market_data', {})
            
            # Calculate position risk metrics
            risk_metrics = await self._calculate_position_risk_metrics(position, market_data)
            
            # Determine risk level
            risk_level = self._determine_risk_level(risk_metrics['risk_score'])
            
            # Generate risk assessment
            assessment = RiskAssessment(
                assessment_id=f"risk_{position['symbol']}_{int(time.time())}",
                symbol=position['symbol'],
                risk_level=risk_level,
                risk_score=risk_metrics['risk_score'],
                risk_factors=risk_metrics['risk_factors'],
                recommendations=risk_metrics['recommendations'],
                confidence=risk_metrics['confidence'],
                timestamp=datetime.now(),
                validity_period=timedelta(minutes=30)
            )
            
            # Store assessment
            self.risk_assessments[assessment.assessment_id] = assessment
            self.metrics['risk_assessments_completed'] += 1
            
            # Generate alert if high risk
            if risk_level in [RiskLevel.HIGH, RiskLevel.VERY_HIGH, RiskLevel.CRITICAL]:
                await self._generate_risk_alert(assessment)
            
            return {
                'status': 'success',
                'assessment': assessment.__dict__,
                'risk_metrics': risk_metrics
            }
            
        except Exception as e:
            self.logger.error(f"Position risk assessment failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _calculate_position_risk_metrics(self, position: Dict[str, Any], 
                                             market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate comprehensive position risk metrics"""
        try:
            symbol = position['symbol']
            size = float(position['size'])
            entry_price = float(position['entry_price'])
            current_price = float(position['current_price'])
            
            # Calculate basic metrics
            unrealized_pnl = (current_price - entry_price) / entry_price
            position_value = size * current_price
            
            # Calculate volatility
            price_history = market_data.get('price_history', [])
            if len(price_history) > 20:
                returns = np.diff(price_history) / price_history[:-1]
                volatility = np.std(returns) * np.sqrt(365)  # Annualized
            else:
                volatility = 0.20  # Default 20% volatility
            
            # Calculate Value at Risk (1 day, 95% confidence)
            var_1d = position_value * volatility / np.sqrt(365) * 1.645
            
            # Calculate maximum loss potential
            stop_loss = position.get('stop_loss')
            if stop_loss:
                max_loss = abs(float(stop_loss) - entry_price) / entry_price
            else:
                max_loss = 0.50  # Assume 50% max loss without stop loss
            
            # Risk score calculation (0-1 scale)
            risk_factors = []
            risk_score = 0.0
            
            # Position size risk
            portfolio_value = await self._get_portfolio_value()
            position_percentage = position_value / portfolio_value if portfolio_value > 0 else 0
            
            if position_percentage > self.risk_limits['max_position_risk']:
                risk_score += 0.3
                risk_factors.append(f"Position size {position_percentage:.1%} exceeds limit")
            
            # Volatility risk
            if volatility > 0.50:  # 50% annual volatility
                risk_score += 0.2
                risk_factors.append(f"High volatility: {volatility:.1%}")
            
            # Unrealized loss risk
            if unrealized_pnl < -0.05:  # -5% unrealized loss
                risk_score += 0.2
                risk_factors.append(f"Unrealized loss: {unrealized_pnl:.1%}")
            
            # No stop loss risk
            if not stop_loss:
                risk_score += 0.2
                risk_factors.append("No stop loss set")
            
            # Liquidity risk
            volume = market_data.get('volume', 0)
            if volume < position_value * 10:  # Position > 10% of daily volume
                risk_score += 0.1
                risk_factors.append("Low liquidity")
            
            # Generate recommendations
            recommendations = []
            if position_percentage > self.risk_limits['max_position_risk']:
                recommendations.append("Reduce position size")
            if not stop_loss:
                recommendations.append("Set stop loss")
            if volatility > 0.50:
                recommendations.append("Consider volatility-adjusted position sizing")
            if unrealized_pnl < -0.10:
                recommendations.append("Consider closing position")
            
            # Confidence based on data quality
            confidence = 0.8 if len(price_history) > 50 else 0.6
            
            return {
                'risk_score': min(risk_score, 1.0),
                'risk_factors': risk_factors,
                'recommendations': recommendations,
                'confidence': confidence,
                'metrics': {
                    'position_value': position_value,
                    'position_percentage': position_percentage,
                    'unrealized_pnl': unrealized_pnl,
                    'volatility': volatility,
                    'var_1d': var_1d,
                    'max_loss_potential': max_loss
                }
            }
            
        except Exception as e:
            self.logger.error(f"Position risk calculation failed: {e}")
            return {
                'risk_score': 1.0,  # Assume high risk on error
                'risk_factors': ['Risk calculation error'],
                'recommendations': ['Manual review required'],
                'confidence': 0.0,
                'metrics': {}
            }
    
    async def _get_portfolio_value(self) -> float:
        """Get current portfolio value"""
        try:
            # Get shared portfolio data from orchestrator
            portfolio_data = self.orchestrator.get_shared_data('portfolio_value')
            if portfolio_data:
                return float(portfolio_data)
            
            # Fallback: calculate from positions
            positions_data = self.orchestrator.get_shared_data('current_positions')
            if positions_data:
                total_value = 0.0
                for position in positions_data:
                    total_value += float(position['size']) * float(position['current_price'])
                return total_value
            
            return 10000.0  # Default portfolio value
            
        except Exception as e:
            self.logger.error(f"Portfolio value calculation failed: {e}")
            return 10000.0
    
    def _determine_risk_level(self, risk_score: float) -> RiskLevel:
        """Determine risk level from risk score"""
        if risk_score < 0.2:
            return RiskLevel.VERY_LOW
        elif risk_score < 0.4:
            return RiskLevel.LOW
        elif risk_score < 0.6:
            return RiskLevel.MEDIUM
        elif risk_score < 0.8:
            return RiskLevel.HIGH
        elif risk_score < 0.95:
            return RiskLevel.VERY_HIGH
        else:
            return RiskLevel.CRITICAL
    
    async def _generate_risk_alert(self, assessment: RiskAssessment):
        """Generate risk alert from assessment"""
        try:
            alert = RiskAlert(
                alert_id=f"alert_{assessment.symbol}_{int(time.time())}",
                alert_type=AlertType.POSITION_RISK,
                risk_level=assessment.risk_level,
                message=f"High risk detected for {assessment.symbol}: {assessment.risk_score:.2f}",
                affected_positions=[assessment.symbol],
                recommended_actions=assessment.recommendations,
                timestamp=datetime.now(),
                auto_action_taken=False,
                resolved=False
            )
            
            # Store alert
            self.active_alerts[alert.alert_id] = alert
            self.metrics['alerts_generated'] += 1
            
            # Send alert to orchestrator
            await self.orchestrator.send_message(
                sender=self.agent_id,
                recipient='orchestrator',
                message_type='alert',
                data={
                    'type': alert.alert_type.value,
                    'severity': alert.risk_level.value,
                    'details': alert.message,
                    'recommendations': alert.recommended_actions,
                    'alert_id': alert.alert_id
                }
            )
            
            # Take automatic action if critical
            if assessment.risk_level == RiskLevel.CRITICAL:
                await self._take_automatic_action(alert)
            
            self.logger.warning(f"Risk alert generated: {alert.message}")
            
        except Exception as e:
            self.logger.error(f"Alert generation failed: {e}")
    
    async def _take_automatic_action(self, alert: RiskAlert):
        """Take automatic action for critical alerts"""
        try:
            if alert.risk_level == RiskLevel.CRITICAL:
                # Request emergency position closure
                await self.orchestrator.send_message(
                    sender=self.agent_id,
                    recipient='trading',
                    message_type='task_request',
                    data={
                        'task_type': 'close_position',
                        'target_agent': 'trading',
                        'priority': 1,  # Critical priority
                        'data': {
                            'symbol': alert.affected_positions[0],
                            'reason': 'emergency_risk_limit'
                        }
                    }
                )
                
                alert.auto_action_taken = True
                self.metrics['auto_actions_taken'] += 1
                
                self.logger.critical(f"Automatic emergency action taken for {alert.alert_id}")
            
        except Exception as e:
            self.logger.error(f"Automatic action failed: {e}")
    
    async def _assess_portfolio_risk_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Assess overall portfolio risk"""
        try:
            positions = data.get('positions', [])
            market_data = data.get('market_data', {})
            
            # Calculate portfolio risk metrics
            portfolio_risk = await self._calculate_portfolio_risk(positions, market_data)
            
            # Update stored portfolio risk
            self.portfolio_risk = portfolio_risk
            
            # Check portfolio risk limits
            risk_violations = await self._check_portfolio_risk_limits(portfolio_risk)
            
            return {
                'status': 'success',
                'portfolio_risk': portfolio_risk.__dict__,
                'risk_violations': risk_violations
            }
            
        except Exception as e:
            self.logger.error(f"Portfolio risk assessment failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _calculate_portfolio_risk(self, positions: List[Dict[str, Any]], 
                                      market_data: Dict[str, Any]) -> PortfolioRisk:
        """Calculate comprehensive portfolio risk metrics"""
        try:
            if not positions:
                return PortfolioRisk(
                    total_exposure=0.0, diversification_score=1.0, correlation_risk=0.0,
                    concentration_risk=0.0, liquidity_risk=0.0, drawdown_risk=0.0,
                    var_1d=0.0, var_7d=0.0, expected_shortfall=0.0, risk_adjusted_return=0.0
                )
            
            # Calculate total exposure
            total_exposure = sum(
                float(pos['size']) * float(pos['current_price']) for pos in positions
            )
            
            # Calculate concentration risk (Herfindahl Index)
            position_weights = []
            for pos in positions:
                weight = (float(pos['size']) * float(pos['current_price'])) / total_exposure
                position_weights.append(weight)
            
            concentration_risk = sum(w**2 for w in position_weights)
            diversification_score = 1 - concentration_risk
            
            # Calculate correlation risk
            correlation_risk = await self._calculate_correlation_risk(positions, market_data)
            
            # Calculate liquidity risk
            liquidity_risk = await self._calculate_liquidity_risk(positions, market_data)
            
            # Calculate drawdown risk
            drawdown_risk = await self._calculate_drawdown_risk(positions)
            
            # Calculate Value at Risk
            var_1d, var_7d = await self._calculate_portfolio_var(positions, market_data)
            
            # Calculate Expected Shortfall (CVaR)
            expected_shortfall = var_1d * 1.5  # Simplified calculation
            
            # Calculate risk-adjusted return (Sharpe ratio approximation)
            portfolio_return = await self._calculate_portfolio_return(positions)
            risk_adjusted_return = portfolio_return / (var_1d * np.sqrt(365)) if var_1d > 0 else 0.0
            
            return PortfolioRisk(
                total_exposure=total_exposure,
                diversification_score=diversification_score,
                correlation_risk=correlation_risk,
                concentration_risk=concentration_risk,
                liquidity_risk=liquidity_risk,
                drawdown_risk=drawdown_risk,
                var_1d=var_1d,
                var_7d=var_7d,
                expected_shortfall=expected_shortfall,
                risk_adjusted_return=risk_adjusted_return
            )
            
        except Exception as e:
            self.logger.error(f"Portfolio risk calculation failed: {e}")
            return PortfolioRisk(
                total_exposure=0.0, diversification_score=0.0, correlation_risk=1.0,
                concentration_risk=1.0, liquidity_risk=1.0, drawdown_risk=1.0,
                var_1d=0.0, var_7d=0.0, expected_shortfall=0.0, risk_adjusted_return=0.0
            )
    
    async def _calculate_correlation_risk(self, positions: List[Dict[str, Any]], 
                                        market_data: Dict[str, Any]) -> float:
        """Calculate portfolio correlation risk"""
        try:
            if len(positions) < 2:
                return 0.0
            
            # Get correlation matrix from research agent
            correlation_data = self.orchestrator.get_shared_data('correlation_matrix')
            if not correlation_data:
                return 0.5  # Assume moderate correlation
            
            # Calculate weighted average correlation
            symbols = [pos['symbol'] for pos in positions]
            weights = []
            
            total_value = sum(float(pos['size']) * float(pos['current_price']) for pos in positions)
            for pos in positions:
                weight = (float(pos['size']) * float(pos['current_price'])) / total_value
                weights.append(weight)
            
            # Calculate portfolio correlation
            total_correlation = 0.0
            total_weight = 0.0
            
            for i, symbol1 in enumerate(symbols):
                for j, symbol2 in enumerate(symbols):
                    if i != j and symbol1 in correlation_data and symbol2 in correlation_data[symbol1]:
                        correlation = correlation_data[symbol1][symbol2]
                        weight = weights[i] * weights[j]
                        total_correlation += abs(correlation) * weight
                        total_weight += weight
            
            return total_correlation / total_weight if total_weight > 0 else 0.0
            
        except Exception as e:
            self.logger.error(f"Correlation risk calculation failed: {e}")
            return 0.5
    
    async def _calculate_liquidity_risk(self, positions: List[Dict[str, Any]], 
                                      market_data: Dict[str, Any]) -> float:
        """Calculate portfolio liquidity risk"""
        try:
            liquidity_scores = []
            
            for pos in positions:
                symbol = pos['symbol']
                position_value = float(pos['size']) * float(pos['current_price'])
                
                # Get market data for symbol
                symbol_data = market_data.get(symbol, {})
                daily_volume = symbol_data.get('volume', 0)
                
                # Calculate liquidity score (position size vs daily volume)
                if daily_volume > 0:
                    liquidity_ratio = position_value / daily_volume
                    liquidity_score = max(0, 1 - liquidity_ratio * 10)  # Penalize large positions
                else:
                    liquidity_score = 0.0  # No volume data = illiquid
                
                liquidity_scores.append(liquidity_score)
            
            # Return average liquidity score (inverted for risk)
            avg_liquidity = np.mean(liquidity_scores) if liquidity_scores else 0.0
            return 1 - avg_liquidity  # Convert to risk measure
            
        except Exception as e:
            self.logger.error(f"Liquidity risk calculation failed: {e}")
            return 1.0  # Assume high liquidity risk on error
    
    async def _calculate_drawdown_risk(self, positions: List[Dict[str, Any]]) -> float:
        """Calculate portfolio drawdown risk"""
        try:
            # Get portfolio performance history
            performance_history = self.orchestrator.get_shared_data('portfolio_performance')
            if not performance_history:
                return 0.0
            
            # Calculate current drawdown
            peak_value = max(performance_history)
            current_value = performance_history[-1]
            current_drawdown = (peak_value - current_value) / peak_value
            
            # Calculate maximum historical drawdown
            max_drawdown = 0.0
            running_max = performance_history[0]
            
            for value in performance_history:
                running_max = max(running_max, value)
                drawdown = (running_max - value) / running_max
                max_drawdown = max(max_drawdown, drawdown)
            
            # Return current drawdown as risk measure
            return current_drawdown
            
        except Exception as e:
            self.logger.error(f"Drawdown risk calculation failed: {e}")
            return 0.0
    
    async def _calculate_portfolio_var(self, positions: List[Dict[str, Any]], 
                                     market_data: Dict[str, Any]) -> Tuple[float, float]:
        """Calculate portfolio Value at Risk"""
        try:
            # Simplified VaR calculation using historical simulation
            portfolio_returns = []
            
            # Get historical returns for each position
            for pos in positions:
                symbol = pos['symbol']
                symbol_data = market_data.get(symbol, {})
                price_history = symbol_data.get('price_history', [])
                
                if len(price_history) > 1:
                    returns = np.diff(price_history) / price_history[:-1]
                    weight = (float(pos['size']) * float(pos['current_price'])) / await self._get_portfolio_value()
                    weighted_returns = returns * weight
                    
                    if len(portfolio_returns) == 0:
                        portfolio_returns = weighted_returns
                    else:
                        # Align lengths and add
                        min_len = min(len(portfolio_returns), len(weighted_returns))
                        portfolio_returns = portfolio_returns[-min_len:] + weighted_returns[-min_len:]
            
            if len(portfolio_returns) == 0:
                return 0.0, 0.0
            
            # Calculate VaR at 95% confidence
            var_1d = np.percentile(portfolio_returns, 5) * await self._get_portfolio_value()
            var_7d = var_1d * np.sqrt(7)
            
            return abs(var_1d), abs(var_7d)
            
        except Exception as e:
            self.logger.error(f"VaR calculation failed: {e}")
            return 0.0, 0.0
    
    async def _calculate_portfolio_return(self, positions: List[Dict[str, Any]]) -> float:
        """Calculate portfolio return"""
        try:
            total_pnl = 0.0
            total_investment = 0.0
            
            for pos in positions:
                entry_price = float(pos['entry_price'])
                current_price = float(pos['current_price'])
                size = float(pos['size'])
                
                pnl = (current_price - entry_price) * size
                investment = entry_price * size
                
                total_pnl += pnl
                total_investment += investment
            
            return total_pnl / total_investment if total_investment > 0 else 0.0
            
        except Exception as e:
            self.logger.error(f"Portfolio return calculation failed: {e}")
            return 0.0
    
    async def _check_portfolio_risk_limits(self, portfolio_risk: PortfolioRisk) -> List[str]:
        """Check portfolio risk against limits"""
        violations = []
        
        try:
            # Check diversification
            if portfolio_risk.diversification_score < 0.5:
                violations.append("Poor diversification")
            
            # Check concentration
            if portfolio_risk.concentration_risk > 0.4:
                violations.append("High concentration risk")
            
            # Check correlation
            if portfolio_risk.correlation_risk > self.risk_limits['max_correlation']:
                violations.append("High correlation risk")
            
            # Check liquidity
            if portfolio_risk.liquidity_risk > (1 - self.risk_limits['min_liquidity']):
                violations.append("High liquidity risk")
            
            # Check drawdown
            if portfolio_risk.drawdown_risk > self.risk_limits['max_drawdown']:
                violations.append("Maximum drawdown exceeded")
            
            # Check VaR
            portfolio_value = await self._get_portfolio_value()
            var_percentage = portfolio_risk.var_1d / portfolio_value
            if var_percentage > self.risk_limits['var_limit']:
                violations.append("VaR limit exceeded")
            
            return violations
            
        except Exception as e:
            self.logger.error(f"Risk limit check failed: {e}")
            return ["Risk check error"]
    
    async def _check_risk_limits_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Check all risk limits"""
        try:
            # Get current positions
            positions = data.get('positions', [])
            
            violations = []
            
            # Check individual position limits
            for pos in positions:
                position_value = float(pos['size']) * float(pos['current_price'])
                portfolio_value = await self._get_portfolio_value()
                position_percentage = position_value / portfolio_value
                
                if position_percentage > self.risk_limits['max_position_risk']:
                    violations.append(f"Position {pos['symbol']} exceeds size limit")
            
            # Check portfolio limits
            if self.portfolio_risk:
                portfolio_violations = await self._check_portfolio_risk_limits(self.portfolio_risk)
                violations.extend(portfolio_violations)
            
            return {
                'status': 'success',
                'violations': violations,
                'risk_limits': self.risk_limits
            }
            
        except Exception as e:
            self.logger.error(f"Risk limits check failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _calculate_var_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate Value at Risk"""
        try:
            positions = data.get('positions', [])
            market_data = data.get('market_data', {})
            confidence_level = data.get('confidence_level', 0.95)
            time_horizon = data.get('time_horizon', 1)  # days
            
            var_1d, var_7d = await self._calculate_portfolio_var(positions, market_data)
            
            # Scale VaR for requested time horizon
            var_scaled = var_1d * np.sqrt(time_horizon)
            
            return {
                'status': 'success',
                'var_1d': var_1d,
                'var_7d': var_7d,
                'var_scaled': var_scaled,
                'confidence_level': confidence_level,
                'time_horizon': time_horizon
            }
            
        except Exception as e:
            self.logger.error(f"VaR calculation task failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _stress_test_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform stress testing on portfolio"""
        try:
            positions = data.get('positions', [])
            scenarios = data.get('scenarios', [])
            
            # Default stress scenarios if none provided
            if not scenarios:
                scenarios = [
                    {'name': 'Market Crash -20%', 'change': -0.20},
                    {'name': 'Market Surge +20%', 'change': 0.20},
                    {'name': 'High Volatility', 'volatility_multiplier': 2.0},
                    {'name': 'Correlation Spike', 'correlation_increase': 0.30}
                ]
            
            stress_results = []
            
            for scenario in scenarios:
                result = await self._run_stress_scenario(positions, scenario)
                stress_results.append(result)
            
            return {
                'status': 'success',
                'stress_results': stress_results
            }
            
        except Exception as e:
            self.logger.error(f"Stress test failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _run_stress_scenario(self, positions: List[Dict[str, Any]], 
                                 scenario: Dict[str, Any]) -> Dict[str, Any]:
        """Run a single stress test scenario"""
        try:
            scenario_name = scenario['name']
            
            # Calculate portfolio value under scenario
            total_pnl_change = 0.0
            
            for pos in positions:
                position_value = float(pos['size']) * float(pos['current_price'])
                
                # Apply scenario shock
                if 'change' in scenario:
                    # Market move scenario
                    pnl_change = position_value * scenario['change']
                    total_pnl_change += pnl_change
                
                elif 'volatility_multiplier' in scenario:
                    # Volatility shock - estimate impact
                    # Higher volatility typically leads to wider spreads and potential losses
                    vol_impact = position_value * 0.01 * scenario['volatility_multiplier']
                    total_pnl_change -= vol_impact  # Negative impact
            
            portfolio_value = await self._get_portfolio_value()
            percentage_impact = total_pnl_change / portfolio_value
            
            return {
                'scenario': scenario_name,
                'pnl_impact': total_pnl_change,
                'percentage_impact': percentage_impact,
                'portfolio_value_after': portfolio_value + total_pnl_change
            }
            
        except Exception as e:
            self.logger.error(f"Stress scenario execution failed: {e}")
            return {
                'scenario': scenario.get('name', 'Unknown'),
                'error': str(e)
            }
    
    async def _emergency_stop_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute emergency stop procedures"""
        try:
            reason = data.get('reason', 'Manual emergency stop')
            
            # Enter emergency mode
            self.emergency_mode = True
            self.metrics['emergency_stops'] += 1
            
            # Send emergency stop to trading agent
            await self.orchestrator.send_message(
                sender=self.agent_id,
                recipient='trading',
                message_type='alert',
                data={
                    'type': 'emergency_stop',
                    'severity': 'critical',
                    'details': f'Emergency stop triggered: {reason}',
                    'action_required': 'close_all_positions'
                }
            )
            
            # Generate emergency alert
            emergency_alert = RiskAlert(
                alert_id=f"emergency_{int(time.time())}",
                alert_type=AlertType.TECHNICAL_RISK,
                risk_level=RiskLevel.CRITICAL,
                message=f"Emergency stop activated: {reason}",
                affected_positions=[],
                recommended_actions=['Close all positions', 'Stop trading'],
                timestamp=datetime.now(),
                auto_action_taken=True
            )
            
            self.active_alerts[emergency_alert.alert_id] = emergency_alert
            
            self.logger.critical(f"Emergency stop executed: {reason}")
            
            return {
                'status': 'success',
                'emergency_mode': True,
                'reason': reason,
                'alert_id': emergency_alert.alert_id
            }
            
        except Exception as e:
            self.logger.error(f"Emergency stop failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _get_risk_report_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive risk report"""
        try:
            report_type = data.get('type', 'summary')
            
            report = {
                'timestamp': datetime.now().isoformat(),
                'portfolio_risk': self.portfolio_risk.__dict__ if self.portfolio_risk else None,
                'active_alerts': [alert.__dict__ for alert in self.active_alerts.values()],
                'risk_assessments': len(self.risk_assessments),
                'risk_limits': self.risk_limits,
                'emergency_mode': self.emergency_mode,
                'metrics': self.metrics
            }
            
            if report_type == 'detailed':
                report['assessment_history'] = [
                    assessment.__dict__ for assessment in self.risk_assessments.values()
                ]
                report['risk_history'] = self.risk_history[-100:]  # Last 100 entries
            
            return {
                'status': 'success',
                'report': report
            }
            
        except Exception as e:
            self.logger.error(f"Risk report generation failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _update_risk_limits_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Update risk limits"""
        try:
            new_limits = data.get('limits', {})
            
            # Validate new limits
            for key, value in new_limits.items():
                if key in self.risk_limits and isinstance(value, (int, float)) and 0 <= value <= 1:
                    self.risk_limits[key] = value
                else:
                    self.logger.warning(f"Invalid risk limit: {key} = {value}")
            
            self.logger.info(f"Risk limits updated: {new_limits}")
            
            return {
                'status': 'success',
                'updated_limits': self.risk_limits
            }
            
        except Exception as e:
            self.logger.error(f"Risk limits update failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _correlation_analysis_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform correlation analysis"""
        try:
            positions = data.get('positions', [])
            
            if len(positions) < 2:
                return {
                    'status': 'success',
                    'correlation_risk': 0.0,
                    'message': 'Insufficient positions for correlation analysis'
                }
            
            # Get correlation data from research agent
            symbols = [pos['symbol'] for pos in positions]
            
            # Request correlation analysis from research agent
            await self.orchestrator.send_message(
                sender=self.agent_id,
                recipient='research',
                message_type='task_request',
                data={
                    'task_type': 'correlation_analysis',
                    'data': {'symbols': symbols}
                }
            )
            
            # Calculate current correlation risk
            correlation_risk = await self._calculate_correlation_risk(positions, {})
            
            return {
                'status': 'success',
                'correlation_risk': correlation_risk,
                'symbols_analyzed': symbols
            }
            
        except Exception as e:
            self.logger.error(f"Correlation analysis failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _liquidity_assessment_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Assess liquidity risk"""
        try:
            positions = data.get('positions', [])
            market_data = data.get('market_data', {})
            
            liquidity_risk = await self._calculate_liquidity_risk(positions, market_data)
            
            # Generate liquidity assessment for each position
            position_assessments = []
            
            for pos in positions:
                symbol = pos['symbol']
                position_value = float(pos['size']) * float(pos['current_price'])
                
                symbol_data = market_data.get(symbol, {})
                daily_volume = symbol_data.get('volume', 0)
                
                liquidity_ratio = position_value / daily_volume if daily_volume > 0 else float('inf')
                
                assessment = {
                    'symbol': symbol,
                    'position_value': position_value,
                    'daily_volume': daily_volume,
                    'liquidity_ratio': liquidity_ratio,
                    'risk_level': 'high' if liquidity_ratio > 0.1 else 'low'
                }
                
                position_assessments.append(assessment)
            
            return {
                'status': 'success',
                'portfolio_liquidity_risk': liquidity_risk,
                'position_assessments': position_assessments
            }
            
        except Exception as e:
            self.logger.error(f"Liquidity assessment failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _drawdown_analysis_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze drawdown risk"""
        try:
            positions = data.get('positions', [])
            
            drawdown_risk = await self._calculate_drawdown_risk(positions)
            
            # Get performance history
            performance_history = self.orchestrator.get_shared_data('portfolio_performance')
            
            if performance_history:
                # Calculate detailed drawdown metrics
                peak = max(performance_history)
                current = performance_history[-1]
                current_drawdown = (peak - current) / peak
                
                # Calculate maximum drawdown
                max_dd = 0.0
                running_max = performance_history[0]
                
                for value in performance_history:
                    running_max = max(running_max, value)
                    dd = (running_max - value) / running_max
                    max_dd = max(max_dd, dd)
                
                # Calculate recovery time (if currently in drawdown)
                recovery_time = 0
                if current_drawdown > 0.01:  # 1% threshold
                    for i in range(len(performance_history) - 1, -1, -1):
                        if performance_history[i] >= peak * 0.99:  # Within 1% of peak
                            recovery_time = len(performance_history) - 1 - i
                            break
                
                return {
                    'status': 'success',
                    'current_drawdown': current_drawdown,
                    'maximum_drawdown': max_dd,
                    'recovery_time_periods': recovery_time,
                    'drawdown_risk_score': drawdown_risk
                }
            else:
                return {
                    'status': 'success',
                    'drawdown_risk_score': drawdown_risk,
                    'message': 'No performance history available'
                }
            
        except Exception as e:
            self.logger.error(f"Drawdown analysis failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _risk_monitoring_loop(self):
        """Main risk monitoring loop"""
        while self.is_running:
            try:
                # Get current positions from orchestrator
                positions_data = self.orchestrator.get_shared_data('current_positions')
                
                if positions_data:
                    # Monitor each position
                    for position in positions_data:
                        await self._monitor_position_risk(position)
                    
                    # Monitor portfolio risk
                    await self._monitor_portfolio_risk(positions_data)
                
                await asyncio.sleep(self.risk_monitoring_interval)
                
            except Exception as e:
                self.logger.error(f"Error in risk monitoring loop: {e}")
                await asyncio.sleep(30)
    
    async def _monitor_position_risk(self, position: Dict[str, Any]):
        """Monitor individual position risk"""
        try:
            # Get market data for position
            symbol = position['symbol']
            market_data = self.orchestrator.get_shared_data(f'market_data_{symbol}')
            
            # Calculate risk metrics
            risk_metrics = await self._calculate_position_risk_metrics(position, market_data or {})
            
            # Check if risk level has changed
            risk_level = self._determine_risk_level(risk_metrics['risk_score'])
            
            # Generate alert if risk is high
            if risk_level in [RiskLevel.HIGH, RiskLevel.VERY_HIGH, RiskLevel.CRITICAL]:
                # Check if we already have an active alert for this position
                existing_alert = any(
                    alert.alert_type == AlertType.POSITION_RISK and 
                    symbol in alert.affected_positions and 
                    not alert.resolved
                    for alert in self.active_alerts.values()
                )
                
                if not existing_alert:
                    assessment = RiskAssessment(
                        assessment_id=f"monitor_{symbol}_{int(time.time())}",
                        symbol=symbol,
                        risk_level=risk_level,
                        risk_score=risk_metrics['risk_score'],
                        risk_factors=risk_metrics['risk_factors'],
                        recommendations=risk_metrics['recommendations'],
                        confidence=risk_metrics['confidence'],
                        timestamp=datetime.now(),
                        validity_period=timedelta(minutes=30)
                    )
                    
                    await self._generate_risk_alert(assessment)
            
        except Exception as e:
            self.logger.error(f"Position risk monitoring failed for {position.get('symbol', 'unknown')}: {e}")
    
    async def _monitor_portfolio_risk(self, positions: List[Dict[str, Any]]):
        """Monitor overall portfolio risk"""
        try:
            # Calculate portfolio risk
            market_data = {}  # Collect market data for all positions
            
            portfolio_risk = await self._calculate_portfolio_risk(positions, market_data)
            self.portfolio_risk = portfolio_risk
            
            # Check portfolio risk limits
            violations = await self._check_portfolio_risk_limits(portfolio_risk)
            
            if violations:
                # Generate portfolio risk alert
                alert = RiskAlert(
                    alert_id=f"portfolio_risk_{int(time.time())}",
                    alert_type=AlertType.PORTFOLIO_RISK,
                    risk_level=RiskLevel.HIGH,
                    message=f"Portfolio risk violations: {', '.join(violations)}",
                    affected_positions=[pos['symbol'] for pos in positions],
                    recommended_actions=["Review position sizes", "Reduce exposure", "Improve diversification"],
                    timestamp=datetime.now()
                )
                
                # Check if we already have an active portfolio alert
                existing_alert = any(
                    alert.alert_type == AlertType.PORTFOLIO_RISK and not alert.resolved
                    for alert in self.active_alerts.values()
                )
                
                if not existing_alert:
                    self.active_alerts[alert.alert_id] = alert
                    self.metrics['alerts_generated'] += 1
                    
                    # Send alert to orchestrator
                    await self.orchestrator.send_message(
                        sender=self.agent_id,
                        recipient='orchestrator',
                        message_type='alert',
                        data={
                            'type': alert.alert_type.value,
                            'severity': alert.risk_level.value,
                            'details': alert.message,
                            'violations': violations
                        }
                    )
            
        except Exception as e:
            self.logger.error(f"Portfolio risk monitoring failed: {e}")
    
    async def _portfolio_risk_loop(self):
        """Portfolio risk calculation loop"""
        while self.is_running:
            try:
                # Update risk metrics average
                if self.risk_assessments:
                    total_score = sum(assessment.risk_score for assessment in self.risk_assessments.values())
                    self.metrics['risk_score_average'] = total_score / len(self.risk_assessments)
                
                # Store risk history
                if self.portfolio_risk:
                    history_entry = {
                        'timestamp': datetime.now().isoformat(),
                        'total_exposure': self.portfolio_risk.total_exposure,
                        'diversification_score': self.portfolio_risk.diversification_score,
                        'var_1d': self.portfolio_risk.var_1d,
                        'drawdown_risk': self.portfolio_risk.drawdown_risk
                    }
                    
                    self.risk_history.append(history_entry)
                    
                    # Keep only last 1000 entries
                    if len(self.risk_history) > 1000:
                        self.risk_history = self.risk_history[-1000:]
                
                await asyncio.sleep(300)  # Update every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Error in portfolio risk loop: {e}")
                await asyncio.sleep(60)
    
    async def _alert_management_loop(self):
        """Alert management and cleanup loop"""
        while self.is_running:
            try:
                current_time = datetime.now()
                resolved_alerts = []
                
                # Check alert resolution and cleanup
                for alert_id, alert in self.active_alerts.items():
                    # Auto-resolve old alerts
                    if current_time - alert.timestamp > timedelta(hours=2):
                        alert.resolved = True
                        resolved_alerts.append(alert_id)
                
                # Remove resolved alerts
                for alert_id in resolved_alerts:
                    del self.active_alerts[alert_id]
                
                await asyncio.sleep(600)  # Check every 10 minutes
                
            except Exception as e:
                self.logger.error(f"Error in alert management loop: {e}")
                await asyncio.sleep(60)
    
    async def _emergency_monitoring_loop(self):
        """Emergency situation monitoring loop"""
        while self.is_running:
            try:
                # Check for emergency conditions
                if self.portfolio_risk:
                    # Emergency conditions
                    emergency_conditions = [
                        self.portfolio_risk.drawdown_risk > 0.15,  # 15% drawdown
                        self.portfolio_risk.var_1d > await self._get_portfolio_value() * 0.05,  # 5% VaR
                        len([alert for alert in self.active_alerts.values() 
                            if alert.risk_level == RiskLevel.CRITICAL]) > 2  # Multiple critical alerts
                    ]
                    
                    if any(emergency_conditions) and not self.emergency_mode:
                        await self._trigger_emergency_protocol()
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Error in emergency monitoring loop: {e}")
                await asyncio.sleep(60)
    
    async def _trigger_emergency_protocol(self):
        """Trigger emergency protocol"""
        try:
            self.emergency_mode = True
            self.metrics['emergency_stops'] += 1
            
            # Send emergency alert to orchestrator
            await self.orchestrator.send_message(
                sender=self.agent_id,
                recipient='orchestrator',
                message_type='alert',
                data={
                    'type': 'system_failure',
                    'severity': 'critical',
                    'details': 'Emergency risk conditions detected - activating emergency protocol',
                    'action_required': 'immediate_attention'
                }
            )
            
            self.logger.critical("Emergency protocol activated due to extreme risk conditions")
            
        except Exception as e:
            self.logger.error(f"Emergency protocol activation failed: {e}")
    
    def get_capabilities(self) -> List[str]:
        """Get agent capabilities"""
        return [
            'assess_position_risk',
            'assess_portfolio_risk',
            'check_risk_limits',
            'calculate_var',
            'stress_test',
            'emergency_stop',
            'get_risk_report',
            'update_risk_limits',
            'correlation_analysis',
            'liquidity_assessment',
            'drawdown_analysis'
        ]
    
    async def get_status(self) -> Dict[str, Any]:
        """Get agent status"""
        return {
            'agent_id': self.agent_id,
            'status': 'active' if self.is_running else 'inactive',
            'emergency_mode': self.emergency_mode,
            'active_alerts': len(self.active_alerts),
            'risk_assessments': len(self.risk_assessments),
            'portfolio_risk_level': self.portfolio_risk.drawdown_risk if self.portfolio_risk else 0.0
        }
    
    async def get_performance_metrics(self) -> Dict[str, float]:
        """Get performance metrics"""
        # Calculate alert accuracy if we have historical data
        if self.metrics['alerts_generated'] > 0:
            # This would require tracking alert outcomes
            # For now, use a placeholder
            self.metrics['alert_accuracy'] = 0.85
        
        return self.metrics.copy()
    
    async def shutdown(self):
        """Shutdown the risk agent"""
        self.logger.info(f"Shutting down Risk Agent {self.agent_id}")
        
        self.is_running = False
        self.emergency_mode = False
        
        # Close all active alerts
        for alert in self.active_alerts.values():
            alert.resolved = True
        
        # Cleanup
        if self.risk_manager:
            await self.risk_manager.shutdown()
        
        self.logger.info(f"Risk Agent {self.agent_id} shutdown complete")
