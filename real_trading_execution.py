#!/usr/bin/env python3
"""
REAL TRADING EXECUTION - NO FAKE DATA
This script will execute ACTUAL trades on your Bybit account
"""

import asyncio
import time
import sqlite3
import redis
from datetime import datetime
from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient

class RealTradingExecutor:
    def __init__(self):
        self.client = None
        self.db_conn = None
        self.redis_conn = None
        self.initial_balance = 0
        self.target_profit = 15000  # $15,000 daily target
        
    async def initialize(self):
        """Initialize real trading components"""
        print("INITIALIZING REAL TRADING EXECUTOR")
        print("=" * 50)
        
        # Initialize Bybit client
        self.client = EnhancedBybitClient()
        await self.client.initialize()
        print("SUCCESS: Bybit API client initialized")
        
        # Initialize database
        self.db_conn = sqlite3.connect('bybit_trading_bot.db')
        print("SUCCESS: Database connection established")
        
        # Initialize Redis
        self.redis_conn = redis.Redis(host='localhost', port=6379, decode_responses=True)
        print("SUCCESS: Redis connection established")
        
        # Get initial account balance
        balance_info = await self.client.get_account_balance()
        if balance_info and 'list' in balance_info and len(balance_info['list']) > 0:
            self.initial_balance = float(balance_info['list'][0]['totalEquity'])
            available_balance = float(balance_info['list'][0]['totalAvailableBalance'])
            print(f"SUCCESS: Account balance retrieved")
            print(f"  Total Equity: ${self.initial_balance:.2f}")
            print(f"  Available Balance: ${available_balance:.2f}")
        else:
            raise Exception("Failed to get account balance")
        
        print("REAL TRADING EXECUTOR READY")
        print()
    
    async def execute_real_scalping_trade(self, symbol="BTCUSDT", trade_size=0.001):
        """Execute a real scalping trade"""
        try:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] EXECUTING REAL TRADE: {symbol}")
            
            # Get current market price
            ticker = await self.client.get_market_data(symbol)
            if not ticker or 'result' not in ticker:
                print(f"ERROR: Could not get market data for {symbol}")
                return False
            
            current_price = float(ticker['result']['list'][0]['lastPrice'])
            print(f"  Current Price: ${current_price:.2f}")
            
            # Calculate entry and exit prices for scalping
            entry_price = current_price * 0.9999  # Slightly below market for quick fill
            exit_price = current_price * 1.0005   # 0.05% profit target
            
            print(f"  Entry Price: ${entry_price:.2f}")
            print(f"  Exit Price: ${exit_price:.2f}")
            print(f"  Trade Size: {trade_size} BTC")
            
            # Place BUY order
            buy_order = await self.client.place_order(
                symbol=symbol,
                side="Buy",
                order_type="Limit",
                qty=str(trade_size),
                price=str(entry_price),
                time_in_force="GTC"
            )
            
            if buy_order and buy_order.get('retCode') == 0:
                order_id = buy_order['result']['orderId']
                print(f"  SUCCESS: BUY order placed - Order ID: {order_id}")
                
                # Record trade in database
                cursor = self.db_conn.cursor()
                cursor.execute('''
                    INSERT INTO trades (timestamp, symbol, side, quantity, price, strategy, order_id, status)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (datetime.now(), symbol, "Buy", trade_size, entry_price, "Real_Scalping", order_id, "placed"))
                self.db_conn.commit()
                
                # Store in Redis
                self.redis_conn.set(f"trade:{order_id}", f"{symbol}:Buy:{trade_size}:{entry_price}")
                self.redis_conn.incr("trades:count")
                
                # Wait for fill and place sell order
                await asyncio.sleep(5)  # Wait 5 seconds
                
                # Check if buy order filled
                order_status = await self.client.get_order_status(symbol, order_id)
                if order_status and order_status.get('result', {}).get('list', []):
                    order_info = order_status['result']['list'][0]
                    if order_info.get('orderStatus') == 'Filled':
                        print(f"  SUCCESS: BUY order FILLED")
                        
                        # Place SELL order
                        sell_order = await self.client.place_order(
                            symbol=symbol,
                            side="Sell",
                            order_type="Limit",
                            qty=str(trade_size),
                            price=str(exit_price),
                            time_in_force="GTC"
                        )
                        
                        if sell_order and sell_order.get('retCode') == 0:
                            sell_order_id = sell_order['result']['orderId']
                            print(f"  SUCCESS: SELL order placed - Order ID: {sell_order_id}")
                            
                            # Record sell trade
                            cursor.execute('''
                                INSERT INTO trades (timestamp, symbol, side, quantity, price, strategy, order_id, status)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (datetime.now(), symbol, "Sell", trade_size, exit_price, "Real_Scalping", sell_order_id, "placed"))
                            self.db_conn.commit()
                            
                            return True
                        else:
                            print(f"  ERROR: Failed to place SELL order: {sell_order}")
                    else:
                        print(f"  INFO: BUY order status: {order_info.get('orderStatus')}")
                
                return True
            else:
                print(f"  ERROR: Failed to place BUY order: {buy_order}")
                return False
                
        except Exception as e:
            print(f"  ERROR: Trade execution failed: {e}")
            return False
    
    async def run_real_trading_loop(self):
        """Run the real trading loop"""
        print("STARTING REAL TRADING LOOP")
        print("TARGET: $15,000 DAILY PROFIT")
        print("STRATEGY: High-frequency scalping")
        print()
        
        trade_count = 0
        start_time = time.time()
        
        while True:
            try:
                # Execute real scalping trade
                success = await self.execute_real_scalping_trade()
                
                if success:
                    trade_count += 1
                    
                    # Check current balance
                    current_balance_info = await self.client.get_account_balance()
                    if current_balance_info and 'list' in current_balance_info:
                        current_balance = float(current_balance_info['list'][0]['totalEquity'])
                        profit = current_balance - self.initial_balance
                        
                        print(f"  PROFIT UPDATE: ${profit:+.2f} (Target: ${self.target_profit:.2f})")
                        
                        # Store profit in Redis
                        self.redis_conn.set("profit:daily", f"{profit:.2f}")
                        self.redis_conn.set("profit:target", f"{self.target_profit:.2f}")
                        
                        # Record AI memory
                        cursor = self.db_conn.cursor()
                        cursor.execute('''
                            INSERT INTO ai_memories (created_at, memory_type, content, ai_component, importance)
                            VALUES (?, ?, ?, ?, ?)
                        ''', (datetime.now(), "profit_tracking", f"Trade #{trade_count}: Profit ${profit:+.2f}", "real_trading_executor", 1.0))
                        self.db_conn.commit()
                        
                        if profit >= self.target_profit:
                            print(f"*** TARGET ACHIEVED! Daily profit: ${profit:.2f} ***")
                            break
                
                # Wait before next trade (high frequency)
                await asyncio.sleep(10)  # 10 second intervals for high frequency
                
                # Status update every 10 trades
                if trade_count % 10 == 0:
                    elapsed_time = time.time() - start_time
                    print(f"STATUS: {trade_count} trades executed in {elapsed_time/60:.1f} minutes")
                
            except Exception as e:
                print(f"ERROR in trading loop: {e}")
                await asyncio.sleep(30)  # Wait 30 seconds on error
    
    async def run(self):
        """Main execution method"""
        try:
            await self.initialize()
            await self.run_real_trading_loop()
        except Exception as e:
            print(f"CRITICAL ERROR: {e}")
        finally:
            if self.db_conn:
                self.db_conn.close()

async def main():
    """Main function"""
    print("REAL TRADING EXECUTION STARTING")
    print("NO FAKE DATA - ONLY REAL TRADES")
    print("=" * 50)
    
    executor = RealTradingExecutor()
    await executor.run()

if __name__ == "__main__":
    asyncio.run(main())
