# REAL TRADING SYSTEM STARTUP - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>L VERSION
Write-Host "STARTING BYBIT TRADING SYSTEM WITH REAL TRADING" -ForegroundColor Green

# Set working directory
Set-Location "E:\The_real_deal_copy\Bybit_Bot\BOT"
Write-Host "Working directory: $(Get-Location)" -ForegroundColor Yellow

# Set Python path
$env:PYTHONPATH = "E:\The_real_deal_copy\Bybit_Bot\BOT"

# Test Python installation
Write-Host "Testing Python installation..." -ForegroundColor Yellow
& "E:\conda\Miniconda3\python.exe" --version

# Start the real trading system
Write-Host "STARTING REAL TRADING SYSTEM - NO FAKE DATA!" -ForegroundColor Red
& "E:\conda\Miniconda3\python.exe" main.py

Write-Host "Trading system stopped." -ForegroundColor Yellow
Read-Host "Press Enter to exit"
