"""
Profit Maximization Risk Configuration
Optimized risk parameters for maximum profit generation while maintaining account safety
"""

from dataclasses import dataclass
from typing import Dict, Any, Optional
import logging

@dataclass
class ProfitMaximizationRiskConfig:
    """
    Risk configuration optimized for profit maximization
    Based on $15,000/day target with aggressive but controlled risk management
    """
    
    # POSITION SIZING - AGGRESSIVE FOR MAXIMUM PROFIT
    max_position_size_percentage: float = 15.0  # 15% of account per position
    min_position_size_usd: float = 1.0          # Minimum $1 order
    max_position_size_usd: float = 10000.0      # Maximum $10k order
    confidence_scaling_enabled: bool = True      # Scale position by confidence
    confidence_min_multiplier: float = 0.6      # Minimum 60% of calculated size
    confidence_max_multiplier: float = 1.0      # Maximum 100% of calculated size
    
    # STOP LOSS - TIGHT CONTROL FOR PROFIT PROTECTION
    stop_loss_percentage: float = 3.0           # 3% stop loss (tight control)
    dynamic_stop_loss_enabled: bool = True      # Adjust stops based on volatility
    trailing_stop_enabled: bool = True          # Trail stops to lock in profits
    trailing_stop_distance: float = 1.5         # 1.5% trailing distance
    
    # TAKE PROFIT - OPTIMIZED RISK/REWARD
    take_profit_percentage: float = 6.0         # 6% take profit (2:1 ratio)
    partial_profit_taking_enabled: bool = True  # Take partial profits
    partial_profit_levels: list = None          # [3%, 4.5%, 6%] profit levels
    partial_profit_percentages: list = None     # [30%, 40%, 30%] of position
    
    # LEVERAGE - AGGRESSIVE FOR MAXIMUM PROFIT
    max_leverage: int = 20                      # 20x leverage for profit maximization
    leverage_scaling_by_volatility: bool = True # Reduce leverage in high volatility
    leverage_scaling_by_confidence: bool = True # Increase leverage with high confidence
    
    # DAILY LIMITS - CONTROLLED AGGRESSION
    max_daily_loss_percentage: float = 8.0     # 8% maximum daily loss
    max_daily_trades: int = 1000               # Up to 1000 trades per day
    daily_profit_target: float = 15000.0       # $15,000 daily target
    hourly_profit_target: float = 625.0        # $625 hourly target (24/7)
    
    # EXPOSURE LIMITS - MAXIMUM UTILIZATION
    max_total_exposure_percentage: float = 80.0 # 80% total account exposure
    max_correlated_exposure_percentage: float = 30.0 # 30% in correlated positions
    max_single_symbol_exposure_percentage: float = 25.0 # 25% in single symbol
    
    # MARGIN MANAGEMENT - SAFETY WITH AGGRESSION
    margin_warning_ratio: float = 80.0         # Warning at 80% margin ratio
    margin_emergency_ratio: float = 95.0       # Emergency close at 95%
    margin_target_ratio: float = 30.0          # Target 30% margin utilization
    liquidation_buffer_percentage: float = 5.0 # 5% buffer from liquidation
    
    # EXECUTION SPEED - ULTRA HIGH FREQUENCY
    max_execution_time_ms: float = 100.0       # 100ms maximum execution time
    order_timeout_seconds: int = 30            # 30 second order timeout
    price_slippage_tolerance: float = 0.1      # 0.1% slippage tolerance
    
    # RISK MONITORING - REAL-TIME CONTROL
    risk_check_interval_seconds: int = 5       # Check risk every 5 seconds
    position_monitoring_interval_seconds: int = 1 # Monitor positions every second
    margin_monitoring_interval_seconds: int = 5   # Monitor margin every 5 seconds
    
    # PROFIT OPTIMIZATION - ADVANCED STRATEGIES
    scalping_enabled: bool = True              # Enable scalping strategies
    arbitrage_enabled: bool = True             # Enable arbitrage detection
    momentum_trading_enabled: bool = True      # Enable momentum strategies
    pattern_recognition_enabled: bool = True   # Enable pattern trading
    
    # AI/ML RISK ASSESSMENT - INTELLIGENT DECISIONS
    ai_risk_assessment_enabled: bool = True    # Use AI for risk assessment
    confidence_threshold_for_trading: float = 0.6 # Minimum 60% confidence
    volatility_adjustment_enabled: bool = True # Adjust for market volatility
    correlation_analysis_enabled: bool = True  # Analyze asset correlations
    
    def __post_init__(self):
        """Initialize derived values"""
        if self.partial_profit_levels is None:
            self.partial_profit_levels = [3.0, 4.5, 6.0]
        if self.partial_profit_percentages is None:
            self.partial_profit_percentages = [30.0, 40.0, 30.0]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API usage"""
        return {
            'max_position_size': self.max_position_size_percentage / 100,
            'max_daily_loss': self.max_daily_loss_percentage / 100,
            'stop_loss_percentage': self.stop_loss_percentage / 100,
            'take_profit_percentage': self.take_profit_percentage / 100,
            'max_leverage': self.max_leverage,
            'risk_free_rate': 0.02,
            'var_confidence': 0.95,
            'max_correlation': 0.8,
            'emergency_stop_loss': self.margin_emergency_ratio / 100
        }
    
    def get_position_size_multiplier(self, confidence: float) -> float:
        """Calculate position size multiplier based on confidence"""
        if not self.confidence_scaling_enabled:
            return 1.0
        
        # Scale between min and max multipliers based on confidence
        confidence = max(0.0, min(1.0, confidence))  # Clamp to [0, 1]
        multiplier = (
            self.confidence_min_multiplier + 
            (self.confidence_max_multiplier - self.confidence_min_multiplier) * confidence
        )
        return multiplier
    
    def get_leverage_for_volatility(self, volatility: float) -> int:
        """Calculate leverage based on market volatility"""
        if not self.leverage_scaling_by_volatility:
            return self.max_leverage
        
        # Reduce leverage in high volatility markets
        if volatility > 0.05:  # High volatility (>5%)
            return max(5, self.max_leverage // 4)
        elif volatility > 0.03:  # Medium volatility (3-5%)
            return max(10, self.max_leverage // 2)
        else:  # Low volatility (<3%)
            return self.max_leverage
    
    def get_leverage_for_confidence(self, confidence: float, base_leverage: int) -> int:
        """Calculate leverage based on confidence level"""
        if not self.leverage_scaling_by_confidence:
            return base_leverage
        
        # Increase leverage with higher confidence
        confidence_multiplier = 0.5 + (confidence * 0.5)  # 0.5x to 1.0x
        adjusted_leverage = int(base_leverage * confidence_multiplier)
        return max(1, min(adjusted_leverage, self.max_leverage))
    
    def is_trade_allowed(self, current_daily_loss: float, current_daily_trades: int) -> bool:
        """Check if trading is allowed based on daily limits"""
        # Check daily loss limit
        if current_daily_loss >= self.max_daily_loss_percentage / 100:
            return False
        
        # Check daily trade limit
        if current_daily_trades >= self.max_daily_trades:
            return False
        
        return True
    
    def calculate_stop_loss_price(self, entry_price: float, side: str, volatility: float = 0.02) -> float:
        """Calculate stop loss price with dynamic adjustment"""
        base_stop_percentage = self.stop_loss_percentage / 100
        
        # Adjust for volatility if enabled
        if self.dynamic_stop_loss_enabled:
            volatility_multiplier = 1.0 + (volatility * 2)  # Increase stop distance in high volatility
            adjusted_stop_percentage = base_stop_percentage * volatility_multiplier
        else:
            adjusted_stop_percentage = base_stop_percentage
        
        if side.lower() == 'buy':
            return entry_price * (1 - adjusted_stop_percentage)
        else:  # sell
            return entry_price * (1 + adjusted_stop_percentage)
    
    def calculate_take_profit_price(self, entry_price: float, side: str) -> float:
        """Calculate take profit price"""
        take_profit_percentage = self.take_profit_percentage / 100
        
        if side.lower() == 'buy':
            return entry_price * (1 + take_profit_percentage)
        else:  # sell
            return entry_price * (1 - take_profit_percentage)
    
    def get_partial_profit_levels(self, entry_price: float, side: str) -> list:
        """Get partial profit taking levels"""
        if not self.partial_profit_taking_enabled:
            return []
        
        levels = []
        for profit_percentage in self.partial_profit_levels:
            profit_decimal = profit_percentage / 100
            if side.lower() == 'buy':
                price = entry_price * (1 + profit_decimal)
            else:  # sell
                price = entry_price * (1 - profit_decimal)
            levels.append(price)
        
        return levels


# Global instance for easy access
PROFIT_MAX_RISK_CONFIG = ProfitMaximizationRiskConfig()

def get_profit_maximization_config() -> ProfitMaximizationRiskConfig:
    """Get the profit maximization risk configuration"""
    return PROFIT_MAX_RISK_CONFIG

def log_risk_configuration():
    """Log the current risk configuration"""
    config = get_profit_maximization_config()
    logger = logging.getLogger(__name__)
    
    logger.info("=== PROFIT MAXIMIZATION RISK CONFIGURATION ===")
    logger.info(f"Daily Profit Target: ${config.daily_profit_target:,.2f}")
    logger.info(f"Hourly Profit Target: ${config.hourly_profit_target:,.2f}")
    logger.info(f"Max Position Size: {config.max_position_size_percentage}% of account")
    logger.info(f"Stop Loss: {config.stop_loss_percentage}%")
    logger.info(f"Take Profit: {config.take_profit_percentage}%")
    logger.info(f"Max Leverage: {config.max_leverage}x")
    logger.info(f"Max Daily Loss: {config.max_daily_loss_percentage}%")
    logger.info(f"Max Total Exposure: {config.max_total_exposure_percentage}%")
    logger.info(f"Execution Speed Target: <{config.max_execution_time_ms}ms")
    logger.info("===============================================")
